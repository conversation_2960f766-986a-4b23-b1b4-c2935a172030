from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class AuthUser(BaseModel):
    id: Optional[str] = Field(None, example="<EMAIL>")
    fullname: Optional[str] = Field(None, example="John Doe")
    email: Optional[str] = Field(None, example="<EMAIL>")
    password_hash: Optional[str] = Field(None, example="bdf39900sdfk3")
    roles: Optional[list[str]] = Field(None, example=["Admin"])
    login_count: Optional[int] = Field(0, example=5)
    last_login: Optional[int] = Field(None, example=1683123456789)
    last_unsuccessful_login: Optional[int] = Field(None, example=1683123456789)
    is_mfa_enabled: Optional[bool] = Field(False, description="Enable Multi Factor Auth", example=True)
    totp_secret: Optional[str] = Field(None, example="secret")
    accepted_tos_date: Optional[int] = Field(None, example=1683123456789)
    company: Optional[str] = Field(None, example="Company Inc.")
    disabled: Optional[bool] = Field(False, description="Is the user disabled", example=False)

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load AuthUser from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load AuthUser from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load AuthUser from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load AuthUser from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())