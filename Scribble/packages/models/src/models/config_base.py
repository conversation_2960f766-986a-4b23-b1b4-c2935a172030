from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class ConfigBase(BaseModel):
    id: Optional[str] = Field("default", example="default")
    name: Optional[str] = Field("default", example="config")
    description: Optional[str] = Field(None, example="This is the base config")
    version: Optional[str] = Field("0.0.0", example="0.0.0")
    created: Optional[int] = Field(None, example=1683123456789, description="Timestamp when the config was created")
    modified: Optional[int] = Field(None, example=1683123456789, description="Timestamp when the config was last modified")
    debug: Optional[bool] = Field(False, description="Enable debug mode", example=False)
    log_level: Optional[str] = Field("INFO", description="Logging level", example="INFO")
    port: Optional[int] = Field(443, example=8053)
    num_pipelines: Optional[int] = Field(0, description="Number of pipelines to run", example=4)
    auth_enabled: Optional[bool] = Field(True, description="Enable authentication", example=True)
    ssl_enabled: Optional[bool] = Field(False, description="Enable SSL", example=True)
    resource_monitor_enabled: Optional[bool] = Field(True, description="Enable Resource Monitor", example=True)
    app_server_enabled: Optional[bool] = Field(True, description="Enable app server", example=True)

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load ConfigBase from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load ConfigBase from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load ConfigBase from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load ConfigBase from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())