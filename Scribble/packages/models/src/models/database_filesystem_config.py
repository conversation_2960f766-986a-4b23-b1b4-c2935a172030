from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class DatabaseFilesystemConfig(BaseModel):
    id: Optional[str] = Field("default", example="default")
    name: Optional[str] = Field("default", description="The default name of this client", example="database")
    database_dir: Optional[str] = Field(None, description="Database Directory", example="data/filesystem_db")

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load DatabaseFilesystemConfig from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load DatabaseFilesystemConfig from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load DatabaseFilesystemConfig from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load DatabaseFilesystemConfig from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())