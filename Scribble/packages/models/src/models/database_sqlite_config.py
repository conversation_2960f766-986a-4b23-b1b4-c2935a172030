from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class DatabaseSqliteConfig(BaseModel):
    id: Optional[str] = Field("default", example="default")
    name: Optional[str] = Field("default", description="The default name of this client", example="database")
    sqlite_path: Optional[str] = Field("data/sqlite_db.sqlite3", description="Database Directory", example="data/sqlite_db.sqlite3")

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load DatabaseSqliteConfig from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load DatabaseSqliteConfig from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load DatabaseSqliteConfig from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load DatabaseSqliteConfig from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())