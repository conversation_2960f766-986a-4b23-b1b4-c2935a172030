from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class DatabaseS3Config(BaseModel):
    id: Optional[str] = Field("default", example="default")
    name: Optional[str] = Field("default", description="The default name of this client", example="database")
    bucket_name: Optional[str] = Field(None, description="S3 Bucket Name", example="my-s3-bucket")
    region_name: Optional[str] = Field(None, description="AWS Region Name", example="us-west-2")
    aws_access_key_id: Optional[str] = Field(None, description="AWS Access Key ID", example="AKIAIOSFODNN7EXAMPLE")
    aws_secret_access_key: Optional[str] = Field(None, description="AWS Secret Access Key", example="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY")
    table_prefix: Optional[str] = Field("", description="Prefix for DynamoDB tables", example="myapp_")

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load DatabaseS3Config from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load DatabaseS3Config from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load DatabaseS3Config from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load DatabaseS3Config from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())