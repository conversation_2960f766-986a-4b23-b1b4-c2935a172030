from abc import ABC, abstractmethod
from typing import List, Dict, Any, Union

# For factory pattern (like MessageConfig)
from pydantic import BaseModel

class DatabaseConfig(BaseModel):
    """Base pydantic config for database clients."""
    name: str

class DatabaseClient(ABC):
    """Abstract interface for database clients (sync, simple)."""
    @abstractmethod
    def insert_item(self, table: str, key: str, item: dict) -> dict:
        pass
    @abstractmethod
    def get_item(self, table: str, key: str) -> dict:
        pass
    @abstractmethod
    def get_all_items(self, table: str) -> list:
        pass
    @abstractmethod
    def delete_item(self, table: str, key: str) -> None:
        pass

class DatabaseAdapter(ABC):

    @abstractmethod
    def upsert_item(self, table: str, key: str, item: dict) -> dict:
        """Upsert an item into the specified table."""
        pass

    @abstractmethod
    def insert_item(self, table: str, key:str, item: dict) -> dict:
        """Insert an item into the specified table."""
        pass

    @abstractmethod
    def get_item(self, table: str, key: str) -> dict:
        """Retrieve an item by its key from the specified table."""
        pass

    @abstractmethod
    def get_binary_item(self, table: str, key: str) -> bytes:
        """Retrieve an item by its key from the specified table."""
        pass

    @abstractmethod
    def get_all_items(self, table: str) -> list:
        """Retrieve all items from the specified table."""
        pass

    @abstractmethod
    def update_item(self, table: str, key: str, updates: dict) -> dict:
        """Update an item in the specified table."""
        pass

    @abstractmethod
    def delete_item(self, table: str, key: str) -> None:
        """Delete an item from the specified table by its key."""
        pass

    @abstractmethod
    def query_items(self, table_name: str, criteria: dict) -> list:
        """Query the database for items matching the given criteria."""
        pass

    @abstractmethod
    def search_by_key_part(
        self, table: str, key_part: str, regex: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Search for items whose keys contain or match a part of the given key.

        :param table: The table to search in.
        :param key_part: The key part to search for.
        :param regex: Whether to treat key_part as a regular expression. Defaults to False (prefix search).
        :return: A list of matching items.
        """
        pass

    @abstractmethod
    def copy_table(self, source_table: str, dest_table: str) -> None:
        """
        Copy all items from source_table to dest_table.
        """
        pass

    @abstractmethod
    def query(self, querystr: str) -> list:
        """
        Execute a backend-specific query string and return a list of results.
        For SQL backends, this is a SQL query. For others, NotImplementedError is raised.
        """
        pass


    @abstractmethod
    def insert_columns(
        self,
        table: str,
        columns: List[str],
        values: List[Any],
        conflict_strategy: str = "IGNORE"  # or "REPLACE", "FAIL", "ABORT"
    ) -> None:
        """
        Insert a new row into the table with specified columns and values.

        :param table: Table name.
        :param columns: List of column names to insert into.
        :param values: List of values corresponding to the columns.
        :param conflict_strategy: Optional SQL conflict strategy (e.g., IGNORE, REPLACE).
        """
        pass    