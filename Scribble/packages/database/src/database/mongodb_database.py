import logging
from typing import List, Dict, Any, Union, Optional
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection
from pymongo.errors import PyMongoError
from bson import ObjectId
import json
from .interface import DatabaseAdapter

logger = logging.getLogger(__name__)

from models.database_mongodb_config import DatabaseMongodbConfig

class MongoDBDatabase(DatabaseAdapter):
    """
    MongoDB database adapter with read replica support.
    Supports both primary (write) and replica (read) connections.
    """

    def __init__(self, config: DatabaseMongodbConfig):
        """Initialize MongoDB connection with strongly typed DatabaseMongodbConfig."""
        if not config:
            raise ValueError("Missing configuration for MongoDB database.")
        self.config = config
        self.replica_client = MongoClient(
            config.mongodb_replica_uri,
            maxPoolSize=config.mongodb_max_pool_size,
            minPoolSize=config.mongodb_min_pool_size
        )
        self.replica_db = self.replica_client[config.mongodb_database_name]
        self.read_db = self.replica_db
        self.write_db = self.replica_db  # Allow write operations
        logger.info(f"MongoDB initialized with read/write access - Replica: {config.mongodb_replica_uri}")
    
    def _get_collection(self, table: str, for_write: bool = False) -> Collection:
        """Get MongoDB collection for the given table."""
        db = self.write_db if for_write else self.read_db
        return db[table]
    
    def _convert_id(self, key: str) -> Union[str, ObjectId]:
        """Convert string key to ObjectId if it's a valid ObjectId, otherwise keep as string."""
        try:
            return ObjectId(key)
        except:
            return key
    
    def upsert_item(self, table: str, key: str, item: dict) -> dict:
        """Upsert an item into the specified table."""
        try:
            collection = self._get_collection(table, for_write=True)
            # Use the key as _id if it's not already present
            if '_id' not in item:
                item['_id'] = self._convert_id(key)
            
            result = collection.replace_one(
                {'_id': item['_id']}, 
                item, 
                upsert=True
            )
            
            if result.upserted_id:
                item['_id'] = str(result.upserted_id)
            else:
                item['_id'] = str(item['_id'])
                
            return item
        except PyMongoError as e:
            logger.error(f"Error upserting item in {table}: {e}")
            raise
    
    def insert_item(self, table: str, key: str, item: dict) -> dict:
        """Insert an item into the specified table."""
        try:
            collection = self._get_collection(table, for_write=True)
            # Use the key as _id if it's not already present
            if '_id' not in item:
                item['_id'] = self._convert_id(key)
            
            result = collection.insert_one(item)
            item['_id'] = str(result.inserted_id)
            return item
        except PyMongoError as e:
            logger.error(f"Error inserting item in {table}: {e}")
            raise
    
    def get_item(self, table: str, key: str) -> dict:
        """Retrieve an item by its key from the specified table."""
        try:
            collection = self._get_collection(table, for_write=False)
            doc_id = self._convert_id(key)
            item = collection.find_one({'_id': doc_id})
            
            if item:
                item['_id'] = str(item['_id'])
                return item
            return {}
        except PyMongoError as e:
            logger.error(f"Error getting item from {table}: {e}")
            raise
    
    def get_binary_item(self, table: str, key: str) -> bytes:
        """Retrieve a binary item by its key from the specified table."""
        try:
            collection = self._get_collection(table, for_write=False)
            doc_id = self._convert_id(key)
            item = collection.find_one({'_id': doc_id})
            
            if item and 'data' in item:
                return item['data']
            return b''
        except PyMongoError as e:
            logger.error(f"Error getting binary item from {table}: {e}")
            raise
    
    def get_all_items(self, table: str) -> list:
        """Retrieve all items from the specified table."""
        try:
            collection = self._get_collection(table, for_write=False)
            items = list(collection.find())
            
            # Convert ObjectIds to strings
            for item in items:
                item['_id'] = str(item['_id'])
            
            return items
        except PyMongoError as e:
            logger.error(f"Error getting all items from {table}: {e}")
            raise
    
    def update_item(self, table: str, key: str, updates: dict) -> dict:
        """Update an item in the specified table."""
        try:
            collection = self._get_collection(table, for_write=True)
            doc_id = self._convert_id(key)
            
            result = collection.update_one(
                {'_id': doc_id}, 
                {'$set': updates}
            )
            
            if result.modified_count > 0:
                return self.get_item(table, key)
            return {}
        except PyMongoError as e:
            logger.error(f"Error updating item in {table}: {e}")
            raise
    
    def delete_item(self, table: str, key: str) -> None:
        """Delete an item from the specified table by its key."""
        try:
            collection = self._get_collection(table, for_write=True)
            doc_id = self._convert_id(key)
            collection.delete_one({'_id': doc_id})
        except PyMongoError as e:
            logger.error(f"Error deleting item from {table}: {e}")
            raise
    
    def query_items(self, table_name: str, criteria: dict) -> list:
        """Query the database for items matching the given criteria."""
        try:
            collection = self._get_collection(table_name, for_write=False)
            
            # Convert string IDs to ObjectIds if they look like ObjectIds
            query = {}
            for key, value in criteria.items():
                if key == '_id' and isinstance(value, str):
                    query[key] = self._convert_id(value)
                else:
                    query[key] = value
            
            items = list(collection.find(query))
            
            # Convert ObjectIds to strings
            for item in items:
                item['_id'] = str(item['_id'])
            
            return items
        except PyMongoError as e:
            logger.error(f"Error querying items from {table_name}: {e}")
            raise
    
    def search_by_key_part(self, table: str, key_part: str, regex: bool = False) -> List[Dict[str, Any]]:
        """Search for items whose keys contain or match a part of the given key."""
        try:
            collection = self._get_collection(table, for_write=False)
            
            if regex:
                import re
                pattern = re.compile(key_part, re.IGNORECASE)
                query = {'_id': pattern}
            else:
                # For MongoDB, we'll search by string pattern
                query = {'_id': {'$regex': key_part, '$options': 'i'}}
            
            items = list(collection.find(query))
            
            # Convert ObjectIds to strings
            for item in items:
                item['_id'] = str(item['_id'])
            
            return items
        except PyMongoError as e:
            logger.error(f"Error searching by key part in {table}: {e}")
            raise
    
    def copy_table(self, source_table: str, dest_table: str) -> None:
        """Copy all items from source_table to dest_table."""
        try:
            source_collection = self._get_collection(source_table, for_write=False)
            dest_collection = self._get_collection(dest_table, for_write=True)
            
            items = source_collection.find()
            if items.count() > 0:
                dest_collection.insert_many(items)
        except PyMongoError as e:
            logger.error(f"Error copying table {source_table} to {dest_table}: {e}")
            raise
    
    def query(self, querystr: str) -> list:
        """Execute a MongoDB aggregation pipeline and return results."""
        try:
            # Parse the query string as JSON (MongoDB aggregation pipeline)
            pipeline = json.loads(querystr)
            
            # Determine which database to use (default to read_db)
            db = self.read_db
            
            # Execute the aggregation pipeline
            result = list(db.command('aggregate', pipeline))
            return result
        except (json.JSONDecodeError, PyMongoError) as e:
            logger.error(f"Error executing MongoDB query: {e}")
            raise
    
    def insert_columns(self, table: str, columns: List[str], values: List[Any], conflict_strategy: str = "IGNORE") -> None:
        """Insert a new row into the table with specified columns and values."""
        try:
            collection = self._get_collection(table, for_write=True)
            
            # Create document from columns and values
            doc = dict(zip(columns, values))
            
            if conflict_strategy == "IGNORE":
                # Use upsert with $setOnInsert to ignore conflicts
                collection.update_one(
                    {'_id': doc.get('_id')}, 
                    {'$setOnInsert': doc}, 
                    upsert=True
                )
            elif conflict_strategy == "REPLACE":
                # Use replace_one to replace existing document
                collection.replace_one(
                    {'_id': doc.get('_id')}, 
                    doc, 
                    upsert=True
                )
            else:
                # Default insert
                collection.insert_one(doc)
        except PyMongoError as e:
            logger.error(f"Error inserting columns in {table}: {e}")
            raise
    
    def close(self):
        """Close database connections."""
        if self.replica_client:
            self.replica_client.close()
        logger.info("MongoDB read-only connections closed")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
