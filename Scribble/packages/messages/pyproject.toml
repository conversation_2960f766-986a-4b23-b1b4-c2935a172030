[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "messages"
version = "0.1.0"
description = "A message management library for CloudSeeder2"
authors = [{ name = "GroGBot", email = "<EMAIL>" }]
license = { file = "LICENSE" }
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "pydantic",
    "slack_sdk",
    "requests"
]

[project.optional-dependencies]
dev = ["pytest", "black", "mypy", "pytest-mock"]

# Optional extras for additional integrations
cloud = ["slack-sdk"]

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
messages = ["*.json"]
