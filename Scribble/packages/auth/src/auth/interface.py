from abc import ABC, abstractmethod
import hashlib
from typing import Optional

class AuthProvider(ABC):
    """Abstract interface for authentication providers."""

    def generate_hash(self, password: str) -> str:
        return hashlib.sha256(password.encode()).hexdigest()

    @abstractmethod
    def register_user(self, username: str, password: str = None, password_hash: str = None) -> dict:
        """Register a new user."""
        pass

    @abstractmethod
    def authenticate(self, username: str, password: str, in_token: str = None) -> Optional[str]:
        """Authenticate a user and return a token if successful."""
        pass

    @abstractmethod
    def get_user(self, token: str) -> Optional[dict]:
        """Retrieve user details from token."""
        pass

    @abstractmethod
    def refresh_token(self, refresh_token: str) -> Optional[str]:
        """Refresh an authentication token."""
        pass

    @abstractmethod
    def logout(self, token: str) -> bool:
        """Logout the user (invalidate token)."""
        pass

    @abstractmethod
    def generate_token(self, user: dict) -> str:
        """Generate a JWT token for the given user."""
        pass
