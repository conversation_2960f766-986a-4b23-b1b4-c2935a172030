import tempfile
import shutil
import os
import pytest
from auth.local_auth import LocalAuthProvider
from database.factory import create_database_client, get_database_client
from models.database_tinydb_config import DatabaseTinydbConfig

@pytest.fixture
def tinydb_instance():
    temp_dir = tempfile.mkdtemp()
    config = DatabaseTinydbConfig()  # use all defaults
    create_database_client(config)
    db = get_database_client()  # default name
    yield db
    shutil.rmtree(temp_dir)

@pytest.fixture
def auth_provider(tinydb_instance):
    config = {"foo": "bar"}
    LocalAuthProvider._instance = None
    provider = LocalAuthProvider(config)
    provider.database = tinydb_instance
    return provider

def test_register_and_authenticate(auth_provider):
    username = "<EMAIL>"
    password = "testpass"
    # Remove user if it already exists for idempotency
    if auth_provider.database.get_item("user", username):
        auth_provider.database.delete_item("user", username)
    result = auth_provider.register_user(username, password_hash=auth_provider.generate_hash(password))
    assert result["username"] == username
    token = auth_provider.authenticate(username, password)
    assert token is not None
    user = auth_provider.get_user(token)
    assert user["id"] == username
    assert user["email"] == username
    assert auth_provider.logout(token) is True

def test_register_duplicate_user(auth_provider):
    username = "<EMAIL>"
    password_hash = auth_provider.generate_hash("pw")
    # Remove user if it already exists for idempotency
    if auth_provider.database.get_item("user", username):
        auth_provider.database.delete_item("user", username)
    auth_provider.register_user(username, password_hash=password_hash)
    with pytest.raises(ValueError):
        auth_provider.register_user(username, password_hash=password_hash)

def test_authenticate_wrong_password(auth_provider):
    username = "<EMAIL>"
    password = "pw1"
    # Remove user if it already exists for idempotency
    if auth_provider.database.get_item("user", username):
        auth_provider.database.delete_item("user", username)
    auth_provider.register_user(username, password_hash=auth_provider.generate_hash(password))
    assert auth_provider.authenticate(username, "badpw") is None

def test_generate_token_and_refresh(auth_provider):
    user = {"id": "<EMAIL>", "email": "<EMAIL>", "roles": ["User"]}
    # Remove user if it already exists for idempotency
    if auth_provider.database.get_item("user", user["id"]):
        auth_provider.database.delete_item("user", user["id"])
    auth_provider.database.insert_item("user", user["id"], user)
    token = auth_provider.generate_token(user)
    assert token is not None
    new_token = auth_provider.refresh_token(token)
    assert new_token is not None
    assert isinstance(new_token, str)
