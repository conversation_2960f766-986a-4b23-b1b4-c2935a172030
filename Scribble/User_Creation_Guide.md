# Scribble User Management Guide

## Overview
This guide explains how to create and manage users in Scribble admin using the available APIs.

## API Endpoints

### Base URL
- **Local Development**: `http://localhost:8001`
- **Production**: Update the `base_url` variable in Postman

### Authentication
Most endpoints require authentication. Use the admin credentials to get a bearer token.

### Master Admin User
The system automatically creates a master admin user on first startup:
- **Email**: `<EMAIL>`
- **Password**: `wD7_wTDQ2gIFc2IgNj8J1w` (stored in `apps/ai_core/admin_password.txt`)
- **Role**: `["Admin"]`

Use these credentials to login and create other users.

## Creating New Users

### Method 1: Direct User Creation (Recommended for Admin)

**Endpoint**: `POST /v1/user`

**Headers**:
```
Content-Type: application/json
Authorization: Bearer {your_token}
```

**Request Body**:
```json
{
  "id": "<EMAIL>",
  "name": "<PERSON>",
  "email": "<EMAIL>", 
  "password_hash": "hashed_password_here",
  "roles": ["User"]
}
```

**Available Roles**:
- `"Admin"` - Full administrative access
- `"User"` - Standard user access
- Custom roles as defined in your system

### Method 2: User Registration Flow

**Endpoint**: `POST /v1/registration`

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "plain_text_password",
  "state": "registered"
}
```

**Registration States**:
- `"registered"` - Initial registration
- `"verified"` - Email verified
- `"mfa"` - MFA setup required
- `"active"` - Fully active user
- `"deactivated"` - Deactivated user

## Password Hashing

⚠️ **Important**: The API expects `password_hash`, not plain text passwords for user creation.

You need to hash passwords before sending them. The frontend uses this approach:

```javascript
const password_hash = await hashPassword(plainTextPassword);
```

For testing, you can use online SHA-256 tools or implement hashing in your preferred language.

## Example Workflows

### 1. Create Admin User
```json
{
  "id": "<EMAIL>",
  "name": "System Administrator", 
  "email": "<EMAIL>",
  "password_hash": "your_hashed_password",
  "roles": ["Admin"]
}
```

### 2. Create Regular User
```json
{
  "id": "<EMAIL>",
  "name": "Regular User",
  "email": "<EMAIL>", 
  "password_hash": "your_hashed_password",
  "roles": ["User"]
}
```

## Using the Postman Collection

1. **Import the Collection**: Import `Scribble_User_Management.postman_collection.json` into Postman

2. **Set Variables**:
   - `base_url`: Set to your AI Core service URL (default: `http://localhost:8001`)
   - `auth_token`: Will be set automatically after login

3. **Authentication Flow**:
   - Use the "Login" request first to get your auth token
   - The token will be automatically stored in the `auth_token` variable

4. **Create Users**:
   - Use the "Create User" request
   - Modify the request body with actual user details
   - Ensure password is properly hashed

## API Response Examples

### Successful User Creation
```json
{
  "id": "<EMAIL>",
  "name": "John Doe", 
  "email": "<EMAIL>",
  "roles": ["User"],
  "modified": 1754896748327
}
```

### Error Response
```json
{
  "status_code": 400,
  "detail": "User already exists"
}
```

## Additional User Management

### Get All Users
`GET /v1/user-list` - Returns list of all users

### Get Specific User  
`GET /v1/user/{user_id}` - Returns specific user details

### Update User
`PUT /v1/user/{user_id}` - Updates user information

### Delete User
`DELETE /v1/user/{user_id}` - Removes user from system

## Security Notes

1. **Authentication Required**: Most endpoints require valid bearer token
2. **Role-Based Access**: Admin role has full access, other roles may be restricted
3. **Password Security**: Always hash passwords before storing
4. **HTTPS**: Use HTTPS in production environments

## Troubleshooting

### Common Issues:
1. **401 Unauthorized**: Check your bearer token
2. **403 Forbidden**: Insufficient permissions (need Admin role)
3. **400 Bad Request**: Check request body format and required fields
4. **500 Internal Server Error**: Check server logs for details

### Debug Tips:
- Enable debug logging in AI Core service
- Check database connectivity
- Verify authentication configuration
