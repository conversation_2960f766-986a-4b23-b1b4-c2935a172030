import { createContext, useContext, useState, ReactNode, useEffect } from 'react';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  accessToken: string | null;
  setAccessToken: (token: string | null) => void;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar: string;
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  login: async () => {},
  logout: () => {},
  accessToken: null,
  setAccessToken: () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessTokenState] = useState<string | null>(
    localStorage.getItem('accessToken') || null
  );

  // Check if user is logged in from localStorage on initial load
  useEffect(() => {
    const storedUser = localStorage.getItem('adminUser');
    const storedToken = localStorage.getItem('accessToken');
    if (storedUser && storedToken) {
      setUser(JSON.parse(storedUser));
      setIsAuthenticated(true);
      setAccessTokenState(storedToken);
    }
  }, []);

  // Helper to decode JWT (returns Record<string, unknown> or null)
  const parseJwt = (token: string): Record<string, unknown> | null => {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch {
      return null;
    }
  };

  const login = async (email: string, _password: string) => {
    // _password is unused, but kept for API compatibility
    const token = localStorage.getItem('accessToken');
    let userFromToken: Partial<User> = {};
    if (token) {
      const payload = parseJwt(token);
      if (payload) {
        userFromToken = {
          id: (payload.sub as string) || (payload.user_id as string) || (payload.email as string) || email,
          name: (payload.name as string) || '',
          email: (payload.email as string) || email,
          role: (payload.role as string) || (payload.roles as string) || 'user',
          avatar: (payload.avatar as string) || `https://i.pravatar.cc/150?u=${(payload.email as string) || email}`,
        };
      }
    }
    const userObj: User = {
      id: userFromToken.id || '1',
      name: userFromToken.name || userFromToken.email || email,
      email: userFromToken.email || email,
      role: userFromToken.role || 'user',
      avatar: userFromToken.avatar || `https://i.pravatar.cc/150?u=${userFromToken.email || email}`,
    };
    localStorage.setItem('adminUser', JSON.stringify(userObj));
    setUser(userObj);
    setIsAuthenticated(true);
    // accessToken should be set by the login page after real API call
  };

  const logout = () => {
    localStorage.removeItem('adminUser');
    localStorage.removeItem('accessToken');
    setUser(null);
    setIsAuthenticated(false);
    setAccessTokenState(null);
  };

  const setAccessToken = (token: string | null) => {
    setAccessTokenState(token);
    if (token) {
      localStorage.setItem('accessToken', token);
    } else {
      localStorage.removeItem('accessToken');
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout, accessToken, setAccessToken }}>
      {children}
    </AuthContext.Provider>
  );
};