import { Box, Button, Typography, Container } from '@mui/material';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const NotFoundPage = () => {
  const navigate = useNavigate();
  
  return (
    <Container maxWidth="md">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          textAlign: 'center',
          py: 5,
        }}
      >
        <Typography 
          variant="h1" 
          sx={{ 
            fontSize: { xs: '6rem', sm: '10rem' }, 
            fontWeight: 700, 
            color: 'primary.main',
            mb: 2,
            lineHeight: 1
          }}
        >
          404
        </Typography>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 2
          }}
        >
          Page Not Found
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ 
            maxWidth: 450, 
            mb: 4,
            fontSize: '1.1rem'
          }}
        >
          The page you're looking for doesn't exist or has been moved.
          Let's get you back to the dashboard.
        </Typography>
        <Button
          variant="contained"
          size="large"
          startIcon={<ArrowLeft size={20} />}
          onClick={() => navigate('/')}
          sx={{ 
            px: 4, 
            py: 1.5,
            fontWeight: 600,
            fontSize: '1rem',
            borderRadius: 2,
            boxShadow: 2,
            '&:hover': {
              transform: 'translateY(-2px)',
              transition: 'transform 0.2s'
            }
          }}
        >
          Back to Dashboard
        </Button>
      </Box>
    </Container>
  );
};

export default NotFoundPage;