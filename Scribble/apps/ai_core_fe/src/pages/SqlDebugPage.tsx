import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Card,
  CardContent,
} from '@mui/material';
import { Play } from 'lucide-react';
import { apiClient } from '../apiClient';

interface SqlResult {
  id: string;
  records: Record<string, string | number | boolean | null>[];
}

interface SqlError {
  message: string;
  code?: string;
  line?: number;
}

const SqlDebugPage: React.FC = () => {
  const [sqlQuery, setSqlQuery] = useState('');
  const [result, setResult] = useState<SqlResult | null>(null);
  const [error, setError] = useState<SqlError | null>(null);
  const [loading, setLoading] = useState(false);

  const executeSql = async () => {
    if (!sqlQuery.trim()) {
      setError({ message: 'Please enter a SQL query' });
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await apiClient<SqlResult>('sql', {
        method: 'POST',
        body: JSON.stringify({
          id: crypto.randomUUID(),
          sql: sqlQuery,
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      }, true);
      
      setResult(response);
    } catch (err: unknown) {
      const error = err as Error;
      setError({
        message: error.message || 'An error occurred while executing the query',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.ctrlKey && event.key === 'Enter') {
      event.preventDefault();
      executeSql();
    }
  };

  const renderTable = () => {
    if (!result || !result.records || result.records.length === 0) return null;

    // Extract column names from the first record
    const columns = Object.keys(result.records[0]);

    return (
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              {columns.map((column, index) => (
                <TableCell key={index} sx={{ fontWeight: 'bold' }}>
                  {column}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {result.records.map((record, rowIndex) => (
              <TableRow key={rowIndex}>
                {columns.map((column, cellIndex) => (
                  <TableCell key={cellIndex}>
                    {record[column] === null || record[column] === undefined ? (
                      <em style={{ color: '#888' }}>NULL</em>
                    ) : (
                      String(record[column])
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Box sx={{ p: 3, maxWidth: '100%', mx: 'auto' }}>
      <Typography variant="h4" component="h1" gutterBottom>
        SQL Debug Console
      </Typography>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        Execute SQL queries directly against the database. Use Ctrl+Enter to run queries.
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            multiline
            rows={8}
            variant="outlined"
            label="SQL Query"
            placeholder="Enter your SQL query here..."
            value={sqlQuery}
            onChange={(e) => setSqlQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            sx={{
              mb: 2,
              '& .MuiInputBase-input': {
                fontFamily: 'monospace',
                fontSize: '14px',
              },
            }}
          />
          
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={20} /> : <Play size={20} />}
              onClick={executeSql}
              disabled={loading || !sqlQuery.trim()}
            >
              {loading ? 'Executing...' : 'Execute Query'}
            </Button>
            
            <Typography variant="body2" color="text.secondary">
              Ctrl+Enter to execute
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="body2" component="div">
            <strong>Error:</strong> {error.message}
            {error.code && (
              <div>
                <strong>Code:</strong> {error.code}
              </div>
            )}
            {error.line && (
              <div>
                <strong>Line:</strong> {error.line}
              </div>
            )}
          </Typography>
        </Alert>
      )}

      {result && (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" component="h2">
                Query Results
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Rows: {result.records?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  ID: {result.id}
                </Typography>
              </Box>
            </Box>
            
            {!result.records || result.records.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                Query executed successfully. No rows returned.
              </Typography>
            ) : (
              renderTable()
            )}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default SqlDebugPage;
