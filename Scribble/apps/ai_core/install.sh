#!/bin/bash
# managed
# install venv
# This script is used to install the ai_core package and its dependencies in a virtual environment.
set -e
# create a virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

source venv/bin/activate
# install the required packages
pip install --upgrade pip
pip install -e .

# move to the packages directory and install the desired packages
cd ../../packages/auth
pip install -e .
cd ../config
pip install -e .
cd ../database
pip install -e .
cd ../messages
pip install -e .
cd ../models
pip install -e .
cd ../monitor
pip install -e .
cd ../queues
pip install -e .
