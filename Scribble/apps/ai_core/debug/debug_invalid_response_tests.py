"""
Debug script for testing invalid response scenarios with detailed logging.

This script runs various invalid response test cases in debug mode with
comprehensive error reporting and step-by-step execution tracking.
"""

import json
import sys
import os
import traceback
import logging
from unittest.mock import patch, MagicMock

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Add the src directory to the path so we can import the module
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..', 'src')
sys.path.insert(0, src_dir)

try:
    from ai_core.answer_questions_langchain_knowledgebase_subgrouping import (
        parse_and_validate_llm_response,
        response_schema,
        single_response_schema
    )
    print("✅ Successfully imported required modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    # Try alternative import
    try:
        sys.path.insert(0, os.path.join(current_dir, '..'))
        from src.ai_core.answer_questions_langchain_knowledgebase_subgrouping import (
            parse_and_validate_llm_response,
            response_schema,
            single_response_schema
        )
        print("✅ Successfully imported required modules (alternative path)")
    except ImportError:
        # Last resort - direct file import
        import importlib.util
        module_path = os.path.join(src_dir, 'ai_core', 'answer_questions_langchain_knowledgebase_subgrouping.py')
        if os.path.exists(module_path):
            spec = importlib.util.spec_from_file_location("answer_questions_module", module_path)
            answer_questions_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(answer_questions_module)

            parse_and_validate_llm_response = answer_questions_module.parse_and_validate_llm_response
            response_schema = answer_questions_module.response_schema
            single_response_schema = answer_questions_module.single_response_schema
            print("✅ Successfully imported required modules (direct import)")
        else:
            print(f"❌ Module file not found at: {module_path}")
            print("Make sure you're running from the correct directory")
            sys.exit(1)


def debug_test_empty_response():
    """Debug test for empty response."""
    print("\n" + "="*60)
    print("🔍 DEBUG TEST: Empty Response")
    print("="*60)
    
    question_answer_list = []
    
    print("📝 Test setup:")
    print(f"   - Initial list length: {len(question_answer_list)}")
    print(f"   - Response: '' (empty string)")
    
    try:
        print("\n🚀 Calling parse_and_validate_llm_response...")
        result = parse_and_validate_llm_response("", question_answer_list)
        
        print(f"✅ Function returned: {result}")
        print(f"📊 Final list length: {len(question_answer_list)}")
        print(f"📋 List contents: {question_answer_list}")
        
    except Exception as e:
        print(f"❌ Exception occurred: {type(e).__name__}: {e}")
        print(f"📍 Traceback:")
        traceback.print_exc()


def debug_test_invalid_json():
    """Debug test for invalid JSON."""
    print("\n" + "="*60)
    print("🔍 DEBUG TEST: Invalid JSON")
    print("="*60)
    
    question_answer_list = []
    invalid_json = "```json\n{invalid json content\n```"
    
    print("📝 Test setup:")
    print(f"   - Initial list length: {len(question_answer_list)}")
    print(f"   - Response: {repr(invalid_json)}")
    
    # Create a mock logger to capture log calls
    mock_logger = MagicMock()
    
    try:
        print("\n🚀 Calling parse_and_validate_llm_response...")
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger', mock_logger):
            result = parse_and_validate_llm_response(invalid_json, question_answer_list)
            
        print(f"✅ Function returned: {result}")
        print(f"📊 Final list length: {len(question_answer_list)}")
        
    except Exception as e:
        print(f"❌ Exception occurred: {type(e).__name__}: {e}")
        print(f"📍 Exception details: {str(e)}")
        print(f"📋 Logger error calls: {mock_logger.error.call_count}")
        if mock_logger.error.called:
            print(f"📝 Logger error message: {mock_logger.error.call_args}")
        print(f"📍 Traceback:")
        traceback.print_exc()


def debug_test_schema_validation_failure_single():
    """Debug test for schema validation failure (single response)."""
    print("\n" + "="*60)
    print("🔍 DEBUG TEST: Schema Validation Failure (Single Response)")
    print("="*60)
    
    question_answer_list = []
    
    # Missing required field 'question_code'
    invalid_response = {
        "question_text": "Primary Diagnosis",
        "question_type": "text",
        "answer_context": "Patient has diabetes",
        "answer_reason": "Based on medical history",
        "answer_text": "Diabetes mellitus",
        "confidence_score": 0.95
    }
    
    response_string = f"```json\n{json.dumps(invalid_response, indent=2)}\n```"
    
    print("📝 Test setup:")
    print(f"   - Initial list length: {len(question_answer_list)}")
    print(f"   - Missing field: 'question_code'")
    print(f"   - Response structure:")
    for key, value in invalid_response.items():
        print(f"     {key}: {value}")
    
    print(f"\n📄 Full response string:")
    print(response_string)
    
    # Create a mock logger to capture log calls
    mock_logger = MagicMock()
    
    try:
        print("\n🚀 Calling parse_and_validate_llm_response...")
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger', mock_logger):
            result = parse_and_validate_llm_response(response_string, question_answer_list)
            
        print(f"✅ Function returned: {result}")
        print(f"📊 Final list length: {len(question_answer_list)}")
        
    except Exception as e:
        print(f"❌ Exception occurred: {type(e).__name__}: {e}")
        print(f"📍 Exception details: {str(e)}")
        print(f"📋 Logger error calls: {mock_logger.error.call_count}")
        if mock_logger.error.called:
            print(f"📝 Logger error message: {mock_logger.error.call_args}")
        print(f"📍 Traceback:")
        traceback.print_exc()


def debug_test_schema_validation_failure_list():
    """Debug test for schema validation failure (list response)."""
    print("\n" + "="*60)
    print("🔍 DEBUG TEST: Schema Validation Failure (List Response)")
    print("="*60)
    
    question_answer_list = []
    
    # One item missing required field
    invalid_responses = [
        {
            "question_code": "M1021",
            "question_text": "Primary Diagnosis",
            "question_type": "text",
            "answer_context": "Patient has diabetes",
            "answer_reason": "Based on medical history",
            "answer_text": "Diabetes mellitus",
            "confidence_score": 0.95
        },
        {
            # Missing question_code
            "question_text": "Secondary Diagnosis",
            "question_type": "text",
            "answer_context": "Patient has hypertension",
            "answer_reason": "Based on vital signs",
            "answer_text": "Hypertension",
            "confidence_score": 0.90
        }
    ]
    
    response_string = f"```json\n{json.dumps(invalid_responses, indent=2)}\n```"
    
    print("📝 Test setup:")
    print(f"   - Initial list length: {len(question_answer_list)}")
    print(f"   - Response contains {len(invalid_responses)} items")
    print(f"   - Item 1: Valid (has question_code)")
    print(f"   - Item 2: Invalid (missing question_code)")
    
    print(f"\n📄 Full response string:")
    print(response_string)
    
    # Create a mock logger to capture log calls
    mock_logger = MagicMock()
    
    try:
        print("\n🚀 Calling parse_and_validate_llm_response...")
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger', mock_logger):
            result = parse_and_validate_llm_response(response_string, question_answer_list)
            
        print(f"✅ Function returned: {result}")
        print(f"📊 Final list length: {len(question_answer_list)}")
        
    except Exception as e:
        print(f"❌ Exception occurred: {type(e).__name__}: {e}")
        print(f"📍 Exception details: {str(e)}")
        print(f"📋 Logger error calls: {mock_logger.error.call_count}")
        if mock_logger.error.called:
            print(f"📝 Logger error message: {mock_logger.error.call_args}")
        print(f"📍 Traceback:")
        traceback.print_exc()


def debug_test_wrong_data_type():
    """Debug test for wrong data type."""
    print("\n" + "="*60)
    print("🔍 DEBUG TEST: Wrong Data Type")
    print("="*60)
    
    question_answer_list = []
    
    # Just a string instead of object/array
    wrong_data = "just a string"
    response_string = f"```json\n{json.dumps(wrong_data)}\n```"
    
    print("📝 Test setup:")
    print(f"   - Initial list length: {len(question_answer_list)}")
    print(f"   - Data type: {type(wrong_data).__name__}")
    print(f"   - Data value: {repr(wrong_data)}")
    
    print(f"\n📄 Full response string:")
    print(response_string)
    
    # Create a mock logger to capture log calls
    mock_logger = MagicMock()
    
    try:
        print("\n🚀 Calling parse_and_validate_llm_response...")
        
        with patch('ai_core.answer_questions_langchain_knowledgebase_subgrouping.logger', mock_logger):
            result = parse_and_validate_llm_response(response_string, question_answer_list)
            
        print(f"✅ Function returned: {result}")
        print(f"📊 Final list length: {len(question_answer_list)}")
        
    except Exception as e:
        print(f"❌ Exception occurred: {type(e).__name__}: {e}")
        print(f"📍 Exception details: {str(e)}")
        print(f"📋 Logger error calls: {mock_logger.error.call_count}")
        if mock_logger.error.called:
            print(f"📝 Logger error message: {mock_logger.error.call_args}")
        print(f"📍 Traceback:")
        traceback.print_exc()


def main():
    """Run all debug tests."""
    print("🐛 DEBUG MODE: Invalid Response Test Cases")
    print("=" * 80)
    
    # Run all debug tests
    debug_test_empty_response()
    debug_test_invalid_json()
    debug_test_schema_validation_failure_single()
    debug_test_schema_validation_failure_list()
    debug_test_wrong_data_type()
    
    print("\n" + "=" * 80)
    print("🏁 All debug tests completed!")
    print("=" * 80)


if __name__ == "__main__":
    main()
