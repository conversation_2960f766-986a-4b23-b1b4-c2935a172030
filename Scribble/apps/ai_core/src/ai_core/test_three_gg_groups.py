#!/usr/bin/env python3
"""
Test the new three-group structure for GG questions
"""

import json
import os

def test_gg_group_mapping():
    """Test that GG questions map to the correct three groups"""
    print("Testing GG question group mapping...")
    
    # Define the expected mappings
    test_cases = [
        # Prior Functioning (GG0100-GG0110)
        ("GG0100", "Prior Functioning"),
        ("GG0105", "Prior Functioning"), 
        ("GG0110", "Prior Functioning"),
        
        # Self-Care (GG0130)
        ("GG0130", "Self-Care"),
        ("GG0130.A", "Self-Care"),
        ("GG0130.B", "Self-Care"),
        
        # Mobility (GG0170)
        ("GG0170", "Mobility"),
        ("GG0170.A", "Mobility"),
        ("GG0170.B", "Mobility"),
    ]
    
    # Test the mapping logic manually (simulating the get_oasis_group function)
    def simulate_get_oasis_group(question_code):
        """Simulate the OASIS group mapping logic"""
        code = question_code.upper().strip()
        
        # Extract number part for GG codes
        if code.startswith("GG"):
            number_part = code[2:].split('.')[0]  # Remove any suffix like ".A"
            q_num = int(number_part)
            
            # Check ranges
            if 100 <= q_num <= 110:
                return "Prior Functioning"
            elif q_num == 130:
                return "Self-Care"
            elif q_num == 170:
                return "Mobility"
        
        return "Unknown"
    
    print("Testing question code mappings:")
    all_passed = True
    
    for code, expected_group in test_cases:
        actual_group = simulate_get_oasis_group(code)
        if actual_group == expected_group:
            print(f"✅ {code} -> {actual_group}")
        else:
            print(f"❌ {code} -> Expected: {expected_group}, Got: {actual_group}")
            all_passed = False
    
    return all_passed

def test_configuration_for_three_groups():
    """Test that configuration includes all three GG groups"""
    print("\nTesting configuration for three GG groups...")
    
    config_path = "clinician_observation_config.json"
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        obs_config = config.get("clinician_observation_config", {})
        
        # Check for the three new GG groups
        expected_gg_groups = ["Prior Functioning", "Self-Care", "Mobility"]
        
        print("Checking GG group configurations:")
        all_found = True
        
        for group_name in expected_gg_groups:
            if group_name in obs_config:
                group_config = obs_config[group_name]
                enabled = group_config.get("enabled", False)
                search_queries = group_config.get("search_queries", [])
                observation_prompt = group_config.get("observation_prompt", "")
                
                print(f"✅ {group_name}:")
                print(f"  - Enabled: {enabled}")
                print(f"  - Search queries: {len(search_queries)}")
                print(f"  - Has observation prompt: {len(observation_prompt) > 0}")
                
                if len(search_queries) > 0:
                    print(f"  - Example query: {search_queries[0]}")
                
            else:
                print(f"❌ {group_name} not found in configuration")
                all_found = False
        
        # Check that old "Functional Abilities" group is removed
        if "Functional Abilities" in obs_config:
            print("⚠️  Old 'Functional Abilities' group still exists - should be removed")
        else:
            print("✅ Old 'Functional Abilities' group successfully removed")
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False

def test_group_specificity():
    """Test that each group has appropriate search queries"""
    print("\nTesting group-specific search queries...")
    
    config_path = "clinician_observation_config.json"
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        obs_config = config.get("clinician_observation_config", {})
        
        # Test specific search query expectations
        group_expectations = {
            "Prior Functioning": ["prior", "before", "previous", "baseline"],
            "Self-Care": ["bathing", "dressing", "grooming", "eating", "self-care"],
            "Mobility": ["wheelchair", "walker", "cane"]
        }
        
        print("Checking search query specificity:")
        all_appropriate = True
        
        for group_name, expected_keywords in group_expectations.items():
            if group_name in obs_config:
                search_queries = obs_config[group_name].get("search_queries", [])
                
                if search_queries:
                    query_text = " ".join(search_queries).lower()
                    found_keywords = [kw for kw in expected_keywords if kw in query_text]
                    
                    print(f"✅ {group_name}:")
                    print(f"  - Expected keywords: {expected_keywords}")
                    print(f"  - Found keywords: {found_keywords}")
                    print(f"  - Query: {search_queries[0]}")
                    
                    if len(found_keywords) > 0:
                        print(f"  - ✅ Contains relevant keywords")
                    else:
                        print(f"  - ⚠️  May not contain expected keywords")
                else:
                    print(f"❌ {group_name} has no search queries")
                    all_appropriate = False
        
        return all_appropriate
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Three GG Groups Structure\n")
    print("=" * 60)
    
    success = True
    
    # Test group mapping
    if not test_gg_group_mapping():
        print("\n❌ GG group mapping test failed")
        success = False
    
    # Test configuration
    if not test_configuration_for_three_groups():
        print("\n❌ Configuration test failed")
        success = False
    
    # Test group specificity
    if not test_group_specificity():
        print("\n❌ Group specificity test failed")
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests completed successfully!")
        print("\n🎯 Three GG Groups Structure:")
        print("  1. Prior Functioning (GG0100-GG0110)")
        print("  2. Self-Care (GG0130)")
        print("  3. Mobility (GG0170)")
        print("\n🚀 Each group has specific, targeted search queries!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit(main())
