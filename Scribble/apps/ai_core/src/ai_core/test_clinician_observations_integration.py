#!/usr/bin/env python3
"""
Integration test for clinician observation generation with RAG system
"""

import json
import os
import sys

# Add the current directory to the path so we can import the module
sys.path.insert(0, os.path.dirname(__file__))

def test_config_loading():
    """Test loading the clinician observation configuration"""
    print("Testing clinician observation configuration loading...")
    
    try:
        from answer_questions_langchain_knowledgebase_subgrouping import load_clinician_observation_config
        
        config = load_clinician_observation_config()
        
        if not config:
            print("❌ Failed to load configuration")
            return False
        
        print("✅ Configuration loaded successfully")
        
        # Check if the main config key exists
        if "clinician_observation_config" not in config:
            print("❌ Missing 'clinician_observation_config' key")
            return False
        
        print("✅ Main configuration key found")
        
        # Check some expected groups
        expected_groups = ["Functional Abilities", "Cognitive Status", "ADLs Assessment"]
        obs_config = config["clinician_observation_config"]
        
        for group in expected_groups:
            if group in obs_config:
                group_config = obs_config[group]
                print(f"✅ Found configuration for '{group}'")
                
                # Check required fields
                if "enabled" in group_config and "search_queries" in group_config and "observation_prompt" in group_config:
                    print(f"  ✅ All required fields present for '{group}'")
                    print(f"  - Enabled: {group_config['enabled']}")
                    print(f"  - Search queries: {len(group_config['search_queries'])} queries")
                    print(f"  - Observation prompt length: {len(group_config['observation_prompt'])} chars")
                else:
                    print(f"  ❌ Missing required fields for '{group}'")
            else:
                print(f"❌ Missing configuration for '{group}'")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_group_mapping():
    """Test OASIS group mapping for different question codes"""
    print("\nTesting OASIS group mapping...")
    
    try:
        from answer_questions_langchain_knowledgebase_subgrouping import get_oasis_group
        
        test_codes = [
            ("GG0130", "Functional Abilities"),
            ("C0100", "Cognitive Status"),
            ("M1800", "ADLs Assessment"),
            ("D0150", "Mood/Behavior"),
            ("J0510", "Pain Assessment"),
            ("M1306", "Skin/Wound Assessment"),
            ("N0415", "Medications"),
            ("B0200", "Vision & Communication"),
            ("M1400", "Respiratory Status"),
            ("M1600", "Elimination Status")
        ]
        
        for code, expected_group in test_codes:
            actual_group = get_oasis_group(code)
            if actual_group == expected_group:
                print(f"✅ {code} -> {actual_group}")
            else:
                print(f"❌ {code} -> Expected: {expected_group}, Got: {actual_group}")
                
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_clinician_observation_function():
    """Test the clinician observation generation function (without RAG system)"""
    print("\nTesting clinician observation generation function...")
    
    try:
        from answer_questions_langchain_knowledgebase_subgrouping import generate_clinician_observations
        
        # Test with None RAG system (should return empty string)
        result = generate_clinician_observations(None, "Functional Abilities")
        if result == "":
            print("✅ Function handles None RAG system correctly")
        else:
            print(f"❌ Expected empty string for None RAG system, got: {result}")
            
        # Test with non-existent group
        result = generate_clinician_observations(None, "Non-existent Group")
        if result == "":
            print("✅ Function handles non-existent group correctly")
        else:
            print(f"❌ Expected empty string for non-existent group, got: {result}")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Clinician Observation Integration\n")
    print("=" * 60)
    
    success = True
    
    # Test configuration loading
    if not test_config_loading():
        print("\n❌ Configuration loading test failed")
        success = False
    
    # Test group mapping
    if not test_group_mapping():
        print("\n❌ Group mapping test failed")
        success = False
    
    # Test clinician observation function
    if not test_clinician_observation_function():
        print("\n❌ Clinician observation function test failed")
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests completed successfully!")
        print("\n📝 Note: Full RAG integration test requires a running system with conversation data.")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit(main())
