
from ai_core.service_response import ServiceResponseMessage
from ai_core.service_response import info_message

from config import get_settings
from database.factory import get_database, get_db

def verify_and_register(token: str):
    """Verify a user's email and or other data"""
    db = get_database(lambda: get_settings().model_dump())
    if not db:
        raise ValueError("verify database not configured")
    # find the registration
    registration = db.get_item("registration", token)
    if not registration:
        raise ValueError(f"Registration not found for token: {token}")
    
    # FIXME - VALIDATE THE USER IS NOT ALREDY REGISTERED !!!!
    
    # update the registration state
    registration["state"] = "verified"
    db.update_item("registration", registration.get("id"), registration)

    # change the registration to a user
    user = registration
    user["id"] = registration.get("user_id") if registration.get("user_id") is not None else registration.get("email")
    # insert the user
    db.insert_item("user", user.get("id"), user)

    # delete the registration?
    
    return info_message("Registration created successfully")

