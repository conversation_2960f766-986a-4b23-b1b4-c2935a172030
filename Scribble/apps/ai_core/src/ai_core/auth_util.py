import time
import random
import logging
from fastapi import Depends, HTTPException, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from auth.factory import get_auth_provider
from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

settings, config_provider = create_app_settings("ai_core", AiCoreConfig)

auth = get_auth_provider(config_provider=config_provider)
security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Security(security)) -> dict:
    """Get current authenticated user from token."""
    logger.debug(f"Getting current user from credentials: {credentials}")
    token = credentials.credentials
    logger.debug(f"Token: {token}")
    
    try:
        user = auth.get_user(token)
        logger.debug(f"User: {user}")
        
        if not user:
            raise HTTPException(status_code=401, detail="Invalid or expired token")
        
        # Check if the token is an MFA token and validate accordingly
        if user.get("mfa_required") and not user.get("mfa_verified"):
            raise HTTPException(status_code=401, detail="MFA verification required")
        
        return user
    except Exception as e:
        logger.error(f"Error while authenticating user: {e}")
        raise HTTPException(status_code=401, detail="Invalid or expired token")

def require_role(required_roles: list):
    """Dependency factory for role-based access control."""
    logger.debug(f"Creating role checker for required roles: {required_roles}")

    def role_checker(user: dict = Depends(get_current_user)):
        logger.debug(f"Checking user roles: {user}")
        user_roles = user.get("roles", [])

        # Allow access if no roles are required
        if not required_roles:
            return user

        if "Admin" in user_roles:
            return user
                
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(status_code=403, detail="Access denied: Insufficient permissions")
        
        return user
    return role_checker

def no_auth_required():
    """No-op dependency for disabled authentication."""
    return {}

def no_role_required():
    """No-op role check when auth is disabled."""
    return {}

def base36encode(number):
    """Convert a positive integer to a base-36 string."""
    if number < 0:
        raise ValueError("number must be positive")
    digits = '0123456789abcdefghijklmnopqrstuvwxyz'
    if number == 0:
        return '0'
    result = ''
    while number:
        number, remainder = divmod(number, 36)
        result = digits[remainder] + result
    return result

def generate_timestamp_id():
    # Get the current time in milliseconds and convert it to base36.
    timestamp_part = base36encode(int(time.time() * 1000))
    # Generate a random integer that, when converted to base36, gives about 6 characters.
    random_part = base36encode(int(random.random() * (36 ** 6))).zfill(6)
    return f"{timestamp_part}-{random_part}"
