from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class User(BaseModel):
    id: Optional[str] = Field(None, example="<EMAIL>")
    name: Optional[str] = Field(None, example="<PERSON>")
    email: Optional[str] = Field(None, example="<EMAIL>")
    password_hash: Optional[str] = Field(None, example="secret")
    roles: Optional[list[str]] = Field(None, example=["Admin"])
    last_login: Optional[int] = Field(None, example=1683123456789)
    modified: Optional[int] = Field(None, example=1683123456789)

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load User from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load User from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load User from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load User from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())