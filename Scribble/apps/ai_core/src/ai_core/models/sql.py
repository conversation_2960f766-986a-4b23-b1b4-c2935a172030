from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal, Dict
import time
import json
import yaml

class Sql(BaseModel):
    id: Optional[str] = Field("default", example="default")
    sql: Optional[str] = Field(None, example="SELECT * FROM my_table")
    records: Optional[list[Any]] = Field([], example=["stuff"])

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load Sql from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load Sql from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load Sql from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load Sql from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())