# unmanaged
import logging
from typing import List
import uuid
from database.factory import get_database_client
from queues.interface import QueueClient
from database.interface import DatabaseAdapter
from ai_core.models.user import User
from fastapi import Request

logger = logging.getLogger(__name__)

# write - Create an item
def create_user(item: User, user: dict, request: Request):
    logger.info("===============create_user called==============")

    item_id = item.id if hasattr(item, "id") and item.id else str(uuid.uuid4())
    logger.info(f"Using item_id: {item_id}")
    new_item = item.model_dump()
    new_item["id"] = item_id  # Store UUID in the database

    logger.info(item)

    db = get_database_client()
    if db:
        db.insert_item("user", item_id, new_item)
        logger.info(f"User created: {new_item}")
        
    if q:
        q.send_message(new_item)
        logger.info(f"Message sent to queue: User created: {new_item}")
        logger.info(f"Queue message count: {q.get_message_count()}")
    return new_item

# read - get all items
def get_all_user(user: dict, request: Request):
    logger.info("===============get_all_user called==============")
    db = get_database_client()
    if db:
        return db.get_all_items("user")
    return None

# read - get an item
def get_user(id: str, user: dict, request: Request):
    logger.info("===============get_user called==============")
    logger.info(f"Received request to retrieve user with id: {id}")
    db = get_database_client()
    if db:
        item = db.get_item("user", id)
    return item

# write - update an item (without modifying ID)
def update_user(id: str, new_item: User, user: dict, request: Request):
    logger.info("===============update_user called==============")
    logger.info(new_item)
    db = get_database_client()
    if db:
        db.update_item("user", id, new_item.model_dump())
        return db.get_item("user", id)

# write - delete an item
def delete_user(id: str, user: dict, request: Request):
    logger.info("===============delete_user called==============")
    logger.info(f"Received request to delete user with id {id}")
    db = get_database_client()
    if db:
        item = db.get_item("user", id)
        if not item:
            logger.warning(f"User with id {id} not found")
            return None
    db.delete_item("user", id)
    return item