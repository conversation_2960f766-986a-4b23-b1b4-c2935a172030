import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Optional, Dict, Any
from datetime import datetime, timedelta
import json
from config import get_settings
from database.interface import DatabaseAdapter
from database.factory import get_database, get_db, get_database_client
from ai_core.models.config import Config
from ai_core.models.tenant_reports import TenantInfo, SimpleTenantInfo, VisitInfo, TenantReportResponse
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage
from ai_core.models.config import Config as AiCoreConfig


# settings, config_provider = create_app_settings("ai_core", Config)
settings: AiCoreConfig = get_settings()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(lambda: get_settings().dict())
security = HTTPBearer()

def get_db_provider() -> DatabaseAdapter:    
    mongodb_config = {"database_type": "mongodb"}
    mongo_db = get_database_client("scribble_admin")
    return mongo_db


@router.get("/tenant-reports/tenants", response_model=List[SimpleTenantInfo])
def get_tenants(
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    """Get list of all available tenants."""
    logger.debug("--> get_tenants")
    admin_db = get_database_client("scribble_admin")
    try:
        ret = safe_invoke("ai_core.services.tenant_reports_service", "get_tenants", 
                         [admin_db, user, request])
    except Exception as e:
        logger.error(f"Error getting tenants: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

@router.get("/tenant-reports/tenant/{tenant_id}/visits", response_model=TenantReportResponse)
def get_tenant_visits(
    tenant_id: str,
    request: Request,
    status: Optional[str] = Query(None, description="Filter by visit status"),
    visit_type: Optional[str] = Query(None, description="Filter by visit type"),
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    limit: int = Query(50, description="Number of records to return"),
    offset: int = Query(0, description="Number of records to skip"),
    db: DatabaseAdapter = Depends(get_db_provider),
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    """Get visits for a specific tenant."""
    logger.debug(f"--> get_tenant_visits: {tenant_id}")
    try:
        ret = safe_invoke("ai_core.services.tenant_reports_service", "get_tenant_visits", 
                         [tenant_id, db, user, request, status, visit_type, start_date, end_date, limit, offset])
    except Exception as e:
        logger.error(f"Error getting tenant visits: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

@router.get("/tenant-reports/tenant/{tenant_id}/visit/{visit_id}", response_model=Dict[str, Any])
def get_visit_details(
    tenant_id: str,
    visit_id: str,
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    """Get detailed information about a specific visit."""
    logger.debug(f"--> get_visit_details: {tenant_id}/{visit_id}")
    try:
        admin_db = get_database_client("scribble_admin")
        ret = safe_invoke("ai_core.services.tenant_reports_service", "get_visit_details", 
                         [tenant_id, visit_id, admin_db, user, request])
    except Exception as e:
        logger.error(f"Error getting visit details: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

@router.get("/tenant-reports/tenant/{tenant_id}/analytics", response_model=Dict[str, Any])
def get_tenant_analytics(
    tenant_id: str,
    request: Request,
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    """Get analytics data for a specific tenant."""
    logger.debug(f"--> get_tenant_analytics: {tenant_id}")
    try:
        admin_db = get_database_client("scribble_admin")
        ret = safe_invoke("ai_core.services.tenant_reports_service", "get_tenant_analytics", 
                         [tenant_id, admin_db, user, request, start_date, end_date])
    except Exception as e:
        logger.error(f"Error getting tenant analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

@router.get("/tenant-reports/tenant/{tenant_id}/export", response_model=Dict[str, Any])
def export_tenant_data(
    tenant_id: str,
    request: Request,
    data_type: str = Query(..., description="Type of data to export (visits, clients, episodes)"),
    format: str = Query("json", description="Export format (json, csv)"),
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    db: DatabaseAdapter = Depends(get_db_provider),
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    """Export tenant data in various formats."""
    logger.debug(f"--> export_tenant_data: {tenant_id}")
    try:
        admin_db = get_database_client("scribble_admin")
        ret = safe_invoke("ai_core.services.tenant_reports_service", "export_tenant_data", 
                         [tenant_id, data_type, format, admin_db, user, request, start_date, end_date])
    except Exception as e:
        logger.error(f"Error exporting tenant data: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret
