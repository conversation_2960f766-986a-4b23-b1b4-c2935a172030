# managed
import logging
from fastapi import Request, APIRouter, HTTPException, Depends, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Union, Dict, Any
import uuid
from config import create_app_settings
from database.interface import DatabaseAdapter
from database.factory import get_database_client
from queues.factory import get_queue_client
from queues.interface import QueueClient
#from ai_core.models.sql import Sql
from ai_core.models.config import Config
from ai_core.models.sql import Sql
from pydantic import BaseModel
from typing import Dict
from auth.factory import get_auth_provider
from ai_core.auth_util import require_role, no_role_required
from ai_core.invoker import safe_invoke
from ai_core.service_response import ServiceResponseMessage

settings, config_provider = create_app_settings("ai_core", Config)


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

auth = get_auth_provider(config_provider)
security = HTTPBearer()


# write - Create an item
@router.post("/sql", response_model=Union[Sql, ServiceResponseMessage])
def create_sql(item: Sql, 
                        request: Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"--> create_sql: {item}")
    try:
        ret = safe_invoke("ai_core.services.sql_service", "create_sql", [item, user, request])
    except Exception as e:
        logger.error(f"Error creating sql: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve all items
@router.get("/sql-list", response_model=Union[List[Sql]])
def get_all_sqls(request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug("--> get_all_sqls")
    try:
        ret = safe_invoke("ai_core.services.sql_service", "get_all_sql", [user, request])
    except Exception as e:
        logger.error(f"Error retrieving all sqls: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


# read - Retrieve a single item
@router.get("/sql/{id}", response_model=Union[Sql, ServiceResponseMessage])
def get_sql(id: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve sql with id: {id}")
    try:
        ret =safe_invoke("ai_core.services.sql_service", "get_sql", [id, user, request])
    except Exception as e:
        logger.error(f"Error retrieving sql with id: {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved sql: {ret}")
    return ret

# read - Retrieve a single item
@router.get("/sql-path/{full_path:path}", response_model=Union[Sql, ServiceResponseMessage])
def get_sql(full_path: str, request:Request,
                     user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to retrieve sql with full_path: {full_path}")
    try:
        ret =safe_invoke("ai_core.services.sql_service", "get_path_sql", [full_path, user, request])
    except Exception as e:
        logger.error(f"Error retrieving sql with full_path: {full_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    logger.debug(f"Retrieved sql: {ret}")
    return ret

# write - Update an item (without modifying ID)
@router.put("/sql/{id}", response_model=Union[Sql, ServiceResponseMessage])
def update_sql(id: str, request:Request,
                        updated_item: Sql, 
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to update sql with id {id}: {updated_item}")
    try:
        ret = safe_invoke("ai_core.services.sql_service", "update_sql", [id, updated_item, user, request])
    except Exception as e:
        logger.error(f"Error updating sql with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret

# write - Delete an item
@router.delete("/sql/{id}", response_model=Union[Sql, ServiceResponseMessage])
def delete_sql(id: str, request:Request,
                        user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)):
    logger.debug(f"Received request to delete sql with id {id}")
    try:
        ret = safe_invoke("ai_core.services.sql_service", "delete_sql", [id, user, request])
    except Exception as e:
        logger.error(f"Error deleting sql with id {id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    if not ret:
        logger.warning(f"Sql with id {id} not found")
        raise HTTPException(status_code=404, detail="Item not found")
    if isinstance(ret, ServiceResponseMessage) and (ret.status_code is not None and ret.status_code >= 300):
        raise HTTPException(status_code=ret.status_code, detail=ret.dict())
    return ret


class RequestResponse(BaseModel):
    metadata: Dict[str, Any]
    records: List[Sql]

@router.post("/sql-request", response_model=RequestResponse)
def sql_request_handler(
    payload: Dict[str, Any],
    request: Request,
    user: dict = Depends(require_role([]) if settings.auth_enabled else no_role_required)
):
    logger.debug(f"--> sql_request_handler: payload={payload}")
    try:
        result = safe_invoke("ai_core.services.sql_service", "process_sql_request", [payload, user, request])
    except Exception as e:
        logger.error(f"Error processing sql-request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    if not isinstance(result, dict) or "metadata" not in result or "records" not in result:
        raise HTTPException(status_code=500, detail="Invalid response from sql service")

    return result
