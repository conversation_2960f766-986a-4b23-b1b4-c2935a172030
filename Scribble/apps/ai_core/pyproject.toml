[tool.poetry]
name = "ai_core"
version = "0.1.0"
description = ""
authors=["<PERSON> <<EMAIL>>"]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai_core"
version = "0.1.0"
description = "A FastAPI application in a monorepo"
authors = [{ name = "GroG Bot", email = "<EMAIL>" }]
requires-python = ">=3.10"
dependencies = [
    "GitPython",
    "deepgram_sdk",
    "fastapi",
    "pydantic-settings",
    "jsonschema",
    "openai==1.65.1",
    "psutil",
    "pydantic",
    "pydantic-settings",
    "pytest",
    "python-dotenv",
    "python-multipart",
    "pyjwt",
    "pyyaml",
    "requests",
    "slack_sdk",
    "uvicorn",
    "pymongo",
    "langchain",
    "langchain-openai",
    "langchain-community",
    "langchain-text-splitters",
    "chromadb",
    "tiktoken"
]


[project.optional-dependencies]
dev = ["pytest"]

[tool.pytest.ini_options]
testpaths = ["tests"]
