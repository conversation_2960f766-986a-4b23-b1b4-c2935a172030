import os
import requests
import datetime
from pathlib import Path
from config import create_app_settings
from ai_core.models.config import Config as AiCoreConfig
import pytest
import json
import sys
import difflib


def find_repo_root(marker=".repo-root"):
    """Find the monorepo root by searching for a marker file up the directory tree."""
    current = Path(__file__).resolve().parent
    for parent in [current] + list(current.parents):
        if (parent / marker).is_file():
            return parent
    raise RuntimeError(f"Could not find repo root with marker: {marker}")

# Usage:
REPO_ROOT = find_repo_root()
print("REPO_ROOT", REPO_ROOT)

env = os.getenv("ENV", "local")

# This prod and dev is here to support testing dev and prod --"from"--> local environment
# the default is local, so if ENV is not set, it will use the local .env file
# which will be appropriate for production ci in dev and prod environments
if env == "prod":
    env_file = f"{REPO_ROOT}/apps/ai_core/.env.prod"
elif env == "dev":
    env_file = f"{REPO_ROOT}/apps/ai_core/.env.dev"
else:
    env_file = f"{REPO_ROOT}/apps/ai_core/.env"

config, _ = create_app_settings("ai_core", AiCoreConfig, env_file=env_file)
config: AiCoreConfig = config


def get_scribble_access_token():
    """Login and return the access token (plain function, not a fixture)."""
    login_url = f"{config.test_scribble_backend_base_url}/api/v1/auth/login"
    payload = {
        "email": config.test_user_email,
        "password": config.test_password
    }
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
        "x-tenant-id": config.test_tenant_id
    }
    response = requests.post(login_url, json=payload, headers=headers)
    assert response.status_code == 200, f"Login failed: {response.status_code}, body: {response.text}"
    data = response.json()
    access_token = data.get("data", {}).get("accessToken")
    assert access_token, f"No access token in response: {data}"
    return access_token


@pytest.fixture(scope="session")
def scribble_access_token():
    return get_scribble_access_token()


@pytest.mark.integration
def test_scribble_login(scribble_access_token):
    print(f"access token: {scribble_access_token}")
    assert scribble_access_token
    print(f"Login successful:")


def load_json_file(filepath):
    with open(filepath, "r") as f:
        return json.load(f)


def dict_diff(d1, d2, path=""):
    """Recursively find differences between two dicts."""
    diffs = []
    for k in d1.keys() | d2.keys():
        if k not in d1:
            diffs.append(f"Key '{path + str(k)}' missing in first dict")
        elif k not in d2:
            diffs.append(f"Key '{path + str(k)}' missing in second dict")
        else:
            v1, v2 = d1[k], d2[k]
            if isinstance(v1, dict) and isinstance(v2, dict):
                diffs.extend(dict_diff(v1, v2, path + str(k) + "."))
            elif v1 != v2:
                diffs.append(f"Value mismatch at '{path + str(k)}': {v1} != {v2}")
    return diffs


@pytest.mark.integration
def test_scribble_get_visit(scribble_access_token, save_to_file=False, compare_with_saved=True):
    """Test GET /visit endpoint with authentication and tenant header. Optionally save output and compare with saved file."""
    visit_url = f"{config.test_scribble_backend_base_url}/api/v1/visit?limit=10&page=1"
    visit_headers = {
        "accept": "application/json",
        "x-tenant-id": config.test_tenant_id,
        "Authorization": f"Bearer {scribble_access_token}"
    }
    visit_response = requests.get(visit_url, headers=visit_headers)
    assert visit_response.status_code == 201, f"GET /visit failed: {visit_response.status_code}, body: {visit_response.text}"
    visit_data = visit_response.json()
    print(f"/visit response: {json.dumps(visit_data)[:500]}")
    assert "data" in visit_data, f"No 'data' in /visit response: {visit_data}"
    if save_to_file:
        with open("get-visit.json", "w") as f:
            json.dump(visit_data, f, indent=2)
        print("Saved /visit response to get-visit.json")
    if compare_with_saved:
        test_dir = os.path.dirname(__file__)
        saved_path = os.path.join(test_dir, "test_data", "get-visit.json")
        saved_data = load_json_file(saved_path)
        diffs = dict_diff(visit_data, saved_data)
        assert not diffs, f"Differences found between response and saved file:\n" + "\n".join(diffs)
        print("Response matches saved file.")


if __name__ == "__main__":
    failed = False
    try:
        token = get_scribble_access_token()
        try:
            test_scribble_login(token)
            print("test_scribble_login: PASSED")
        except AssertionError as e:
            print(f"test_scribble_login: FAILED: {e}")
            failed = True
        try:
            test_scribble_get_visit(token, save_to_file=True)
            print("test_scribble_get_visit: PASSED")
        except AssertionError as e:
            print(f"test_scribble_get_visit: FAILED: {e}")
            failed = True
    except Exception as e:
        print(f"Setup or fixture error: {e}")
        exit(2)
    if failed:
        exit(1)
    print("All tests passed.")
