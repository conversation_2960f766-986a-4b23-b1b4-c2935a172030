name: Build and Push to ECR

on:
  push:
    branches:
      - main
      - '**'  # Run on all branches
env:
  ACTIONS_STEP_DEBUG: true
  AWS_SDK_LOAD_CONFIG: 1

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set Build Version
        run: |
          BUILD_NUMBER=$(echo "${{ github.run_number }}")
          VERSION="0.9.${BUILD_NUMBER}"
          echo "VERSION=${VERSION}" >> $GITHUB_ENV
          echo "Build Version: ${VERSION}"

      - name: Set AWS Region
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            AWS_REGION="us-east-1"
          else
            AWS_REGION="us-east-2"
          fi
          echo "AWS_REGION=${AWS_REGION}" >> $GITHUB_ENV
          echo "Using AWS Region: ${AWS_REGION}"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/cicd-role-name
          role-skip-session-tagging: true
          aws-access-key-id: ${{ secrets.AWS_ACCOUNT_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      # Old command line way
      # - name: Log in to Amazon ECR
      #   run: |
      #     aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ***.dkr.ecr.us-east-1.amazonaws.com
      

      # https://github.com/aws-actions/amazon-ecr-login
      # The followin will not work
      # Needs to be done this way

      # - name: Configure AWS credentials
      #   uses: aws-actions/configure-aws-credentials@v4 # More information on this action can be found below in the 'AWS Credentials' section
      #   with:
      #     role-to-assume: arn:aws:iam::************:user/cicd-system-user
      #     aws-region: us-east-2       
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build Docker Image
        run: |
          REPOSITORY_NAME="ai_core"
          IMAGE_TAG="${{ env.VERSION }}"
          ECR_URI="${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com"
          docker build -t ${REPOSITORY_NAME}:${IMAGE_TAG} -f apps/ai_core/Dockerfile .
          docker tag ${REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_URI}/${REPOSITORY_NAME}:${IMAGE_TAG}

      - name: Push Docker Image to ECR
        run: |
          ECR_URI="${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com"
          REPOSITORY_NAME="ai_core"
          IMAGE_TAG="${{ env.VERSION }}"
          docker push ${ECR_URI}/${REPOSITORY_NAME}:${IMAGE_TAG}

      - name: Print Success
        run: echo "Build and Push Successful"

      - name: Set up SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.AWS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ************* >> ~/.ssh/known_hosts

      - name: Deploy on AWS EC2
        run: |
          ssh ubuntu@************* << 'EOF'
            echo "AWS Region: us-west-2"
            export AWS_REGION="us-west-2"

            echo "Configuring AWS CLI..."
            export AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}"
            export AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}"

            echo "Logging into AWS ECR..."
            aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin ************.dkr.ecr.us-west-2.amazonaws.com

            echo "Stopping existing container..."
            docker stop ai_core 2>/dev/null || true
            docker rm ai_core 2>/dev/null || true

            echo "Pulling latest image..."
            docker pull ************.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest

            echo "Starting container..."
            docker run --env-file /home/<USER>/.env --rm -d --name ai_core -p 8001:8001 ************.dkr.ecr.us-west-2.amazonaws.com/ai_core:latest

            echo "Deployment completed!"
          EOF