import * as Device from 'expo-device';
import * as Network from 'expo-network';
import * as Localization from 'expo-localization';

export const getDeviceId = async () => {
  const deviceId = Device.osInternalBuildId ?? Device.modelId ?? 'unknown';
  return deviceId
};
export const getDeviceName =()=>{
    return Device.modelName;
}
export const getOsVersion=()=>{
    return Device.osVersion;
}
export const getIPAddress = async () => {
  const ip = await Network.getIpAddressAsync();
  return ip;

};
export const getDeviceTimeZone= ()=>{
    const calendars = Localization.getCalendars();
  const timezone = calendars?.[0]?.timeZone;
  return timezone;
}