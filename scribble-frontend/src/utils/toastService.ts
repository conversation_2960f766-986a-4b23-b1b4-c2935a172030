import React from 'react';
import { EventEmitter } from "events";

type ToastType = "success" | "error" | "info" | "warning";
type ToastPosition = "top" | "bottom";

interface ToastConfig {
  message: string;
  type: ToastType;
  duration?: number;
  position?: ToastPosition;
  bottomOffset?: number;
  icon?: string;
  autoHide?: boolean;
  onPress?: () => void;
  backgroundColor?: string;
  customContent?: React.ReactNode;
}

class ToastService {
  static emitter = new EventEmitter();

  static show(config: ToastConfig) {
    // Set defaults for optional parameters
    const finalConfig = {
      ...config,
      position: config.position || "top",
      duration: config.duration || 3000, // Default duration of 3 seconds
      autoHide: config.autoHide !== undefined ? config.autoHide : true, // Default to auto-hide
      bottomOffset: config.bottomOffset || 20,
    };
    this.emitter.emit("show", finalConfig);
  }

  static success(message: string, duration: number = 3000, position: ToastPosition = "top", bottomOffset?: number, icon?: string, autoHide: boolean = true, onPress?: () => void, backgroundColor?: string, customContent?: React.ReactNode) {
    this.show({ message, type: "success", duration, position, bottomOffset, icon, autoHide, onPress, backgroundColor, customContent });
  }

  static error(message: string, duration: number = 3000, position: ToastPosition = "top", bottomOffset?: number, icon?: string, autoHide: boolean = true, onPress?: () => void, backgroundColor?: string, customContent?: React.ReactNode) {
    this.show({ message, type: "error", duration, position, bottomOffset, icon, autoHide, onPress, backgroundColor, customContent });
  }

  static info(message: string, duration: number = 3000, position: ToastPosition = "top", bottomOffset?: number, icon?: string, autoHide: boolean = true, onPress?: () => void, backgroundColor?: string, customContent?: React.ReactNode) {
    this.show({ message, type: "info", duration, position, bottomOffset, icon, autoHide, onPress, backgroundColor, customContent });
  }

  static warning(message: string, duration: number = 3000, position: ToastPosition = "top", bottomOffset?: number, icon?: string, autoHide: boolean = true, onPress?: () => void, backgroundColor?: string, customContent?: React.ReactNode) {
    this.show({ message, type: "warning", duration, position, bottomOffset, icon, autoHide, onPress, backgroundColor, customContent });
  }
  
  // Method to manually hide the current toast
  static hide() {
    this.emitter.emit("hide");
  }

  static addEventListener(listener: (config: ToastConfig) => void) {
    this.emitter.addListener("show", listener);
    return () => this.removeEventListener(listener);
  }

  static removeEventListener(listener: (config: ToastConfig) => void) {
    this.emitter.removeListener("show", listener);
  }
}

export default ToastService;