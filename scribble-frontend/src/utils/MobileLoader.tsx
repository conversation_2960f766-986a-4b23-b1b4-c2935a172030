import React from 'react';
import { ActivityIndicator, Modal, StyleSheet, View } from 'react-native';

const FullScreenLoader = ({ visible }: { visible: boolean }) => {
  //console.log("FullScreenLoader visible:", visible);
  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => {}}
    >
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)', // Semi-transparent background
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FullScreenLoader;
