import { Dimensions, PixelRatio, Platform } from "react-native";


const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

// Base dimensions
const baseWidth = 375; // Standard iPhone width
const baseHeight = 812; // Standard iPhone height

// Scaling factors
const wScale = SCREEN_WIDTH / baseWidth;
const hScale = SCREEN_HEIGHT / baseHeight;

/**
 * Returns responsive width based on design width
 * @param width number
 */
export const w = (width: number): number => {
  return PixelRatio.roundToNearestPixel(width * wScale);
};

/**
 * Returns responsive height based on design height
 * @param height number
 */
export const h = (height: number): number => {
  return PixelRatio.roundToNearestPixel(height * hScale);
};

/**
 * Returns responsive font size
 * @param size number
 */
export const fs = (size: number): number => {
  const newSize = size * wScale;
  if (Platform.OS === "ios") {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  }
  return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
};

/**
 * Returns responsive padding/margin
 * @param size number
 */
export const ms = (size: number): number => {
  return PixelRatio.roundToNearestPixel(size * wScale);
};
const screenWidth = SCREEN_WIDTH;
const screenHeight =SCREEN_HEIGHT;
export { screenHeight, screenWidth };
