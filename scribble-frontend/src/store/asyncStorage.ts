import AsyncStorage from "@react-native-async-storage/async-storage";

// store single value
const storeData = async (item: string, value: any): Promise<boolean> => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(item, jsonValue);
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
};

// multiple store value. (The value pair is "[["@MyApp_user", "value_1"],["@MyApp_key", "value_2"]]"
const multipleStoreData = async (
  itemArr: [string, string][],
): Promise<boolean> => {
  try {
    await AsyncStorage.multiSet(itemArr);
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
};

// Merges an existing value stored under key, with new value.
const mergeItem = async (item: string, value: any): Promise<boolean> => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.mergeItem(item, jsonValue);
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
};

// get single item
const getData = async <T = any>(item: string): Promise<T | null> => {
  try {
    const jsonValue = await AsyncStorage.getItem(item);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (e) {
    console.log(e);
    return null;
  }
};

// Fetches multiple key-value pairs for given array of keys in a batch. (items are like "['@MyApp_item1', '@MyApp_item2']")
const getMultipleData = async (
  items: string[],
): Promise<[string, string][] | null> => {
  try {
    const values = await AsyncStorage.multiGet(items);
    return values.map(([key, value]) => [key, value ?? ""]);
  } catch (e) {
    console.log(e);
    return null;
  }
};

// Returns all keys known to your App, for all callers, libraries, etc
const getAllKeys = async (): Promise<readonly string[] | null> => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    return keys;
  } catch (e) {
    console.log(e);
    return null;
  }
};

// Removes item for a key. (item is like "@MyApp_key")
const singleRemove = async (item: string): Promise<boolean | null> => {
  try {
    await AsyncStorage.removeItem(item);
    return true;
  } catch (e) {
    console.log(e);
    return null;
  }
};

// Multi remove (items are in array like "['@MyApp_ITEM_1', '@MyApp_ITEM_2']")
const multipleRemove = async (items: string[]): Promise<boolean> => {
  try {
    await AsyncStorage.multiRemove(items);
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
};

// clear all data from the storage
const clearStorage = async (): Promise<boolean> => {
  try {
    await AsyncStorage.clear();
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
};

export {
  storeData,
  multipleStoreData,
  mergeItem,
  getData,
  getMultipleData,
  getAllKeys,
  singleRemove,
  multipleRemove,
  clearStorage,
};
