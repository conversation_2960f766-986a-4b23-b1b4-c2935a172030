import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSelector } from 'react-redux';
import { RootState } from 'src/redux/store';
import { useResponsive } from 'src/hooks/useResponsive';
import { Role } from 'src/enums/Role';
import { useNavigationState } from '@react-navigation/native';
import { clearStorage } from '@/src/store/asyncStorage';
import { getSecureItem } from '@/src/utils/cryptoHelper';


const SplashScreen = ({ navigation }: { navigation: any }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const user = useSelector((state: RootState) => state.auth.user); // "clinician" or "admin"

  const { isTabletOrMobileDevice } = useResponsive();

  let currentRoute = useRef('');

  currentRoute.current = useNavigationState(
    state => state.routes[state.index]?.name,
  );
  // useEffect(()=>{
  //   clearStorage()
  // },[])

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  useEffect(() => {

    const checkAuth = async () => {
      const token = await getSecureItem("authToken");
      
      const userRole = await getSecureItem("userRole");
      const org = await getSecureItem("org");

      // console.log("token :",token)
      console.log("userRole :",userRole)
      // console.log("org :",org)

      if (token && userRole) {
        if (user?.isFirstLogin) {
          navigation.replace('ResetPassword');
        } else if (user?.role === Role.CLINICIAN) {
          if (!isTabletOrMobileDevice) {
            navigation.replace('AccessRestrictionScreen');
          } else {
            navigation.replace('Dashboard');
          }
        } else if (user?.role === Role.USER_ADMIN) {
          if (isTabletOrMobileDevice) {
            navigation.replace('AccessRestrictionScreen');
          } else {
            navigation.replace('AdminDashboard');
          }
        } else {
          navigation.replace('LoginTenant');
        }
      } else if (org && org !== "") {
        navigation.replace('LoginTenant');
      } else {
        navigation.replace('Login');
      }
    }

    if (currentRoute.current === 'ResetPassword') {
      return;
    } else {
      setTimeout(() => {

        if (currentRoute.current === 'ResetPassword') {
          return;
        }
        // navigation.replace('LoginTenant');
         checkAuth()
      }, 2000);
    }
  }, [navigation, user, currentRoute]);

  return (
    <LinearGradient
      colors={['#4C51BF', '#6B46C1']} // from indigo-600 to purple-700
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.container}
    >
      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        <Image
          source={require('assets/images/logo_3.png')}
          style={styles.logo}
        />
        <View style={styles.textContainer}>
          <Text style={styles.text}>AI-Powered</Text>
          <View style={styles.secondLineContainer}>
            <Text style={styles.text}>
              Clinician <Text style={styles.assistantText}>Assistant</Text>
            </Text>
          </View>
        </View>
      </Animated.View>
    </LinearGradient>
  );
};

export default SplashScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // The gradient covers the full screen; align items in the center
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  content: {
    alignItems: 'center',
  },
  logo: {
    width: 150,
    height: 150,
    resizeMode: 'contain',
    marginBottom: 20,
  },
  textContainer: {
    alignItems: 'center',
    paddingTop: 64,
  },
  // Using Poppins_700Bold for a large headline (like nameText in HomeScreen)
  text: {
    fontSize: 26,
    color: '#ffffff',
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
  },
  // Highlight the word "Assistant" with a color change or possibly different font
  // Here, keep the same family for consistent style, but you can change it if desired
  assistantText: {
    color: '#FFD700',
    fontFamily: 'Poppins_600SemiBold',
  },
  secondLineContainer: {
    alignItems: 'center',
  },
});
