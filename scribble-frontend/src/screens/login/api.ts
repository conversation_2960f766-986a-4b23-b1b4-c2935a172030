import {apiClient} from "src/api/apiClient";

export const loginApi = ({ email, password }) => {
  return apiClient
    .post("v1/auth/login", 
      { email, password }
    )
    .then((response) => response.data)
    .catch((error) => {
      throw error.errorMessage || "Login failed";
    });
};
export const loginApiV2 = ({ email, password }) => {
  return apiClient
    .post("v2/auth/login", 
      { email, password }
    )
    .then((response) => response.data)
    .catch((error) => {
      //Íconsole.log("Error in resetPass", JSON.stringify(error));
      throw error.errorMessage || "Login failed";
    });
};



export const forgotPasswordEmail = ({ email }) => {
  return apiClient
    .post("v1/auth/recover-password-email", { email })
    .then((response) => response.data)
    .catch((error) => {
      throw error.errorMessage || "Error sending forgot password email";
    });
};

export const forgotPasswordApi = ({ newPassword, token }) => {
  return apiClient
    .post(
      "v1/auth/recover-password",
      { token, newPassword },
      // {
      //   headers: {
      //     Authorization: `Bearer ${token}`,
      //   },
      // }
    )
    .then((response) => response.data)
    .catch((error) => {
      throw error.errorMessage || "Login failed";
    });
};

export const resetPasswordApi = ({ password, oldPassword }) => {
  return apiClient
    .put("v1/auth/change-password", { newPassword: password, oldPassword })
    .then((response) => response.data)
    .catch((error) => {
      
      throw error.errorMessage || "Reset Password failed";
    });
};

export const getGridData = ({ gridName }) => {
  return apiClient
    .get(`v1/setting/gridView?gridName=${gridName}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error.response?.data?.message || "Failed to Load Clinicians";
    });
};

export const getCliniciansList = ({ limit = 10, page = 1 }) => {
  return apiClient
    .get(`v1/user/clinician?limit=${limit}&page=${page}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error.response?.data?.message || "Failed to Load Clinicians";
    });
};

export const getPatientsList = ({ limit = 10, page = 1 }) => {
  return apiClient
    .get(`v1/user/client?limit=${limit}&page=${page}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error.response?.data?.message || "Failed to Load Clients";
    });
};

export const getVisitList = ({ limit = 10, page = 1 }) => {
  return apiClient
    .get(`v1/visit?limit=${limit}&page=${page}`)
    .then((response) => response.data)
    .catch((error) => {
      throw error.response?.data?.message || "Failed to Load Visits";
    });
};
export const getProfileData = () => {
  return apiClient
    .get(`v2/auth/profile`)
    .then((response) => response.data)
    .catch((error) => {
      throw error.response?.data?.message || "Failed to Load Profile data";
    });
};
export const getAccessToken = (refreshToken :string) => {
  return apiClient
    .post("v1/auth/refresh", { refreshToken })
    .then((response) => response.data)
    .catch((error) => {
      throw error.response?.data?.message || "Login failed";
    });
};
