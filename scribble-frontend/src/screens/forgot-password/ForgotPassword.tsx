import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
// import { Mail, ChevronLeft } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useResponsive } from 'src/hooks/useResponsive';
import LoginRightPane from 'src/components/login-right-pane/LoginRightPane';
import { forgotPasswordEmail, loginApi } from 'src/screens/login/api';
import { validateEmail } from 'src/utils/ProfileUtils';
import { MessageModal } from '@/src/utils/MessageModal';
import { AntDesign, Feather, FontAwesome5, Ionicons } from '@expo/vector-icons';
import { globalStyles } from '@/src/styles';

const ForgotPassword = ({ navigation }: { navigation: any }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isDisabled, setDisabled] = useState(true);

  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success")

  const { isTabletOrMobileDevice } = useResponsive();

  const styles = forgotPasswordStyles(isTabletOrMobileDevice);

  useEffect(() => {
    setErrorMessage('');
    setDisabled(!validateEmail(email));
  }, [email]);

  const handleSubmit = () => {
    // navigation.replace("Verification",{email})
    setIsLoading(true);
    forgotPasswordEmail({ email })
      .then(result => {
        // console.log(result);
        if (result?.status.toLowerCase() === 'ok') {
          setIsLoading(false);
          // alert(
          //   'An email with instructions to reset your password has been sent. Please check your inbox and follow the link provided.',
          // );
          setShowMessageModal(true)
          setMessage("An email with instructions to reset your password has been sent. Please check your inbox and follow the link provided.")
          setMessageType("success")
          // navigation.replace('Login');
        } else {
          setErrorMessage('User does not exist!');
          setIsLoading(false);
          setDisabled(true);
        }
      })
      .catch(error => {
        setErrorMessage('User does not exist!');
        setIsLoading(false);
        setDisabled(true);
      });
  };

  const onBackClick = () => {
    navigation.replace('Login');
  };

  const renderBackButton = () => {
    return (
      <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
        {/* <ChevronLeft color="black" size={20} /> */}
        <Feather name="chevron-left" size={20} color="black" />

      </TouchableOpacity>
    );
  };
  const onModalCancel = () => {
    setShowMessageModal(false)
    onBackClick()
  };
  return (
        <SafeAreaView style={globalStyles.flex1}>
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={styles.outerContainer}
    >
      <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} />
      <LinearGradient
        colors={['#e0e7ff', '#ffffff', '#f3e8ff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBG}
      >
        {isTabletOrMobileDevice && renderBackButton()}
        {/* Main container that can hold both left form + right pane if not mobile */}
        <View
          style={[
            styles.container,
            isTabletOrMobileDevice
              ? styles.containerMobile
              : styles.containerDesktop,
          ]}
        >
          {/* Left (login card) */}
          <View style={styles.loginCard}>
            <View style={styles.loginContainer}>
              <View style={styles.iconContainer}>
                <Image
                  source={require('assets/images/icon.png')}
                  style={styles.logo}
                />
              </View>

              {/* Header */}
              <View style={styles.headerContainer}>
                <Text allowFontScaling={false} style={styles.title}>Reset Password</Text>
                <Text allowFontScaling={false} style={styles.subtitle}>
                Please enter your email address. We’ll send you a link to reset your password
                </Text>
              </View>

              {/* Email Input */}
              <View style={styles.inputContainer}>
                <View style={styles.iconWrapper}>
                  {/* <Mail color="#9CA3AF" size={20} /> */}
                </View>
                <TextInput
                allowFontScaling={false}
                  style={styles.input}
                  placeholder="Email address"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  placeholderTextColor="grey"
                />
              </View>

              {/* Submit Button */}
              <TouchableOpacity
                style={[
                  styles.buttonContainer,
                  isDisabled && styles.disabledButton,
                ]}
                activeOpacity={isDisabled ? 0.5 : 0.9}
                onPress={() => !isLoading && handleSubmit()}
                disabled={isDisabled}
              >
                <LinearGradient
                  colors={
                    isDisabled ? ['#A0A0A0', '#C0C0C0'] : ['#4C51BF', '#6B46C1']
                  }
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.buttonGradient}
                >
                  <View style={styles.buttonContent}>
                    {isLoading ? (
                      <ActivityIndicator
                        size={isTabletOrMobileDevice ? 'small' : 'large'}
                        color="#FFFFFF"
                      />
                    ) : (
                      <Text  style={styles.buttonText}>
                        Send Link
                      </Text>
                    )}
                  </View>
                </LinearGradient>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.backContainer}
                onPress={onBackClick}
              >
                <Text allowFontScaling={false} style={styles.backText}>Back To Login</Text>
              </TouchableOpacity>
              {errorMessage && (
                <View style={styles.errorContainer}>
                  <Text
                  allowFontScaling={false}
                    style={[
                      styles.errorText,
                      isTabletOrMobileDevice
                        ? styles.mobileError
                        : styles.desktopError,
                    ]}
                  >
                    {errorMessage}
                  </Text>
                </View>
              )}
            </View>
            {!isTabletOrMobileDevice && <LoginRightPane />}
          </View>
        </View>
      </LinearGradient>
    </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ForgotPassword;

const forgotPasswordStyles = (isMobile: boolean) =>
  // StyleSheet.create({
  //   outerContainer: {
  //     flex: 1,
  //   },
  //   gradientBG: {
  //     flex: 1,
  //     padding: 16,
  //     justifyContent: 'center',
  //   },
  //   container: {
  //     backgroundColor: 'transparent',
  //     borderRadius: 16,
  //     overflow: 'hidden',
  //     paddingTop: 10,
  //     paddingBottom: 10,
  //   },
  //   // Layout differences for mobile vs. desktop
  //   containerMobile: {
  //     flexDirection: 'column',
  //     alignItems: 'center',
  //     paddingHorizontal: 5,
  //   },
  //   containerDesktop: {
  //     flexDirection: 'row',
  //     alignItems: 'center',
  //     justifyContent: 'center',
  //     width: '100%',
  //   },
  //   // The login card (left side)
  //   loginCard: {
  //     width: isMobile ? '100%' : '70%',
  //     borderRadius: 16,
  //     shadowColor: '#000',
  //     shadowOpacity: 0.1,
  //     shadowRadius: 8,
  //     elevation: 4,
  //     display: 'flex',
  //     flexDirection: 'row',
  //   },
  //   loginContainer: {
  //     alignItems: 'center',
  //     justifyContent: 'center',
  //     maxWidth: isMobile ? '100%' : '100%',
  //     width: isMobile ? '100%' : '50%',
  //     padding: isMobile ? 20 : 100,
  //     paddingVertical: 30,
  //     paddingBottom: 40,
  //     backgroundColor: '#FFFFFF',
  //     borderBottomRightRadius: isMobile ? 16 : 0,
  //     borderTopRightRadius: isMobile ? 16 : 0,
  //     borderTopLeftRadius: 16,
  //     borderBottomLeftRadius: 16,
  //     height: 550,
  //   },
  //   backButton: {
  //     gap: 8,
  //     position: 'absolute',
  //     top: isMobile ? 16 : 32,
  //     left: isMobile ? 16 : 32,
  //     zIndex: 10,
  //     width: 48,
  //     height: 48,
  //     backgroundColor: isMobile ? '#FFFFFF' : '#f9fafb',
  //     borderRadius: 24,
  //     justifyContent: 'center',
  //     alignItems: 'center',
  //     shadowColor: '#000',
  //     shadowOffset: { width: 0, height: 2 },
  //     shadowOpacity: 0.1,
  //     shadowRadius: 8,
  //     elevation: 3,
  //   },
  //   iconContainer: {
  //     display: 'flex',
  //     justifyContent: 'center',
  //     marginBottom: 20,
  //     alignItems: 'center',
  //   },
  //   logo: {
  //     width: 80,
  //     height: 80,
  //     resizeMode: 'contain',
  //   },
  //   headerContainer: {
  //     marginBottom: 36,
  //     alignItems: 'center',
  //   },
  //   // Using Poppins_600SemiBold for a strong heading
  //   title: {
  //     fontSize: 24,
  //     fontFamily: 'Poppins_600SemiBold',
  //     color: '#1F2937',
  //     marginBottom: 12,
  //   },
  //   // Using Poppins_400Regular for normal text
  //   subtitle: {
  //     fontSize: 14,
  //     fontFamily: 'Poppins_400Regular',
  //     color: '#4B5563',
  //     textAlign: 'center',
  //   },
  //   inputContainer: {
  //     position: 'relative',
  //     marginBottom: 16,
  //     flexDirection: 'row',
  //     alignItems: 'center',
  //     minWidth:  '100%' ,
  //   },
  //   iconWrapper: {
  //     position: 'absolute',
  //     left: 12,
  //     zIndex: 1,
  //   },
  //   // Using Poppins_400Regular for input text
  //   input: {
  //     flex: 1,
  //     paddingVertical: 10,
  //     paddingLeft: 40, // space for icon
  //     paddingRight: 16,
  //     borderWidth: 1,
  //     borderColor: '#D1D5DB',
  //     borderRadius: 12,
  //     fontSize: 16,
  //     fontFamily: 'Poppins_400Regular',
  //     color: '#000000',
  //     height: 58,
  //   },
  //   buttonContainer: {
  //     height: 56,
  //     marginTop: 16,
  //     minWidth: isMobile ? '100%' : 350,
  //   },
  //   disabledButton: {
  //     cursor: 'not-allowed',
  //   },
  //   buttonGradient: {
  //     borderRadius: 12,
  //     paddingVertical: 12,
  //   },
  //   buttonContent: {
  //     flexDirection: 'row',
  //     alignItems: 'center',
  //     justifyContent: 'center',
  //     height: 32,
  //   },
  //   // Using Poppins_600SemiBold for button text
  //   buttonText: {
  //     marginLeft: 8,
  //     color: '#FFFFFF',
  //     fontSize: 16,
  //     fontFamily: 'Poppins_600SemiBold',
  //   },
  //   backContainer: {
  //     flexDirection: 'row',
  //     justifyContent: 'center',
  //     alignItems: 'center',
  //     marginTop: 24,
  //   },
  //   backText: {
  //     fontSize: 16,
  //     fontFamily: 'Poppins_400Regular',
  //     color: '#4C51BF',
  //   },
  //   errorContainer: {
  //     backgroundColor: '#FEF2F2',
  //     alignItems: 'center',
  //     justifyContent: 'center',
  //     paddingVertical: isMobile ? 8 : 12,
  //     paddingHorizontal: 30,
  //     marginTop: 16,
  //     borderRadius: 4,
  //   },
  //   errorText: {
  //     color: '#DC2626',
  //     fontSize: 14,
  //     textAlign: 'center',
  //     fontFamily: 'Poppins_400Regular',
  //   },
  //   mobileError: {
  //     fontSize: 14,
  //   },
  //   desktopError: {
  //     fontSize: 16,
  //   },
  // });
  StyleSheet.create({
    outerContainer: {
      flex: 1,
    },
    backButton: {
      gap: 8,
      position: 'absolute',
      top: isMobile ? 16 : 32,
      left: isMobile ? 16 : 32,
      zIndex: 10,
      width: 48,
      height: 48,
      backgroundColor: isMobile ? '#FFFFFF' : '#f9fafb',
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    },
    backContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 24,
    },
    backText: {
      fontSize: 16,
      fontFamily: 'Poppins_400Regular',
      color: '#4C51BF',
    },
    gradientBG: {
      flex: 1,
      padding: 16,
      justifyContent: "center",
    },
    container: {
      backgroundColor: "transparent",
      borderRadius: 16,
      overflow: "hidden",
      paddingTop: 10,
      paddingBottom: 10,
    },
    // Layout differences for mobile vs. desktop
    containerMobile: {
      flexDirection: "column",
      alignItems: "center",
    },
    containerDesktop: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
    },
    // The login card (left side)
    loginCard: {
      width: isMobile ? "95%" : "70%",
      borderRadius: 16,
      shadowColor: "#000",
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      display: "flex",
      flexDirection: "row",
    },
    loginContainer: {
      alignSelf: "center",
      maxWidth: isMobile ? "100%" : "100%",
      width: isMobile ? "100%" : "50%",
      padding: isMobile ? 20 : 100,
      paddingVertical: isMobile ? 20 : 30,
      paddingBottom: isMobile ? 30 : 40,
      backgroundColor: "#FFFFFF",
      borderBottomRightRadius: isMobile ? 16 : 0,
      borderTopRightRadius: isMobile ? 16 : 0,
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
    },
    iconContainer: {
      display: "flex",
      justifyContent: "center",
      marginBottom: 20,
      alignItems: "center",
    },
    logo: {
      width: isMobile ? 70 : 80,
      height: isMobile ? 70 : 80,
      resizeMode: "contain",
    },
    headerContainer: {
      marginBottom: isMobile ? 24 : 36,
      alignItems: "center",
    },
    title: {
      fontSize: isMobile ? 22 : 24,
      fontFamily: "Poppins_600SemiBold",
      color: "#1F2937",
      marginBottom: isMobile ? 6 : 12,
    },
    subtitle: {
      fontSize: 14,
      fontFamily: "Poppins_400Regular",
      color: "#4B5563",
      textAlign:'center'
    },
    inputContainer: {
      position: "relative",
      marginBottom: 16,
      flexDirection: "row",
      alignItems: "center",
      width: "100%",
    },
    iconWrapper: {
      position: "absolute",
      left: 14,
      zIndex: 1,
      opacity: 0.7,
    },
    input: {
      flex: 1,
      paddingVertical: 14,
      paddingLeft: 42,
      paddingRight: 16,
      borderWidth: 1,
      borderColor: "#E5E7EB",
      borderRadius: 8,
      fontSize: 16,
      color: "#1F2937",
      backgroundColor: "white",
    },

    buttonContainer: {
      height: isMobile ? 48 : 56,
    },
    disabledButton: {
      cursor: "not-allowed",
    },
    buttonGradient: {
      borderRadius: 12,
      paddingVertical: 12,
    },
    buttonContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      height: isMobile ? 24 : 32,
    },
    buttonText: {
      marginLeft: 8,
      color: "#FFFFFF",
      fontSize: isMobile ? 14 : 16,
      fontFamily: "Poppins_600SemiBold",
    },
    errorText: {
      color: "#DC2626",
      fontSize: 14,
      textAlign: "center",
      fontFamily: "Poppins_400Regular",
    },
    mobileError: {
      fontSize: 14,
    },
    desktopError: {
      fontSize: 16,
    },
    errorContainer: {
      backgroundColor: "#FEF2F2",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: isMobile ? 8 : 12,
      paddingHorizontal: 30,
      marginTop: 16,
      borderRadius: 4,
    },
  });