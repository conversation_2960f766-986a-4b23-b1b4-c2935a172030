import React, { useEffect, useState, useRef } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    KeyboardAvoidingView,
    Platform,
    Image,
    ActivityIndicator,
} from 'react-native';
// import {  ChevronLeft } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useResponsive } from 'src/hooks/useResponsive';
import LoginRightPane from 'src/components/login-right-pane/LoginRightPane';
import { forgotPasswordEmail, loginApi } from 'src/screens/login/api';
import { useNavigation } from '@react-navigation/native';

const VerificationPassword = ({route}) => {
    const email = route.params?.email;
    const navigation = useNavigation()
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [isDisabled, setDisabled] = useState(true);
    const [otp, setOtp] = useState(['', '', '', '', '', '']);
    const inputRefs = useRef([]);

    const { isTabletOrMobileDevice } = useResponsive();

    const styles = verificationStyles(isTabletOrMobileDevice);

    useEffect(() => {
        setErrorMessage('');
        // Check if all OTP digits are filled
        const isComplete = otp.every(digit => digit !== '');
        setDisabled(!isComplete);
    }, [otp]);

    const handleOtpChange = (value, index) => {
        // Only allow numbers
        if (/^[0-9]?$/.test(value)) {
            const newOtp = [...otp];
            newOtp[index] = value;
            setOtp(newOtp);

            // Auto-focus next input if current input is filled
            if (value !== '' && index < 5) {
                inputRefs.current[index + 1].focus();
            }
        }
    };

    const handleKeyPress = (e, index) => {
        // Move to previous input on backspace if current input is empty
        if (e.nativeEvent.key === 'Backspace' && otp[index] === '' && index > 0) {
            inputRefs.current[index - 1].focus();
        }
    };

    const handleSubmit = () => {
        // Add your verification logic here
        const otpCode = otp.join('');
        console.log('Submitting OTP:', otpCode);

        // Example verification flow
        setIsLoading(true);

        // Simulate API call
        setTimeout(() => {
            setIsLoading(false);
            navigation.replace('SetPassword')
            // Navigate to next screen or show error
            // navigation.replace('NextScreen');
        }, 1500);
    };

    const onBackClick = () => {
        navigation.replace('Login');
    };

    const renderBackButton = () => {
        return (
            <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
                {/* <ChevronLeft color="black" size={20} /> */}
            </TouchableOpacity>
        );
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            style={styles.outerContainer}
        >
            <LinearGradient
                colors={['#e0e7ff', '#ffffff', '#f3e8ff']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.gradientBG}
            >
                {isTabletOrMobileDevice && renderBackButton()}
                {/* Main container that can hold both left form + right pane if not mobile */}
                <View
                    style={[
                        styles.container,
                        isTabletOrMobileDevice
                            ? styles.containerMobile
                            : styles.containerDesktop,
                    ]}
                >
                    {/* Left (login card) */}
                    <View style={styles.loginCard}>
                        <View style={styles.loginContainer}>
                            <View style={styles.iconContainer}>
                                <Image
                                    source={require('assets/images/icon.png')}
                                    style={styles.logo}
                                />
                            </View>

                            {/* Header */}
                            <View style={styles.headerContainer}>
                                <Text style={styles.title}>Verification</Text>
                                <Text style={styles.subtitle}>
                                    Enter the 6-digit code sent to your email
                                </Text>
                                <Text style={styles.emailText}>(Jh****@gmail.com)</Text>
                            </View>

                            {/* OTP Verification */}
                            <View style={styles.otpContainer}>
                                <View style={styles.otpInputContainer}>
                                    {otp.map((digit, index) => (
                                        <TextInput
                                            key={index}
                                            ref={ref => {
                                                inputRefs.current[index] = ref;
                                            }}
                                            style={styles.otpInput}
                                            value={digit}
                                            onChangeText={value => handleOtpChange(value, index)}
                                            onKeyPress={e => handleKeyPress(e, index)}
                                            keyboardType="number-pad"
                                            maxLength={1}
                                            selectTextOnFocus
                                        />
                                    ))}
                                </View>
                            </View>

                            {/* Submit Button */}
                            <TouchableOpacity
                                style={[
                                    styles.buttonContainer,
                                    isDisabled && styles.disabledButton,
                                ]}
                                activeOpacity={isDisabled ? 0.5 : 0.9}
                                onPress={() => !isLoading && handleSubmit()}
                                disabled={isDisabled}
                            >
                                <LinearGradient
                                    colors={
                                        isDisabled ? ['#A0A0A0', '#C0C0C0'] : ['#4C51BF', '#6B46C1']
                                    }
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.buttonGradient}
                                >
                                    <View style={styles.buttonContent}>
                                        {isLoading ? (
                                            <ActivityIndicator
                                                size={isTabletOrMobileDevice ? 'small' : 'large'}
                                                color="#FFFFFF"
                                            />
                                        ) : (
                                            <Text style={styles.buttonText}>
                                                Verify
                                            </Text>
                                        )}
                                    </View>
                                </LinearGradient>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.backContainer}
                                onPress={onBackClick}
                            >
                                <Text style={styles.backText}>Back To Login</Text>
                            </TouchableOpacity>
                            {errorMessage && (
                                <View style={styles.errorContainer}>
                                    <Text
                                        style={[
                                            styles.errorText,
                                            isTabletOrMobileDevice
                                                ? styles.mobileError
                                                : styles.desktopError,
                                        ]}
                                    >
                                        {errorMessage}
                                    </Text>
                                </View>
                            )}
                        </View>
                        {!isTabletOrMobileDevice && <LoginRightPane />}
                    </View>
                </View>
            </LinearGradient>
        </KeyboardAvoidingView>
    );
};

export default VerificationPassword;

const verificationStyles = (isMobile: boolean) =>

    StyleSheet.create({
        outerContainer: {
            flex: 1,
        },
        backButton: {
            gap: 8,
            position: 'absolute',
            top: isMobile ? 16 : 32,
            left: isMobile ? 16 : 32,
            zIndex: 10,
            width: 48,
            height: 48,
            backgroundColor: isMobile ? '#FFFFFF' : '#f9fafb',
            borderRadius: 24,
            justifyContent: 'center',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 3,
        },
        backContainer: {
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: 24,
        },
        backText: {
            fontSize: 16,
            fontFamily: 'Poppins_400Regular',
            color: '#4C51BF',
        },
        gradientBG: {
            flex: 1,
            padding: 16,
            justifyContent: "center",
        },
        container: {
            backgroundColor: "transparent",
            borderRadius: 16,
            overflow: "hidden",
            paddingTop: 10,
            paddingBottom: 10,
        },
        // Layout differences for mobile vs. desktop
        containerMobile: {
            flexDirection: "column",
            alignItems: "center",
        },
        containerDesktop: {
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
        },
        // The login card (left side)
        loginCard: {
            width: isMobile ? "95%" : "70%",
            borderRadius: 16,
            shadowColor: "#000",
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
            display: "flex",
            flexDirection: "row",
        },
        loginContainer: {
            alignSelf: "center",
            maxWidth: isMobile ? "100%" : "100%",
            width: isMobile ? "100%" : "50%",
            padding: isMobile ? 20 : 100,
            paddingVertical: isMobile ? 20 : 30,
            paddingBottom: isMobile ? 30 : 40,
            backgroundColor: "#FFFFFF",
            borderBottomRightRadius: isMobile ? 16 : 0,
            borderTopRightRadius: isMobile ? 16 : 0,
            borderTopLeftRadius: 16,
            borderBottomLeftRadius: 16,
        },
        iconContainer: {
            display: "flex",
            justifyContent: "center",
            marginBottom: 20,
            alignItems: "center",
        },
        logo: {
            width: isMobile ? 70 : 80,
            height: isMobile ? 70 : 80,
            resizeMode: "contain",
        },
        headerContainer: {
            marginBottom: isMobile ? 24 : 36,
            alignItems: "center",
        },
        title: {
            fontSize: isMobile ? 22 : 24,
            fontFamily: "Poppins_600SemiBold",
            color: "#1F2937",
            marginBottom: isMobile ? 6 : 12,
        },
        subtitle: {
            fontSize: 14,
            fontFamily: "Poppins_400Regular",
            color: "#4B5563",
            marginBottom: 4,
        },
        emailText: {
            fontSize: 14,
            fontFamily: "Poppins_400Regular",
            color: "#6B7280",
            textAlign: "center",
        },
        // OTP Styles
        otpContainer: {
            marginBottom: 24,
        },
        otpInputContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginTop: 16,
        },
        otpInput: {
            width: isMobile ? 40 : 48,
            height: isMobile ? 40 : 48,
            borderWidth: 1,
            borderColor: '#D1D5DB',
            borderRadius: 8,
            textAlign: 'center',
            fontSize: 18,
            fontFamily: 'Poppins_500Medium',
            backgroundColor: '#FFFFFF',
        },
        buttonContainer: {
            height: isMobile ? 48 : 56,
        },
        disabledButton: {
            cursor: "not-allowed",
        },
        buttonGradient: {
            borderRadius: 12,
            paddingVertical: 12,
        },
        buttonContent: {
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            height: isMobile ? 24 : 32,
        },
        buttonText: {
            marginLeft: 8,
            color: "#FFFFFF",
            fontSize: isMobile ? 14 : 16,
            fontFamily: "Poppins_600SemiBold",
        },
        errorText: {
            color: "#DC2626",
            fontSize: 14,
            textAlign: "center",
            fontFamily: "Poppins_400Regular",
        },
        mobileError: {
            fontSize: 14,
        },
        desktopError: {
            fontSize: 16,
        },
        errorContainer: {
            backgroundColor: "#FEF2F2",
            alignItems: "center",
            justifyContent: "center",
            paddingVertical: isMobile ? 8 : 12,
            paddingHorizontal: 30,
            marginTop: 16,
            borderRadius: 4,
        },
    });