import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useResponsive } from 'src/hooks/useResponsive';
import LoginRightPane from 'src/components/login-right-pane/LoginRightPane';
import { SafeAreaView } from 'react-native-safe-area-context';
import { globalStyles } from 'src/styles';
import { StatusBar } from 'expo-status-bar';
import theme from 'src/theme';
import { forgotPasswordApi, resetPasswordApi } from 'src/screens/login/api';
import { MessageModal } from '@/src/utils/MessageModal';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { Feather, Ionicons } from '@expo/vector-icons';

export default function ResetPasswordScreen({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) {

  
  const [oldPassword, setOldPassword] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isDisabled, setDisabled] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Password validation states
  const [hasCapital, setHasCapital] = useState(false);
  const [hasNumber, setHasNumber] = useState(false);
  const [hasSpecial, setHasSpecial] = useState(false);
  const [hasMinLength, setHasMinLength] = useState(false);
  const [passwordsMatch, setPasswordsMatch] = useState(false);

  
  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success")


  const { isTabletOrMobileDevice } = useResponsive();
  const styles = resetPasswordStyles(isTabletOrMobileDevice);
  // const email = route.params?.email || '';
  const token = route.params?.token || '';

  // let isForgotPassword = route.params?.isForgotPassword || false;

  let isForgotPassword = false;

  if ( token != "") {
    isForgotPassword = true;
  }

  // console.log(isForgotPassword,typeof isForgotPassword)

  // Check password validation on input change
  useEffect(() => {
    setErrorMessage('');
    setSuccessMessage('');

    // Check password requirements
    setHasCapital(/[A-Z]/.test(password));
    setHasNumber(/[12-64]/.test(password));
    //setHasNumber(/[0-9]/.test(password));
    setHasSpecial(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password));
    setHasMinLength(password.length >= 12 && password.length<=64);

    // Check if passwords match
    setPasswordsMatch(password === confirmPassword && password.length > 0);

    const isOldPasswordValid = isForgotPassword
      ? true
      : oldPassword.length >= 8;

    // Enable button only if all requirements met
    setDisabled(
      !hasCapital ||
      !hasNumber ||
      !hasSpecial ||
      !hasMinLength ||
      !passwordsMatch ||
      !isOldPasswordValid,
    );
  }, [
    password,
    oldPassword,
    confirmPassword,
    hasCapital,
    hasNumber,
    hasSpecial,
    hasMinLength,
    passwordsMatch,
    isForgotPassword,
  ]);

  useEffect(()=>{
    setTimeout(()=>{
      setPageTitle('Reset Password');
    },100)
  },[])
  const navigateToLogin = () => {
    navigation.replace('Login');
  };

  const onBackClick = () => {
    navigation.goBack();
  };

  const renderBackButton = () => {
    return (
      <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
        <Ionicons name="chevron-back-outline" size={20} color="black" />
      </TouchableOpacity>
    );
  };

  const onResetPassword = () => {
    resetPasswordApi({ password, oldPassword })
      .then(response => {

        setIsLoading(false);
        if (response.status === 'ok') {
          // alert(
          //   'Password reset successfully! Please login again with updated password',
          // );
          setShowMessageModal(true)
          setMessage("Password reset successfully! Please login again with updated password.")
          setMessageType("success")
          navigateToLogin();
        } else {
          setErrorMessage(response?.errorMessage || 'Password Reset Failed');
        }
      })
      .catch(error => {
        setIsLoading(false);
        setErrorMessage(error || 'Password Reset Failed');
      });
  };

  const onForgotPassword = () => {
    forgotPasswordApi({ newPassword: password,token })
      .then(response => {
        setIsLoading(false);
        if (response.status === 'ok') {
          setShowMessageModal(true)
          setMessage("Password reset successfully! Please login again with updated password.")
          setMessageType("success")
          // alert(
          //   'Password reset successfully! Please login again with updated password',
          // );
          // navigateToLogin();
        } else {
          // console.log(response?.errorMessage)
          setErrorMessage(response?.errorMessage || 'Password Reset Failed');
        }
      })
      .catch(error => {
        setIsLoading(false);
        // console.log(JSON.stringify(error))
        setErrorMessage(error);
      });
  };

  const handleResetPassword = () => {
    setIsLoading(true);

    if (isForgotPassword) {
      onForgotPassword();
    } else {
      onResetPassword();
    }
  };

  const ValidationItem = ({ isValid, text }) => (
    <View style={styles.validationItem}>
      {isValid ? (
        <Feather name="check" size={26} color="#10B981" />
      ) : (
        <Feather name="alert-circle" size={16} color="#9CA3AF" />
      )}
      <Text
        style={[
          styles.validationText,
          isValid ? styles.validText : styles.invalidText,
        ]}
      >
        {text}
      </Text>
    </View>
  );
  const onModalCancel = () => {
    setShowMessageModal(false)
    navigateToLogin();
  };

  return (
    <SafeAreaView style={globalStyles.flex1}>
      <StatusBar style="dark" />
      <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.outerContainer}
      >
        {/* <ScrollView> */}
        <LinearGradient
          colors={['#e0e7ff', '#ffffff', '#f3e8ff']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientBG}
        >
          <ScrollView>
          <View
            style={[
              styles.container,
              isTabletOrMobileDevice
                ? styles.containerMobile
                : styles.containerDesktop,
            ]}
          >
            <View style={styles.resetCard}>
              <View style={styles.resetContainer}>
                {renderBackButton()}
                <View style={styles.headerContainer}>
                  <Text allowFontScaling={false} style={styles.title}>Reset Password</Text>
                  <Text allowFontScaling={false} style={styles.subtitle}>
                    Create a new secure password
                  </Text>
                </View>
                {!isForgotPassword && (
                  <View style={styles.inputContainer}>
                    <View style={styles.iconWrapper}>
                      {/* <Lock color="#9CA3AF" size={20} /> */}
                      <Feather name="lock" size={20} color="#9CA3AF" />
                    </View>
                    <TextInput
                    allowFontScaling={false}
                      style={styles.input}
                      placeholder="Current password"
                      placeholderTextColor="grey"
                      value={oldPassword}
                      onChangeText={setOldPassword}
                      secureTextEntry
                    />
                  </View>
                )}

                {/* New Password input */}
                <View style={styles.inputContainer}>
                  <View style={styles.iconWrapper}>
                  <Feather name="lock" size={20} color="#9CA3AF" />
                  </View>
                  <TextInput
                  allowFontScaling={false}
                    style={styles.input}
                    placeholder="New password"
                    placeholderTextColor="grey"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                  />
                </View>

                {/* Confirm Password input */}
                <View style={styles.inputContainer}>
                  <View style={styles.iconWrapper}>
                  <Feather name="lock" size={20} color="#9CA3AF" />
                  </View>
                  <TextInput
                  allowFontScaling={false}
                    style={styles.input}
                    placeholder="Confirm password"
                    placeholderTextColor="grey"
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    secureTextEntry
                  />
                </View>

                {/* Password Requirements */}
                <View style={styles.requirementsContainer}>
                  <Text  style={styles.requirementsTitle}>
                    Password must contain:
                  </Text>
                  <View style={styles.requirementsList}>
                    <ValidationItem
                      isValid={hasCapital}
                      text="At least one capital letter"
                    />
                    <ValidationItem
                      isValid={hasNumber}
                      text="At least one number"
                    />
                    <ValidationItem
                      isValid={hasSpecial}
                      text="At least one special character"
                    />
                    <ValidationItem
                      isValid={hasMinLength}
                      text="Minimum 12 and Maximum 64 characters"
                    />
                    <ValidationItem
                      isValid={passwordsMatch}
                      text="Passwords match"
                    />
                  </View>
                </View>

                {/* Reset Button */}
                <TouchableOpacity
                  testID="reset-password-btn"
                  data-testid="reset-password-btn"
                  style={[
                    styles.buttonContainer,
                    isDisabled && styles.disabledButton,
                  ]}
                  activeOpacity={isDisabled ? 0.5 : 0.9}
                  onPress={() => !isLoading && handleResetPassword()}
                  disabled={isDisabled}
                >
                  <LinearGradient
                    colors={
                      isDisabled
                        ? ['#A0A0A0', '#C0C0C0']
                        : ['#4C51BF', '#6B46C1']
                    }
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.buttonGradient}
                  >
                    <View style={styles.buttonContent}>
                      {isLoading ? (
                        <ActivityIndicator
                          size={isTabletOrMobileDevice ? 'small' : 'large'}
                          color="#FFFFFF"
                        />
                      ) : (
                        <Text style={styles.buttonText}>Reset Password</Text>
                      )}
                    </View>
                  </LinearGradient>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.backContainer}
                  onPress={() => navigation.goBack()}
                >
                  <Text style={styles.backText}>Go Back</Text>
                </TouchableOpacity>

                {/* Error Message */}
                {errorMessage ? (
                  <View style={styles.errorContainer}>
                    <Text
                      style={[
                        styles.errorText,
                        isTabletOrMobileDevice
                          ? styles.mobileError
                          : styles.desktopError,
                      ]}
                    >
                      {errorMessage}
                    </Text>
                  </View>
                ) : null}

                {/* Success Message */}
                {successMessage ? (
                  <View style={styles.successContainer}>
                    <Text
                      style={[
                        styles.successText,
                        isTabletOrMobileDevice
                          ? styles.mobileSuccess
                          : styles.desktopSuccess,
                      ]}
                    >
                      {successMessage}
                    </Text>
                  </View>
                ) : null}
              </View>
              {!isTabletOrMobileDevice && <LoginRightPane />}
            </View>
          </View>
          </ScrollView>
        </LinearGradient>
       {/* </ScrollView> */}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const resetPasswordStyles = (isMobile: boolean) =>
  StyleSheet.create({
    outerContainer: {
      flex: 1,
    },
    gradientBG: {
      flex: 1,
      padding: 16,
      justifyContent: 'center',
    },
    container: {
      backgroundColor: 'transparent',
      borderRadius: 16,
      overflow: 'hidden',
      paddingTop: 10,
      paddingBottom: 10,
    },
    containerMobile: {
      flexDirection: 'column',
      alignItems: 'center',
    },
    containerDesktop: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    },
    resetCard: {
      width: isMobile ? '95%' : '70%',
      borderRadius: 16,
      shadowColor: '#000',
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      display: 'flex',
      flexDirection: 'row',
    },
    resetContainer: {
      alignSelf: 'center',
      maxWidth: isMobile ? '100%' : '100%',
      width: isMobile ? '100%' : '50%',
      padding: isMobile ? 20 : 100,
      paddingVertical: isMobile ? 20 : 30,
      paddingBottom: isMobile ? 30 : 40,
      backgroundColor: '#FFFFFF',
      borderBottomRightRadius: isMobile ? 16 : 0,
      borderTopRightRadius: isMobile ? 16 : 0,
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
    },
    iconContainer: {
      display: 'flex',
      justifyContent: 'center',
      marginBottom: 20,
      alignItems: 'center',
    },
    logo: {
      width: isMobile ? 70 : 80,
      height: isMobile ? 70 : 80,
      resizeMode: 'contain',
    },
    headerContainer: {
      marginBottom: isMobile ? 24 : 36,
      alignItems: 'center',
    },
    title: {
      fontSize: isMobile ? 22 : 24,
      fontFamily: 'Poppins_600SemiBold',
      color: '#1F2937',
      marginBottom: isMobile ? 6 : 12,
    },
    subtitle: {
      fontSize: 14,
      fontFamily: 'Poppins_400Regular',
      color: '#4B5563',
    },
    inputContainer: {
      position: "relative",
      marginBottom: 16,
      flexDirection: "row",
      alignItems: "center",
      width: "100%",
    },
    iconWrapper: {
      position: "absolute",
      left: 14,
      zIndex: 1,
      opacity: 0.7,
    },
    input: {
      flex: 1,
      paddingVertical: 14,
      paddingLeft: 42,
      paddingRight: 16,
      borderWidth: 1,
      borderColor: "#E5E7EB",
      borderRadius: 8,
      fontSize: 16,
      color: "#1F2937",
      backgroundColor: "white",
    },
    requirementsContainer: {
      marginTop: 10,
      marginBottom: 20,
    },
    requirementsTitle: {
      fontSize: 14,
      fontFamily: 'Poppins_500Medium',
      color: '#4B5563',
      marginBottom: 8,
    },
    requirementsList: {
      backgroundColor: '#F9FAFB',
      borderRadius: 8,
      padding: 12,
    },
    validationItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 4,
    },
    validationText: {
      marginLeft: 8,
      fontSize: 13,
      fontFamily: 'Poppins_400Regular',
    },
    validText: {
      color: '#10B981',
    },
    invalidText: {
      color: '#6B7280',
    },
    buttonContainer: {
      height: isMobile ? 48 : 56,
      marginTop: 8,
    },
    disabledButton: {
      cursor: 'not-allowed',
    },
    buttonGradient: {
      borderRadius: 12,
      paddingVertical: 12,
    },
    buttonContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: isMobile ? 24 : 32,
    },
    buttonText: {
      marginLeft: 8,
      color: '#FFFFFF',
      fontSize: isMobile ? 14 : 16,
      fontFamily: 'Poppins_600SemiBold',
    },
    backContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
    },
    backText: {
      fontSize: isMobile ? 14 : 16,
      fontFamily: 'Poppins_400Regular',
      color: '#4C51BF',
    },
    errorText: {
      color: '#DC2626',
      fontSize: 14,
      textAlign: 'center',
      fontFamily: 'Poppins_400Regular',
    },
    mobileError: {
      fontSize: 14,
    },
    desktopError: {
      fontSize: 16,
    },
    errorContainer: {
      backgroundColor: '#FEF2F2',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: isMobile ? 8 : 12,
      paddingHorizontal: 30,
      marginTop: 16,
      borderRadius: 4,
    },
    successText: {
      color: '#059669',
      fontSize: 14,
      textAlign: 'center',
      fontFamily: 'Poppins_400Regular',
    },
    mobileSuccess: {
      fontSize: 14,
    },
    desktopSuccess: {
      fontSize: 16,
    },
    successContainer: {
      backgroundColor: '#ECFDF5',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: isMobile ? 8 : 12,
      paddingHorizontal: 30,
      marginTop: 16,
      borderRadius: 4,
    },
    backButton: {
      gap: 8,
      position: 'absolute',
      top: isMobile ? 16 : 32,
      left: isMobile ? 16 : 32,
      zIndex: 10,
      width: 48,
      height: 48,
      backgroundColor: isMobile ? '#FFFFFF' : '#f9fafb',
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    },
  });
