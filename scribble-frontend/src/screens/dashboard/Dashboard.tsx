import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Text,
  ScrollView,
  Platform,
  Image,
  Modal,
  BackHandler,
  Alert
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
// import {
//   LayoutDashboard,
//   User,
//   Bell,
//   CalendarClock,
// } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect, useNavigation, useIsFocused } from '@react-navigation/native';

import ClinicianDashboard from 'src/features/clinician-dashboard/screens/ClinicianDashboard';
import VisitScreen from '@/src/features/visits/screens/DailyVisitScreen';
import Assessments from 'src/screens/Assessments/Assessments';
import NotificationsScreen from 'src/features/notification/screens/NotificationsScreen';
import ProfileScreen from 'src/features/profile/screens/ProfileScreen';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;
import { useDispatch } from 'react-redux';
import { logout } from 'src/redux/slices/authSlice';
import AsyncStorage from "@react-native-async-storage/async-storage";
import Octicons from '@expo/vector-icons/Octicons';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Feather } from '@expo/vector-icons';
import { MENU_HOME_NONSELECT, MENU_HOME_SELECT, MENU_NOTI_NONSELECT, MENU_PROFILE_NONSELECT, MENU_PROFILE_SELECT, MENU_VISIT_NONSELECT, MENU_VISIT_SELECT } from '@/assets/images';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';


type Screen =
  | 'home'
  | 'schedule'
  | 'daily'
  | 'new'
  | 'messages'
  | 'notification';

export default function Dashboard() {

  const dispatch = useDispatch();
  const [currentScreen, setCurrentScreen] = useState<Screen>('home');
  const insets = useSafeAreaInsets();
  const [showExitConfirmation, setShowExitConfirmation] = useState(false);
  const navigation = useNavigation();
  const isFocused = useIsFocused();

  // Use refs to track state across re-renders
  const needsUnloadWarning = useRef(false);
  const isProcessingBackEvent = useRef(false);
  // Setup back button handling when Dashboard is focused
  useEffect(() => {
    if (Platform.OS === 'web' && isFocused) {

      // Wait a short time to ensure we don't catch navigation TO Dashboard
      const setupDelay = setTimeout(() => {
        needsUnloadWarning.current = true;

        // Replace the current history state with our custom state
        window.history.replaceState({ dashboardPage: true }, '', window.location.pathname);

        // Then push a new state that will trigger our handler when back is pressed
        window.history.pushState({ exitConfirmState: true }, '', window.location.pathname);

        // Setup the beforeunload warning to prevent accidental navigation away
        window.addEventListener('beforeunload', handleBeforeUnload);
      }, 300);

      // Setup the popstate handler to catch the back button
      const handlePopState = (event) => {
        const state = event.state;

        // Only handle if we're on the Dashboard page
        const isDashboardPath =
          window.location.pathname === '/home' ||
          window.location.pathname === '/' ||
          window.location.pathname === '';

        // Detect if we're truly trying to exit the Dashboard
        const isDashboardExitAttempt =
          isDashboardPath &&
          needsUnloadWarning.current &&
          !isProcessingBackEvent.current;

        if (isDashboardExitAttempt) {
          // Prevent immediate navigation by flagging we're processing
          isProcessingBackEvent.current = true;

          // Show the confirmation dialog
          setShowExitConfirmation(true);

          // Push state again to stay on the same page
          window.history.pushState({ exitConfirmState: true }, '', window.location.pathname);

          // Reset the processing flag after a short delay
          setTimeout(() => {
            isProcessingBackEvent.current = false;
          }, 100);

          return;
        }
      };

      // Add listener for back button via popstate
      window.addEventListener('popstate', handlePopState);

      // Cleanup function
      return () => {
        clearTimeout(setupDelay);
        window.removeEventListener('popstate', handlePopState);
        window.removeEventListener('beforeunload', handleBeforeUnload);
        needsUnloadWarning.current = false;
        isProcessingBackEvent.current = false;
      };
    }
  }, [isFocused]);



  // Warning for users trying to close the tab or navigate away
  const handleBeforeUnload = (event) => {
    if (needsUnloadWarning.current) {
      const message = "Are you sure you want to leave? Your changes may not be saved.";
      event.preventDefault();
      event.returnValue = message;
      return message;
    }
  };

  // Handle the "Stay" button in the confirmation modal
  const handleStayInApp = () => {
    setShowExitConfirmation(false);
  };

  // Handle the "Exit" button in the confirmation modal
  const handleExitApp = () => {
    setShowExitConfirmation(false);
    needsUnloadWarning.current = false;

    // Close the window/tab
    if (Platform.OS === 'web') {
      // Try window.close() which works if the window was opened by script
      window.close();

      // If window.close() didn't work, redirect to a blank page
      setTimeout(() => {
        window.location.href = 'about:blank';
        onLogout()
      }, 100);
    }
  };
  const onLogout = async () => {
    dispatch(logout());
    await AsyncStorage.clear()
    navigation.replace('Login');
  };
  const renderScreen = () => {
    switch (currentScreen) {
      case 'home':
        return <ClinicianDashboard />;
      case 'schedule':
        return <VisitScreen />;
      case 'daily':
        return <Assessments />;
      case 'notification':
        return <NotificationsScreen />;
      case 'messages':
        return <ProfileScreen />;
      default:
        return <ClinicianDashboard />;
    }
  };

  // const getScreenTitle = () => {
  //   switch (currentScreen) {
  //     case 'home':
  //       return 'Dashboard';
  //     case 'schedule':
  //       return 'My Visits';
  //     case 'daily':
  //       return 'Assessments';
  //     case 'notification':
  //       return 'Notifications';
  //     case 'messages':
  //       return 'Profile';
  //     default:
  //       return 'Dashboard';
  //   }
  // };

  // Calculate footer height based on platform
  const footerHeight = Platform.OS === 'android' ? 80 + insets.bottom : 60 + insets.bottom;
  // Calculate header height
  let headerHeight = 0
  // let headerHeight = currentScreen === 'home' ? 60 :0;

  return (
    <SafeAreaView style={styles.outerContainer}>
      <StatusBar style="dark" />

      {/* Fixed Header */}
      {/* {currentScreen === 'home' &&
        <View style={[styles.header, { height: headerHeight }]}>
          <SafeAreaView style={styles.headerSafeArea}>
            <Text style={styles.headerTitle}>{getScreenTitle()}</Text>
          </SafeAreaView>
        </View>
      } */}

      {/* Scrollable Content */}
      <View style={[
        styles.contentWrapper,
        {
          top: headerHeight,
          height: screenHeight - headerHeight - footerHeight
        }
      ]}>
        <LinearGradient
          colors={['#e0e7ff', '#ffffff', '#f3e8ff']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradientContainer}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
          >
            {renderScreen()}
          </ScrollView>
        </LinearGradient>
      </View>

      {/* Fixed Footer */}
      <View style={[styles.footer, { height: footerHeight }]}>
        <View style={styles.bottomNav}>
          <TouchableOpacity
            style={styles.navItem}
            onPress={() => setCurrentScreen('home')}
          >

            <Image source={currentScreen === 'home' ? MENU_HOME_SELECT : MENU_HOME_NONSELECT} style={{ width: 20, height: 20 }} />
            <Text id="home-text"
              style={[
                styles.navText,
                {
                  color: currentScreen === 'home' ? '#3B82F6' : '#94A3B8',
                },
              ]}
            >
              Home
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => setCurrentScreen('schedule')}
          >

            <Image source={currentScreen === 'schedule' ? MENU_VISIT_SELECT : MENU_VISIT_NONSELECT} style={{ width: 20, height: 20 }} />
            <Text id="my-visits-text"
              style={[
                styles.navText,
                {
                  color: currentScreen === 'schedule' ? '#3B82F6' : '#94A3B8',
                },
              ]}
            >
              My Visits
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => setCurrentScreen('notification')}
          >
            {/* <Bell
                size={24}
                color={currentScreen === 'notification' ? '#3B82F6' : '#94A3B8'}
              /> */}
            <MaterialCommunityIcons
              name="bell-outline"
              size={24}
              color={currentScreen === 'notification' ? '#3B82F6' : '#94A3B8'}
            />
            <Text id="notification-text"
              style={[
                styles.navText,
                {
                  color:
                    currentScreen === 'notification' ? '#3B82F6' : '#94A3B8',
                },
              ]}
            >
              Notifications
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => setCurrentScreen('messages')}
          >

            <Image source={currentScreen === 'messages' ? MENU_PROFILE_SELECT : MENU_PROFILE_NONSELECT} style={{ width: 20, height: 20 }} />
            <Text id="profile-text"
              style={[
                styles.navText,
                {
                  color: currentScreen === 'messages' ? '#3B82F6' : '#94A3B8',
                },
              ]}
            >
              Profile
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Exit Confirmation Modal */}
      <Modal
        visible={showExitConfirmation}
        transparent={true}
        animationType="fade"
        onRequestClose={handleStayInApp}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Leave Site?</Text>
            <Text style={styles.modalText}>
              Changes that you made may not be saved.
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonCancel]}
                onPress={handleStayInApp}
              >
                <Text style={styles.modalButtonTextCancel}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonConfirm]}
                onPress={handleExitApp}
              >
                <Text style={styles.modalButtonTextConfirm}>Leave</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    zIndex: 10,
  },
  headerSafeArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Poppins_600SemiBold',
    color: '#1E293B',
  },
  contentWrapper: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 1,
  },
  gradientContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    // paddingBottom: 20,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    zIndex: 10,
  },
  footerSafeArea: {
    flex: 1,
  },
  bottomNav: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  navItem: {
    marginTop: 10,
    alignItems: 'center',
    //justifyContent: 'center',
  },
  navText: {
    fontSize: 11,
    marginTop: 0,
    fontFamily: 'Poppins_400Regular',
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    width: '80%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1E293B',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#64748B',
    marginBottom: 24,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  modalButtonCancel: {
    backgroundColor: '#E2E8F0',
    marginRight: 10,
  },
  modalButtonConfirm: {
    backgroundColor: '#3B82F6',
    marginLeft: 10,
  },
  modalButtonTextCancel: {
    fontFamily: 'Poppins_500Medium',
    fontSize: 12,
    color: '#1E293B',
  },
  modalButtonTextConfirm: {
    fontFamily: 'Poppins_500Medium',
    fontSize: 12,
    color: '#FFFFFF',
  },
});
