import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Assessment {
  id: string;
  title: string;
  completed: boolean;
}

interface AssessmentState {
  currentAssessment: Assessment | null;
  assessments: Assessment[];
}

const initialState: AssessmentState = {
  currentAssessment: null,
  assessments: [],
};

const assessmentSlice = createSlice({
  name: 'assessment',
  initialState,
  reducers: {
    setCurrentAssessment: (state, action: PayloadAction<Assessment>) => {
      state.currentAssessment = action.payload;
    },
    clearCurrentAssessment: state => {
      state.currentAssessment = null;
    },
    addAssessment: (state, action: PayloadAction<Assessment>) => {
      state.assessments.push(action.payload);
    },
    markAssessmentComplete: (state, action: PayloadAction<string>) => {
      const assessment = state.assessments.find(a => a.id === action.payload);
      if (assessment) {
        assessment.completed = true;
      }
    },
  },
});

export const {
  setCurrentAssessment,
  clearCurrentAssessment,
  addAssessment,
  markAssessmentComplete,
} = assessmentSlice.actions;
export default assessmentSlice.reducer;
