import { createSlice } from '@reduxjs/toolkit';

interface RecordingState {
  isRecording: boolean;
}

const initialState: RecordingState = {
  isRecording: false,
};

const recordingSlice = createSlice({
  name: 'recording',
  initialState,
  reducers: {
    startRecording: state => {
      state.isRecording = true;
    },
    stopRecording: state => {
      state.isRecording = false;
    },
  },
});

export const { startRecording, stopRecording } = recordingSlice.actions;
export default recordingSlice.reducer;
