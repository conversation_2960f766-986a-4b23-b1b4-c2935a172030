import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from 'src/redux/store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { login, logout } from 'src/redux/slices/authSlice';
import { useNavigation } from '@react-navigation/native';

export const useAuth = () => {
  const [isLoading, setIsLoading] = useState(true);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);
  const user = useSelector((state: RootState) => state.auth.user);

  // Check authentication status on app load
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const token = await AsyncStorage.getItem('authToken');
        const email = await AsyncStorage.getItem('email');
        const firstName = await AsyncStorage.getItem('firstName');
        const lastName = await AsyncStorage.getItem('lastName');
        const staffId = await AsyncStorage.getItem('staffId');
        const discipline = await AsyncStorage.getItem('discipline');
        const phone = await AsyncStorage.getItem('phone');
        const dateOfBirth = await AsyncStorage.getItem('dateOfBirth');

        if (token && email) {
          // Restore user session from AsyncStorage
          dispatch(login({
            user: {
              accessToken: token,
              email,
              firstName: firstName || '',
              lastName: lastName || '',
              staffId: staffId || '',
              disciplineId: discipline || '',
              phone: phone || '',
              dateOfBirth: dateOfBirth || '',
              isFirstLogin: false,
              // Add other user properties as needed
            },
            role: user?.role || 'user' // Default to user role if not available
          }));
        } else {
          // No valid session found
          dispatch(logout());
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
        dispatch(logout());
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, [dispatch]);

  // Function to handle user logout
  const handleLogout = async () => {
    try {
      // Clear all authentication data from AsyncStorage
      await AsyncStorage.multiRemove([
        'authToken',
        'email',
        'firstName',
        'lastName',
        'staffId',
        'discipline',
        'phone',
        'dateOfBirth'
      ]);

      // Update Redux state
      dispatch(logout());

      // Navigate to login screen
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return {
    isLoading,
    isAuthenticated,
    user,
    logout: handleLogout
  };
};

export default useAuth;
