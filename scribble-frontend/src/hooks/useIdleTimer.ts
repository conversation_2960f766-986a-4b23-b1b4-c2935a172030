import { useEffect } from 'react';
import { useLogoutUser } from '../hooks/useLogoutUser';

export const useIdleTimer = () => {
  const { logOutUser, updateLastActive, isSessionExpired } = useLogoutUser();
  
  useEffect(() => {
    const updateActivity = () => {
      updateLastActive()
    };

    const events = ['mousemove', 'mousedown', 'keypress', 'scroll', 'touchstart'];
    events.forEach(e => window.addEventListener(e, updateActivity));

    const interval = setInterval(async () => {
      if (await isSessionExpired()) {
        await logOutUser();
      }
    }, 60 * 1000); // Check every 1 minute

    return () => {
      events.forEach(e => window.removeEventListener(e, updateActivity));
      clearInterval(interval);
    };
  }, []);
};