import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';
import { useNavigation } from '@react-navigation/native';
// import { ChevronLeft } from 'lucide-react-native';

const ReviewAssessment = () => {
  const navigation = useNavigation();

  const [bloodPressure, setBloodPressure] = useState('120/80');
  const [medications, setMedications] = useState(
    'Aspirin 81mg daily\nMetformin 500mg twice daily',
  );
  const [temperature, setTemperature] = useState('');
  const [notes, setNotes] = useState('');

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <View style={styles.header}>
        <TouchableOpacity
          style={{ position: 'absolute', left: 20, top: 20 }}
          onPress={() => navigation.goBack()}
        >
          {/* <ChevronLeft size={26} /> */}
        </TouchableOpacity>
        <Text style={styles.title}>Review Assessment</Text>
      </View>

      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: '60%' }]} />
        </View>
        <Text style={styles.stepText}>Section 7 of 10</Text>
      </View>
      <View style={{ flex: 1, height: screenHeight - 200 }}>
        <ScrollView style={styles.scrollView}>
          <View style={styles.questionContainer}>
            <Text style={styles.questionLabel}>Question Code: M1001</Text>
            <Text style={styles.questionText}>
              Patient's blood pressure reading?
            </Text>
            <TextInput
              style={styles.input}
              value={bloodPressure}
              onChangeText={setBloodPressure}
              placeholder="Enter blood pressure"
            />
          </View>

          <View style={styles.questionContainer}>
            <Text style={styles.questionLabel}>Question Code: M1001</Text>
            <Text style={styles.questionText}>Current medication list?</Text>
            <TextInput
              style={[styles.input, styles.multilineInput]}
              value={medications}
              onChangeText={setMedications}
              multiline
              numberOfLines={4}
              placeholder="Enter medications"
            />
          </View>

          <View style={styles.missedQuestionsContainer}>
            <Text style={styles.missedQuestionsTitle}>Missed Questions</Text>
            <View style={styles.warningContainer}>
              <MaterialIcons name="warning" size={24} color="#F6A609" />
              <View style={styles.warningContent}>
                <Text style={styles.warningTitle}>
                  Question 3 - Not Answered
                </Text>
                <Text style={styles.warningText}>
                  Patient's temperature reading?
                </Text>
                <TouchableOpacity style={styles.goToButton}>
                  <Text style={styles.goToButtonText}>Go to question</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View style={styles.notesContainer}>
            <Text style={styles.notesTitle}>Assessment Notes</Text>
            <TextInput
              style={[styles.input, styles.multilineInput]}
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              placeholder="Add any notes about your assessment here..."
              placeholderTextColor="#999"
            />
          </View>
        </ScrollView>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.saveButton}>
          <Text style={styles.saveButtonText}>Save Draft</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.submitButton}>
          <Text style={styles.submitButtonText}>Submit</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 20,
    height: screenHeight - 160,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    marginLeft: 24,
  },
  progressContainer: {
    paddingHorizontal: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4285F4',
    borderRadius: 4,
  },
  stepText: {
    color: '#666',
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
  },
  questionContainer: {
    marginBottom: 16,
  },
  questionLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    fontFamily: 'Poppins_500Medium',
  },
  questionText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
    fontFamily: 'Poppins_500Medium',
  },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  missedQuestionsContainer: {
    marginBottom: 24,
  },
  missedQuestionsTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 16,
  },
  warningContainer: {
    backgroundColor: '#FFF9E6',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  warningContent: {
    marginLeft: 12,
    flex: 1,
  },
  warningTitle: {
    color: '#F6A609',
    fontWeight: '600',
    marginBottom: 4,
  },
  warningText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    marginBottom: 8,
  },
  goToButton: {
    alignSelf: 'flex-start',
  },
  goToButtonText: {
    color: '#F6A609',
    fontWeight: '500',
  },
  notesContainer: {
    marginBottom: 24,
  },
  notesTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 16,
    // flex: 1,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 22,
    height: 74,
  },
  saveButton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    width: screenWidth / 2 - 24,
  },
  submitButton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#4285F4',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    width: screenWidth / 2 - 24,
  },
  saveButtonText: {
    color: '#666',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
});

export default ReviewAssessment;
