import React, { useCallback, useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform, ActivityIndicator, FlatList, Linking } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons, FontAwesome, MaterialIcons } from '@expo/vector-icons';
// import { ChevronLeft, LucideIcon, MapPin, Mic, Phone } from 'lucide-react-native';
import theme from '@/src/theme';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { globalStyles } from '@/src/styles';
import { SafeAreaView } from 'react-native-safe-area-context';
import { fetchFormListVisitWise } from '../../visits/components/upcoming-visit/visit-api';
import { dateConvert, formatLocalTimeFromGMT } from '@/src/utils/DateUtils';

import { useDispatch, useSelector } from 'react-redux';
import { hideRecorder, showRecorder } from '@/src/redux/slices/audioRecorderSlice';
import { RootState } from '@/src/redux/store';
import { DynamicMessageModal, DynamicMessageModalWithConfirm, MessageModal } from '@/src/utils/MessageModal';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { Feather } from '@expo/vector-icons';
import { fs, ms } from '@/src/utils/ScreenUtils';
import { status } from '@/src/constants/status';
import { getSecureItem } from '@/src/utils/cryptoHelper';

const VisitScreen = ({ route }) => {
    const recorderState = useSelector((state: RootState) => state.audioRecorder);
    const { visit } = route.params;
    const visitStatus = visit.status
    // console.log("status :"+visit.visitStatus)

    const navigation = useNavigation();
    const dispatch = useDispatch();
    const [forms, setForms] = useState([])
    const [loading, setLoading] = useState(true);
    const [loadingMore, setLoadingMore] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const isWeb = Platform.OS === 'web';
    const containerRef = useRef(null);
    const scrollTimeout = useRef(null);
    setPageTitle('Client Visits');

    const [showMessageModal, setShowMessageModal] = useState(false)
    const [message, setMessage] = useState("")
    const [messageType, setMessageType] = useState("success")
    const [msgTitle, setMsgTitle] = useState("")

    // Safari detection - check if it's iOS or iPadOS
    // const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
    //     /iPad|iPhone|iPod/.test(navigator.userAgent);

    function isSafari() {
        if (Platform.OS == "web") {
            const ua = navigator.userAgent;
            const isSafariDesktop = ua.includes("Safari") && !ua.includes("Chrome") && !ua.includes("Chromium");
            const isIOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
            return isSafariDesktop || isIOS;
        } else {
            // if(Platform.OS==="ios"){
            //     return true;
            // }
            return false
        }
    }

    // console.log("isSafari : " + isSafari())
    const hasMic = [status.IN_PROGRESS, status.NOT_STARTED]
    // const renderStatusCard = (title, status, statusColor, showMic = false) => {
    const renderStatusCard = (form: any) => {
        let showMic = (hasMic.indexOf(form.status)) > -1 ? true : false
        // const onClickMic = () => {
        //     navigation.navigate("Recording", { assesId: form._id })
        // }
        const onClick = async() => {
            if (showMic) {
                if (visitStatus != "Missed") {
                    const recordingURI = recorderState.recordingURI;
                    const isFloatingRecorderRunning = recorderState.showPauseResume;
                    const isFloatingRecorderVisible = recorderState.isVisible;
                    let hasError = false;
                    const storedRecording = await getSecureItem("recording");
                    console.log("storedRecording:", storedRecording)
                    if(storedRecording){
                        hasError=true
                    }
                    // console.log(isFloatingRecorderRunning, isFloatingRecorderVisible, recordingURI)
                    if (isFloatingRecorderRunning || recordingURI != null || hasError) {
                        setShowMessageModal(true)
                        setMessage("Complete the current recording before starting a new one.")
                        setMsgTitle("Recording in Progress?")
                        // setMessageType("error")
                    } else {
                        if (isFloatingRecorderVisible) {
                            dispatch(hideRecorder())
                            setTimeout(() => {
                                dispatch(showRecorder({
                                    expanded: true,
                                    assesId: form._id
                                }));
                            }, 100)

                        } else {
                            // navigation.navigate("Recording", { assesId: form._id })
                            dispatch(showRecorder({
                                expanded: true,
                                assesId: form._id
                            }));
                        }
                    }
                }
            } else {
                console.log("Assesment--->",form._id)
                navigation.navigate('Assesment', { assesId: form._id });
            }
        }

        // console.log("showMic:",showMic)
        const color = getStatusTextColor(form.status)
        return (
            <TouchableOpacity key={form._id} style={styles.card} onPress={onClick}>
                <View style={styles.statusContainer}>
                    <View style={{ gap: 12 }}>
                        <View>
                            <Text style={styles.cardTitle}>{(form?.name) ? form.name : form.formId}</Text>
                        </View>
                        <View style={[styles.statusBadge, { backgroundColor: color.backgroundColor }]}
                            testID={`visit-status-badge-${form._id}`}
                            data-testid={`visit-status-badge-${form._id}`}
                        >

                            <Text style={[styles.statusText, { color: color.color }]}>{form.status}</Text>
                        </View>
                    </View>
                    {showMic && (
                        visitStatus !== 'Missed' ? (
                            <View style={styles.icon}>
                                <FontAwesome name="microphone" size={20} color="#007AFF" />
                            </View>
                        ) : (
                            <View style={styles.icon}>
                                <FontAwesome name="microphone-slash" size={20} color="gray" />
                            </View>
                        )
                    )}

                </View>
            </TouchableOpacity>
        );
    };
    const getStatusTextColor = (status: string) => {
        switch (status) {
            case 'In Progress':
                return { color: '#F9A704', backgroundColor: 'rgba(249, 167, 4, 0.12' };
            case 'Not Started':
                return { color: '#FF7882', backgroundColor: 'rgba(255, 120, 130, 0.12)' };
            case 'Submitted to AI':
                return { color: '#007AFF', backgroundColor: 'rgba(0, 122, 255, 0.12)' };
            case 'Submitted to EMR':
                return { color: '#44AFC7', backgroundColor: 'rgba(68, 175, 199, 0.12)' };
            case 'Completed':
                return { color: '#6847C1', backgroundColor: 'rgba(104, 71, 193, 0.12)' };
            default:
                return { color: '#000000', backgroundColor: 'rgba(241, 196, 15, 0.2)' };
        }
    };
    // const renderIcon = (Icon: LucideIcon, onClick: () => void) => (
    //     <TouchableOpacity style={styles.icon} onPress={onClick}>
    //         <Icon fill={'#1c73d6'} stroke={'#EFF6FF'} size={22} />
    //     </TouchableOpacity>
    // );
    const onClickMap = () => {
        const latitude = 40.74958102694104;
        const longitude = -73.98537418664596;
        const url = `https://www.google.com/maps?q=${latitude},${longitude}`;

        Linking.openURL(url);
    };
    const onClickCall = () => {

    };
    const limit = 5;

    const fetchFormList = async (pageNumber = 1) => {
        if (loadingMore || !hasMore) return;

        try {
            if (pageNumber === 1) setLoading(true);
            else setLoadingMore(true);

            const result = await fetchFormListVisitWise({ limit: limit, page: pageNumber, visitId: visit._id });
            //   console.log("data length:" + result.data.length);
            if (result.data.length > 0) {
                setForms((prev) => (pageNumber === 1 ? result.data : [...prev, ...result.data]));
                setPage(pageNumber + 1);
                if (result.data.length < limit || result.total_records_available <= limit * pageNumber) {
                    setHasMore(false);
                }
            } else {
                setHasMore(false);
            }
        } catch (error) {
            console.error(error);
        } finally {
            // console.log("----finally")
            setLoading(false);
            setLoadingMore(false);
        }
    };
    const onBackClick = () => {
        navigation.goBack();
    };
    const onProfileClick = () => {
        // console.log(JSON.stringify(visit))
        navigation.navigate("VisitDetails", { visit });
    }
    useFocusEffect(
        useCallback(() => {

            setHasMore(true);
            setPage(1);
            fetchFormList(1); // Fetch initial data
        }, [])
    );

    const handleLoadMore = () => {
        if (!loadingMore && hasMore) {
            fetchFormList(page);
        }
    };

    const handleWebScroll = (event) => {
        if (scrollTimeout.current) clearTimeout(scrollTimeout.current);

        scrollTimeout.current = setTimeout(() => {
            if (event.target) {
                const { scrollTop, scrollHeight, clientHeight } = event.target;
                console.log(`ScrollTop: ${scrollTop}, ScrollHeight: ${scrollHeight}, ClientHeight: ${clientHeight}`);
                if (scrollHeight - scrollTop <= clientHeight + 20) {
                    handleLoadMore();
                }
            }
        }, 100);
    };
    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleWebScroll);
        }

        // Check for null before removing the event listener
        return () => {
            if (container) {
                container.removeEventListener('scroll', handleWebScroll);
            }
            if (scrollTimeout.current) {
                clearTimeout(scrollTimeout.current);
            }
        };
    }, [page, loadingMore, hasMore]);

    const renderWebContent = () => {
        return (
            <div ref={containerRef} style={{ overflowY: 'auto', height: '70vh', padding: '0px 15px 15px 15px', }}>
                {forms.map((form) =>
                    renderStatusCard(form)
                )}
                {/* {renderStatusCard('Start Of Care', 'In Progress', 'rgba(241, 196, 15, 0.2)', true)}
                {renderStatusCard('Fall Risk', 'Not Started', 'rgba(231, 76, 60, 0.2)', true)}
                {renderStatusCard('Hospitalization Risk', 'Submitted to AI', 'rgba(52, 152, 219, 0.2)')}
                {renderStatusCard('Advance Directive', 'Submitted to EMR', 'rgba(46, 204, 113, 0.2)')}
                {renderStatusCard('Advance Directive', 'Completed', 'rgba(155, 89, 182, 0.2)')} */}
                {loadingMore && (
                    <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10, marginBottom: 10 }} />
                )}
            </div>
        )
    }
    const renderNativeContent = () => {
        return (
            <FlatList
                style={styles.content}
                data={forms}
                keyExtractor={(item) => item?._id}
                renderItem={({ item }) => (
                    renderStatusCard(item)
                )}
                onEndReached={handleLoadMore}
                onEndReachedThreshold={0.1}
                ListFooterComponent={
                    loadingMore ? (
                        <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10 }} />
                    ) : null
                }
            />
        )
    }
    const onModalCancel = () => {
        setShowMessageModal(false)
    };
    return (
        <SafeAreaView style={styles.container}>
            {/* <StatusBar style="dark" /> */}
            <DynamicMessageModalWithConfirm visible={showMessageModal} onCancel={onModalCancel} message={message} title={msgTitle} iconComponent={<MaterialIcons name="multitrack-audio" size={24} color="#1D75F5" />}/>
            {/* <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} /> */}
            {/* Header */}
            <View style={styles.header}>
                <View style={[styles.headerRow, styles.topRow]}>
                    <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
                        {/* <ChevronLeft color={theme.colors.text} size={24} /> */}
                        <Feather name="chevron-left" size={24} color={theme.colors.text} />

                    </TouchableOpacity>
                    <Text style={styles.backText}>Start Of Care</Text>
                    <View style={styles.headerContentRight}>

                        {/* {renderIcon(Phone, onClickCall)} */}
                        {/* {renderIcon(MapPin, onClickMap)} */}
                    </View>
                </View>
                <View style={[styles.headerRow, styles.bottomRow]}>
                    <TouchableOpacity style={styles.headerContentLeft} onPress={onProfileClick}>
                        {/* <View style={styles.avatarContainer}>
                            <Text style={styles.avatarText}>{visit?.clientFirstName[0]}</Text>
                        </View> */}
                        <View style={styles.profileBox}>
                            <Text style={styles.profileInitials} allowFontScaling={false}>
                                {visit?.clientFirstName[0] || "P"}
                            </Text>
                        </View>
                        <Text allowFontScaling={false} style={[styles.patientName, isSafari() && styles.nameMarTop]}>{visit.clientFirstName} {visit.clientLastName}</Text>
                    </TouchableOpacity>
                    <View style={styles.headerContentRight}>
                        <Ionicons name="calendar-outline" size={16} color="#666" style={{ marginTop: 1 }} />
                        <Text allowFontScaling={false} 
                            style={[styles.appointmentText, isSafari() && styles.timeMarTop]}
                            testID="visit-date-time-label"
                            data-testid="visit-date-time-label"
                        >
                            {dateConvert(visit.visitDate, 3)}  {/* Date part, e.g. 'Jul 26, 2025' */}
                            {formatLocalTimeFromGMT(visit.visitDate)}
                        </Text>

                    </View>
                </View>
            </View>

            {/* Content */}
            <View style={styles.contentContainer}>

                {loading ? (
                    <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
                ) : forms.length > 0 ? (
                    isWeb ? renderWebContent() : renderNativeContent()
                ) : (
                    <View style={styles.blankTextContainer}>
                        <Text  style={styles.blankText}>No data available</Text>
                    </View>
                )}
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f9fafc',
    },
    blankTextContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center"
    },
    blankText: {
        textAlign: "center",
        marginTop: 20,
        fontSize: 20,
        fontFamily: "Poppins_500Medium",
    },
    header: {

        // paddingHorizontal: 12,
        // borderBottomWidth: 1,
        // borderBottomColor: "#E2E8F0",
        backgroundColor: "#FFFFFF",
        position: "relative",

        // ...globalStyles.shadow,
    },
    headerRow: {
        flexDirection: "row",
        alignItems: "center",
        // height: 50,
    },
    topRow: {
        paddingTop: 20,
        paddingBottom: 10,
        paddingHorizontal: 15
    },
    bottomRow: {
        paddingTop: 5,
        paddingBottom: 15,
        paddingHorizontal: 15,
        alignItems: "center",
        justifyContent: 'space-between',
        
        

    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    backText: {
        fontFamily: "Poppins_400Regular",
        fontSize: 18,
        // color: theme.colors.text,
        paddingHorizontal: 10,
        textAlign: "center",

    },
    headerContentRight: {
        // display: 'flex',
        flexDirection: 'row',
        // flex: 1,
        gap: 8,
        alignItems:'center',
        marginLeft: 20,
       // minWidth: 100,    
       maxWidth:'50%',  
       paddingVertical:ms(6),
       paddingRight:10,
    
    // backgroundColor:'blue',
    },
    headerContentLeft: {
        // display: 'flex',
        flexDirection: 'row',
        // flex: 1,
        gap: 8,
        minWidth: 0,  
        alignItems:'center',     
        maxWidth: '50%',
        paddingHorizontal:10

    // backgroundColor:'red',
        // marginTop: 6,
    },

    // avatarContainer: {
    //     width: 30,
    //     height: 30,
    //     borderRadius: 15,
    //     backgroundColor: '#E5E5EA',
    //     alignItems: 'center',
    //     justifyContent: 'center',
    //     // marginRight: 8
    // },
    // avatarText: {
    //     fontSize: 20,
    //     fontWeight: '600',
    // },
    profileBox: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: "#CBD5E1",
        justifyContent: "center",
        alignItems: "center",
        // marginRight: 6,
    },
    profileInitials: {
        fontFamily: "Poppins_600SemiBold",
        color: "#FFFFFF",
        fontSize: 14,
    },
    patientName: {
        fontSize: 14,
        fontFamily: 'Poppins_500Medium',
        color: '#251F38',

        // marginTop: 6,
    },
    nameMarTop: {
        marginTop: -1,
    },
    appointmentText: {
        fontSize: fs(12),
        color: '#666',
        fontFamily: 'Poppins_400Regular',

        // marginLeft: 4,
    },
    timeMarTop: {
        marginTop: -2,
    },
    contentContainer: {
        flex: 1,
        backgroundColor: '#F2F2F7',
    },
    sectionTitle: {
        fontSize: 22,
        fontWeight: '600',
        marginVertical: 16,
        marginHorizontal: 16,
        color: '#000',
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    card: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 16,
        // marginBottom: 12,
        // shadowColor: '#000',
        // shadowOffset: { width: 0, height: 1 },
        // shadowOpacity: 0.1,
        // shadowRadius: 2,
        // elevation: 2,
        // marginVertical: 5,
        marginTop: 10,
        ...globalStyles.shadow,
        // marginHorizontal: 4,
    },
    cardTitle: {
        fontSize: 16,
        fontFamily: 'Poppins_600SemiBold',
        marginBottom: 10,
        color: '#000',
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    statusBadge: {
        paddingVertical: 4,
        paddingHorizontal: 12,
        borderRadius: 16,
        alignSelf: 'flex-start',
    },
    statusText: {
        fontSize: 12,
        fontFamily: 'Poppins_600SemiBold'
    },
    micIcon: {
        marginRight: 8,
    },
    icon: {
        width: 36,
        height: 36,
        backgroundColor: '#EFF6FF',
        borderRadius: 18,
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex',
    },
});

export default VisitScreen;