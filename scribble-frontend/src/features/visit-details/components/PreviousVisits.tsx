import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  StyleSheet,
  Animated,
  TouchableOpacity,
  TextInput,
  Platform,
  Linking,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
// import {
//   Bell,
//   Siren,
//   MoreVertical,
//   CalendarClock,
//   Trash,
//   Search,
//   Clock,
//   MapPin,
//   Stethoscope,
//   ChevronLeft,
//   Calendar,
// } from 'lucide-react-native';\
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { globalStyles } from 'src/styles';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import VisitItem from 'src/features/visits/components/visit-item/VisitItem';
import theme from 'src/theme';
import { getInitials } from 'src/utils/ProfileUtils';
import { SafeAreaView } from 'react-native-safe-area-context';
import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';
import { getPastVisit } from '../../visits/components/upcoming-visit/visit-api';
import { dateConvert, formatDateDifference_v1 } from '@/src/utils/DateUtils';
import { setPageTitle } from '@/src/utils/GeneralUtils';

// Mock visits data

export default function PreviousVisits({ route }) {
  setPageTitle('Client Visits');
  const navigation = useNavigation();
  const clientId = route.params.clientId

  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [visits, setVisits] = useState([]);
  const containerRef = useRef(null);
  const scrollTimeout = useRef(null);


  // console.log(clientId)
  const onClickVisit = (visit) => {
    // console.log("Clicked")
    navigation.navigate("VisitScreen",{ visit })
    // navigation.navigate('Assesment', { assesId: visit._id });
  };

  const getBadgeColor = status => {
    switch (status) {
      case 'Scheduled':
        return {
          backgroundColor: '#E0F2FE',
          color: '#0284C7',
        };
      case 'In Progress':
        return {
          backgroundColor: '#FEF3C7',
          color: '#D97706',
        };
      case 'Completed':
        return {
          backgroundColor: '#DCFCE7',
          color: '#047857',
        };
      case 'Past Due':
        return {
          backgroundColor: '#FEE2E2',
          color: '#DC2626',
        };
      default:
        return {
          backgroundColor: '#E0F2FE',
          color: '#0284C7',
        };
    }
  };
  const limit = 5;
  const fetchPastVisits = async (pageNumber = 1) => {


    if (loadingMore || !hasMore) return;

    try {
      if (pageNumber === 1) setLoading(true);
      else setLoadingMore(true);

      const result = await getPastVisit({ limit: limit, page: pageNumber, clientId: clientId });
      // console.log(result.data)
      if (result.data.length > 0) {
        setVisits((prev) => (pageNumber === 1 ? result.data : [...prev, ...result.data]));
        setPage(pageNumber + 1);
        if (result.total_records_available <= limit) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };
  useFocusEffect(
    useCallback(() => {
      setHasMore(true);
      fetchPastVisits(1); // Fetch initial data
    }, [])
  );
  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      fetchPastVisits(page);
    }
  };

  const handleWebScroll = (event) => {
    if (scrollTimeout.current) clearTimeout(scrollTimeout.current);

    scrollTimeout.current = setTimeout(() => {
      if (event.target) {
        const { scrollTop, scrollHeight, clientHeight } = event.target;
        console.log(`ScrollTop: ${scrollTop}, ScrollHeight: ${scrollHeight}, ClientHeight: ${clientHeight}`);
        if (scrollHeight - scrollTop <= clientHeight + 20) {
          handleLoadMore();
        }
      }
    }, 100);
  };
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleWebScroll);
    }

    // Check for null before removing the event listener
    return () => {
      if (container) {
        container.removeEventListener('scroll', handleWebScroll);
      }
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [page, loadingMore, hasMore]);

  const renderVisitItem = (visit) => {
    return (
      <TouchableOpacity
        key={visit._id}
        style={styles.visitCard}
        onPress={() => onClickVisit(visit)}
      >

        
        <View style={styles.visitContent}>
          {/* Row with patient + EVV tag */}
          <View style={styles.topRow}>
            {visit.service != undefined || null ?
            <View style={{ flex: 1 }}>
              <Text style={styles.patientName}>{visit.service}</Text>
            </View> : null
            }
            
          </View>

          <View style={styles.addressContainer}>
            {/* <Stethoscope size={20} style={styles.timeIcon} color={'#475569'} /> */}
            <MaterialCommunityIcons
              name="stethoscope"
              size={20}
              color="#475569"
              style={styles.timeIcon}
              />
            <Text style={styles.address}>{visit.clinicianFirstName} {visit.clinicianLastName}</Text>
          </View>

          <View style={styles.addressContainer}>
            {/* <CalendarClock
              size={20}
              style={styles.timeIcon}
              color={'#475569'}
            /> */}
            <MaterialCommunityIcons
            style={styles.timeIcon}
            name="calendar-clock"
            size={24}
            color={'#475569'}
          />
            <Text style={styles.address}>{dateConvert(visit.visitDate, 3)}</Text>
            
          </View>

          <View style={styles.border} />

          <View style={styles.visitTypeContainer}>
            <LinearGradient
              colors={['#4C51BF', '#6B46C1']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.shortNameContainer}
            >
              <Text style={styles.shortNameText}>{visit.visitType}</Text>
            </LinearGradient>
            <View
              style={[
                styles.statusBadge,
                {
                  backgroundColor: getBadgeColor(visit.status).backgroundColor,
                },
              ]}
            >
              <Text
                style={[
                  styles.statusBadgeText,
                  { color: getBadgeColor(visit.status).color },
                ]}
              >
                {visit.visitStatus}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderWebContent = () => {
    return (
      <div ref={containerRef}
        data-testid="visit-scrollable-container"
        style={{
          overflowY: 'auto',
          height: '70vh',
          padding: '10px',
        }}
      >
        {visits.map(item => (
          renderVisitItem(item)
        ))}
        
        {loadingMore && (
          <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10, marginBottom: 10 }} />
        )}
        
      </div>
    );
  };
  const renderNativeContent = () => {
    return (
      <FlatList
        style={{ paddingHorizontal: 16, marginTop: 16 }}
        data={visits}
        keyExtractor={(item) => item?._id}
        renderItem={({ item }) => (
          renderVisitItem(item)
        )}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1} // Trigger when 10% away from the bottom
        ListFooterComponent={
          loadingMore ? (
            <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10 }} />
          ) : null
        }
      />
    );
  };
  return (
    <View
      style={styles.scrollContainer}
    >
      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
      ) : visits.length > 0 ? (
        Platform.OS === 'web' ? renderWebContent() : renderNativeContent()
      ) : (
        <View style={styles.blankTextContainer}>
          <Text style={styles.blankText}>No visits available</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  blankTextContainer: {
    flex: 1, justifyContent: "center", alignItems: "center"
  },
  blankText: {
    textAlign: "center", marginTop: 20, fontSize: 20, fontWeight: "bold"
  },
  scrollContainer: {
    display: 'flex',
    padding: 16,
    paddingBottom: 100,
    height: screenHeight - 125,
  },
  visitCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
    marginBottom: 16,
    position: 'relative',
  },
  visitBorder: {
    position: 'absolute',
    left: 0,
    top: 20,
    bottom: 20,
    width: 4,
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
  },
  visitContent: {
    padding: 16,
  },
  timelineBar: {
    width: 20,
    alignItems: 'center',
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  profileBox: {
    width: 34,
    height: 34,
    borderRadius: 17,
    backgroundColor: '#f26649',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  profileInitials: {
    fontFamily: 'Poppins_600SemiBold',
    color: '#FFFFFF',
    fontSize: 14,
  },
  patientName: {
    fontFamily: 'Poppins_500Medium',
    color: '#1E293B',
    fontSize: 15,
    marginBottom: 2,
  },
  visitType: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 13,
    color: '#64748B',
  },
  address: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 13,
    color: '#475569',
    marginBottom: 4,
  },
  statusBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusBadgeText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 12,
    color: '#0284C7',
  },
  timeRangeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeIcon: {
    marginLeft: 2,
    marginRight: 6,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeValue: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 13,
    color: '#475569',
  },
  timeSeperator: {
    fontFamily: 'Poppins_400Regular',
    marginHorizontal: 2,
  },
  border: {
    marginTop: 6,
    marginBottom: 12,
    marginHorizontal: 2,
    height: 1,
    backgroundColor: '#CBD5E1',
  },
  addressContainer: {
    display: 'flex',
    flexDirection: 'row',
    marginBottom: 4,
  },
  visitTypeContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  shortNameContainer: {
    borderRadius: 8,
    paddingVertical: 2,
    paddingHorizontal: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  shortNameText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Poppins_600SemiBold',
  },
});
