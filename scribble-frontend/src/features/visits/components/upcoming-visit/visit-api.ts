import { apiClient } from "src/api/apiClient";


export const getVisitList = ({ limit = 10, page = 1, status = "New",screen }) => {
  let queryString = `v1/limit=${limit}&page=${page}&status=${encodeURIComponent(status)}`;
    if (screen !== undefined && screen !== null && screen !== '') {
    queryString += `&screen=${encodeURIComponent(screen)}`;
  }
  return apiClient
    .get(`v1/visit?${queryString}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.message || "Failed to Load Visits";
    });
};
export const getPastVisit = ({ limit = 10, page = 1, clientId = 0 }) => {
  return apiClient
    .get(`v1/visit?limit=${limit}&page=${page}&clientId=${clientId}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Load Visits";
    });
};

export const visitDetails = () => {
  return apiClient
    .get("v1/user/clinician/visitDetails")
    .then((response) => response.data)
    .catch((error) => {
      throw error.response?.data?.message || "Failed to Load Visit Details";
    });
};

export const fetchFormListVisitWise = ({ limit = 10, page = 1, visitId = 0 }) => {
  return apiClient
    .get(`v1/visit/assessment?limit=${limit}&page=${page}&visitId=${visitId}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Load Forms";
    });
};
export const getDateWiseVisit = ({ limit = 10, page = 1, visitDate = "" }) => {
  return apiClient
    .get(`v1/visit?limit=${limit}&page=${page}&visitDate=${visitDate}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Load Visits";
    });
};
export const changeVisitStatus = ({ visitId, status }) => {
  return apiClient

    .put(`v1/visit/${visitId}`, { status: status })
    .then((response) => {
      return response.data
    })
    .catch((error) => {
      throw error.response?.data?.message || "Change Visit Status failed";
    });
};
