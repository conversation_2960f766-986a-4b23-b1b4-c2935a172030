import React, { useCallback, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Linking,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
// import {
//   MessageSquare,
//   MoreVertical,
//   MapPin,
//   CalendarDays,
//   Clock,
//   Mic,
//   Mic2Icon,
//   MicIcon,
//   MicOff,
// } from "lucide-react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useFonts } from "expo-font";
import theme from "src/theme";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { getVisitList } from "src/features/visits/components/upcoming-visit/visit-api";
import { FontAwesome } from '@expo/vector-icons';
import { dateConvert } from "@/src/utils/DateUtils";

import { useState, useEffect } from "react";
import { UPCOMING_CALENDAR_BLACK, UPCOMING_CALENDAR_WHITE, UPCOMING_CLOCK_BLACK, UPCOMING_CLOCK_WHITE } from "@/assets/images";
import { fs, ms } from "@/src/utils/ScreenUtils";

const { width } = Dimensions.get('window');
import { PixelRatio } from 'react-native';

const getFontScale = () => PixelRatio.getFontScale();
const getCardHeight = () => {
  const fontScale = getFontScale();
  const baseHeight = 150;
  // Increase height proportionally with font scale
  return Math.min(baseHeight * fontScale, 240); // Cap at 240
};
const AppointmentCard = ({ appointment, index }) => {
  const navigation = useNavigation();
  // console.log("index-----"+index)
  // console.log(JSON.stringify(appointment))
  if (index == 0) {
    appointment.isHighlighted = true
  } else {
    appointment.isHighlighted = false
  }

  const onClickMic=()=>{
    navigation.navigate("Recording",{visitId:appointment._id})
    // navigation.navigate('Assesment',{visitId:appointment._id});
  }
  const onOpenVisit = () => {
    // @ts-ignore
    // navigation.navigate("VisitDetails", {
    //   visit: appointment,
    // });
    navigation.navigate("VisitScreen",{  visit: appointment })
  };

  const onClickMap = () => {
    const latitude = 40.74958102694104;
    const longitude = -73.98537418664596;
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`;

    Linking.openURL(url);
  };

  
  return (
    <View style={styles.shadowWrapper}>
    <LinearGradient
      colors={
        appointment.isHighlighted
          ? ["#0079FE", "#0056D2"]
          : ["#FFFFFF", "#FFFFFF"]
      }
      style={[styles.card, !appointment.isHighlighted && styles.cardWhite]}
    >
      <TouchableOpacity style={styles.innerCard} onPress={onOpenVisit} data-testid={`appointment-card-${appointment._id}`}> 
        <Text style={{display: 'none'}} testID={`appointment-id-${appointment._id}`}>{appointment._id}</Text>
        <View style={styles.row}>
          <View style={styles.profileBox}>
            <Text style={styles.profileInitials} >
              {appointment?.clientFirstName[0] || "P"}
            </Text>
          </View>
          {/* <Image
            source={{ uri: appointment.image }}
            style={styles.profileImage}
          /> */}
          <View style={{ flex: 1 }}>
            <Text 
              style={[
                styles.doctorName,
                !appointment.isHighlighted && styles.darkText,
              ]}
            >
              {appointment.clientFirstName} {appointment.clientLastName}
            </Text>
          </View>
          {/*<TouchableOpacity>*/}
          {/*  <MoreVertical*/}
          {/*    size={24}*/}
          {/*    color={appointment.isHighlighted ? '#fff' : '#64748B'}*/}
          {/*  />*/}
          {/*</TouchableOpacity>*/}
          {/* <View style={styles.shortNameContainer}>
            <Text style={styles.shortNameText}>{appointment.shortName}</Text>
          </View> */}
        </View>
        <View
          style={[
            styles.border,
            appointment.isHighlighted && styles.borderWhite,
          ]}
        />
        <View>
        <View style={styles.rowIcon}>
          {/* <CalendarDays
            size={14}
            color={appointment.isHighlighted ? "#fff" : "#007AFF"}
          /> */}
          <Image style={{height:18,width:18}}
          source={appointment.isHighlighted? UPCOMING_CALENDAR_WHITE: UPCOMING_CALENDAR_BLACK}
          />
          <Text 
            style={[styles.date, !appointment.isHighlighted && styles.darkText]}
          >
            {dateConvert(appointment.visitDate,3)}
          </Text>
        </View>
        <View style={styles.rowIcon}>
          {/* <Clock
            size={16}
            color={appointment.isHighlighted ? "#fff" : "#272727"}
          /> */}
          <Image style={{height:18,width:18}}
          source={appointment.isHighlighted? UPCOMING_CLOCK_WHITE: UPCOMING_CLOCK_BLACK}
          />
          <Text 
            style={[styles.time, !appointment.isHighlighted && styles.darkText]}
          >
            {appointment.visitStartTime ? appointment.visitStartTime :"" }
          </Text>
        </View>
        <View style={styles.iconsRow}>
          {/* <TouchableOpacity style={styles.chatButton} onPress={onClickMap}>
            <MapPin size={28} fill={"#007AFF"} stroke={"#EFF6FF"} />
          </TouchableOpacity> */}
          {/* <TouchableOpacity style={styles.chatButton} onPress={onClickMic}>
            <FontAwesome name="microphone" size={20} color="#007AFF" />
          </TouchableOpacity> */}
        </View>
        </View>
      </TouchableOpacity>
    </LinearGradient>
    </View>
  );
};

export default function UpcomingVisit() {
  const [loading, setLoading] = useState(true);
  const navigation = useNavigation();

  const [upcomingVisit, setUpcomingVisit] = useState([]);

  const onClickOpenVisit = () => {
    // navigation.navigate("OpenVisits");
    navigation.navigate("AllVisits", { initialTab: "New" });

  };
  const fetchVisitList = () => {
    setLoading(true);
    getVisitList({ limit: 10, page: 1, status: "New",screen:"home" })
      .then((result) => {
        // console.log(result);
        setUpcomingVisit(result.data);
      })
      .catch((error) => {
        // console.log(error);
      }).finally(() => setLoading(false));;
  }
  useFocusEffect(
    useCallback(() => {
      fetchVisitList();
    }, [])
  );
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.title}>Upcoming Visits</Text>
        <TouchableOpacity onPress={onClickOpenVisit}>
          <Text style={styles.viewAll}>View All</Text>
        </TouchableOpacity>
      </View>
      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
      ) : upcomingVisit.length > 0 ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.scrollView}
        >
          {upcomingVisit.map((appointment, index) => (
            <AppointmentCard key={appointment._id} appointment={appointment} index={index} />
          ))}
        </ScrollView>
      ) : (
        <View style={styles.blankTextSection}>
          <Image
          source={require('assets/images/cenlender.png')}
          style={styles.noDataImage}
          />
        <Text style={styles.blankText}>No Upcoming Visits</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    //backgroundColor: theme.colors.background,
    paddingHorizontal: 20,
  },
  blankTextSection:{
    // padding:10,
    height:150,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: {
    width: 0,
    height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    marginBottom:16,
    justifyContent:'center',
    alignItems:'center',
    // alignSelf:'center',
    },
  blankText: {
    textAlign: "center",
    // marginTop: 10,
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color:'#272727'
  },
  iconsRow: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    gap: 12,
    // marginTop: 8,
    justifyContent: 'flex-end',
  },
  profileBox: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f26649",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  profileInitials: {
    fontFamily: "Poppins_600SemiBold",
    color: "#FFFFFF",
    fontSize: 14,
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: fs(18),
    color: "#251F38",
    fontFamily: "Poppins_500Medium",
  },
  viewAll: {
    fontSize: fs(14),
    color: "#000",
    fontFamily: "Poppins_400Regular",
  },
  scrollView: {
    flexDirection: "row",
    paddingBottom: 16,
  },
  card: {
    padding: 20,
    borderRadius: 16,
    marginRight: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
    width:"100%",
    height: "100%"
    
  },
  innerCard: {
    width: "100%",
    height: "100%",
  },
  cardWhite: {
    // borderWidth: 1,
    // borderColor: '#E5E7EB',
    backgroundColor: "#fff",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
    justifyContent:"center",
   // backgroundColor:"red"
    },
  profileImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 10,
    borderWidth: 2,
    borderColor: "#fff",
  },
  noDataImage: {
    width: 32,
    height: 32,
    marginBottom:10,
  },
  doctorName: {
    fontSize: fs(18),
    color: "#fff",
    fontFamily: "Poppins_400Regular",
  },
  specialization: {
    fontSize: 16,
    color: "#fff",
    fontFamily: "Poppins_400Regular",
  },
  darkText: {
    color: "#1E293B",
  },
  grayText: {
    color: "#64748B",
  },
  date: {
    fontSize: fs(14),
    color: "#FFF",
    fontFamily: "Poppins_400Regular",
  },
  time: {
    fontSize: fs(14),
    color: "#FFF",
    fontFamily: "Poppins_400Regular",
  },
  chatButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#EFF6FF",
  },
  chatButton1: {
    position: "absolute",
    bottom: 0,
    right: 40,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#EFF6FF",
  },
  border: {
    marginTop: 4,
    marginBottom: 7,
    height: 1,
    backgroundColor: "#CBD5E1",
  },
  borderWhite: {
    borderColor: "#FFF",
  },
  rowIcon: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginTop: 5,
  },
  shortNameContainer: {
    borderRadius: 8,
    paddingVertical: 2,
    paddingHorizontal: 6,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#f26649",
  },
  shortNameText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontFamily: "Poppins_600SemiBold",
  },
  shadowWrapper: {
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.3,
  shadowRadius: 6,
  borderRadius: 16,
  backgroundColor: 'transparent', 
  marginRight: 15,
  // width: 316,
  width:width*0.80,
  // height: 160,
    height: getCardHeight(), // Dynamic height based on font scale


}
});
