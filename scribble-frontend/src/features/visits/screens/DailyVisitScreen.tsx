import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Platform,
  ActivityIndicator,
} from 'react-native';
// import {
//   ChevronLeft,
//   ChevronRight,
//   CalendarDays,
//   Clock,
// } from 'lucide-react-native';
import theme from 'src/theme';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { getDateWiseVisit } from '../components/upcoming-visit/visit-api';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { Feather } from '@expo/vector-icons';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';


// ----- Date Helpers -----
function getStartOfWeek(date: Date): Date {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1);
  d.setDate(diff);
  return d;
}

function getDaysOfWeek(startOfWeek: Date): Date[] {
  return Array.from({ length: 7 }, (_, i) => {
    const d = new Date(startOfWeek);
    d.setDate(d.getDate() + i);
    return d;
  });
}

export default function DailyVisitsScreen() {
  const navigation = useNavigation();
  setPageTitle('My Visits');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState(
    getStartOfWeek(new Date()),
  );
  const [visits, setVisits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const isWeb = Platform.OS === 'web';
  const containerRef = useRef(null);
  const scrollTimeout = useRef(null);

  const daysInView = getDaysOfWeek(currentWeekStart);
  const monthYear = currentDate.toLocaleDateString('en-US', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });

  const handlePrevWeek = () => {
    const newStart = new Date(currentWeekStart);
    newStart.setDate(newStart.getDate() - 7);
    setCurrentWeekStart(newStart);
  };

  const handleNextWeek = () => {
    const newStart = new Date(currentWeekStart);
    newStart.setDate(newStart.getDate() + 7);
    setCurrentWeekStart(newStart);
  };

  const handleDayPress = (day: Date) => {
    setCurrentDate(day);
    setHasMore(true);
    setPage(1);
    setLoadingMore(false);
    fetchVisits(1, getFormattedDate(day), true);
    // Update the lastFetchedDate ref to prevent duplicate fetch on refocus
    lastFetchedDate.current = getFormattedDate(day);
  };
  
  const limit = 10;

  const fetchVisits = async (pageNumber = 1, visitDate: string, hasMoreData: boolean) => {
    if (loadingMore || !hasMoreData) return;

    try {
      if (pageNumber === 1) setLoading(true);
      else setLoadingMore(true);

      const result = await getDateWiseVisit({ limit: limit, page: pageNumber, visitDate: visitDate });
      if (result.data.length > 0) {
        setVisits((prev) => (pageNumber === 1 ? result.data : [...prev, ...result.data]));
        setPage(pageNumber + 1);
        if (result.data.length < limit || result.total_records_available <= limit * pageNumber) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
        if (pageNumber === 1) {
          setVisits([]);
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Use refs to track screen state
  const isInitialMount = useRef(true);
  const lastFetchedDate = useRef(null);

  useFocusEffect(
    useCallback(() => {
      // For the initial load, use the current date
      if (isInitialMount.current) {
        const today = new Date();
        setCurrentDate(today);
        setCurrentWeekStart(getStartOfWeek(today));
        isInitialMount.current = false;
        
        // Initial data fetch
        setHasMore(true);
        setPage(1);
        fetchVisits(1, getFormattedDate(today), true);
        lastFetchedDate.current = getFormattedDate(today);
      } else {
        // On screen refocus, only fetch if we haven't already fetched for this date
        const formattedCurrentDate = getFormattedDate(currentDate);
        if (lastFetchedDate.current !== formattedCurrentDate) {
          setHasMore(true);
          setPage(1);
          fetchVisits(1, formattedCurrentDate, true);
          lastFetchedDate.current = formattedCurrentDate;
        }
      }
    }, [currentDate])
  );

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      fetchVisits(page, getFormattedDate(currentDate), hasMore);
    }
  };
  
  const handleWebScroll = (event) => {
    if (scrollTimeout.current) clearTimeout(scrollTimeout.current);

    scrollTimeout.current = setTimeout(() => {
      if (event.target) {
        const { scrollTop, scrollHeight, clientHeight } = event.target;
        if (scrollHeight - scrollTop <= clientHeight + 20) {
          handleLoadMore();
        }
      }
    }, 100);
  };
  
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleWebScroll);
    }

    // Check for null before removing the event listener
    return () => {
      if (container) {
        container.removeEventListener('scroll', handleWebScroll);
      }
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [page, loadingMore, hasMore]);

  const getFormattedDate = (day: Date) => {
    const year = day.getFullYear();
    const month = (day.getMonth() + 1).toString().padStart(2, '0');
    const date = day.getDate().toString().padStart(2, '0');
    const currDate = month + "/" + date + "/" + year;
    return currDate;
  };
  
  const handleTodayPress = () => {
    const today = new Date();
    setCurrentDate(today);
    setCurrentWeekStart(getStartOfWeek(today));
    setHasMore(true);
    setPage(1);
    setLoadingMore(false);
    fetchVisits(1, getFormattedDate(today), true);
    // Update the lastFetchedDate ref to prevent duplicate fetch on refocus
    lastFetchedDate.current = getFormattedDate(today);
  };

  const onClickVisit = visit => {
    console.log("is this clicked101 ")
    navigation.navigate('VisitDetails', { visit });
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'Completed':
        return {
          backgroundColor: 'rgba(0, 121, 254, 0.12)',
          color: '#0079FE',
        };
      case 'In Progress':
        return {
          backgroundColor: 'rgba(249, 167, 4, 0.12)',
          color: '#F9A704',
        };
      case 'New':
        return {
          backgroundColor: 'rgba(68, 175, 199, 0.12);',
          color: '#44AFC7',
        };
      case 'Past Due':
        return {
          backgroundColor: 'rgba(242, 102, 73, 0.12)',
          color: '#F26649',
        };
      default:
        return { backgroundColor: '#ECF2F3', color: '#64748B' };
    }
  };
  
  function convertMinutes(min: number) {
    if (min > 60) {
      const hours = (min / 60).toFixed(1); // keeps one decimal place
      return `${hours} Hours`;
    } else {
      return `${min} Minutes`;
    }
  }
  
  const renderWebContent = () => {
    return (
      <div
        ref={containerRef}
        style={{
          overflowY: 'auto',
          height: '60vh',
          padding: '10px',
        }}
      >
        {visits.map((visit, index) => {
          const statusStyle = getStatusBadgeStyle(visit.status);

          return (
            <View key={visit._id} style={styles.visitContainer}>
              <View style={styles.timelineContainer}>
                <View
                  style={[
                    styles.timelineDot,
                    { backgroundColor: statusStyle.color },
                  ]}
                />
                {index < visits.length && <View style={styles.timelineLine} />}
              </View>

              <TouchableOpacity
                style={styles.visitCard}
                onPress={() => onClickVisit(visit)}
              >
                <View style={styles.rowIcon}>
                  {/* <Clock size={14} color={'#6B7280'} /> */}
                  <Feather
                    name="clock"
                    size={14}
                    color="#6B7280"
                  />
                  <View style={styles.shortNameContainer}>
                    <Text style={styles.shortNameText}>{visit.serviceCode}</Text>
                  </View>
                </View>
                <Text style={styles.patientName}>
                  {visit.clientFirstName} {visit.clientLastName}
                </Text>
                <View style={styles.visitFooter}>
                  <Text style={styles.duration}>
                    {convertMinutes(visit.episodeDuration)}
                  </Text>
                  <View
                    style={[
                      styles.statusBadge,
                      { backgroundColor: statusStyle.backgroundColor },
                    ]}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        { color: statusStyle.color },
                      ]}
                    >
                      {visit.status}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          );
        })}
        {loadingMore ? (
          <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10 }} />
        ) : null}
      </div>
    );
  };
  
  const renderNativeContent = () => {
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={handleLoadMore}
        scrollEventThrottle={16}
        contentContainerStyle={{ padding: 20 }}
      >
        {visits.map((visit, index) => {
          console.log("visit-->",visit)
          const statusStyle = getStatusBadgeStyle(visit.status);

          return (
            <View key={visit._id} style={styles.visitContainer}>
              <View style={styles.timelineContainer}>
                <View
                  style={[
                    styles.timelineDot,
                    { backgroundColor: statusStyle.color },
                  ]}
                />
                {index < visits.length - 1 && <View style={styles.timelineLine} />}
              </View>

              <TouchableOpacity
                style={styles.visitCard}
                onPress={() => onClickVisit(visit)}
              >
                <View style={styles.rowIcon}>
                  {/* <Clock size={14} color={'#6B7280'} /> */}
                  <Feather
                    name="clock"
                    size={14}
                    color="#6B7280"
                  />
                  {
                    visit.serviceCod != undefined || null ?
                     <View style={styles.shortNameContainer}>
                    <Text style={styles.shortNameText}>{visit.serviceCode}</Text>
                  </View> : null
                  }
                 
                </View>
                <Text style={styles.patientName}>
                  {visit.clientFirstName} {visit.clientLastName}
                </Text>
                {visit.visitType && (
                  <Text style={styles.visitType}>{visit.visitType}</Text>
                )}
                <View style={styles.visitFooter}>
                  <Text style={styles.duration}>
                    {convertMinutes(visit.episodeDuration)}
                  </Text>
                  {
                    visit.status != undefined || null ?
                  <View
                    style={[
                      styles.statusBadge,
                      { backgroundColor: statusStyle.backgroundColor },
                    ]}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        { color: statusStyle.color },
                      ]}
                    >
                      {visit.status}
                    </Text>
                  </View>: null

                  }
                 
                </View>
              </TouchableOpacity>
            </View>
          );
        })}
        {loadingMore ? (
          <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10 }} />
        ) : null}
      </ScrollView>
    );
  };
  
  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <View style={styles.header}>
        <View style={styles.monthRow}>
          <Text style={styles.monthYear}>{'My Visits'}</Text>
          <TouchableOpacity
            style={styles.todayButton}
            onPress={handleTodayPress}
          >
            {/* <CalendarDays size={18} color={'#fff'} /> */}
             <MaterialCommunityIcons
              name="calendar-month-outline"
              size={18}
              color="#fff"
            />
            <Text style={styles.todayButtonText}>Today</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.weekRow}>
          <TouchableOpacity onPress={handlePrevWeek}>
            {/* <ChevronLeft size={24} color="#fff" /> */}
            <Feather
              name="chevron-left"
              size={24}
              color="#fff"
            />
          </TouchableOpacity>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {daysInView.map((day, index) => {
              const isSelected =
                day.getDate() === currentDate.getDate() &&
                day.getMonth() === currentDate.getMonth() &&
                day.getFullYear() === currentDate.getFullYear();
              const dayName = day
                .toLocaleDateString('en-US', { weekday: 'short' })
                .toUpperCase();
              const dayNum = day.getDate();
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => handleDayPress(day)}
                  style={[
                    styles.dayButton,
                    isSelected && styles.selectedDayButton,
                  ]}
                >
                  <Text
                    style={[
                      styles.dayName,
                      isSelected && styles.selectedDayText,
                    ]}
                  >
                    {dayName}
                  </Text>
                  <Text
                    style={[
                      styles.dayNumber,
                      isSelected && styles.selectedDayText,
                    ]}
                  >
                    {dayNum}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>

          <TouchableOpacity onPress={handleNextWeek}>
            {/* <ChevronRight size={24} color="#fff" /> */}
            <Feather
              name="chevron-right"
              size={24}
              color="#fff"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.todayTitle}>{monthYear}</Text>
        {loading ? (
          <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
        ) : visits.length > 0 ? (
          isWeb ? renderWebContent() : renderNativeContent()
        ) : (
          <View style={styles.blankTextContainer}>
            <Text style={styles.blankText}>No visits available</Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#3B81F6',
    paddingTop:40
  },
  header: {
    paddingVertical: 16,
  },
  blankTextContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: '#fff', 

  },
  blankText: {
    textAlign: "center",
    marginTop: 20,
    fontSize: 20,
    fontFamily: 'Poppins_500Medium'
  },
  monthRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  monthYear: {
    fontSize: 20,
    fontFamily: 'Poppins_600Semibold',
    color: '#FFFFFF',
  },
  todayButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: theme.colors.secondary,
    gap: 4,
    borderWidth: 1,
    borderColor: '#fff',
  },
  todayButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
  },
  weekRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  dayButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  selectedDayButton: {
    backgroundColor: '#FFFFFF',
  },
  dayName: {
    fontSize: 12,
    color: '#C7D2FE',
    marginBottom: 4,
    fontFamily: 'Poppins_400Regular',
  },
  dayNumber: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Poppins_600Semibold',
  },
  selectedDayText: {
    color: theme.colors.secondary,
  },
  content: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
  },
  todayTitle: {
    fontSize: 16,
    marginBottom: 20,
    fontFamily: 'Poppins_600Semibold',
  },
  visitContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineContainer: {
    alignItems: 'center',
    width: 24,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#E2E8F0',
    marginTop: 4,
  },
  visitCard: {
    flex: 1,
    backgroundColor: 'rgb(239, 246, 255)',
    borderRadius: 12,
    padding: 16,
    marginLeft: 12,
  },
  timeLabel: {
    fontSize: 13,
    color: '#6B7280',
    fontFamily: 'Poppins_400Regular',
  },
  patientName: {
    fontSize: 14,
    color: '#1F2937',
    marginBottom: 4,
    fontFamily: 'Poppins_600Semibold',
  },
  visitType: {
    fontSize: 13,
    color: '#6B7280',
    marginBottom: 4,
    fontFamily: 'Poppins_400Regular',
  },
  visitFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  duration: {
    fontSize: 13,
    color: '#6B7280',
    fontFamily:'Poppins_400Regular'
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontFamily:'Poppins_500Medium'
  },
  rowIcon: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 5,
    marginBottom: 4,
  },
  shortNameContainer: {
    borderRadius: 10,
    paddingVertical: 2,
    paddingHorizontal: 6,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 120, 130, 0.12)',
    position: 'absolute',
    right: 0,
  },
  shortNameText: {
    color: '#FF7882',
    fontSize: 12,
    fontFamily: 'Poppins_400Regular',
  },
});