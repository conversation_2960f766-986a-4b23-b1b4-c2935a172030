import React from "react";
import { useNavigation, useRoute } from "@react-navigation/native";
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
// import { ChevronLeft, Search } from "lucide-react-native";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";

import theme from "src/theme";
import { screenHeight, screenWidth } from "src/utils/ScreenUtils";
import OpenVisits from "src/features/visits/screens/OpenVisits";

import { SafeAreaView } from "react-native-safe-area-context";
import { Feather } from "@expo/vector-icons";

const Tab = createMaterialTopTabNavigator();

const AllVisits = () => {
  const navigation = useNavigation();
  const route = useRoute()

  const visit = route.params?.initialTab;
  // const screen =route.params?.screen;
  // console.log("---tab clicked----:" + visit)
  const onBackClick = () => {
    // console.log("Back clicked");
    navigation.goBack();
  };
  const renderBackButton = () => {
    return (
      <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
        <Feather name="chevron-left" size={24} color={theme.colors.text} />
        {/* <ChevronLeft color={theme.colors.text} size={24} /> */}
      </TouchableOpacity>
    );
  };
  

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        {renderBackButton()}
        <View >
          <Text style={styles.headerText}>All Visits</Text>
        </View>
      </View>
      <View style={styles.tabContainer}>
        <Tab.Navigator initialRouteName={visit}
          screenOptions={({ route }) => ({
            tabBarStyle: styles.tabBar,
            tabBarLabelStyle: styles.tabLabel,
            tabBarLabel: ({ focused }) =>
              focused ? (
                <Text style={[styles.tabLabel, styles.tabLabelSelected]}>
                  {route.name}
                </Text>
              ) : (
                <Text style={styles.tabLabel}>{route.name}</Text>
              ),
            tabBarIndicatorStyle: styles.tabIndicator,
            lazy: true,
            tabBarScrollEnabled: true,
            tabBarItemStyle: styles.tab,
          })}
          
        >
          <Tab.Screen name="New" initialParams={{"type":"New"}} component={OpenVisits} />
          <Tab.Screen name="In Progress" initialParams={{"type":"To be reviewed"}} component={OpenVisits} />
          <Tab.Screen name="Past Due" initialParams={{"type":"Past Due"}} component={OpenVisits} />
          <Tab.Screen name="Missed" initialParams={{"type":"Missed"}} component={OpenVisits} />
          <Tab.Screen name="Completed" initialParams={{"type":"Completed"}} component={OpenVisits} />
        </Tab.Navigator>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    
    backgroundColor: "#FFFFFF",
    position: "relative",
    height: 65,
  },
  headerText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 20,
    color: '#000',
    paddingHorizontal: 10,
    textAlign: "center",
    
  },
  backButton: {
    alignItems: "center",
    justifyContent: "center",
    // marginRight: 14,
    //marginLeft:5,
    paddingLeft: 5,
    paddingRight:14
    
  },
  tab: {
    width: 'auto',
    minWidth: 100, // Minimum width for each tab
  },
  profileBox: {
    width: 34,
    height: 34,
    borderRadius: 17,
    backgroundColor: "#CBD5E1",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  profileInitials: {
    fontFamily: "Poppins_600SemiBold",
    color: "#FFFFFF",
    fontSize: 14,
  },
  patientName: {
    fontFamily: "Poppins_500Medium",
    color: "#1E293B",
    fontSize: 15,
    marginBottom: 2,
  },
  visitType: {
    fontFamily: "PlusJakartaSans_400Regular",
    fontSize: 13,
    color: "#64748B",
  },
  tabBar: {
    backgroundColor: "#fff",
  },
  tabLabel: {
    color: theme.colors.ternaryText,
    fontFamily: "Poppins_400Regular",
    fontSize: 13,
    textAlign:'center'
  },
  tabLabelSelected: {
    color: "#000",
  },
  tabIndicator: {
    backgroundColor: "#000",
    height: 3,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  sectionContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
  },
  detailItem: {
    fontSize: 16,
    marginBottom: 5,
  },
  label: {
    fontWeight: "bold",
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F3F4F6",
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "bold",
  },
  cardSubtitle: {
    fontSize: 14,
    color: "#666",
  },
  status: {
    color: "#4F46E5",
    fontWeight: "bold",
    marginTop: 5,
  },
  tabContainer: {
    display: "flex",
    flex: 1,
    height: screenHeight - 65,
  },
});

export default AllVisits;
