import React, { useCallback, useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  Switch,
  SafeAreaView,
  ScrollView,
  Modal,
  Dimensions,
  Platform,
  Linking,
} from 'react-native';

import {
  AntDesign,
  Feather,
  MaterialCommunityIcons,
  MaterialIcons,
  Octicons,
} from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');
import Constants from 'expo-constants';
import theme from 'src/theme';
// import { LucideIcon } from 'lucide-react-native';
import { logout } from 'src/redux/slices/authSlice';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import AsyncStorage from "@react-native-async-storage/async-storage";
import { hideRecorder, resetRecorder } from '@/src/redux/slices/audioRecorderSlice';
import { DynamicMessageModalWithConfirm, MessageModal, MessageModalWithConfirm } from '@/src/utils/MessageModal';
import { RootState } from '@/src/redux/store';
import { Ionicons } from '@expo/vector-icons';
import { GlobalFunctions } from '@/src/utils/GlobalFunction';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { StatusBar } from 'expo-status-bar';
import { deleteAccount, logOut } from '../profile-api';
import FullScreenLoader from '@/src/utils/MobileLoader';
import { useLogoutUser } from '@/src/hooks/useLogoutUser';
import { getSecureItem } from '@/src/utils/cryptoHelper';
import { PRIVACY_POLICY_URL, TERMS_OF_SERVCIE } from '@/src/utils/externalLinks';
import { DELETE_ACCOUNT_MESSAGE, DELETE_ACCOUNT_TITLE, LOGOUT_SUCCESS,LOGOUT_TITLE,LOGOUT_MESSAGE } from '@/src/components/messages';

const FeatherIcon = (props) => <Feather  {...props} />;
const MaterialCommunityIcon = (props) => <MaterialCommunityIcons  {...props} />;
const AntDesignIcon = (props) => <AntDesign  {...props} />;
const MaterialIcon = (props) => <MaterialIcons  {...props} />;
const OcticonIcon = (props) => <Octicons  {...props} />;

const ProfileScreen = () => {
  const dispatch = useDispatch();
  const recorderState = useSelector((state: RootState) => state.audioRecorder);
  const navigation = useNavigation();
  const [name, setName] = useState<any>('')
  const [staffId, setStaffId] = useState('')
  const [firstName, setFirstName] = useState<any>('')
  const [lastName, setLastName] = useState<any>('')

  const [showMessageModal, setShowMessageModal] = useState(false)
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false)
    const [showLogoutModal, setShowLogoutModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success");
  const [isOpenMessageModal, setIsOpenMessageModal] = useState(false)

  const { logOutUser } = useLogoutUser();
  // const deleteMessage = "Are you sure you’d like to delete your account?"

  const [isLoading, setIsLoading] = useState(false)
  setPageTitle('Settings');

  const onLogout = async () => {
    let success = await logOutUser();
    console.log("Logout api status :", success)
    if (success) {
      //setIsOpenMessageModal(true)
      // setShowMessageModal(true)
      // setMessage("Something Went Wrong")
      // setMessageType("error")
      if (Platform.OS === 'web') {
        window.location.href = '/LoginTenant';
      } else {
        navigation.replace("LoginTenant")
      }
    } else {
      //   if (Platform.OS === 'web') {
      //     window.location.href = '/LoginTenant';
      //   } else {
      //     navigation.replace("LoginTenant")
      //   }
      // }
    }

  };
  const openLogOutModal = async () => {
    setShowLogoutModal(false)
    const recordingURI = recorderState.recordingURI;
    const isFloatingRecorderRunning = recorderState.showPauseResume;
    if (isFloatingRecorderRunning || recordingURI != null) {
      if (GlobalFunctions.closeRecording) {
        // Call the function directly
        await GlobalFunctions.closeRecording();
        onLogout();
      }
    } else {
      onLogout();
    }
  }
  const openDeleteAccountModal = () => {
    setShowDeleteAccountModal(true)
  }

    const openLogOutModalConfirm = () => {
    setShowLogoutModal(true)
  }
  const fetchUserDetails = async () => {
    const firstname = await getSecureItem("firstName");
    const lastname = await getSecureItem("lastName");
    const staffId = await getSecureItem("staffId");
    setFirstName(`${firstname || ''}`)
    setLastName(`${lastname || ''}`)
    setName(`${firstname || ''} ${lastname || ''}`)
    setStaffId(`${staffId || ''}`)
  }
  useFocusEffect(
    useCallback(() => {
      fetchUserDetails()
    }, [])
  )

  const MenuItem = ({ Icon, title, onPress, name }) => (
    <TouchableOpacity style={styles.menuItem} onPress={onPress}>
      <View style={styles.menuItemLeft}>
        <View style={styles.iconContainer}>
          <Icon size={20} name={name} color={theme.colors.secondary} />
        </View>
        <View style={styles.menuItemTextContainer}>
          <Text style={styles.menuItemTitle}>{title}</Text>
        </View>
      </View>
      <View style={styles.menuItemRight}>
        <Feather name="chevron-right" size={20} color="#94A3B8" />
      </View>
    </TouchableOpacity>
  );


  const renderLastSync = () => {
    return (
      <View style={styles.lastSync}>
        {/* <CloudDownload size={20} color="#A16207" /> */}
        <Text style={styles.lastSyncText}>
          {'Last synced on February 10, 2025, at 12:00 AM.'}
        </Text>
      </View>
    );
  };
  const onModalCancel = () => {
    setShowMessageModal(false)
  };
  const onConfirmDelete = async () => {
    setShowDeleteAccountModal(false)
    const email = await getSecureItem("email");
    if (email && email !== "") {
      setIsLoading(true)
      deleteAccount({ "email": email })
        .then(async (result: any) => {
          if (result.status == "ok") {
            //openLogOutModal()
            await logOutUser();
            if (Platform.OS === 'web') {
              window.location.href = '/LoginTenant';
            } else {
              navigation.replace("LoginTenant")
            }
          } else {
            setShowMessageModal(true)
            setMessage(result.errorMessage)
            setMessageType("error")
          }
        })
        .catch((error) => {
          console.log(error);
        }).finally(() => setIsLoading(false));
    } else {
      setShowMessageModal(true)
      setMessage("Your email id is not matched .Please contact with Gooodly Team")
      setMessageType("error")
    }
  }
  const openExternalUrl = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.log("Can't open url ")
      }
    } catch (error) {
      console.error("Error : Opening Url :", error)
    }
  }
  // const onCancelSuccessModal = () => {
  //   setIsOpenMessageModal(false)
  //   if (Platform.OS === 'web') {
  //     window.location.href = '/LoginTenant';
  //   } else {
  //     navigation.replace("LoginTenant")
  //   }
  // }
  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      {/* <RecordDeleteModal visible={showMessageModal} onCancel={onModalCancel} onConfirm={onLogout} /> */}
      <FullScreenLoader visible={isLoading} />
      <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} />
      {/* <DynamicMessageModalWithConfirm
        visible={isOpenMessageModal}
        onCancel={onCancelSuccessModal}
        message={LOGOUT_SUCCESS}
        iconComponent={<Ionicons name="checkmark-circle" size={35} color="#1D75F5" />} cancelButtonText='OK' /> */}

      <DynamicMessageModalWithConfirm
        visible={showDeleteAccountModal}
        onCancel={() => setShowDeleteAccountModal(false)}
        message={DELETE_ACCOUNT_MESSAGE}
        onConfirm={onConfirmDelete}
        title={DELETE_ACCOUNT_TITLE}
        iconComponent={<Ionicons name="close-circle" size={48} color="#1D75F5" />} />

        <DynamicMessageModalWithConfirm
        visible={showLogoutModal}
        onCancel={() => setShowLogoutModal(false)}
        message={LOGOUT_MESSAGE}
        onConfirm={openLogOutModal}
        title={LOGOUT_TITLE}
        iconComponent={<Ionicons name="close-circle" size={48} color="#1D75F5" />} />

      <View style={styles.header}>
        <View style={styles.profileContainer}>
          <View style={styles.profileBox}>
            <Text allowFontScaling={false} style={styles.profileInitials}>
              {firstName != "" && firstName[0]}{lastName != "" && lastName[0]}
            </Text>
          </View>
          {/* <Image
            source={{
              uri: 'https://randomuser.me/api/portraits/women/52.jpg',
            }}
            style={styles.profileImage}
          /> */}
          <View style={styles.profileInfo}>
            <Text allowFontScaling={false} style={styles.profileName}>{name}</Text>
            <Text allowFontScaling={false} style={styles.profileHandle}>Clinician - {staffId}</Text>
          </View>
        </View>
      </View>
      {/*{renderLastSync()}*/}

      <ScrollView>
        <View style={styles.section}>
          {/* <MenuItem Icon={User} title="My Account" onPress={() => { navigation.navigate("MyAccount") }} /> */}
          <MenuItem Icon={FeatherIcon} name="user" title="My Account" onPress={() => navigation.navigate('MyAccount')} />
          <MenuItem Icon={MaterialIcon} name="password" title="Change Password" onPress={() => navigation.navigate('ResetPassword')} />
          <MenuItem Icon={MaterialCommunityIcon} name="shield-outline" title="Privacy Policy" onPress={() => openExternalUrl(PRIVACY_POLICY_URL)} />
          <MenuItem Icon={OcticonIcon} name="code-of-conduct" title="Terms & Conditions" onPress={() => openExternalUrl(TERMS_OF_SERVCIE)} />
          <MenuItem Icon={AntDesignIcon} name="deleteuser" title="Delete Account" onPress={openDeleteAccountModal} />
          {/* <MenuItem Icon={FeatherIcon} name="log-out" title="Log out" onPress={openLogOutModalConfirm} /> */}

          <MenuItem Icon={FeatherIcon} name="log-out" title="Log out" onPress={openLogOutModal} />


          {/* <MenuItem Icon={Shield} title="Change Password" onPress={() => { navigation.navigate("ResetPassword"); }} />
          <MenuItem Icon={LifeBuoy} title="Help & Support" onPress={() => { }} />
          <MenuItem
            Icon={MessageSquareText}
            title="Feedback"
            onPress={() => { }}
          />
          <MenuItem Icon={LogOut} title="Log out" onPress={openLogOutModal} /> */}
        </View>
      </ScrollView>
    </View>
  );
};
const modalStyles = StyleSheet.create({
  icon: {
    width: 60,
    height: 60,
    backgroundColor: '#EFF6FF',
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.95,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 25,
  },
  title: {
    color: '#000',
    textAlign: 'center',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 24,
    fontStyle: 'normal',
  },
  message: {
    color: '#7C7887',
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: '400',
    marginTop: 10,
    marginBottom: 15
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: theme.colors.buttonColor,
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.buttonColor,
    paddingVertical: 10,
    borderRadius: 24,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'Poppins_500Medium',
  },
})
const styles = StyleSheet.create({
  profileBox: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#CBD5E1",
    justifyContent: "center",
    alignItems: "center",
    // marginRight: 6,
  },
  profileInitials: {
    fontFamily: "Poppins_600SemiBold",
    color: "#FFFFFF",
    fontSize: 20,
  },
  container: {
    flex: 1,
    // paddingTop: Constants.statusBarHeight,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#3B81F6',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    paddingTop: Constants.statusBarHeight + 20,
    paddingBottom: 30
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFF',
    borderWidth: 2,
    borderColor: '#fff',
  },
  profileInfo: {
    marginLeft: 12,
  },
  profileName: {
    fontSize: 17,
    fontFamily: 'Poppins_600Semibold',
    color: '#FFF',
  },
  profileHandle: {
    fontSize: 14,
    color: '#fff',
    fontFamily: 'Poppins_500Medium',
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    marginHorizontal: 20,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    marginBottom: 16,
    fontFamily: 'Poppins_600Semibold',
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#EFF6FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuItemTextContainer: {
    marginLeft: 12,
  },
  menuItemTitle: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 2,
    fontFamily: 'Poppins_400Regular',
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  warningIcon: {
    marginRight: 8,
  },
  lastSync: {
    marginTop: 16,
    height: 44,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
    backgroundColor: '#FEFCE8',
    borderColor: '#A16207',
    borderWidth: 1,
    marginHorizontal: 16,
    borderRadius: 8,
  },
  lastSyncText: {
    fontSize: 12,
    color: '#A16207',
    fontFamily: 'Poppins_400Regular',
  },
});

export default ProfileScreen;
