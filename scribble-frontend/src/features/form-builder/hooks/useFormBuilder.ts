import { useState } from 'react';
import {
  TemplateType,
  FormLayoutComponentsType,
  FormLayoutComponentChildrenType,
  FormLayoutComponentContainerType,
  FormLayoutContainerType,
  FormLayoutItemType
} from 'src/features/form-builder/types/FormTemplateTypes';
import dayjs from 'dayjs';
import { Alert, Modal } from 'react-native';
import { generateID } from 'src/utils/GeneralUtils';
import {
  FormItemTypes,
  FormPublishStatus,
} from 'src/features/form-builder/utils/FormBuilderUtils';

interface useFormBuilderProps {
  template: TemplateType;
}


const useFormBuilder = (props: useFormBuilderProps) => {
  const [selectedTemplate, setSelectedTemplate] = useState<null | TemplateType>(
    props.template,
  );

  

  // Convert old format to new format if needed
  const initialFormLayoutComponents = props.template.formLayoutComponents.map(component => {
    if ('children' in component && !('items' in component)) {
      const items: FormLayoutItemType[] = [];

      // Add regular controls from children array
      if (component.children) {
        items.push(...component.children.map(child => ({
          ...child,
          isContainer: false,
          level: 0 // Base level
        })));
      }

      // Add subcontainers as container items
      if (component.subContainers) {
        items.push(...component.subContainers.map(sc => {
          // Convert subcontainer to the new container format
          const containerItem: FormLayoutContainerType = {
            id: sc.id,
            controlName: sc.controlName,
            displayText: sc.displayText,
            itemType: sc.itemType,
            heading: sc.heading,
            subHeading: sc.subHeading,
            containerId: sc.containerId,
            isContainer: true,
            level: 1, // First level of nesting
            items: sc.children.map(child => ({
              ...child,
              isContainer: false,
              level: 1
            })),
            icon: sc.icon
          };

          return containerItem;
        }));
      }

      return {
        container: component.container,
        items
      };
    }

    // Already in new format
    return component;
  });

  const [formLayoutComponents, setFormLayoutComponents] = useState<
    FormLayoutComponentsType[]
  >(initialFormLayoutComponents);

  const [selectedControl, setSelectedControl] = useState<
    | undefined
    | FormLayoutComponentContainerType
    | FormLayoutComponentChildrenType
    | FormLayoutContainerType
  >(props.template.formLayoutComponents[0]?.container);

  // Helper function to find a container by ID at any nesting level
  const findContainerById = (
    items: FormLayoutItemType[],
    containerId: string
  ): { container: FormLayoutContainerType | null, parentItems: FormLayoutItemType[], index: number } => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (item.isContainer && item.id === containerId) {
        return {
          container: item as FormLayoutContainerType,
          parentItems: items,
          index: i
        };
      }

      // Recursively search in nested containers
      if (item.isContainer && (item as FormLayoutContainerType).items) {
        const found = findContainerById((item as FormLayoutContainerType).items, containerId);
        if (found.container) {
          return found;
        }
      }
    }

    return { container: null, parentItems: [], index: -1 };
  };

  // Handles a Container or item added to the form builder
  const handleItemAdded = (
    item: FormLayoutComponentChildrenType | FormLayoutContainerType | FormLayoutComponentContainerType,
    containerId: string,
    level: number,
  ) => {
    setFormLayoutComponents(prevState => {
      let newState = [...prevState]; // Always derive state from the previous state

      // Adding a main container
      if (item.itemType === FormItemTypes.CONTAINER && !containerId) {
        const newContainer = {
          ...(item as FormLayoutComponentContainerType),
          id: generateID(),
          // isContainer: true,
        };
        newState.push({
          container: newContainer,
          items: [], // Initialize with empty items array
        });
        setSelectedControl(newContainer);
        return newState;
      }

      // For everything else, we need a valid containerId
      if (!containerId) {
        Alert.alert('Error', 'Container ID is required to add an item.');
        return prevState;
      }

      // Find the main container
      const formContainerIndex = newState.findIndex(
        f => f.container.id === containerId
      );

      // If we're adding directly to a main container
      if (formContainerIndex !== -1) {
        const formContainer = { ...newState[formContainerIndex] };

        if (item.itemType === FormItemTypes.SUBCONTAINER) {
          // Create a new container with proper nesting level
          const newContainer: FormLayoutContainerType = {
            id: generateID(),
            controlName: item.controlName,
            displayText: item.displayText,
            itemType: item.itemType,
            heading: item.heading || 'New Section',
            subHeading: item.subHeading || 'Section Description',
            containerId: containerId,
            isContainer: true,
            level,
            items: [],
            icon: item.icon,
          };

          // Add the container to the items array
          if (!formContainer.items) {
            formContainer.items = [];
          }

          formContainer.items.push(newContainer);
          newState[formContainerIndex] = formContainer;
          setSelectedControl(newContainer);
        } else if (item.itemType === FormItemTypes.CONTROL) {
          // Add a regular control to the main container
          const newControl: FormLayoutComponentChildrenType = {
            ...(item as FormLayoutComponentChildrenType),
            id: generateID(),
            containerId: containerId,
            isContainer: false,
            level,
          };

          if ((item as FormLayoutComponentChildrenType).items) {
            newControl.items = JSON.parse(
              JSON.stringify((item as FormLayoutComponentChildrenType).items),
            );
          }

          // Add to main container's items array
          if (!formContainer.items) {
            formContainer.items = [];
          }

          formContainer.items.push(newControl);
          newState[formContainerIndex] = formContainer;
          setSelectedControl(newControl);
        }

        return newState;
      }

      // We're adding to a nested container at some level
      // Find the container at any level of nesting
      for (let i = 0; i < newState.length; i++) {
        const mainContainer = { ...newState[i] };

        if (!mainContainer.items) continue;

        // Look for the target container in the nested structure
        const result = findContainerById(mainContainer.items, containerId);

        if (result.container) {
          const targetContainer = { ...result.container };

          if (item.itemType === FormItemTypes.SUBCONTAINER) {
            // Create a new subcontainer
            const newContainer: FormLayoutContainerType = {
              id: generateID(),
              controlName: item.controlName,
              displayText: item.displayText,
              itemType: item.itemType,
              heading: item.heading || 'New Section',
              subHeading: item.subHeading || 'Section Description',
              containerId: containerId,
              isContainer: true,
              level: targetContainer.level + 1, // Increment nesting level
              items: [],
              icon: item.icon,
            };

            // Add to the target container's items
            if (!targetContainer.items) {
              targetContainer.items = [];
            }

            targetContainer.items.push(newContainer);
            setSelectedControl(newContainer);
          } else if (item.itemType === FormItemTypes.CONTROL) {
            // Add a regular control
            const newControl: FormLayoutComponentChildrenType = {
              ...(item as FormLayoutComponentChildrenType),
              id: generateID(),
              containerId: containerId,
              isContainer: false,
              level: targetContainer.level,
            };

            if ((item as FormLayoutComponentChildrenType).items) {
              newControl.items = JSON.parse(
                JSON.stringify((item as FormLayoutComponentChildrenType).items),
              );
            }

            // Add to target container's items
            if (!targetContainer.items) {
              targetContainer.items = [];
            }

            targetContainer.items.push(newControl);
            setSelectedControl(newControl);
          }

          // Update the container in the parent items array
          result.parentItems[result.index] = targetContainer;

          // Update the main container
          newState[i] = mainContainer;
          return newState;
        }
      }

      // If we got here, we couldn't find the container
      Alert.alert('Error', 'Target container not found.');
      return prevState;
    });
  };
  // This excerpt includes the fixed delete functions from useFormBuilder.ts

  // Delete a container from the layout (works at any level)
  const deleteContainer = (containerId: string, parentContainerId?: string) => {
    if (!containerId) {
      console.error('No containerId provided to deleteContainer');
      return;
    }

    console.log(`deleteContainer called with container: ${containerId}, parent: ${parentContainerId || 'none'}`);

    // If it has a parent, we should be using deleteSubContainer instead
    if (parentContainerId) {
      return deleteSubContainer(containerId, parentContainerId);
    }

    setFormLayoutComponents(prevState => {
      let newState = [...prevState];

      // If it's a main container (doesn't have a parent)
      newState = newState.filter(comp => comp.container.id !== containerId);
      return newState;
    });

    // Update selected control if needed
    setSelectedControl(prev => {
      if (!prev) return prev;

      // If selected control is the container being deleted
      if (prev.id === containerId) return undefined;

      // If selected control is in the container being deleted
      if ((prev as FormLayoutComponentChildrenType).containerId === containerId)
        return undefined;

      return prev;
    });
  };

  // Delete a subcontainer - this is for containers nested inside other containers
  const deleteSubContainer = (containerId: string, parentContainerId: string) => {
    if (!containerId || !parentContainerId) {
      Alert.alert('Error', 'Invalid container IDs.');
      return;
    }

    console.log(`Attempting to delete subcontainer: ${containerId} from parent: ${parentContainerId}`);

    setFormLayoutComponents(prevState => {
      let newState = JSON.parse(JSON.stringify(prevState));

      // Case 1: If parent is a main container
      const mainContainerIndex = newState.findIndex(comp => comp.container.id === parentContainerId);
      if (mainContainerIndex !== -1) {
        console.log('Found parent as main container at index:', mainContainerIndex);

        // Remove the subcontainer from parent's items array
        newState[mainContainerIndex].items = newState[mainContainerIndex].items.filter(
          item => !(item.isContainer && item.id === containerId)
        );

        console.log('Items after deletion:', newState[mainContainerIndex].items.length);
        return newState;
      }

      // Case 2: Parent is a nested container - need recursive search
      const findAndRemoveSubcontainer = (items) => {
        if (!items) return items;

        // Look for the parent container
        for (let i = 0; i < items.length; i++) {
          const item = items[i];

          if (item.isContainer) {
            // Check if this is the parent container we're looking for
            if (item.id === parentContainerId) {
              console.log('Found parent container in nested structure:', item.id);

              // Remove the subcontainer from this parent's items
              item.items = item.items.filter(subItem =>
                !(subItem.isContainer && subItem.id === containerId)
              );

              console.log('Parent container items after deletion:', item.items.length);
              return true; // Container found and removed
            }

            // Otherwise recursively search this container's items
            if (item.items && item.items.length > 0) {
              const found = findAndRemoveSubcontainer(item.items);
              if (found) return true;
            }
          }
        }

        return false; // Not found in this branch
      };

      // Process all main containers
      for (let i = 0; i < newState.length; i++) {
        if (newState[i].items) {
          const found = findAndRemoveSubcontainer(newState[i].items);
          if (found) break;
        }
      }

      return newState;
    });

    // Clear the selection if the deleted container was selected
    if (selectedControl?.id === containerId) {
      selectControl(undefined);
    }
  };

  // Delete a control from the layout
  const deleteControl = (controlId: string, containerId: string) => {
    setFormLayoutComponents(prevState => {
      let newState = [...prevState];

      // First check if it's in a main container
      const mainContainerIndex = newState.findIndex(
        comp => comp.container.id === containerId
      );

      if (mainContainerIndex !== -1) {
        const mainContainer = { ...newState[mainContainerIndex] };

        if (mainContainer.items) {
          mainContainer.items = mainContainer.items.filter(
            item => item.id !== controlId
          );

          newState[mainContainerIndex] = mainContainer;
          return newState;
        }
      }

      // Check in nested containers
      for (let i = 0; i < newState.length; i++) {
        const mainContainer = { ...newState[i] };

        if (!mainContainer.items) continue;

        // Look for the container in the item tree
        const result = findContainerById(mainContainer.items, containerId);

        if (result.container) {
          const targetContainer = { ...result.container };

          // Remove the control from the container's items
          if (targetContainer.items) {
            targetContainer.items = targetContainer.items.filter(
              item => item.id !== controlId
            );
          }

          // Update the container
          result.parentItems[result.index] = targetContainer;

          // Update the main container
          newState[i] = mainContainer;
          break;
        }
      }

      return newState;
    });

    // Update selected control if needed
    setSelectedControl(prev =>
      prev && prev.id === controlId ? undefined : prev
    );
  };

  // Select a control on click
  const selectControl = (
    item:
      | FormLayoutComponentChildrenType
      | FormLayoutComponentContainerType
      | FormLayoutContainerType
      | undefined,
  ) => {
    setSelectedControl(item);
  };

  // Edit properties of a control or container
  const editProperties = (item: any) => {

    setFormLayoutComponents(prevState => {
      let newState = [...prevState];

      // If it's a main container
      if (item.itemType === FormItemTypes.CONTAINER && !item.containerId) {
        const mainContainerIndex = newState.findIndex(
          comp => comp.container.id === item.id
        );

        if (mainContainerIndex !== -1) {
          const mainContainer = { ...newState[mainContainerIndex] };
          mainContainer.container = {
            ...mainContainer.container,
            heading: item.heading,
            subHeading: item.subHeading,
          };

          newState[mainContainerIndex] = mainContainer;
          return newState;
        }
      }
      // Find the parent container
      const containerId = item.containerId;

      if (!containerId) {
        // This should not happen for non-main containers
        return prevState;
      }

      // Check if it's a direct child of a main container
      // const mainContainerIndex = newState.findIndex(
      //   comp => comp.container.id === containerId
      // );

      const mainContainerIndex = newState.findIndex(
        comp => {
          console.log(comp.container.id,containerId)
          return comp.container.id === containerId
        }
      );

      console.log("mainContainerIndex:"+mainContainerIndex)
      if (mainContainerIndex !== -1) {
        const mainContainer = { ...newState[mainContainerIndex] };

        if (mainContainer.items) {
          const itemIndex = mainContainer.items.findIndex(
            i => i.id === item.id
          );

          if (itemIndex !== -1) {
            mainContainer.items[itemIndex] = { ...item };
            newState[mainContainerIndex] = mainContainer;
            return newState;
          }
        }
      }

      // Look in nested containers
      for (let i = 0; i < newState.length; i++) {
        const mainContainer = { ...newState[i] };

        if (!mainContainer.items) continue;

        // Look for the parent container
        const result = findContainerById(mainContainer.items, containerId);

        if (result.container) {
          const parentContainer = { ...result.container };

          if (parentContainer.items) {
            const itemIndex = parentContainer.items.findIndex(
              i => i.id === item.id
            );

            if (itemIndex !== -1) {
              parentContainer.items[itemIndex] = { ...item };

              // Update the parent container
              result.parentItems[result.index] = parentContainer;

              // Update the main container
              newState[i] = mainContainer;
              return newState;
            }
          }
        }
      }

      return prevState;
    });
  };

  // Aliases for backwards compatibility
  const editControlProperties = editProperties;
  const editContainerProperties = editProperties;

  // Move a control or container
  const moveControl = (
    item: FormLayoutItemType,
    dragIndex: number,
    hoverIndex: number,
    containerId: string,
  ) => {
    if (!item || !containerId) {
      return;
    }

    setFormLayoutComponents(prevState => {
      let newState = [...prevState];

      // Check if it's in a main container
      const mainContainerIndex = newState.findIndex(
        comp => comp.container.id === containerId
      );

      if (mainContainerIndex !== -1) {
        const mainContainer = { ...newState[mainContainerIndex] };

        if (mainContainer.items && mainContainer.items.length > 0) {
          // Remove the item from its current position
          const [movedItem] = mainContainer.items.splice(dragIndex, 1);

          // Insert it at the new position
          mainContainer.items.splice(hoverIndex, 0, movedItem);

          newState[mainContainerIndex] = mainContainer;
          return newState;
        }
      }
      // Check in nested containers
      for (let i = 0; i < newState.length; i++) {
        const mainContainer = { ...newState[i] };

        if (!mainContainer.items) continue;

        // Find the container at any nesting level
        const result = findContainerById(mainContainer.items, containerId);

        if (result.container) {
          const targetContainer = { ...result.container };

          if (targetContainer.items && targetContainer.items.length > 0) {
            // Remove the item from its current position
            const [movedItem] = targetContainer.items.splice(dragIndex, 1);

            // Insert it at the new position
            targetContainer.items.splice(hoverIndex, 0, movedItem);

            // Update the container
            result.parentItems[result.index] = targetContainer;

            // Update the main container
            newState[i] = mainContainer;
            return newState;
          }
        }
      }

      return prevState;
    });
  };
  const moveContainer = (containerIndex:number, targetIndex:number) => {
    if (containerIndex < 0 || targetIndex < 0 || 
        containerIndex >= formLayoutComponents.length || 
        targetIndex >= formLayoutComponents.length) {
      return; // Invalid indices
    }
    
    setFormLayoutComponents(prevState => {
      const newState = [...prevState];
      
      // Remove the container from its current position
      const [movedContainer] = newState.splice(containerIndex, 1);
      
      // Insert it at the new position
      newState.splice(targetIndex, 0, movedContainer);
      
      return newState;
    });
  };
  // Move a control from the properties panel
  const moveControlFromSide = (
    item: FormLayoutComponentChildrenType,
    { containerId, position }: FormLayoutComponentChildrenType,
  ) => {
    if (!item || !containerId) {
      Alert.alert('Error', 'Invalid item or container ID.');
      return;
    }

    setFormLayoutComponents(prevState => {
      let newState = JSON.parse(JSON.stringify(prevState));

      // First, remove the item from its current location
      let itemFound = false;
      let itemToMove: FormLayoutComponentChildrenType | null = null;

      // Check in all containers at all levels
      for (let i = 0; i < newState.length; i++) {
        const mainContainer = newState[i];

        if (!mainContainer.items) continue;

        // Function to recursively search and remove the item
        const removeItemRecursively = (items: FormLayoutItemType[]): FormLayoutItemType[] => {
          const newItems = [];

          for (const currItem of items) {
            // Skip the item we're looking for
            if (currItem.id === item.id) {
              itemToMove = { ...currItem } as FormLayoutComponentChildrenType;
              itemFound = true;
              continue;
            }

            // If it's a container, search its items recursively
            if (currItem.isContainer) {
              const containerItem = { ...currItem } as FormLayoutContainerType;

              if (containerItem.items) {
                containerItem.items = removeItemRecursively(containerItem.items);
              }

              newItems.push(containerItem);
            } else {
              newItems.push(currItem);
            }
          }

          return newItems;
        };

        // Search and remove from this container
        mainContainer.items = removeItemRecursively(mainContainer.items);

        if (itemFound) break;
      }

      if (!itemFound || !itemToMove) {
        Alert.alert('Error', 'Item not found.');
        return prevState;
      }

      // Now add the item to its new location

      // Update the item's container ID
      itemToMove.containerId = containerId;

      // First check if it's going to a main container
      const targetMainIndex = newState.findIndex(
        comp => comp.container.id === containerId
      );

      if (targetMainIndex !== -1) {
        const targetMain = newState[targetMainIndex];

        if (!targetMain.items) targetMain.items = [];

        if (position !== undefined) {
          targetMain.items.splice(position, 0, itemToMove);
        } else {
          targetMain.items.push(itemToMove);
        }

        return newState;
      }

      // It's going to a nested container
      for (let i = 0; i < newState.length; i++) {
        const mainContainer = newState[i];

        if (!mainContainer.items) continue;

        // Look for the target container
        const result = findContainerById(mainContainer.items, containerId);

        if (result.container) {
          const targetContainer = { ...result.container };

          if (!targetContainer.items) targetContainer.items = [];

          // Add the item to the target position or end
          if (position !== undefined) {
            targetContainer.items.splice(position, 0, itemToMove);
          } else {
            targetContainer.items.push(itemToMove);
          }

          // Update the level to match the container
          itemToMove.level = targetContainer.level;

          // Update the container
          result.parentItems[result.index] = targetContainer;

          break;
        }
      }

      return newState;
    });
  };

  const checkIfControlsInContainer = () => {
    for (let i = 0; i < formLayoutComponents.length; i++) {
      const items = formLayoutComponents[i].items || [];

      // Function to recursively check if any container has controls
      const hasControlsRecursive = (items: FormLayoutItemType[]): boolean => {
        // If there are any non-container items, then we have controls
        if (items.some(item => !item.isContainer)) {
          return true;
        }

        // Check all nested containers
        for (const item of items) {
          if (item.isContainer && (item as FormLayoutContainerType).items) {
            if (hasControlsRecursive((item as FormLayoutContainerType).items)) {
              return true;
            }
          }
        }

        return false;
      };

      if (!hasControlsRecursive(items)) {
        Alert.alert(
          "Warning",
          "You need to have controls inside containers before updating."
        );
        return false;
      }
    }
    return true;
  };

  const publishForm = () => {
    if (formLayoutComponents.length === 0) {
      Alert.alert("Warning", "Form cannot be empty");
      return;
    }

    if (!checkIfControlsInContainer()) {
      return;
    }

    let currentTemplate: TemplateType = JSON.parse(
      JSON.stringify(selectedTemplate),
    );

    // Check if there is a change in the previous published version
    if (
      currentTemplate.publishHistory.length > 0 &&
      JSON.stringify(currentTemplate.publishHistory[0].formLayoutComponents) ===
      JSON.stringify(formLayoutComponents)
    ) {
      Alert.alert("Info", "No change in current & previous published version.");
      return;
    }

    let updatedAt = dayjs().valueOf();

    if (currentTemplate.lastPublishedAt !== 0) {
      // Add current layout components to publish history
      currentTemplate.publishHistory.splice(0, 0, {
        lastPublishedAt: currentTemplate.lastPublishedAt,
        formLayoutComponents: currentTemplate.formLayoutComponents,
      });
    }
    currentTemplate.formLayoutComponents = formLayoutComponents;
    currentTemplate.publishStatus = FormPublishStatus.PUBLISHED;
    currentTemplate.lastPublishedAt = updatedAt;
    currentTemplate.updatedAt = updatedAt;

    console.log('Current Template: ', currentTemplate);

    // For now, just update the local state
    setSelectedTemplate(currentTemplate);
    Alert.alert("Success", "Changes in Form Published.");
  };

  const saveForm = (formname:string,id:string) => {
    
    if (formLayoutComponents.length === 0) {
      // Alert.alert("Warning", "Form cannot be empty");
      alert("Form cannot be empty.")
      return;
    }

    if (!checkIfControlsInContainer()) {
      return;
    }

    const currentTemplate = JSON.parse(JSON.stringify(selectedTemplate));

    // if (
    //   JSON.stringify(currentTemplate.formLayoutComponents) ===
    //   JSON.stringify(formLayoutComponents)
    // ) {
    //   // Alert.alert("Info", "No change in current & previous saved version.");
    //   return;
    // }

    currentTemplate.formLayoutComponents = formLayoutComponents;
    currentTemplate.publishStatus = FormPublishStatus.DRAFT;
    currentTemplate.updatedAt = dayjs().valueOf();

    // console.log('Current Template: ', JSON.stringify(currentTemplate));
    // For now, just update the local state
    setSelectedTemplate(currentTemplate);
  };

  return {
    handleItemAdded,
    deleteContainer,
    deleteSubContainer,
    deleteControl,
    selectControl,
    editControlProperties,
    editContainerProperties,
    moveControlFromSide,
    moveControl,
    moveContainer,
    publishForm,
    saveForm,
    selectedTemplate,
    formLayoutComponents,
    selectedControl,
    setFormLayoutComponents
  };
};

export default useFormBuilder;