import React, { FunctionComponent, useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import useFormBuilder from '../hooks/useFormBuilder';
import useFormPreview from '../hooks/useFormPreview';
import {
  TemplateType,
  FormLayoutComponentContainerType,
} from '../types/FormTemplateTypes';
import DropContainerComponent from 'src/features/form-builder/components/DropContainerComponent';
import EditPropertiesComponent from 'src/features/form-builder/components/EditPropertiesComponent';
import { FormItemTypes } from 'src/features/form-builder/utils/FormBuilderUtils';
import FormPreview from 'src/features/form-builder/components/FormPreview';
import LeftSidebar from 'src/features/form-builder/components/LeftSideBar';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { DndProvider } from 'react-dnd';
import FormBuilderHeader from 'src/features/form-builder/components/FormBuilderHeader';
import { screenHeight } from 'src/utils/ScreenUtils';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
const { width, height } = Dimensions.get('window');

import { fetchDiscipline } from 'src/features/profile/profile-api';
import { saveFormData, updateFormData, fetchFormDetails, fetchTemplateDetails, updateTemplateData } from '../form-api';
interface FormBuilderProps {
  route: any;
}
import { MessageModal } from '@/src/utils/MessageModal';

import { Checkbox } from 'react-native-paper';


const isTabletOrMobileDevice = () => {
  const { width, height } = Dimensions.get('window');
  // Define breakpoints for mobile and tablet
  const isTablet = Math.min(width, height) >= 768;
  return isTablet;
};
const FullScreenLoader = ({ visible }: { visible: boolean }) => {
  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => { }}
    >
      <View style={loaderstyles.container}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    </Modal>
  );
};
const ConfirmationModal = ({ visible, onCancel, onConfirm }) => {
  // Modal content remains the same
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.iconContainer}>
            <Ionicons name="checkmark-circle" size={48} color="#1D75F5" />
          </View>
          {/* <Text style={styles.title}>Submit Recording?</Text> */}
          <Text style={confModalStyle.message}>
            Do you want to save the changes?
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const OpenFormnameModal = ({ visible, onCancel, onConfirm }) => {
  // Modal content remains the same
  const clickConfirm = () => {
    if (formName.trim() == "") {
      setIsError(true)
    } else {
      onConfirm(formName)
    }
  }
  const [formName, setFormName] = useState("")
  const [isError, setIsError] = useState(false)
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>

          {isError &&
            <Text style={styles.textError}>
              Form Name Can't be blank.
            </Text>
          }
          <View style={{ width: '100%' }}>
            <Text style={styles.message}>
              Enter Form Name:
            </Text>
            <TextInput
              style={styles.input}
              placeholder="Enter Form Name"
              placeholderTextColor="grey"
              value={formName}
              onChangeText={setFormName}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.confirmButton} onPress={() => clickConfirm()}>
              <Text style={styles.confirmButtonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
const FormBuilderScreen: FunctionComponent<FormBuilderProps> = ({
  route,
}) => {
  const navigation = useNavigation()
  let {
    handleItemAdded,
    saveForm,
    deleteContainer,
    deleteSubContainer,
    deleteControl,
    editContainerProperties,
    editControlProperties,
    moveControl,
    moveContainer,
    moveControlFromSide,
    publishForm,
    selectControl,
    selectedTemplate,
    setFormLayoutComponents,
    formLayoutComponents,
    selectedControl,
  } = useFormBuilder({
    template: {
      id: '0',
      name: '',
      createdAt: 0,
      creator: '',
      formLayoutComponents: [
        {
          container: {
            id: 'fokk8854gzmagli617rrdg',
            controlName: 'step-container',
            displayText: 'Add Section',
            itemType: 'container',
            heading: 'Section Code',
            subHeading: 'Section Name',
          },
          items: [], // Start with empty items array
        },
      ],

      lastPublishedAt: 0,
      publishHistory: [],
      publishStatus: 'draft',
      updatedAt: 0,
    },
  });
  const [isLoading, setIsLoading] = useState(false)
  // const formId = route.params.formId;
  const [formId, setFormId] = useState(route.params.formId)
  const type = route.params.type;
  const [formName, setFormName] = useState("")
  const [isPublished, setIsPublished] = useState(false)
  const { showPreview, openPreviewDrawer, closePreviewDrawer } =
    useFormPreview();

  const [isMobile, setIsMobile] = useState<boolean>(false);

  const [showModal, setShowModal] = useState(false)

  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success")

  const [showConfModal, setShowConfModal] = useState(false)
  const [showDisciplineModal, setShowDisciplineModal] = useState(false)
  const [startingLayout, setStartingLayout] = useState("")
  const [disciplineId, setDisciplineId] = useState("");
  const [formTypeId, setFormTypeId] = useState("")
  const [isPageLoadingComplete, setIsPageLoadingComplete] = useState(false)


  const [publishDisciplines, setPublishDisciplines] = useState([]);
  const [disciplines, setSDisciplines] = useState([]);



  const fetchAllDiscipline = () => {
    fetchDiscipline()
      .then((result: any) => {
        if (result.status == "ok") {
          let disciplines = result.data.map((dis) => {
            return { label: dis.name, value: dis._id }
          })
          setSDisciplines(disciplines)
        }
      })
      .catch((error) => {
        console.log(error);
      })
  }
  useEffect(() => {
    fetchAllDiscipline()
  }, [])


  useEffect(() => {
    if (formId != "new") {
      setIsLoading(true);
      if (type == "form") {

        fetchFormDetails(formId)
          .then((result) => {
            if (result.status == "ok") {
              // setForms(result.data)
              setFormLayoutComponents(result.data.question)
              setStartingLayout(JSON.parse(JSON.stringify(result.data.question)))
              setFormName(result.data.name)
              setIsPublished(result.data.isPublished)
              setIsPageLoadingComplete(true)
            }
          })
          .catch((error) => {
            console.log(error);
          }).finally(() => setIsLoading(false));
      } else {
        fetchTemplateDetails(formId)
          .then((result) => {
            if (result.status == "ok") {
              setFormLayoutComponents(result.data.question)
              setStartingLayout(JSON.parse(JSON.stringify(result.data.question)))
              setFormName(result.data.name)
              setDisciplineId(result.data.disciplineId);
              setFormTypeId(result.data.formTypeId)
              setIsPageLoadingComplete(true)
            }
          })
          .catch((error) => {
            console.log(error);
          }).finally(() => setIsLoading(false));
      }
    } else {

      setStartingLayout(JSON.parse(JSON.stringify(formLayoutComponents)))
      setShowModal(true);
    }

  }, [])

  useEffect(() => {
    const handleDimensionsChange = () => {
      const { width, height } = Dimensions.get('window');
      setIsMobile(Math.min(width, height) < 1024 && !isTabletOrMobileDevice());
    };

    // Initial check
    handleDimensionsChange();

    Dimensions.addEventListener('change', handleDimensionsChange);

    // Cleanup
    return () => {
      Dimensions.removeEventListener('change', handleDimensionsChange);
    };
  }, []);

  /**
     * Update the assesment form after 10 seconds if any changes occurs 
     * in the form
     */
  useEffect(() => {
    let interval = setInterval(() => {
      if (isPageLoadingComplete) {
        if (JSON.stringify(startingLayout) != JSON.stringify(formLayoutComponents)) {
          if (formId != "new") {
            updateForm(isPublished).then((result: any) => {
              if (result.status == "ok") {
                setStartingLayout(JSON.parse(JSON.stringify(formLayoutComponents)))
              } else {
              }
            })
          }
        }
      }
    }, 30000);
    return () => clearInterval(interval);
  })
  const onsubmit = (formNameString: string) => {

    onModalCancel()
    let data: any = { formName: formNameString, assessmentForm: formLayoutComponents, isPublished: false }
    if (type != "form") {
      data["disciplineId"] = disciplineId;
      data["formTypeId"] = formTypeId
    }
    setIsLoading(true);
    saveFormData(data)
      .then((result) => {
        if (result.status == "ok") {
          // alert("Form Added Successfully.")
          setShowMessageModal(true)
          setMessage("Form Added Successfully.")
          setMessageType("success")

          setFormId(result.data._id)
          setFormName(formNameString)
          setIsPublished(result.data.isPublished)
          setStartingLayout(JSON.parse(JSON.stringify(formLayoutComponents)))

        } else {
          setShowMessageModal(true)
          setMessage(result.errorMessage)
          setMessageType("error")
        }
      })
      .catch((error) => {
        console.log(error);
        // alert("Something went wrong.")
      }).finally(() => setIsLoading(false));
  }
  const handleBack = () => {
    if (JSON.stringify(startingLayout) == JSON.stringify(formLayoutComponents)) {
      navigation.goBack()
    } else {
      setShowConfModal(true)
    }

  };
  const openModal = () => {
    if (formId != "new") {
      if (type == "form") {
        setIsLoading(true);
        updateForm(isPublished).then((result: any) => {
          if (result.status == "ok") {
            // alert("Form Updated Successfully.")
            setShowMessageModal(true)
            setMessage("Form Updated Successfully.")
            setMessageType("success")

            setStartingLayout(JSON.parse(JSON.stringify(formLayoutComponents)))
          } else {
            // alert(result.errorMessage)
            setShowMessageModal(true)
            setMessage(result.errorMessage)
            setMessageType("error")
          }
        }).finally(() => setIsLoading(false));
      } else {
        setShowModal(true);
      }

    } else {
      setShowModal(true);
    }
  }
  const updateForm = (isPublishedForm: boolean, selectedDisciplines: string[] = []) => {

    // const data = { formName: formName, assessmentForm: formLayoutComponents, isPublished: isPublishedForm , desciplines:selectedDisciplines}
    console.log("isPublishedForm:" + isPublishedForm)
    return new Promise((resolve, reject) => {
      const data = { formName: formName, question: formLayoutComponents, isPublished: isPublishedForm }
      updateFormData(data, formId)
        .then((result) => {
          resolve(result)
        })
        .catch((error) => {

          reject()
        })
    })
  }
  const onModalCancel = () => {
    setShowModal(false);
    setShowMessageModal(false)
  };
  const onConfModalCancel = () => {
    setShowConfModal(false);
    navigation.goBack();
  }
  const onConfModalConfirm = () => {
    setShowConfModal(false);
    openModal()
  }

  const onPublishForm = (selectedDisciplines = []) => {
    if (formId != "new") {
      setIsPublished(true)

      setIsLoading(true);
      updateForm(true, selectedDisciplines).then((result: any) => {
        if (result.status == "ok") {
          // alert("Form Published Successfully.")
          setShowMessageModal(true)
          setMessage("Form Published Successfully.")
          setMessageType("success")
          setStartingLayout(JSON.parse(JSON.stringify(formLayoutComponents)))
        } else {
          // alert(result.errorMessage)
          setShowMessageModal(true)
          setMessage(result.errorMessage)
          setMessageType("error")
        }
      }).finally(() => setIsLoading(false));
    }
  }

  const onDisciplineModalopen = () => {
    setShowDisciplineModal(true);
  }



  const DisciplineConfirmModal = ({ visible, onPublishForm, disciplines = [] }) => {

    const [selectedDisciplines, setSelectedDisciplines] = useState([]);


    const toggleDiscipline = (discipline) => {
      const exists = selectedDisciplines.find(d => d.value === discipline.value);
      if (exists) {
        setSelectedDisciplines(selectedDisciplines.filter(d => d.value !== discipline.value));
      } else {
        setSelectedDisciplines([...selectedDisciplines, discipline]);
      }
    };

    const isDisciplineSelected = (discipline) => {
      return selectedDisciplines.some(d => d.value === discipline.value);
    };


    const onDisciplineModalConfirm = () => {
      onPublishForm(selectedDisciplines);
      setShowDisciplineModal(false);
      setSelectedDisciplines([]);

    }

    const onDisciplineModalCancel = () => {
      setShowDisciplineModal(false);
    }


    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={visible}
      >
        <View style={styles.overlay}>
          <View style={discciplineModalStyle.modalContainer}>
            {/* <View style={styles.iconContainer}>
              <Ionicons name="checkmark-circle" size={48} color="#1D75F5" />
            </View> */}
            <Text style={confModalStyle.message}>
              Please Select a Discipline No.
            </Text>

            <ScrollView
              style={discciplineModalStyle.scrollBoxStyle}
              contentContainerStyle={discciplineModalStyle.containerStyle}
            >
              {disciplines.map((discipline, index) => (
                <TouchableOpacity
                  key={discipline.value}
                  // style={[discciplineModalStyle.checkBoxStyle,{ borderBottomWidth: index !== disciplines.length - 1 ? 1 : 0,}]}
                  style={[discciplineModalStyle.checkBoxStyle]}
                  onPress={() => toggleDiscipline(discipline)}
                >
                  <Checkbox
                    status={isDisciplineSelected(discipline) ? 'checked' : 'unchecked'}
                    color="#1D75F5"
                  />

                  <Text style={discciplineModalStyle.checBoxContent}>
                    {discipline.label}
                  </Text>
                </TouchableOpacity>

              ))}
            </ScrollView>

            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.cancelButton} onPress={onDisciplineModalCancel}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.confirmButton} onPress={onDisciplineModalConfirm}>
                <Text style={styles.confirmButtonText}>Confirm</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };


  return (
    <DndProvider backend={HTML5Backend}>
      <OpenFormnameModal visible={showModal} onCancel={onModalCancel} onConfirm={onsubmit} />
      <ConfirmationModal visible={showConfModal} onCancel={onConfModalCancel} onConfirm={onConfModalConfirm} />
      <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} />
      <FullScreenLoader visible={isLoading} />
      <View style={styles.root}>
        {/* Custom Header */}
        <FormBuilderHeader
          handleBack={handleBack}
          onSave={openModal}
          onPreview={openPreviewDrawer}
          onPublish={onPublishForm}
        // onPublish={onDisciplineModalopen}
        // visiblePublish={type == "form"}
        />

        {/* <DisciplineConfirmModal
          visible={showDisciplineModal}
          onPublishForm={onPublishForm}
          disciplines={disciplines}
        /> */}

        <View style={styles.wrapper}>

          {/* Left Sidebar */}
          <LeftSidebar
            handleItemAdded={handleItemAdded}
            formLayoutComponents={formLayoutComponents}
          />

          {/* Main Content */}
          <View style={styles.mainContent}>
            {/* Form Layout */}
            <ScrollView
              style={styles.formScroll}
              contentContainerStyle={styles.formScrollContent}
            >
              <View style={styles.formLayout}>
                {formLayoutComponents.map((layout, ind) => (
                  <DropContainerComponent
                    key={layout.container.id}
                    index={ind}
                    itemsLength={formLayoutComponents.length}
                    layout={layout.container}
                    layoutContainer={layout}
                    selectedControl={selectedControl}
                    items={layout.items}
                    deleteContainer={deleteContainer}
                    deleteSubContainer={deleteSubContainer} // Add this line
                    deleteControl={deleteControl}
                    selectControl={selectControl}
                    accept={FormItemTypes.CONTROL}
                    moveControl={moveControl}
                    handleItemAdded={handleItemAdded}
                    moveContainer={moveContainer}
                  />
                ))}

                {/* Add Container */}
                <DropContainerComponent
                  accept={FormItemTypes.CONTAINER}
                  name="Parent Component"
                  handleItemAdded={handleItemAdded}
                />
                <View style={{ height: 100 }} />
              </View>
            </ScrollView>
          </View>

          {/* Right Sidebar */}
          <EditPropertiesComponent
            selectedControl={selectedControl}
            selectControl={selectControl}
            formLayoutComponents={formLayoutComponents}
            moveControlFromSide={moveControlFromSide}
            editContainerProperties={editContainerProperties}
            editControlProperties={editControlProperties}
          />
        </View>

        {/* Form Preview Modal */}
        <FormPreview
          screenType="mobile"
          showPreview={showPreview}
          formLayoutComponents={formLayoutComponents}
          closePreviewDrawer={closePreviewDrawer}
        />
      </View>
    </DndProvider>
  );
};

export default FormBuilderScreen;

const discciplineModalStyle = StyleSheet.create({
  modalContainer: {
    width: width * 0.23,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,
  },
  scrollBoxStyle: {
    maxHeight: 200,
    marginBottom: 20,
    // borderRadius: 8,
    // borderColor: '#ddd',
    // borderWidth: 1,
    // backgroundColor: '#fafafa',
    minWidth: 200,
    marginTop: 20,
  },
  containerStyle: {
    paddingVertical: 5,
  },
  checkBoxStyle: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 3,
    // borderBottomColor: '#eee',
  },
  checBoxContent: { marginLeft: 10, fontSize: 16, color: '#333' },
})

const confModalStyle = StyleSheet.create({
  message: {
    color: "#7C7887",
    textAlign: "center",
    fontFamily: "Poppins",
    fontSize: 18,
    fontStyle: "normal",
    fontWeight: 400,
  },
})
const loaderstyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)', // Semi-transparent background
    justifyContent: 'center',
    alignItems: 'center',
  },
});
const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  wrapper: {
    flex: 1,
    flexDirection: 'row',
    height: screenHeight - 65,
  },
  sidebar: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    marginRight: 8,
  },
  mainContent: {
    flex: 1,
  },
  formHeader: {
    marginBottom: 16,
  },
  formName: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 24,
    color: '#1F2937',
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  outlinedButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#4C51BF',
  },
  containedButton: {
    backgroundColor: '#4C51BF',
  },
  outlinedButtonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#4C51BF',
  },
  containedButtonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#FFFFFF',
  },
  formScroll: {
    flex: 1,
    padding: 24,
  },
  formScrollContent: {
    height: screenHeight - 89,
  },
  formLayout: {
    flex: 1,
    height: screenHeight - 89,
  },
  mobileContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  mobileText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 18,
    color: '#4B5563',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 18,
    color: '#4B5563',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.26,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    color: "#000",
    textAlign: "center",
    fontFamily: "Poppins",
    fontSize: 24,
    fontStyle: "normal",
    fontWeight: "bold",
  },
  message: {
    color: "#000",
    // textAlign: "center",
    fontFamily: "Poppins",
    fontSize: 18,
    fontStyle: "normal",
    fontWeight: 400,
    paddingBottom: 10
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 15
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#1D75F5',
    paddingVertical: 12,
    borderRadius: 24,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#1D75F5',
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#1D75F5',
    paddingVertical: 12,
    borderRadius: 24,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '500',
  },
  input: {
    flex: 1,
    paddingVertical: 10,
    paddingLeft: 40,
    paddingRight: 16,
    borderWidth: 1,
    // borderColor: "#D1D5DB",
    borderRadius: 12,
    fontSize: 16,
    color: "#000000",
    fontFamily: "Poppins_400Regular",
    height: 58,
  },
  textError: {
    width: '100%',
    color: 'red',
    fontWeight: 'bold',
    fontFamily: 'Poppins',
    paddingBottom: 20,
    fontSize: 16,
    textAlign: 'center'
  }
});