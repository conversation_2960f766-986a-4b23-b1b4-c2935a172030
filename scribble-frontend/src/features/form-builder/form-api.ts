import {apiClient} from "src/api/apiClient";

export const fetchFormList = (limit:number,page:number) => {
  return apiClient
    .get(`v1/visit/form?limit=${limit}&page=${page}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Fetch Form List";
    });
};
export const fetchTemplateList = (limit:number,page:number) => {
  return apiClient
    .get(`v1/visit/template?limit=${limit}&page=${page}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Fetch Template List";
    });
};
export const saveFormData = (data:any) => {
  return apiClient
    .post(`v1/visit/form`,data)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Save Form ";
    });
};
export const updateFormData = (data:any,id:string) => {
  return apiClient
    .put(`v1/visit/form/${id}`,data)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to update Form ";
    });
};
export const updateTemplateData = (data:any,id:string) => {
  return apiClient
    .put(`v1/visit/template/${id}`,data)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to update Form ";
    });
};
export const deleteFormData = (id:string) => {
  return apiClient
    .delete(`v1/visit/form/${id}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to delete Form ";
    });
};
export const fetchFormDetails = (id:string) => {
  return apiClient
    .get(`v1/visit/form/${id}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Fetch Form ";
    });
};
export const fetchTemplateDetails = (id:string) => {
  return apiClient
    .get(`v1/visit/template/${id}`)
    .then((response) => response.data)
    .catch((error) => {
      console.log("Error:", error);
      throw error.response?.data?.message || "Failed to Fetch template";
    });
};
