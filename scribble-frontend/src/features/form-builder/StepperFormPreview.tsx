import React, { FC, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import RenderItem from './RenderItem';
import { FormLayoutComponentsType } from '../types/FormTemplateTypes';

interface StepperFormPreviewProps {
  formLayoutComponents: FormLayoutComponentsType[];
  screenType: string; // e.g. "mobile" or "web"
}

const StepperFormPreview: FC<StepperFormPreviewProps> = ({
  formLayoutComponents,
  screenType,
}) => {
  const [componentIndex, setComponentIndex] = useState(0);
  const component = formLayoutComponents[componentIndex];

  const nextPrevIndex = (val: number) => {
    setComponentIndex(prev => prev + val);
  };

  // const isMobile = true;
  //
  // // Adjust sizing dynamically based on device width
  // const { width, height } = Dimensions.get('window');
  // const containerWidth = isMobile ? width * 0.9 : '80%'; // 90% of the bezel width
  // const containerHeight = height * 0.75; // 75% of the bezel height

  if (formLayoutComponents.length === 0) {
    return (
      <View style={styles.noContentContainer}>
        <Text style={styles.noContentText}>
          You need to add Containers and Controls to see output here.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.root}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={[styles.previewWindow]}>
          <View style={styles.stepTitleContainer}>
            <Text style={styles.stepTitle}> {component.container.heading}</Text>
          </View>

          {/* Heading & Subheading */}
          <View style={styles.headingContainer}>
            <Text style={styles.headingText}>
              {component.container.subHeading}
            </Text>
          </View>

          {/* Render each child item */}
          {component.children.map((child, ind) => (
            <View key={child.id} style={styles.childContainer}>
              <Text style={styles.childLabel}>
                {child.labelName}
                {child.required ? ' *' : ''}
              </Text>
              {!!child.description && (
                <Text style={styles.childDescription}>{child.description}</Text>
              )}

              <RenderItem key={child.id} item={child} />
            </View>
          ))}

          {/* Navigation Buttons */}
          <View style={styles.buttonRow}>
            {componentIndex !== 0 && (
              <TouchableOpacity
                style={[styles.navButton, styles.backButton]}
                onPress={() => nextPrevIndex(-1)}
              >
                <Text style={styles.navButtonText}>Back</Text>
              </TouchableOpacity>
            )}

            {componentIndex < formLayoutComponents.length - 1 && (
              <TouchableOpacity
                style={[styles.navButton, styles.nextButton]}
                onPress={() => nextPrevIndex(1)}
              >
                <Text style={styles.navButtonText}>Next</Text>
              </TouchableOpacity>
            )}

            {componentIndex + 1 === formLayoutComponents.length && (
              <TouchableOpacity
                style={styles.submitButton}
                onPress={() => {
                  console.log('Submit clicked');
                }}
              >
                <Text style={styles.submitButtonText}>Submit</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default StepperFormPreview;

// -------------- Styles --------------
const styles = StyleSheet.create({
  root: {
    width: 354,
    height: 765,
  },
  scrollContent: {
    alignItems: 'center',
  },
  noContentContainer: {
    flex: 1,
    alignItems: 'center',
  },
  noContentText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: '#1F2937',
    textAlign: 'center',
  },
  previewWindow: {
    backgroundColor: '#FFFFFF',
    // borderColor: 'rgba(0,0,0,0.1)',
    // borderWidth: 1,
    // borderRadius: 20,
    // shadowColor: '#000',
    // shadowOffset: { width: 0, height: 5 },
    // shadowOpacity: 0.1,
    // shadowRadius: 6,
    // elevation: 4,
    width: 354,
    height: 765,
    padding: 16,
    borderRadius: 44,
  },
  stepTitleContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    paddingBottom: 10,
  },
  stepTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#1F2937',
    textAlign: 'center',
  },
  headingContainer: {
    marginVertical: 16,
    alignItems: 'center',
  },
  headingText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 4,
  },
  childContainer: {
    marginBottom: 16,
  },
  childLabel: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 15,
    color: '#1F2937',
    marginBottom: 6,
  },
  childDescription: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 13,
    color: '#6B7280',
    marginBottom: 6,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center', // Center buttons
    alignItems: 'center',
    marginTop: 16,
  },
  navButton: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginHorizontal: 8, // Adds space between buttons
  },
  backButton: {
    backgroundColor: '#F3F4F6',
  },
  nextButton: {
    backgroundColor: '#F3F4F6',
  },
  navButtonText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#1F2937',
  },
  submitButton: {
    backgroundColor: '#4C51BF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  submitButtonText: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 14,
    color: '#FFFFFF',
  },
});
