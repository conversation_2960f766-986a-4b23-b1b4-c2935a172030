import React, { FC, useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Switch,
  StyleSheet,
  ScrollView,
} from 'react-native';

// If you prefer expo-checkbox for booleans, import it:
import Checkbox from 'expo-checkbox';
import ScoreCalculatorComponent from './ScoreCalculatorComponent';
import SkipLogicComponent from './SkipLogicComponent';

// A placeholder for your "ManageItemsListComponent" in RN
import ManageItemsListComponent from './ManageItemsListComponent';

// You said you have these enums or objects:
import {
  FormControlNames,
  FormItemTypes,
} from 'src/features/form-builder/utils/FormBuilderUtils';
import {
  FormLayoutComponentChildrenType,
  FormLayoutComponentContainerType,
  FormLayoutCoponentChildrenItemsType,
  FormLayoutComponentsType,
  FormLayoutContainerType,
} from 'src/features/form-builder/types/FormTemplateTypes';
import { screenHeight } from 'src/utils/ScreenUtils';

interface EditPropertiesComponentProps {
  selectedControl?:
  | FormLayoutComponentChildrenType
  | FormLayoutComponentContainerType
  | FormLayoutContainerType
  | undefined;
  selectControl?: (
    layout:
      | FormLayoutComponentChildrenType
      | FormLayoutComponentContainerType
      | FormLayoutContainerType
      | undefined,
  ) => void;
  editControlProperties: (updatedItem: FormLayoutComponentChildrenType) => void;
  editContainerProperties: (
    updatedItem: FormLayoutComponentContainerType | FormLayoutContainerType,
  ) => void;
  formLayoutComponents: FormLayoutComponentsType[];
  moveControlFromSide: (
    selectedControl: FormLayoutComponentChildrenType,
    moveControlObj: FormLayoutComponentChildrenType,
  ) => void;
}

const EditPropertiesComponent: FC<EditPropertiesComponentProps> = props => {
  const {
    selectedControl,
    selectControl,
    editControlProperties,
    editContainerProperties,
    formLayoutComponents,
    moveControlFromSide,
  } = props;

  // The item being edited (question or container)
  const [updatedItem, setUpdatedItem] = useState<
    FormLayoutComponentChildrenType | FormLayoutComponentContainerType | FormLayoutContainerType | {}
  >({});

  const childUpdatedItem = updatedItem as FormLayoutComponentChildrenType;
  const containerUpdatedItem = updatedItem as FormLayoutComponentContainerType | FormLayoutContainerType;

  const [isUpdatedItemRequired, setIsUpdatedItemRequired] = useState(false);
  const [isDisplayCheatSheet, setisDisplayCheatSheet] = useState(false)

  // For "move control" logic
  const [moveControlObj, setMoveControlObj] =
    useState<FormLayoutComponentChildrenType | null>(null);
  const [controlsInContainer, setControlsInContainer] = useState<
    number | undefined
  >(undefined);

  useEffect(() => {
    if (selectedControl) {
      // If it's a child with .items, clone them
      if (
        (selectedControl as FormLayoutComponentChildrenType).items !== undefined ||
        (selectedControl as FormLayoutContainerType).items !== undefined
      ) {
        let cloneItems;
        if ((selectedControl as FormLayoutComponentChildrenType).items !== undefined) {
          cloneItems = JSON.parse(
            JSON.stringify(
              (selectedControl as FormLayoutComponentChildrenType).items,
            ),
          );
        } else if ((selectedControl as FormLayoutContainerType).items !== undefined) {
          cloneItems = JSON.parse(
            JSON.stringify(
              (selectedControl as FormLayoutContainerType).items,
            ),
          );
        }
        setUpdatedItem({ ...selectedControl, items: cloneItems });
      } else {
        setUpdatedItem({ ...selectedControl });
      }
      // If has "required", set local state
      if ('required' in selectedControl) {
        setIsUpdatedItemRequired(
          (selectedControl as FormLayoutComponentChildrenType).required,
        );
      }
      // console.log(JSON.stringify(selectedControl))
      // If has "isDisplayCheatSheet" ,set local state
      if ('isDisplayCheatSheet' in selectedControl) {
        setisDisplayCheatSheet(
          (selectedControl as FormLayoutComponentChildrenType).isDisplayCheatSheet,
        );
      } else {
        setisDisplayCheatSheet(false)
      }
    } else {
      setUpdatedItem({});
    }

    setMoveControlObj(null);
    setControlsInContainer(undefined);
  }, [selectedControl]);

  const handleChangeText = (fieldName: string, value: string) => {
    setUpdatedItem((prevState: any) => ({
      ...prevState,
      [fieldName]: value,
    }));
  };

  const handleRequiredChange = (newVal: boolean) => {
    setIsUpdatedItemRequired(newVal);
    setUpdatedItem((prevState: any) => ({
      ...prevState,
      required: newVal,
    }));
  };
  const handleCheatSheetChange = (newVal: boolean) => {
    setisDisplayCheatSheet(newVal);
    setUpdatedItem((prevState: any) => ({
      ...prevState,
      isDisplayCheatSheet: newVal,
    }));
  };

  // For sub-list items
  const addItemInList = (item: FormLayoutCoponentChildrenItemsType) => {
    if (!childUpdatedItem.items) {
      childUpdatedItem.items = [];
    }
    const newItems = [...childUpdatedItem.items, item];
    setUpdatedItem((prev: any) => ({
      ...prev,
      items: newItems,
    }));
  };

  const deleteItemFromList = (item: FormLayoutCoponentChildrenItemsType) => {
    if (!childUpdatedItem.items) return;
    const newItems = childUpdatedItem.items.filter(i => i.id !== item.id);
    setUpdatedItem((prev: any) => ({
      ...prev,
      items: newItems,
    }));
  };

  const editIteminList = (item: FormLayoutCoponentChildrenItemsType) => {
    if (!childUpdatedItem.items) return;
    const newItems = childUpdatedItem.items.map(i => {
      if (i.id === item.id) {
        return { ...i, label: item.label, value: item.value };
      }
      return i;
    });
    setUpdatedItem((prev: any) => ({
      ...prev,
      items: newItems,
    }));
  };

  // Submitting for question
  const onFormSubmit = () => {
    editControlProperties(updatedItem as FormLayoutComponentChildrenType);
    if (selectControl) {
      selectControl(undefined);
    }
  };

  // Submitting for container or subcontainer
  const onContainerFormSubmit = () => {
    editContainerProperties(updatedItem as FormLayoutComponentContainerType | FormLayoutContainerType);
    if (selectControl) {
      selectControl(undefined);
    }
  };

  // Move control logic
  const handleMoveControlSelectChange = (fieldName: string, value: string) => {
    // e.g. containerId or position
    if (fieldName === 'containerId') {
      const container = formLayoutComponents.find(
        con => con.container.id === value,
      );
      if (container) {
        let stepsInContainer = container.children ? container.children.length : 0;
        if (container.items) {
          stepsInContainer = container.items.length;
        }
        if (
          (selectedControl as FormLayoutComponentChildrenType).containerId ===
          value
        ) {
          stepsInContainer -= 1;
        }
        setControlsInContainer(stepsInContainer);
      }
    }

    setMoveControlObj(prev => ({
      ...(prev ?? {}),
      [fieldName]: value,
    }));
  };

  const getPositions = () => {
    if (controlsInContainer !== undefined) {
      // e.g. produce array from 0..(controlsInContainer)
      const arr = Array.from(
        { length: controlsInContainer + 1 },
        (_, idx) => idx,
      );
      return arr.map(pos => ({
        label: `${pos + 1}`,
        value: pos,
      }));
    }
    return [];
  };

  const onMoveControlFormSubmit = () => {
    // Move the control
    if (moveControlObj) {
      props.moveControlFromSide(
        selectedControl as FormLayoutComponentChildrenType,
        moveControlObj as FormLayoutComponentChildrenType,
      );
      if (selectControl) {
        selectControl(undefined);
      }
    }
  };

  const isSubContainer = () => {
    // Check if the selected item is a subcontainer
    return (
      containerUpdatedItem.itemType === FormItemTypes.SUBCONTAINER ||
      (containerUpdatedItem.isContainer === true && containerUpdatedItem.containerId)
    );
  };

  // Render specific properties for Numeric with Units
  const renderNumericWithUnitsProperties = () => {
    if (childUpdatedItem.controlName === FormControlNames.NUMERICWITHUNITS) {
      return (
        <>
          <Text style={styles.label}>Value Placeholder</Text>
          <TextInput
            style={styles.textInput}
            value={childUpdatedItem.placeholder || ''}
            onChangeText={val => handleChangeText('placeholder', val)}
            placeholder="Enter Value Placeholder (e.g. 'Value')"
          />

          {/* <Text style={styles.label}>Unit Placeholder</Text>
          <TextInput
            style={styles.textInput}
            value={childUpdatedItem.unitPlaceholder || ''}
            onChangeText={val => handleChangeText('unitPlaceholder', val)}
            placeholder="Enter Unit Placeholder (e.g. 'kg', 'cm')"
          /> */}
        </>
      );
    }
    return null;
  };

  // If none is selected
  if (!selectedControl) {
    return (
      <View style={styles.mainForm}>
        <Text style={styles.sectionTitle}>Question Properties</Text>
        <View style={styles.alertBox}>
          <Text style={styles.alertTitle}>Note!</Text>
          <Text style={styles.alertDesc}>
            You need to select a section/question to edit question properties.
          </Text>
        </View>
      </View>
    );
  }

  // If selected is a container or subcontainer
  if (
    containerUpdatedItem.itemType === FormItemTypes.CONTAINER ||
    containerUpdatedItem.itemType === FormItemTypes.SUBCONTAINER ||
    containerUpdatedItem.isContainer === true
  ) {
    return (
      <View style={styles.mainForm}>
        <Text style={styles.formTitle}>
          {isSubContainer() ? 'Subsection Properties' : 'Section Properties'}
        </Text>

        <Text style={styles.label}>
          {isSubContainer() ? 'Subsection Code' : 'Section Code'}
        </Text>
        <TextInput
          style={styles.textInput}
          value={containerUpdatedItem.heading || ''}
          onChangeText={val => handleChangeText('heading', val)}
        />

        <Text style={styles.label}>
          {isSubContainer() ? 'Subsection Name' : 'Section Name'}
        </Text>
        <TextInput
          style={styles.textInput}
          value={containerUpdatedItem.subHeading || ''}
          onChangeText={val => handleChangeText('subHeading', val)}
        />

        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={styles.submitButton}
            onPress={onContainerFormSubmit}
          >
            <Text style={styles.submitButtonText}>Update</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => selectControl && selectControl(undefined)}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (childUpdatedItem.controlName === FormControlNames.SCORECALCULATOR) {
    return (
      <ScrollView style={styles.mainForm}>
        <Text style={styles.formTitle}>Score Calculator Properties</Text>

        {/* Basic properties */}
        <Text style={styles.label}>Question Label</Text>
        <TextInput
          style={styles.textInput}
          value={childUpdatedItem.labelName || ''}
          onChangeText={val => handleChangeText('labelName', val)}
        />

        <Text style={styles.label}>Question Code</Text>
        <TextInput
          style={styles.textInput}
          value={childUpdatedItem.questionCode || ''}
          onChangeText={val => handleChangeText('questionCode', val)}
        />

        <Text style={styles.label}>Question Description</Text>
        <TextInput
          style={[styles.textInput, { height: 80 }]}
          multiline
          value={childUpdatedItem.description || ''}
          onChangeText={val => handleChangeText('description', val)}
        />

        {/* Score Calculator Configuration */}
        <ScoreCalculatorComponent
          item={childUpdatedItem}
          formLayoutComponents={formLayoutComponents}
          onChange={(updatedItem) => {
            setUpdatedItem(updatedItem);
          }}
        />

        {/* Required checkbox */}
        <View style={styles.checkRow}>
          <Checkbox
            value={isUpdatedItemRequired}
            onValueChange={val => handleRequiredChange(val)}
            color={isUpdatedItemRequired ? '#4f46e5' : 'grey'}
          />
          <Text style={styles.checkRowLabel}>Required</Text>
        </View>

        {/* Display on Cheat Sheet checkbox */}
        <View style={styles.checkRow}>
          <Checkbox
            value={isDisplayCheatSheet}
            onValueChange={val => handleCheatSheetChange(val)}
            color={isDisplayCheatSheet ? '#4f46e5' : 'grey'}
          />
          <Text style={styles.checkRowLabel}>Display on Cheatsheet</Text>
        </View>
        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.submitButton} onPress={onFormSubmit}>
            <Text style={styles.submitButtonText}>Update</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => selectControl && selectControl(undefined)}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  }

  // If selected is a child control
  return (
    <ScrollView style={styles.mainForm}>
      <Text style={styles.formTitle}>Question Properties</Text>

      {/* Field Label Name */}
      <Text style={styles.label}>Question Label</Text>
      <TextInput
        style={styles.textInput}
        value={childUpdatedItem.labelName || ''}
        onChangeText={val => handleChangeText('labelName', val)}
      />

      <Text style={styles.label}>Question Code</Text>
      <TextInput
        style={styles.textInput}
        value={childUpdatedItem.questionCode || ''}
        onChangeText={val => handleChangeText('questionCode', val)}
      />

      {/* Field Placeholder? */}
      {[
        FormControlNames.INPUTTEXTFIELD,
        FormControlNames.INPUTMULTILINE,
        FormControlNames.CHECKBOX,
        FormControlNames.TOGGLE,
      ].includes(childUpdatedItem.controlName!) && (
          <>
            <Text style={styles.label}>Question Placeholder</Text>
            <TextInput
              style={styles.textInput}
              value={childUpdatedItem.placeholder || ''}
              onChangeText={val => handleChangeText('placeholder', val)}
            />
          </>
        )}

      {/* Numeric with Units specific properties */}
      {renderNumericWithUnitsProperties()}

      {/* Field Description */}
      <Text style={styles.label}>Question Description</Text>
      <TextInput
        style={[styles.textInput, { height: 80 }]}
        multiline
        value={childUpdatedItem.description || ''}
        onChangeText={val => handleChangeText('description', val)}
      />

      {/* Required */}
      <View style={styles.checkRow}>
        <Checkbox
          value={isUpdatedItemRequired}
          onValueChange={val => handleRequiredChange(val)}
          color={isUpdatedItemRequired ? '#4f46e5' : 'grey'}
        />
        <Text style={styles.checkRowLabel}>Required</Text>
      </View>

      {/* Hide from Cheet Sheet*/}
      <View style={styles.checkRow}>
        <Checkbox
          value={isDisplayCheatSheet}
          onValueChange={val => handleCheatSheetChange(val)}
          color={isDisplayCheatSheet ? '#4f46e5' : 'grey'}
        />
        <Text style={styles.checkRowLabel}>Display on Cheatsheet</Text>
      </View>

      {/* If user can add list items */}
      {[
        FormControlNames.RADIOGROUP,
        FormControlNames.SELECTDROPDOWN,
        FormControlNames.CHECKLIST,
      ].includes(childUpdatedItem.controlName!) && (
          <View>
            <Text style={styles.label}>List Items</Text>
            {/* ManageItemsListComponent => must also be converted or replaced with RN approach */}
            <ManageItemsListComponent
              addItemInList={addItemInList}
              editIteminList={editIteminList}
              deleteItemFromList={deleteItemFromList}
              items={childUpdatedItem.items || []}
            />
          </View>
        )}

      {/* Skip Logic Component */}
      {[
        FormControlNames.RADIOGROUP,
        FormControlNames.SELECTDROPDOWN,
        FormControlNames.CHECKLIST,
      ].includes(childUpdatedItem.controlName!) && (
          <SkipLogicComponent
            item={childUpdatedItem}
            formLayoutComponents={formLayoutComponents}
            onSkipLogicChange={(skipLogic) => {
              setUpdatedItem((prev: any) => ({
                ...prev,
                skipLogic,
              }));
            }}
          />
        )}

      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.submitButton} onPress={onFormSubmit}>
          <Text style={styles.submitButtonText}>Update</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => selectControl && selectControl(undefined)}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>


    </ScrollView>
  );
};

export default EditPropertiesComponent;

const styles = StyleSheet.create({
  mainForm: {
    width: 450,
    maxWidth: 450,
    backgroundColor: '#FFFFFF',
    borderLeftColor: '#E2E8F0',
    borderLeftWidth: 1,
    padding: 16,
    height: screenHeight - 65,
  },
  formTitle: {
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 16,
    fontFamily: 'Poppins_600SemiBold',
  },
  label: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
    fontFamily: 'Poppins_400Regular',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    fontSize: 14,
    marginBottom: 10,
    color: '#000',
    fontFamily: 'Poppins_400Regular',
  },
  checkRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  checkRowLabel: {
    marginLeft: 8,
    fontSize: 14,
    color: '#1F2937',
    fontFamily: 'Poppins_400Regular',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 10,
  },
  submitButton: {
    backgroundColor: '#4C51BF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Poppins_400Regular',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
    fontFamily: 'Poppins_400Regular',
  },
  cancelButtonText: {
    color: '#1F2937',
    fontFamily: 'Poppins_400Regular',
  },
  sectionTitle: {
    fontSize: 16,
    marginBottom: 8,
    color: '#1F2937',
    fontFamily: 'Poppins_600SemiBold',
  },
  alertBox: {
    backgroundColor: '#E5E7EB',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  alertTitle: {
    fontSize: 16,
    marginBottom: 4,
    color: '#1F2937',
    fontFamily: 'Poppins_500Medium',
  },
  alertDesc: {
    fontSize: 14,
    color: '#4B5563',
    fontFamily: 'Poppins_400Regular',
  },
  selectBox: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 8,
    marginBottom: 10,
  },
  selectItem: {
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  selectItemText: {
    fontSize: 14,
    color: '#1F2937',
    fontFamily: 'Poppins_400Regular',
  },
});