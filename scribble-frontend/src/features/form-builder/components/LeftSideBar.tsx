import React, { FunctionComponent, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
// import { Plus } from 'lucide-react-native';
import {
  Form<PERSON>ontainerList,
  FormControlList,
} from 'src/features/form-builder/utils/FormBuilderUtils';
import ControlDragComponent from './ControlDragComponent';
import {
  FormLayoutComponentChildrenType,
  FormLayoutComponentContainerType,
  FormLayoutComponentsType,
} from 'src/features/form-builder/types/FormTemplateTypes';
import { screenHeight } from 'src/utils/ScreenUtils';
import { AntDesign } from '@expo/vector-icons';

interface LeftSidebarProps {
  handleItemAdded: (
    item: FormLayoutComponentChildrenType | FormLayoutComponentContainerType,
    containerId?: string,
  ) => void;
  formLayoutComponents: FormLayoutComponentsType[];
}

const LeftSidebar: FunctionComponent<LeftSidebarProps> = ({
  handleItemAdded,
  formLayoutComponents,
}) => {
  const [hovered, setHovered] = useState(false);

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.innerContainer}>
          <TouchableOpacity
            style={[styles.addSectionContainer, hovered && styles.hover]}
            onPress={() => handleItemAdded(FormContainerList[0])}
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
          >
            <AntDesign name="plus" size={20} color="#4C51BF" />
            {/* <Plus size={20} color="#4C51BF" /> */}
            <Text style={styles.addSectionText}> Add Section </Text>
          </TouchableOpacity>

          <Text style={styles.sectionTitle}>Question Types</Text>

          <View style={styles.questionTypeContainer}>
            {FormControlList.map(control => (
              <ControlDragComponent
                item={control}
                handleItemAdded={handleItemAdded}
                formLayoutComponents={formLayoutComponents}
              />
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default LeftSidebar;

const styles = StyleSheet.create({
  container: {
    width: 240,
    backgroundColor: '#FFFFFF',
    borderRightColor: '#E2E8F0',
    borderRightWidth: 1,
    height: screenHeight - 65,
    justifyContent: 'center',
    display: 'flex',
  },
  scrollContainer: {
    display: 'flex',
  },
  innerContainer: {
    justifyContent: 'center',
    display: 'flex',
    padding: 20,
  },
  sectionTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
    marginVertical: 16,
  },
  addSectionContainer: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#4C51BF',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    padding: 10,
    alignSelf: 'center',
  },
  addSectionText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: '#4C51BF',
  },
  questionTypeContainer: {
    flexDirection: 'column',
    gap: 12,
  },
  column: {
    width: '48%', // Equivalent to Bootstrap's col-6 with some spacing
    marginBottom: 16,
  },
  spacer: {
    height: 30,
  },
  hover: {
    transform: [{ scale: 1.01 }],
  },
});
