import React, { FC, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import Checkbox from 'expo-checkbox';
import { Picker } from '@react-native-picker/picker';
// import { Trash2 } from 'lucide-react-native';
import {
  FormLayoutComponentsType,
  FormLayoutComponentChildrenType,
  FormLayoutItemType,
} from '../types/FormTemplateTypes';
import { FormControlNames } from '../utils/FormBuilderUtils';
import { Feather } from '@expo/vector-icons';

interface ScoreCalculatorComponentProps {
  item: FormLayoutComponentChildrenType;
  formLayoutComponents: FormLayoutComponentsType[];
  onChange?: (updatedItem: FormLayoutComponentChildrenType) => void;
}

const ScoreCalculatorComponent: FC<ScoreCalculatorComponentProps> = ({
  item,
  formLayoutComponents,
  onChange,
}) => {
  const [calculationType, setCalculationType] = useState<'sum' | 'average'>(
    item.calculationType || 'sum'
  );
  const [displayFormat, setDisplayFormat] = useState(item.displayFormat || '');
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>(
    item.selectedQuestions || []
  );
  const [questionScores, setQuestionScores] = useState(
    item.questionScores || {}
  );
  const [selectedDropdownQuestion, setSelectedDropdownQuestion] = useState<string>('');
  // Get all available questions that can be used for scoring
  const getAvailableQuestions = (): FormLayoutComponentChildrenType[] => {
    const availableQuestions: FormLayoutComponentChildrenType[] = [];
    
    const processItems = (items: FormLayoutItemType[]) => {
      items.forEach(itemObj => {
        if (!itemObj.isContainer) {
          const question = itemObj as FormLayoutComponentChildrenType;
          // Only include questions with selectable options
          if ([
            FormControlNames.CHECKLIST,
            FormControlNames.RADIOGROUP,
            FormControlNames.SELECTDROPDOWN,
          ].includes(question.controlName!) && 
          question.id !== item.id) { // Don't include the current score calculator
            availableQuestions.push(question);
          }
        } else {
          // Recursively process nested containers
          const container = itemObj as any; // FormLayoutContainerType
          if (container.items) {
            processItems(container.items);
          }
        }
      });
    };

    formLayoutComponents.forEach(layout => {
      if(layout.container.id === item.containerId){
        if (layout.items) {
          processItems(layout.items);
        }
      }
    });

    return availableQuestions;
  };

  const availableQuestions = getAvailableQuestions();

  // Add question from dropdown
  const addQuestionFromDropdown = () => {
    if (selectedDropdownQuestion && !selectedQuestions.includes(selectedDropdownQuestion)) {
      const newSelectedQuestions = [...selectedQuestions, selectedDropdownQuestion];
      setSelectedQuestions(newSelectedQuestions);
      
      // Initialize scores for the new question
      const question = availableQuestions.find(q => q.id === selectedDropdownQuestion);
      if (question && question.items) {
        const newQuestionScores = {
          ...questionScores,
          [selectedDropdownQuestion]: {}
        };
        // Initialize all options with 0 score
        question.items.forEach(option => {
          newQuestionScores[selectedDropdownQuestion][option.value] = 0;
        });
        setQuestionScores(newQuestionScores);
        updateItem(newSelectedQuestions, newQuestionScores);
      }
      
      setSelectedDropdownQuestion('');
    }
  };

  // Remove question from selected list
  const removeQuestion = (questionId: string) => {
    const newSelectedQuestions = selectedQuestions.filter(id => id !== questionId);
    setSelectedQuestions(newSelectedQuestions);
    
    // Clean up scores for removed question
    const newQuestionScores = { ...questionScores };
    delete newQuestionScores[questionId];
    
    setQuestionScores(newQuestionScores);
    updateItem(newSelectedQuestions, newQuestionScores);
  };

  // Handle score assignment for question options
  const updateOptionScore = (questionId: string, optionValue: string, score: string) => {
    const numericScore = parseFloat(score) || 0;
    
    const newQuestionScores = {
      ...questionScores,
      [questionId]: {
        ...questionScores[questionId],
        [optionValue]: numericScore,
      }
    };
    
    setQuestionScores(newQuestionScores);
    updateItem(selectedQuestions, newQuestionScores);
  };

  // Handle calculation type change
  const handleCalculationTypeChange = (type: 'sum' | 'average') => {
    setCalculationType(type);
    updateItem();
  };

  // Handle display format change
  const handleDisplayFormatChange = (format: string) => {
    // Validate format (should be like .1, .2, .12, etc.)
    // const formatRegex = /^\.?\d{0,2}$/;
    // if (format === '' || formatRegex.test(format)) {
    //   setDisplayFormat(format);
    //   updateItem();
    // }
    const formatRegex =  /^\.[xX]{1,2}$/;
    if (
      format === '' ||              // Empty is allowed
      format === '.' ||             // Just dot is okay during typing
      /^\.[xX]?$/.test(format) ||   // Allow ".x" or ".X" in progress
      formatRegex.test(format)      // Final valid formats
    ){
        setDisplayFormat(format);
        updateItem();
      }
  };

  // Update the parent item
  const updateItem = (
    newSelectedQuestions = selectedQuestions,
    newQuestionScores = questionScores
  ) => {
    if (onChange) {
      const updatedItem = {
        ...item,
        calculationType,
        displayFormat,
        selectedQuestions: newSelectedQuestions,
        questionScores: newQuestionScores,
      };
      onChange(updatedItem);
    }
  };

  // Effect to update parent when local state changes
  useEffect(() => {
    updateItem();
  }, [calculationType, displayFormat]);

  // Get question by ID
  const getQuestionById = (questionId: string) => {
    return availableQuestions.find(q => q.id === questionId);
  };

  // Get unselected questions for dropdown
  const getUnselectedQuestions = () => {
    return availableQuestions.filter(q => !selectedQuestions.includes(q.id));
  };

  const renderDropdown = () => {
    const unselectedQuestions = getUnselectedQuestions();
    
    if (Platform.OS === 'web') {
      return (
        <View style={styles.dropdownContainer}>
          <select
            style={styles.webSelect}
            value={selectedDropdownQuestion}
            onChange={(e) => setSelectedDropdownQuestion(e.target.value)}
          >
            <option value="">Select a question to add...</option>
            {unselectedQuestions.map(question => (
              <option key={question.id} value={question.id}>
                {question.questionCode || 'Q'}: {question.labelName}
              </option>
            ))}
          </select>
          <TouchableOpacity
            style={[styles.addButton, !selectedDropdownQuestion && styles.addButtonDisabled]}
            onPress={addQuestionFromDropdown}
            disabled={!selectedDropdownQuestion}
          >
            <Text style={[styles.addButtonText, !selectedDropdownQuestion && styles.addButtonTextDisabled]}>
              Add Question
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.dropdownContainer}>
        <View style={styles.pickerContainer}>
          <Picker
            selectedValue={selectedDropdownQuestion}
            onValueChange={setSelectedDropdownQuestion}
            style={styles.picker}
          >
            <Picker.Item label="Select a question to add..." value="" />
            {unselectedQuestions.map(question => (
              <Picker.Item
                key={question.id}
                label={`${question.questionCode || 'Q'}: ${question.labelName}`}
                value={question.id}
              />
            ))}
          </Picker>
        </View>
        <TouchableOpacity
          style={[styles.addButton, !selectedDropdownQuestion && styles.addButtonDisabled]}
          onPress={addQuestionFromDropdown}
          disabled={!selectedDropdownQuestion}
        >
          <Text style={[styles.addButtonText, !selectedDropdownQuestion && styles.addButtonTextDisabled]}>
            Add Question
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.sectionTitle}>Score Calculator Configuration</Text>
      
      {/* Calculation Type Selection */}
      <View style={styles.section}>
        <Text style={styles.label}>Calculation Method</Text>
        <View style={styles.radioGroup}>
          <TouchableOpacity
            style={styles.radioOption}
            onPress={() => handleCalculationTypeChange('sum')}
          >
            <View style={[
              styles.radio,
              calculationType === 'sum' && styles.radioSelected
            ]}>
              {calculationType === 'sum' && <View style={styles.radioInner} />}
            </View>
            <Text style={styles.radioLabel}>Sum</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.radioOption}
            onPress={() => handleCalculationTypeChange('average')}
          >
            <View style={[
              styles.radio,
              calculationType === 'average' && styles.radioSelected
            ]}>
              {calculationType === 'average' && <View style={styles.radioInner} />}
            </View>
            <Text style={styles.radioLabel}>Average</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Display Format */}
      <View style={styles.section}>
        <Text style={styles.label}>Display Format</Text>
        <Text style={styles.helperText}>
          Enter decimal places (e.g., '.XX' for 2 decimal places, leave blank for whole numbers)
        </Text>
        <TextInput
          style={styles.textInput}
          value={displayFormat}
          onChangeText={handleDisplayFormatChange}
          placeholder="e.g., .X or .XX"
          maxLength={3}
        />
      </View>

      {/* Question Selection Dropdown */}
      <View style={styles.section}>
        <Text style={styles.label}>Add Questions to Score</Text>
        {availableQuestions.length === 0 ? (
          <Text style={styles.noQuestionsText}>
            No questions with selectable options found. Add Checklist, Radio, or Dropdown questions first.
          </Text>
        ) : (
          renderDropdown()
        )}
      </View>

      {/* Selected Questions with Scores */}
      {selectedQuestions.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.label}>Selected Questions & Scores</Text>
          {selectedQuestions.map(questionId => {
            const question = getQuestionById(questionId);
            if (!question) return null;

            return (
              <View key={questionId} style={styles.selectedQuestionItem}>
                <View style={styles.questionHeader}>
                  <Text style={styles.questionTitle}>
                    {question.questionCode || 'Q'}: {question.labelName}
                  </Text>
                  <TouchableOpacity
                    onPress={() => removeQuestion(questionId)}
                    style={styles.deleteButton}
                  >
                    <Feather name="trash-2" size={18} color="#EF4444" />
                    {/* <Trash2 size={18} color="#E53E3E" /> */}
                  </TouchableOpacity>
                </View>
                
                {question.items && (
                  <View style={styles.optionsContainer}>
                    <Text style={styles.optionsTitle}>Assign Points to Options:</Text>
                    {question.items.map(option => (
                      <View key={option.id} style={styles.optionItem}>
                        <Text style={styles.optionLabel}>{option.label}:</Text>
                        <TextInput
                          style={styles.scoreInput}
                          value={
                            questionScores[questionId]?.[option.value]?.toString() || '0'
                          }
                          onChangeText={(text) => 
                            updateOptionScore(questionId, option.value, text)
                          }
                          keyboardType="numeric"
                          placeholder="0"
                        />
                      </View>
                    ))}
                  </View>
                )}
              </View>
            );
          })}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#1F2937',
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#374151',
  },
  helperText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  radioGroup: {
    flexDirection: 'row',
    gap: 16,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#4C51BF',
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioSelected: {
    borderColor: '#4C51BF',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4C51BF',
  },
  radioLabel: {
    fontSize: 16,
    color: '#374151',
  },
  noQuestionsText: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 16,
  },
  // New dropdown styles
  dropdownContainer: {
    marginBottom: 16,
  },
  webSelect: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 12,
    width: '100%',
    backgroundColor: '#FFFFFF',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: '#FFFFFF',
  },
  picker: {
    height: 50,
  },
  addButton: {
    backgroundColor: '#4C51BF',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  addButtonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  addButtonTextDisabled: {
    color: '#9CA3AF',
  },
  // Selected question styles
  selectedQuestionItem: {
    marginBottom: 16,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionTitle: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '600',
    flex: 1,
  },
  deleteButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: '#FEE2E2',
  },
  optionsContainer: {
    marginTop: 8,
  },
  optionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#4B5563',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingVertical: 4,
  },
  optionLabel: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
  },
  scoreInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
    width: 80,
    textAlign: 'center',
    backgroundColor: '#FFFFFF',
  },
});

export default ScoreCalculatorComponent;