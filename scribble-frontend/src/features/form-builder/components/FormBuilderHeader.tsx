import { AntDesign } from '@expo/vector-icons';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
// import { ChevronLeft, Eye, Save } from 'lucide-react-native';

const FormBuilderHeader = ({ handleBack, onSave, onPreview, onPublish,visiblePublish=true }) => (
  <View style={styles.header}>
    <TouchableOpacity style={styles.backButton} onPress={handleBack}>
      {/* <ChevronLeft color="#1F2937" size={24} /> */}
      <AntDesign name="leftcircleo" size={24} color="#1F2937" />
    </TouchableOpacity>
    <Text style={styles.headerTitle}>Form Builder</Text>
    <View style={styles.headerButtons}>
      <TouchableOpacity style={styles.previewButton} onPress={onPreview}>
        <Text style={styles.previewButtonText}>Preview</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.saveButton} onPress={onSave}>
        {/*<Save color="#FFFFFF" size={18} />*/}
        <Text style={styles.saveButtonText}>Save</Text>
      </TouchableOpacity>
      {visiblePublish && 
      <TouchableOpacity style={styles.saveButton} onPress={onPublish}>
        {/*<Eye color="#FFFFFF" size={18} />*/}
        <Text style={styles.saveButtonText}>Publish</Text>
      </TouchableOpacity>
      }
    </View>
  </View>
);

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    height: 65,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    position: 'relative',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 16,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    color: '#1F2937',
    fontFamily: 'Poppins_600SemiBold',
  },
  headerButtons: {
    position: 'absolute',
    right: 16,
    flexDirection: 'row',
    gap: 12,
  },
  previewButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#f3f4f6',
    minWidth: 80,
  },
  previewButtonText: {
    color: '#374151',
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#4f46e5',
    minWidth: 80,
  },
  saveButtonText: {
    color: '#fff',
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
  },
});

export default FormBuilderHeader;
