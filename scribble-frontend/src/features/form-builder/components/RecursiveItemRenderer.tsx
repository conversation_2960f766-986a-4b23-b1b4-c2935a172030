import React, { FC, useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import RenderItem from './RenderItem';
import { FormLayoutComponentChildrenType } from 'src/features/form-builder/types/FormTemplateTypes';
import CustomTooltip from '@/src/components/atoms/customTooltip/CustomTooltip';
import { Poppins_500Medium } from '@expo-google-fonts/poppins';

interface RecursiveItemRendererProps {
  item: any;
  level: number;
  onItemChange?: (updatedItem: FormLayoutComponentChildrenType) => void;
  onAnswerUpdate?: () => void;
  allAnswers?: Record<string, string[]>;
  containerItems?: FormLayoutComponentChildrenType[];
  isGreyedOut?: boolean;
  isDisabledFields?: boolean;
}

const RecursiveItemRenderer: FC<RecursiveItemRendererProps> = ({
  item,
  level,
  onItemChange,
  onAnswerUpdate,
  allAnswers = {},
  containerItems = [],
  isGreyedOut = false,
  isDisabledFields=false,
}) => {
  // Handle subcontainer type items
  let hasContext = false;

  // console.log("answer_context :",item?.answer_context)
  if (item?.answer_context && Array.isArray( item?.answer_context) && item?.answer_context[0] != "" && item?.answer_context[0] !== "Not Available") {
    hasContext = true;
  }
  if (item.itemType === 'subcontainer' && item.isContainer) {
    return (
      <View style={[styles.subcontainer, { marginLeft: level * 12 }]}>
        {/* Subcontainer header */}
        <View style={styles.subcontainerHeader}>
          <Text style={styles.subcontainerHeading}>
            {item.heading}{item.subHeading ? `: ${item.subHeading}` : ''}
          </Text>
        </View>

        {/* Render all items within this subcontainer */}
        {item.items && item.items.map((childItem: any) => (
          <RecursiveItemRenderer
            key={childItem.id}
            item={childItem}
            level={childItem.level || level + 1}
            onItemChange={onItemChange}
            onAnswerUpdate={onAnswerUpdate}
            allAnswers={allAnswers}
            containerItems={containerItems}
            isDisabledFields={isDisabledFields}
          />
        ))}
      </View>
    );
  }

  // Function to handle item changes
  const handleItemChange = (updatedItem: FormLayoutComponentChildrenType) => {
    if (onItemChange) {
      onItemChange(updatedItem);
    }
  };


  const [showTip, setTip] = useState(false);
  // Handle regular control items
  return (
    <View key={item.id} style={[
      styles.childContainer,
      { marginLeft: level * 12 },
      isGreyedOut && styles.greyedOutContainer
    ]}>
      <Text style={[
        styles.questionCode,
        isGreyedOut && styles.greyedOutText
      ]}>{item.questionCode}</Text>
      <View style={styles.labelContainer}>

        <Text style={[
          styles.childLabel,
          isGreyedOut && styles.greyedOutText
        ]}>
          {item.labelName}
          {item.required ? ' *' : ''}
        </Text>
        {/* Info icon */}

        {hasContext &&
          <CustomTooltip
            content={item.answer_context}>
            <Ionicons name="information-circle-outline" size={22} color="#6B7280" />
          </CustomTooltip>
        }
      </View>

      {!!item.description && (
        <Text style={[
          styles.childDescription,
          isGreyedOut && styles.greyedOutText
        ]}>{item.description}</Text>
      )}

      <RenderItem
        item={item}
        onChange={handleItemChange}
        onAnswerUpdate={onAnswerUpdate}
        allAnswers={allAnswers}
        containerItems={containerItems}
        disabled={isGreyedOut}
        isDisabledFields={isDisabledFields}
      />
    </View>


  );
};

const styles = StyleSheet.create({
  questionCode: {
    color: '#7C7887',
    fontFamily: 'Poppins_500Medium',
    fontSize: 12,
  },
  subcontainer: {
    marginBottom: 20,
    borderLeftWidth: 2,
    borderLeftColor: '#E2E8F0',
    paddingLeft: 10,
    // Ensure proper stacking context for nested items
    position: 'relative',
  },
  subcontainerHeader: {
    marginBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    paddingBottom: 8,
  },
  subcontainerHeading: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
  },
  childContainer: {
    marginTop: 20,
    // marginBottom: 16,
    // Establish positioning context
    position: 'relative',
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  childLabel: {
    fontFamily: 'Poppins_500Medium',
    fontSize: 16,
    color: '#251F38',
    flex: 1,
  },
  infoIcon: {
    paddingLeft: 8,
    // Make sure the icon is easily tappable
    padding: 5,
    marginRight: -5,
  },
  childDescription: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#7C7887',
    marginBottom: 6,
  },

  tooltip: {
    borderRadius: 8,
    padding: 0,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tooltipContainer: {
    width: 300,
  },
  tooltipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 1,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tooltipTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
  },
  tooltipContent: {
    paddingHorizontal: 6,
    paddingVertical: 10,
  },
  tooltipText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  greyedOutContainer: {
    //opacity: 0.6,
  },
  greyedOutText: {
    //color: '#9CA3AF',
    //opacity: 0.6,
  },



});

export default RecursiveItemRenderer;