import React, { FunctionComponent, useState } from 'react';
import { Text, View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useDrag } from 'react-dnd';
import {
  FormLayoutComponentChildrenType,
  FormLayoutComponentContainerType,
} from '../types/FormTemplateTypes';

interface ControlDragComponentProps {
  handleItemAdded: (
    item: FormLayoutComponentChildrenType | FormLayoutComponentContainerType,
    containerId?: string,
    subcontainerId?: string,
  ) => void;
  item: FormLayoutComponentChildrenType | FormLayoutComponentContainerType;
}

const ControlDragComponent: FunctionComponent<ControlDragComponentProps> = ({
  item,
  handleItemAdded,
}) => {
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: item.itemType,
      item: () => {
        // Create a fresh copy of the item each time it's dragged
        // This is crucial for allowing multiple instances of the same control
        return {
          ...JSON.parse(JSON.stringify(item)),
          // Don't generate an ID here - let the container handle it
          // Temporarily use a timestamp to ensure unique reference
          _tempId: Date.now()
        };
      },
      collect: monitor => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [item], // Only depend on the item structure, not on any IDs
  );

  // const { icon: Icon } = item;

  const [hovered, setHovered] = useState(false);

  // Handle click to add item directly (for container types)
  const handleClick = () => {
    if (item.itemType === 'container') {
      handleItemAdded(item);
    }
  };

  return (
    <View
      ref={drag}
      style={[
        styles.draggableItem,
        isDragging && styles.dragging,
        hovered && styles.hover,
      ]}
    >
      <TouchableOpacity
        style={styles.touchable}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        onPress={handleClick}
      >
        <Image source={item.icon} /> 
        {/* <Icon size={20} color="#6b7280" /> */}
        <Text style={styles.text}>{item.displayText}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ControlDragComponent;

const styles = StyleSheet.create({
  draggableItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    cursor: 'pointer',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dragging: {
    opacity: 0.5,
  },
  touchable: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    fontSize: 20,
    marginRight: 8,
  },
  text: {
    marginLeft: 12,
    fontSize: 14,
    color: '#374151',
    fontFamily: 'Poppins_400Regular',
  },
  hover: {
    backgroundColor: '#F1F5F9',
    transform: [{ scale: 1.01 }],
  },
});