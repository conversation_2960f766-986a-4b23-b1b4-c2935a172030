import React, { Component } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { FormLayoutComponentsType } from 'src/features/form-builder/types/FormTemplateTypes';
import StepperFormPreview from 'src/features/form-builder/components/StepperFormPreview';
// import { X } from 'lucide-react-native';

interface FormPreviewProps {
  screenType: string;
  showPreview: boolean;
  closePreviewDrawer: () => void;
  formLayoutComponents: FormLayoutComponentsType[];
}

interface FormPreviewStates {
  screenType: string;
}

export default class FormPreview extends Component<
  FormPreviewProps,
  FormPreviewStates
> {
  constructor(props: FormPreviewProps) {
    super(props);
    this.state = {
      screenType: this.props.screenType || 'mobile',
    };
    this.handleCloseClick = this.handleCloseClick.bind(this);
  }

  handleCloseClick() {
    this.props.closePreviewDrawer();
  }

  render() {
    const { showPreview, formLayoutComponents } = this.props;
    const { screenType } = this.state;

    return (
      <Modal
        visible={showPreview}
        transparent={true}
        onRequestClose={this.handleCloseClick}
      >
        {/* Outer overlay */}
        <View style={styles.overlay}>
          {/* Drawer container on the right */}
          <View style={styles.drawerContainer}>
            <TouchableOpacity
              style={styles.closeIconContainer}
              onPress={this.handleCloseClick}
            >
              {/* <X color="#1F2937" size={24} /> */}
            </TouchableOpacity>

            {/* StepperFormPreview is placed here */}
            <View style={styles.deviceFrameContainer}>
              <ImageBackground
                source={require('assets/images/iphone14.png')}
                style={styles.deviceBezel}
                resizeMode="contain"
              >
                {/* The UI inside the bezel */}
                <StepperFormPreview
                  screenType={screenType}
                  formLayoutComponents={formLayoutComponents}
                />
              </ImageBackground>
            </View>
            {/*<View style={styles.previewContent}>*/}
            {/*<StepperFormPreview*/}
            {/*  screenType={screenType}*/}
            {/*  formLayoutComponents={formLayoutComponents}*/}
            {/*/>*/}
            {/*</View>*/}
          </View>
        </View>
      </Modal>
    );
  }
}

const { width } = Dimensions.get('window');
const drawerWidth = width * 0.8; // Adjust modal width

// Calculate height based on aspect ratio
const aspectRatio = 428 / 926;
const deviceHeight = width * aspectRatio;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)', // dim the background
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawerContainer: {
    // width: '60%',
    // height: '80%',
    // backgroundColor: '#f8f9fa',
    // padding: 16,
    // borderRadius: 8,
    // shadowColor: '#000',
    // shadowOffset: { width: -2, height: 0 },
    // shadowOpacity: 0.1,
    // shadowRadius: 6,
    // elevation: 5,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  closeIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 8,
    right: -24,
  },
  closeIcon: {
    fontSize: 18,
    color: '#1F2937',
    // You can replace with an actual icon if you'd like
  },
  titleText: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#1F2937',
    marginLeft: 8,
    marginTop: 8,
  },
  previewContent: {
    flex: 1,
    // Optional: add scrolling or additional styling
  },
  deviceFrameContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deviceBezel: {
    width: 428, // Use full width of the container
    height: 820, // Height calculated based on aspect ratio
    alignItems: 'center',
    justifyContent: 'center',
  },
});
