import React, { FunctionComponent } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useDrop } from 'react-dnd';
// import { Plus, MoreVertical, Trash2, ArrowUp, ArrowDown } from 'lucide-react-native';
import ControlViewComponent from './ControlViewComponent';
import RecursiveContainerComponent from './RecursiveContainerComponent';
import {
  FormLayoutComponentChildrenType,
  FormLayoutComponentContainerType,
  FormLayoutContainerType,
  FormLayoutItemType,
  SelectedScoreCalcualtorItemsType
} from '../types/FormTemplateTypes';
import {
  FormContainerList,
  FormControlNames,
  FormItemTypes,
} from 'src/features/form-builder/utils/FormBuilderUtils';
import { AntDesign, Feather } from '@expo/vector-icons';

interface DropContainerComponentProps {
  accept: string;
  name?: string;
  index: number;
  itemsLength: number;
  handleItemAdded?: (
    item: FormLayoutComponentChildrenType | FormLayoutContainerType,
    containerId: string,
    level: number,
  ) => void;
  layoutContainer: any;
  layout?: FormLayoutComponentContainerType;
  selectedControl?: any;
  items?: FormLayoutItemType[];  // Unified items array
  deleteContainer?: (containerId: string, parentContainerId?: string) => void;
  deleteSubContainer?: (containerId: string, parentContainerId: string) => void;
  deleteControl?: (controlId: string, containerId: string) => void;
  selectControl?: (layout: any) => void;
  moveContainer?: (containerIndex: number, targetIndex: number) => void;
  moveControl?: (
    item: FormLayoutItemType,
    dragIndex: number,
    hoverIndex: number,
    containerId: string,

  ) => void;
}

const DropContainerComponent: FunctionComponent<
  DropContainerComponentProps
> = props => {
  const {
    accept,
    layout,
    layoutContainer,
    items = [],
    index,
    itemsLength,
    deleteContainer,
    deleteSubContainer,
    deleteControl,
    selectControl,
    selectedControl,
    handleItemAdded,
    moveControl,
    moveContainer
  } = props;
  
  const [{ isOver }, drop] = useDrop(
    () => ({
      accept: [accept, FormItemTypes.SUBCONTAINER, FormItemTypes.CONTROL],
      drop: (item, monitor) => {
        // Stop event propagation if we're handling this in a child already
        if (monitor.didDrop()) {
          return;
        }

        // console.log('Dropping in main container:', layout?.id, 'Item:', item);

        // Only handle drops of new items (those without an ID)
        if (!item.id && layout?.id) {
          if (item.itemType === FormItemTypes.SUBCONTAINER) {
            // console.log('Adding new subcontainer to container', layout.id);
            handleItemAdded && handleItemAdded(item, layout.id, 1); // Level 1 for direct subcontainers
          } else if (item.itemType === FormItemTypes.CONTROL) {
            // console.log('Adding new control to container', layout.id);
            handleItemAdded && handleItemAdded(item, layout.id, 0); // Level 0 for main container controls
          }
        }

        return { name: layout?.id, level: 0 }; // Level 0 for main container
      },
      collect: monitor => ({
        isOver: monitor.isOver(),
      }),
    }),
    [layout, handleItemAdded],
  );

  const backgroundColor = isOver ? '#F1F5F9' : '#FFFFFF';
  const borderColor =
    selectedControl?.id === layout?.id ? 'rgb(255, 193, 7)' : '#E5E7EB';

  const renderDropQuestionContainer = () => {
    return (
      <View style={styles.dropField}>
        {/* <Plus size={24} color="#4C51BF" /> */}
        <Text style={styles.dropFieldText}> Drag and drop items here </Text>
      </View>
    );
  };

  if (accept === FormItemTypes.CONTAINER) {
    return (
      <TouchableOpacity
        style={styles.addButton}
        onPress={() =>
          handleItemAdded && handleItemAdded({ ...FormContainerList[0] }, '', 0)
        }
      >
        {/* <Plus size={20} color="#4C51BF" /> */}
        <Text style={styles.addText}>Add New Section</Text>
      </TouchableOpacity>
    );
  }

  // Helper function to render an item based on its type
  const renderItem = (item: FormLayoutItemType, index: number,items:FormLayoutItemType[]) => {
    let selectedScoreItems:SelectedScoreCalcualtorItemsType[]=[]
    if(item.controlName ===FormControlNames.SCORECALCULATOR && item?.selectedQuestions.length>0){
      const targetIds = new Set(item.selectedQuestions);
      selectedScoreItems=items
      .filter(item => targetIds.has(item.id))
      .map(({ id, questionCode, labelName }) => ({
        id,
        questionCode,
        labelName
      }));
      // console.log("selectedScoreItems : "+JSON.stringify(selectedScoreItems))
    }
    if (item) {
      if (item.isContainer ==undefined || item.isContainer) {
        // Render a container using the recursive component
        return (
          <RecursiveContainerComponent
            key={item.id}
            container={item as FormLayoutContainerType}
            parentContainerId={layout?.id}
            selectedControl={selectedControl}
            handleItemAdded={handleItemAdded}
            deleteContainer={deleteContainer}
            deleteSubContainer={deleteSubContainer} // Make sure this is passed down
            deleteControl={deleteControl}
            selectControl={selectControl}
            index={index}
            itemsLength={items.length}
            moveControl={moveControl}
            selectedScoreItems={selectedScoreItems}
            nestingLevel={1}
          />
        );
      } else {
        // Render a regular control
        return (
          <ControlViewComponent
            key={item.id}
            item={item as FormLayoutComponentChildrenType}
            deleteControl={(controlId) =>
              deleteControl && deleteControl(controlId, layout?.id as string)
            }
            selectControl={layout =>
              selectControl && selectControl(layout)
            }
            selectedControl={selectedControl}
            containerId={layout?.id as string}
            index={index}
            itemsLength={items.length}
            selectedScoreItems={selectedScoreItems}
            moveControl={(item, dragIndex, hoverIndex) => {

              moveControl &&
                moveControl(item, dragIndex, hoverIndex, layout?.id as string)
            }
            }
          />
        );
      }
    }
  };
  const movePosition = (targetPosition: number) => {
    moveContainer(index, targetPosition);
  }
  return (
    <View
      ref={drop}
      style={[styles.containerDrop, { backgroundColor, borderColor }]}
    >
      {/* Controls and Containers */}
      {accept === FormItemTypes.CONTROL && (
        <>
          <TouchableOpacity
            onPress={() => selectControl && selectControl(layout)}
            style={styles.containerHeader}
          >
            <View style={styles.titleContainer}>
              <Text style={styles.stepTitle}>
                {layout?.heading}
              </Text>
              <Text style={styles.space}>:</Text>
              <Text style={styles.heading}>
                {layout?.subHeading}
              </Text>
            </View>
            <View style={styles.containerActions}>
              {index > 0 &&
                <TouchableOpacity style={styles.iconRound} onPress={() => movePosition(index - 1)}>
                  <AntDesign name="arrowup" size={16} color="#4B5563" />
                  {/* <ArrowUp size={16} color="#4B5563" /> */}
                </TouchableOpacity>
              }
              {index < itemsLength - 1 &&
                <TouchableOpacity style={styles.iconRound} onPress={() => movePosition(index + 1)}>
                  {/* <ArrowDown size={16} color="#4B5563" /> */}
                  <AntDesign name="arrowdown" size={16} color="#4B5563" />
                </TouchableOpacity>
              }
              <TouchableOpacity
                onPress={() =>
                  deleteContainer && layout?.id && deleteContainer(layout.id)
                }
              >
                <Feather name="trash-2" size={20} color="#E53E3E" />
                {/* <Trash2 size={20} color="#E53E3E" /> */}
              </TouchableOpacity>
            </View>
          </TouchableOpacity>

          <View style={styles.itemsContainer}>
            {/* Render all items in the exact order they were added */}
            {items.length === 0
              ? renderDropQuestionContainer()
              : items.map((item, index) => renderItem(item, index,items))
            }

            {/* Always show an additional drop area */}
            {items.length > 0 && (
              <View style={styles.additionalDropArea}>
                <AntDesign name="plus" size={16} color="#4C51BF" />
                {/* <Plus size={16} color="#4C51BF" /> */}
                <Text style={styles.additionalDropText}>Drop more items here</Text>
              </View>
            )}
          </View>
        </>
      )}
    </View>
  );
};

export default DropContainerComponent;

const styles = StyleSheet.create({
  containerDrop: {
    width: '100%',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: '#E5E7EB',
  },
  addContainer: {
    minHeight: 90,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconRound: {
    padding: 6,
    marginLeft: 8,
    backgroundColor: "#ccc9c9",
    borderRadius: 18,

  },
  addButton: {
    width: '100%',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#4C51BF',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    padding: 10,
    alignSelf: 'center',
  },
  addText: {
    fontFamily: 'Poppins_500Medium',
    fontSize: 16,
    color: '#4C51BF',
    marginLeft: 8,
  },
  containerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomColor: 'rgba(0,0,0,0.1)',
    borderBottomWidth: 1,
    paddingBottom: 12,
    marginBottom: 12,
  },
  titleContainer: {
    display: 'flex',
    flexDirection: 'row',
  },
  stepTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
  },
  space: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
    marginHorizontal: 4,
  },
  containerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  heading: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 4,
  },
  subHeading: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 16,
  },
  itemsContainer: {
    minHeight: 150,
    width: '100%',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  dropField: {
    width: '100%',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 2,
    borderColor: '#4C51BF',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    padding: 40,
    alignSelf: 'center',
    backgroundColor: 'rgba(76, 81, 191, 0.05)',
  },
  dropFieldText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 16,
    color: '#4C51BF',
  },
  additionalDropArea: {
    width: '100%',
    borderRadius: 8,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#4C51BF',
    padding: 12,
    marginTop: 16,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    backgroundColor: 'rgba(76, 81, 191, 0.02)',
  },
  additionalDropText: {
    fontFamily: 'Poppins_400Regular',
    fontSize: 14,
    color: '#4C51BF',
    marginLeft: 8,
  },
});