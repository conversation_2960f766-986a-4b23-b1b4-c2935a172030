import React, { FunctionComponent } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Platform } from 'react-native';
import { useDrop } from 'react-dnd';
// import { Plus, MoreVertical, Trash2, ChevronDown, ChevronUp, ArrowUp, ArrowDown } from 'lucide-react-native';
import ControlViewComponent from './ControlViewComponent';
import {
    FormLayoutComponentChildrenType,
    FormLayoutContainerType,
    FormLayoutItemType,
    SelectedScoreCalcualtorItemsType
} from '../types/FormTemplateTypes';
import {
    FormItemTypes,
} from 'src/features/form-builder/utils/FormBuilderUtils';
import { AntDesign, Feather } from '@expo/vector-icons';

interface RecursiveContainerComponentProps {
    container: FormLayoutContainerType;
    selectedControl?: any;
    parentContainerId?: string;
    handleItemAdded?: (
        item: FormLayoutComponentChildrenType | FormLayoutContainerType,
        containerId: string,
        level: number,
    ) => void;
    deleteContainer?: (containerId: string, parentContainerId?: string) => void;
    deleteSubContainer?: (containerId: string, parentContainerId: string) => void;
    deleteControl?: (controlId: string, containerId: string) => void;
    selectControl?: (layout: any) => void;
    index: number;
    itemsLength: number;
    moveControl?: (
        item: FormLayoutItemType,
        dragIndex: number,
        hoverIndex: number,
        containerId: string,
    ) => void;
    nestingLevel?: number;
    selectedScoreItems?:SelectedScoreCalcualtorItemsType[]
}

const RecursiveContainerComponent: FunctionComponent<RecursiveContainerComponentProps> = props => {
    const {
        container,
        parentContainerId,
        handleItemAdded,
        deleteContainer,
        deleteSubContainer,
        deleteControl,
        selectControl,
        selectedControl,
        index,
        itemsLength,
        moveControl,
        nestingLevel = 0,
        selectedScoreItems,
    } = props;

    const [collapsed, setCollapsed] = React.useState(false);
    const [{ isOver }, drop] = useDrop(
        () => ({
            accept: [FormItemTypes.CONTROL, FormItemTypes.SUBCONTAINER],
            drop: (item, monitor) => {
                // Stop event propagation so parent containers don't also handle it
                if (monitor.didDrop()) {
                    return;
                }

                // console.log(`Dropping in container level ${nestingLevel}:`, container.id);

                // Only handle drops of new items (those without an ID)
                if (!item.id && handleItemAdded) {
                    if (item.itemType === FormItemTypes.SUBCONTAINER) {
                        console.log('Adding new subcontainer to container', container.id);
                        handleItemAdded(item, container.id, nestingLevel + 1);
                    } else if (item.itemType === FormItemTypes.CONTROL) {
                        console.log('Adding new control to container', container.id);
                        handleItemAdded(item, container.id, nestingLevel);
                    }
                }

                return { name: container.id, level: nestingLevel };
            },
            collect: monitor => ({
                isOver: monitor.isOver(),
            }),
        }),
        [container.id, nestingLevel, handleItemAdded],
    );

    // Calculate background color based on nesting level
    const getBackgroundColor = () => {
        if (isOver) {
            return '#F1F5F9'; // Highlight color when dragging over
        }

        // Different background colors based on nesting level
        const baseColors = ['#FFFFFF', '#FAFAFA', '#F5F5F5', '#F0F0F0', '#EBEBEB'];
        const colorIndex = Math.min(nestingLevel, baseColors.length - 1);
        return baseColors[colorIndex];
    };

    // Calculate border color based on selection and nesting level
    const getBorderColor = () => {
        if (selectedControl?.id === container.id) {
            return 'rgb(255, 193, 7)'; // Selected color
        }

        // Border colors based on nesting level
        const borderColors = ['#E5E7EB', '#D1D5DB', '#9CA3AF', '#6B7280', '#4B5563'];
        const colorIndex = Math.min(nestingLevel, borderColors.length - 1);
        return borderColors[colorIndex];
    };

    const renderDropQuestionContainer = () => {
        return (
            <View style={styles.dropField}>
                <AntDesign name="plus" size={24} color="#4C51BF" />
                {/* <Plus size={24} color="#4C51BF" /> */}
                <Text style={styles.dropFieldText}>
                    Drag and drop items here
                </Text>
            </View>
        );
    };

    // Helper function to render an item based on its type
    const renderItem = (item: FormLayoutItemType, indexNumber: number, itemsLength: number) => {
        if (item.isContainer) {
            // Recursively render a nested container
            return (
                <RecursiveContainerComponent
                    key={item.id}
                    container={item as FormLayoutContainerType}
                    parentContainerId={container.id}
                    selectedControl={selectedControl}
                    handleItemAdded={handleItemAdded}
                    deleteContainer={deleteContainer}
                    deleteSubContainer={deleteSubContainer}
                    deleteControl={deleteControl}
                    selectControl={selectControl}
                    index={indexNumber}
                    itemsLength={itemsLength}
                    moveControl={moveControl}
                    nestingLevel={nestingLevel + 1}
                    selectedScoreItems={selectedScoreItems}
                />
            );
        } else {
            // Render a regular control
            return (
                <ControlViewComponent
                    key={item.id}
                    item={item as FormLayoutComponentChildrenType}
                    deleteControl={(controlId) =>
                        deleteControl &&
                        deleteControl(controlId, container.id)
                    }
                    selectControl={layout =>
                        selectControl && selectControl(layout)
                    }
                    selectedControl={selectedControl}
                    containerId={container.id}
                    index={indexNumber}
                    itemsLength={itemsLength}
                    selectedScoreItems={selectedScoreItems}
                    moveControl={(item, dragIndex, hoverIndex) =>
                        moveControl &&
                        moveControl(item, dragIndex, hoverIndex, container.id)
                    }
                />
            );
        }
    };

    // Left padding for indentation based on nesting level
    const levelPadding = {
        paddingLeft: nestingLevel > 0 ? nestingLevel * 8 : 0,
    };

    const handleDeleteContainer = () => {
        console.log('Delete button clicked for container:', container.id,
            'Parent:', parentContainerId || 'none',
            'isSubContainer:', !!parentContainerId);
        if (!container.id) return;

        if (parentContainerId) {
            // For subcontainers, we need to call deleteSubContainer with both IDs
            if (Platform.OS === 'web') {
                const isConfirmed = window.confirm('Are you sure you want to delete this sub section?');
                if (isConfirmed) {
                    console.log('Deleting subcontainer', container.id, 'from parent', parentContainerId);
                    if (deleteSubContainer && parentContainerId) {
                        deleteSubContainer(container.id, parentContainerId);
                    }
                }
            } else {
                Alert.alert(
                    'Confirm Deletion',
                    'Are you sure you want to delete this subsection?',
                    [
                        { text: 'Cancel', style: 'cancel' },
                        {
                            text: 'OK',
                            onPress: () => {
                                // console.log('Deleting subcontainer', container.id, 'from parent', parentContainerId);
                                if (deleteSubContainer && parentContainerId) {
                                    deleteSubContainer(container.id, parentContainerId);
                                }
                            },
                        },
                    ],
                    { cancelable: false }
                );
            }
            // if (deleteSubContainer && parentContainerId) {
            //     deleteSubContainer(container.id, parentContainerId);
            // }
        } else {
            // For main containers
            if (Platform.OS === 'web') {
                const isConfirmed = window.confirm('Are you sure you want to delete this Section ?');
                if (isConfirmed) {
                    // console.log('Deleting subcontainer', container.id, 'from parent', parentContainerId);
                    if (deleteContainer) {
                        deleteContainer(container.id);
                    }
                }
            } else {
                Alert.alert(
                    'Confirm Deletion',
                    'Are you sure you want to delete this subsection?',
                    [
                        { text: 'Cancel', style: 'cancel' },
                        {
                            text: 'OK',
                            onPress: () => {
                                console.log('Deleting subcontainer', container.id, 'from parent', parentContainerId);
                                if (deleteContainer) {
                                    deleteContainer(container.id);
                                }
                            },
                        },
                    ],
                    { cancelable: false }
                );
            }
           
        }
    };

    const movePosition = (targetPosition: number) => {
        moveControl && moveControl(container, index, targetPosition, parentContainerId);
    }
    return (
        <View
            ref={drop}
            style={[
                styles.containerDrop,
                {
                    backgroundColor: getBackgroundColor(),
                    borderColor: getBorderColor(),
                    borderWidth: selectedControl?.id === container.id ? 2 : 1,
                }
            ]}
        >
            <TouchableOpacity
                onPress={() => selectControl && selectControl(container)}
                style={[styles.containerHeader, levelPadding]}
            >
                <View style={styles.titleContainer}>
                    {/* Collapse/Expand button */}
                    <TouchableOpacity
                        onPress={() => setCollapsed(!collapsed)}
                        style={styles.collapseButton}
                    >
                        {collapsed
                            ? <Feather name="chevron-down" size={16} color="#4B5563" />
                            : <Feather name="chevron-up" size={16} color="#4B5563" />
                        }
                    </TouchableOpacity>

                    {/* Heading with level indicator */}
                    <Text style={styles.stepTitle}>
                        {nestingLevel > 0 ? `Sub ${nestingLevel}:` : ''} {container.heading}
                    </Text>
                    <Text style={styles.space}>:</Text>
                    <Text style={styles.heading}>
                        {container.subHeading}
                    </Text>
                </View>

                <View style={styles.containerActions}>
                    {index > 0 &&
                        <TouchableOpacity style={styles.iconRound} onPress={() => movePosition(index - 1)}>
                            <AntDesign name="arrowup" size={16} color="#4B5563" />
                            {/* <ArrowUp size={16} color="#4B5563" /> */}
                        </TouchableOpacity>
                    }
                    {index < itemsLength - 1 &&
                        <TouchableOpacity style={styles.iconRound} onPress={() => movePosition(index + 1)}>
                            <AntDesign name="arrowdown" size={16} color="#4B5563" />
                            {/* <ArrowDown size={16} color="#4B5563" /> */}
                        </TouchableOpacity>
                    }
                    <TouchableOpacity
                        onPress={handleDeleteContainer}
                    >
                        <Feather name="trash-2" size={20} color="#E53E3E" />
                        {/* <Trash2 size={20} color="#E53E3E" /> */}
                    </TouchableOpacity>
                </View>
            </TouchableOpacity>

            {!collapsed && (
                <View style={[styles.itemsContainer, levelPadding]}>
                    {/* Render all items in the exact order they were added */}
                    {(!container.items || container.items.length === 0)
                        ? renderDropQuestionContainer()
                        : container.items.map((item, index) => renderItem(item, index, container.items.length))
                    }

                    {/* Always show an additional drop area */}
                    {container.items && container.items.length > 0 && (
                        <View style={styles.additionalDropArea}>
                            <AntDesign name="plus" size={16} color="#4C51BF" />
                        
                            {/* <Plus size={16} color="#4C51BF" /> */}
                            <Text style={styles.additionalDropText}>
                                Drop more items here
                            </Text>
                        </View>
                    )}
                </View>
            )}
        </View>
    );
};

export default RecursiveContainerComponent;

const styles = StyleSheet.create({
    containerDrop: {
        width: '100%',
        padding: 16,
        marginBottom: 16,
        borderRadius: 8,
        borderWidth: 1,
        borderStyle: 'solid',
    },
    containerHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottomColor: 'rgba(0,0,0,0.1)',
        borderBottomWidth: 1,
        paddingBottom: 12,
        marginBottom: 12,
    },
    collapseButton: {
        marginRight: 8,
        width: 24,
        height: 24,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 4,
        backgroundColor: 'rgba(0,0,0,0.05)',
    },
    titleContainer: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
    },
    stepTitle: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 16,
        color: '#1F2937',
    },
    space: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 16,
        color: '#1F2937',
        marginHorizontal: 4,
    },
    containerActions: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    heading: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 16,
        color: '#1F2937',
        marginBottom: 4,
    },
    subHeading: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 14,
        color: '#4B5563',
        marginBottom: 16,
    },
    itemsContainer: {
        minHeight: 100,
        width: '100%',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    iconRound: {
        padding: 6,
        marginLeft: 8,
        backgroundColor: "#ccc9c9",
        borderRadius: 18,

    },
    dropField: {
        width: '100%',
        borderRadius: 8,
        borderStyle: 'dashed',
        borderWidth: 2,
        borderColor: '#4C51BF',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        padding: 20,
        alignSelf: 'center',
        backgroundColor: 'rgba(76, 81, 191, 0.05)',
    },
    dropFieldText: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 16,
        color: '#4C51BF',
    },
    additionalDropArea: {
        width: '100%',
        borderRadius: 8,
        borderStyle: 'dashed',
        borderWidth: 1,
        borderColor: '#4C51BF',
        padding: 12,
        marginTop: 16,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        backgroundColor: 'rgba(76, 81, 191, 0.02)',
    },
    additionalDropText: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 14,
        color: '#4C51BF',
        marginLeft: 8,
    },
});