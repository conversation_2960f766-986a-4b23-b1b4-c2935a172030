import { ARROW_DOWN, <PERSON><PERSON><PERSON><PERSON>, CHEC<PERSON>, MENU, RADIO, TAG, TASK, TEXT, TIMER, TOGGLE } from '@/assets/images';
import { generateID } from 'src/utils/GeneralUtils';
//import { Type, CheckSquare, Radio, Calendar, Timer } from 'lucide-react-native';

export const FormControlNames = {
  STEPCONTAINER: 'step-container',
  SUBCONTAINER: 'sub-container',
  INPUTTEXTFIELD: 'text-field',
  LABEL: 'label',
  INPUTMULTILINE: 'multiline-text-field',
  CHECKBOX: 'checkbox',
  RADIOGROUP: 'radio-group',
  SELECTDROPDOWN: 'select-drop-down',
  DATEFIELD: 'date-field',
  TIMEFIELD: 'time-field',
  TOGGLE: 'toggle',
  CHECKLIST: 'checklist',
  MULTICHOICES: 'multi-choices',
  NUMERICWITHUNITS:'numeric-with-units',
  SCORECALCULATOR: 'score-calculator',
};

export const FormTextDataTypes = {
  TEXT: 'text',
};

export const FormItemTypes = {
  CONTROL: 'control',
  CONTAINER: 'container',
  SUBCONTAINER: 'subcontainer',
};

export const FormPublishStatus = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
};

export const FormContainerList = [
  {
    id: '',
    controlName: FormControlNames.STEPCONTAINER,
    displayText: 'Add Section',
    itemType: FormItemTypes.CONTAINER,
    heading: 'Section Code',
    subHeading: 'Section Name',
  },
];

export const FormControlList = [
  
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.INPUTTEXTFIELD,
    displayText: 'Question Label',
    placeholder: 'Placeholder',
    description: 'Question Description',
    labelName: 'Question Label',
    itemType: FormItemTypes.CONTROL,
    dataType: FormTextDataTypes.TEXT,
    icon: TEXT,
    required: false,
    isDisplayCheatSheet:false,
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.LABEL,
    displayText: 'Label',
    
    labelName: ' Label',
    itemType: FormItemTypes.CONTROL,
    icon: TAG,
    required: false,
    isDisplayCheatSheet:false,
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.INPUTMULTILINE,
    displayText: 'Notes',
    description: 'Question Description',
    placeholder: 'Please write your notes here.',
    labelName: 'Notes',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: MENU,
    required: false,
    isDisplayCheatSheet:false,
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.RADIOGROUP,
    displayText: 'Radio',
    description: 'Question Description',
    labelName: 'Label for Radio',
    itemType: FormItemTypes.CONTROL,
    icon: RADIO,
    required: false,
    isDisplayCheatSheet:false,
    items: [
      {
        id: generateID(),
        value: '1',
        label: 'Button 1',
      },
      {
        id: generateID(),
        value: '2',
        label: 'Button 2',
      },
    ],
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.TOGGLE,
    displayText: 'Toggle',
    description: 'Question Description',
    labelName: 'Label for Toggle',
    placeholder: 'Place Holder Text',
    itemType: FormItemTypes.CONTROL,
    icon: TOGGLE,
    required: false,
    isDisplayCheatSheet:false,
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.CHECKLIST,
    displayText: 'Checklist',
    description: 'Question Description',
    labelName: 'Label for Checklist',
    itemType: FormItemTypes.CONTROL,
    icon: TASK,
    required: false,
    isDisplayCheatSheet:false,
    items: [
      {
        id: generateID(),
        value: '1',
        label: 'Check 1',
      },
      {
        id: generateID(),
        value: '2',
        label: 'Check 2',
      },
    ],
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.SELECTDROPDOWN,
    displayText: 'Dropdown',
    description: 'Question Description',
    labelName: 'Label for Dropdown',
    itemType: FormItemTypes.CONTROL,
    icon: ARROW_DOWN,
    required: false,
    isDisplayCheatSheet:false,
    items: [
      {
        id: generateID(),
        value: '1',
        label: 'Option 1',
      },
      {
        id: generateID(),
        value: '2',
        label: 'Option 2',
      },
    ],
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.CHECKBOX,
    displayText: 'Checkbox',
    description: 'Question Description',
    labelName: 'Label for Checkbox',
    placeholder: 'Place Holder Text',
    itemType: FormItemTypes.CONTROL,
    icon: CHECK,
    required: false,
    isDisplayCheatSheet:false,
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.DATEFIELD,
    displayText: 'Date Picker',
    description: 'Question Description',
    labelName: 'Label for Date',
    itemType: FormItemTypes.CONTROL,
    icon: CALENDER,
    required: false,
    isDisplayCheatSheet:false,
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.SCORECALCULATOR,
    displayText: 'Score Calculator',
    description: 'Calculate scores based on selected questions',
    labelName: 'Score Calculator',
    itemType: FormItemTypes.CONTROL,
    icon: TIMER, // You'll need to import this icon
    required: false,
    isDisplayCheatSheet: false,
    containerId: '',
    calculationType: 'sum', // 'sum' or 'average'
    displayFormat: '', // For decimal places like '.2' or '.1'
    selectedQuestions: [], // Array of question IDs to calculate from
    questionScores: {}, // Object mapping question options to scores
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.TIMEFIELD,
    displayText: 'Time',
    description: 'Question Description',
    labelName: 'Label for Time',
    itemType: FormItemTypes.CONTROL,
    icon: TIMER,
    required: false,
    isDisplayCheatSheet:false,
    containerId: '',
  },
  {
    id: '',
    questionCode: '',
    controlName: FormControlNames.NUMERICWITHUNITS,
    displayText: 'Numeric with Units',
    description: 'Question Description',
    labelName: 'Label for Numeric',
    itemType: FormItemTypes.CONTROL,
    icon: TIMER,
    required: false,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.SUBCONTAINER,
    displayText: 'Add Sub Section',
    itemType: FormItemTypes.SUBCONTAINER,
    heading: 'Sub Section Code',
    subHeading: 'Sub Section Name',
    icon: TIMER,
    required: false,
    containerId: '',
  },
];
