import theme from '@/src/theme';
import { fs, ms } from '@/src/utils/ScreenUtils';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { View, Text, StyleSheet, Dimensions, ActivityIndicator } from 'react-native';
import Svg, { G, Circle, Path, Text as SvgText } from 'react-native-svg';
const { width } = Dimensions.get('window');
const { height } = Dimensions.get('window');

export const VisitStatusPieChart = ({ data, loading }) => {
    // Chart dimensions
    const chartSize = 100;
    const width = chartSize;
    const height = chartSize;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(centerX, centerY) * 0.9;
    const innerRadius = radius * 0.8;

    // Calculate total for percentages
    const total = data.totalVisits;

    // Define colors for each segment
    const colors = {
        new: '#f87171', // Coral/red for "Not Started"
        inProgress: '#F9A704', // Orange/yellow for "In Progress"
        completed: '#3b82f6', // Blue for "Completed"
        empty: '#e5e7eb', // Light gray for empty chart
    };

    // Calculate the arc segments
    const calculateArc = (value, startAngle, color) => {
        const percentage = value / total;
        const endAngle = startAngle + percentage * 2 * Math.PI;

        // Calculate path
        const x1 = centerX + radius * Math.sin(startAngle);
        const y1 = centerY - radius * Math.cos(startAngle);
        const x2 = centerX + radius * Math.sin(endAngle);
        const y2 = centerY - radius * Math.cos(endAngle);

        // Determine if the arc is more than half the circle
        const largeArcFlag = percentage > 0.5 ? 1 : 0;

        // Create the path
        const path = `
      M ${centerX} ${centerY}
      L ${x1} ${y1}
      A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}
      Z
    `;

        return { path, endAngle, color };
    };

    // Generate the segments
    let startAngle = -Math.PI / 2; // Start from the top (270 degrees)
    const segments = [];

    // Case 1: Total visits is 0, show empty gray circle
    if (total === 0) {
        // Create a full circle with gray color
        segments.push({
            path: `
        M ${centerX} ${centerY - radius}
        A ${radius} ${radius} 0 1 1 ${centerX - 0.01} ${centerY - radius}
        Z
      `,
            color: colors.empty
        });
    }
    // Case 2: Normal chart with visit data
    else {
        // Track if we've added any segments
        let segmentsAdded = 0;

        if (data.newVisits > 0) {
            const segment = calculateArc(data.newVisits, startAngle, colors.new);
            segments.push(segment);
            startAngle = segment.endAngle;
            segmentsAdded++;
        }

        if (data.inProgressVisits > 0) {
            const segment = calculateArc(data.inProgressVisits, startAngle, colors.inProgress);
            segments.push(segment);
            startAngle = segment.endAngle;
            segmentsAdded++;
        }

        if (data.completedVisits > 0) {
            const segment = calculateArc(data.completedVisits, startAngle, colors.completed);
            segments.push(segment);
            segmentsAdded++;
        }

        // Case 3: Only one category has all the data (e.g., all completed)
        // If only one segment was added and it represents 100% of the visits,
        // we need to ensure it's rendered as a full circle
        if (segmentsAdded === 1 && segments[0] && segments[0].endAngle - (-Math.PI / 2) >= 2 * Math.PI - 0.01) {
            const onlyCategory = segments[0].color;
            segments[0] = {
                path: `
          M ${centerX} ${centerY - radius}
          A ${radius} ${radius} 0 1 1 ${centerX - 0.01} ${centerY - radius}
          Z
        `,
                color: onlyCategory
            };
        }
    }

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Today's Visits</Text>
        <View style={styles.shadowCard}>
            <LinearGradient colors={['#ffffff', '#ffffff']} style={styles.card}>
                <View style={styles.contentContainer}>
                {loading && (
                        <View style={styles.loaderOverlay}>
                            <ActivityIndicator size="large" color="#0000ff" />
                        </View>
                    )}
                    <Svg width={width} height={height}>
                        {/* Render the pie segments */}
                        <G>
                            {segments.map((segment, index) => (
                                <Path
                                    key={index}
                                    d={segment.path}
                                    fill={segment.color}
                                    stroke="white"
                                    strokeWidth={2}
                                />
                            ))}

                            {/* Inner circle (white background) */}
                            <Circle
                                cx={centerX}
                                cy={centerY}
                                r={innerRadius}
                                fill="white"
                            />

                            {/* Total visits text */}
                            <SvgText
                                x={centerX}
                                y={centerY - 5}
                                fontSize="16"
                                textAnchor="middle"
                                fill="#000"
                                fontFamily='Poppins_500Medium'
                                
                            >
                                {data.totalVisits}
                            </SvgText>

                            <SvgText
                                x={centerX}
                                y={centerY + 15}
                                fontSize="13"
                                textAnchor="middle"
                                fill="#B9B9B9"
                                fontFamily='Poppins_500Medium'
                            >
                                Visits
                            </SvgText>
                        </G>
                    </Svg>

                    {/* Legend */}
                    <View style={styles.legend}>
                        {/* In Progress */}
                        <View style={styles.legendItem}>
                            <View style={[styles.legendColor, { backgroundColor: colors.inProgress }]} />
                            <Text allowFontScaling={false}  style={styles.legendText}>In Progress:</Text>
                            <Text allowFontScaling={false}  style={styles.legendValue}>{data.inProgressVisits}</Text>
                        </View>

                        {/* Not Started */}
                        <View style={styles.legendItem}>
                            <View style={[styles.legendColor, { backgroundColor: colors.new }]} />
                            <Text allowFontScaling={false} style={styles.legendText}>Not Started:</Text>
                            <Text allowFontScaling={false}  style={styles.legendValue}>{data.newVisits}</Text>
                        </View>

                        {/* Completed */}
                        <View style={styles.legendItem}>
                            <View style={[styles.legendColor, { backgroundColor: colors.completed }]} />
                            <Text allowFontScaling={false} style={styles.legendText}>Completed:</Text>
                            <Text allowFontScaling={false}  style={styles.legendValue}>{data.completedVisits}</Text>
                        </View>
                    </View>
                </View>
            </LinearGradient>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        //backgroundColor: '#F8FAFC',
        paddingHorizontal: 20,
        flex: 1,
    },

    title: {
        fontSize: 20,
        fontFamily: 'Poppins_500Medium',
        color: '#251F38',
    },
    card: {
        backgroundColor: '#FFFFFF',
        borderRadius: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
        elevation: 4,
        height:"100%",
        width:"100%",
        padding: 20,

    },
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 15,
    },
    legend: {
        flex: 1,
        marginLeft: 20,
    },
    legendItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
        marginVertical: 8,
    },
    legendColor: {
        width: 15,
        height: 15,
        borderRadius: 10,
        marginRight: 12,
    },
    legendText: {
        
        fontSize: 15,
        color: '#7C7C7C',
        fontFamily: 'Poppins_400Regular',
    },
    legendValue: {
        fontSize: 16,
        color: theme.colors.text,
        fontFamily: 'Poppins_600SemiBold',
    },
    loaderOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(255, 255, 255, 0.7)', // Slight transparency
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 10, // Ensure it appears on top
        borderRadius: 8, // Match the widget's border radius
    },
    shadowCard:{
        shadowColor: '#000',
        width:width-40,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 6,
        borderRadius: 16,
        backgroundColor: '#FFFFFF', 
        marginRight: 15,
        marginTop: 10,
        marginBottom:20
}
});
