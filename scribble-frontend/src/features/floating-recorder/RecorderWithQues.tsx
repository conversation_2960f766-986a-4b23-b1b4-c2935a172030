import React, { useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StatusBar,
    Alert,
    Modal,
    Dimensions,
    ActivityIndicator,
    Platform, Image
} from 'react-native';
import { FontAwesome, Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
// import { Play, PlayCircle, RefreshCcwIcon } from 'lucide-react-native';

import { Audio } from 'expo-av';

const { width, height } = Dimensions.get('window');
import { uploadRecord, fetchAssesmentId, fetchFormDetails } from './record-api';
import { useNotification } from '@/src/navigation/NotificationContext';
import ToastService from '@/src/utils/toastService';

// Calculate dimensions for fixed elements
const HEADER_HEIGHT = 60;
const PROGRESS_HEIGHT = 50;
const FOOTER_HEIGHT = 110;

const FullScreenLoader = ({ visible, onCancel, onConfirm, value }) => {
    // Modal content remains the same
    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={visible}
        >
            <View style={styles.overlay}>
                <View style={styles.modalContainer}>
                    <View style={styles.iconContainer}>
                        <View style={styles.icon}>
                            <Ionicons name="checkmark-circle" size={35} color="#1D75F5" />
                        </View>
                    </View>
                    <Text style={styles.title}>Submit Recording?</Text>
                    <Text style={styles.message}>
                        You have {value} unanswered questions. You may complete them before submitting to EMR?
                    </Text>
                    <View style={styles.buttonContainer}>
                        <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
                            <Text style={styles.cancelButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
                            <Text style={styles.confirmButtonText}>Confirm</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

const RestartRecordModal = ({ visible, onCancel, onConfirm }) => {
    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={visible}
        >
            <View style={styles.overlay}>
                <View style={styles.modalContainer}>
                    <View style={styles.iconContainer}>
                        <View style={styles.icon}>
                            <Image source={require('assets/images/restart.svg')} />

                        </View>
                    </View>
                    <Text style={styles.title}>Restart Recording?</Text>
                    <Text style={styles.message}>
                        Your recording will be deleted,
                        is that okay?
                    </Text>
                    <View style={styles.buttonContainer}>
                        <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
                            <Text style={styles.cancelButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
                            <Text style={styles.confirmButtonText}>Restart</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};

export default function RecordingProgressScreen({ route }) {
    const assesId = route.params.assesId;
    const navigation = useNavigation();
    const [checkedCount, setCheckedCount] = useState(0);

    const recordingRef = useRef(null);
    const [isRecording, setIsRecording] = useState(false);
    const [recordingURI, setRecordingURI] = useState(null);
    const [recordingTime, setRecordingTime] = useState(0);
    const [isPaused, setIsPaused] = useState(false);
    const [recordedSegments, setRecordedSegments] = useState([]);
    const [isFirstTimeRecording, setIsFirstTimeRecording] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [loading, setLoading] = useState(true);
    const [restartLoading, setRestartLoading] = useState(false);
    const [cancelRecording, setCancelRecording] = useState(false);
    const [sound, setSound] = useState(null);
    const [isPlaying, setIsPlaying] = useState(false);

    const [playbackPosition, setPlaybackPosition] = useState(0);
    const [showPauseResume, setShowPauseResume] = useState(false);
    const [assesmentId, setAssesmentId] = useState(0);

    const [categories, setCategories] = useState([{
        container: { id: '', heading: '', subHeading: '' },
        items: []
    }]);
    let [totalQuestions, setTotalQuestions] = useState(0);
    let [progress, setProgress] = useState(0);

    const fetchAssesment = () => {
        fetchFormDetails(assesId)
            .then((result) => {
                if (result.status == "ok") {

                    let assesQues = result.data.question;
                    let totalCount = 0

                    for (let i = 0; i < assesQues.length; i++) {
                        for (let j = 0; j < assesQues[i].items.length; j++) {
                            if (!assesQues[i].items[j].isContainer) {
                                assesQues[i].items[j].checked = false;
                                totalCount++
                            }
                        }
                    }
                    setTotalQuestions(totalCount);
                    setCategories(assesQues);
                } else {
                    Alert.alert(result.errorMessage);
                }
            })
            .catch((error) => {
                console.log(error);
                // Alert.alert(error, 'Something went wrong. Please try again later');
            })
            .finally(() => setLoading(false));
    };

    useEffect(() => {
        setAssesmentId(assesId)
        fetchAssesment();
    }, []);

    useEffect(() => {
        const unsubscribe = navigation.addListener('beforeRemove', (e) => {
            // console.log("----going back----");
            _stopRecording();
        });

        return unsubscribe;
    }, [navigation]);

    // Format the time as MM:SS
    const formatTime = seconds => {
        const hrs = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    // Increment the timer
    useEffect(() => {
        let interval;
        if (isRecording) {
            interval = setInterval(() => {
                setRecordingTime(prev => prev + 1);
            }, 1000);
        }
        return () => clearInterval(interval);
    }, [isRecording]);

    // Don't forget to unload audio when component unmounts
    useEffect(() => {
        return () => {
            if (sound) {
                sound.unloadAsync();
            }
        };
    }, [sound]);

    // useEffect(() => {
    //     if (recordingTime == 90) {
    //         _pauseRecording();
    //     }
    // }, [recordingTime]);

    const _startRecording = async () => {
        try {
            console.log('Requesting permissions...');
            const { status } = await Audio.requestPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert('Permission Denied', 'Enable microphone in settings.');
                return;
            }

            await Audio.setAudioModeAsync({
                allowsRecordingIOS: true,
                playsInSilentModeIOS: true,
            });

            // Safari-specific workaround
            if (Platform.OS === 'web' && /^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
                try {
                    // Safari requires specific MIME types
                    const recording = new Audio.Recording();

                    // Modify recording options for Safari compatibility
                    const recordingOptions = {
                        ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
                        web: {
                            mimeType: 'audio/mp4', // Try audio/mp4 as it's better supported in Safari
                            bitsPerSecond: 128000,
                        },
                    };

                    await recording.prepareToRecordAsync(recordingOptions);
                    await recording.startAsync();
                    recordingRef.current = recording;
                    setIsRecording(true);
                    setIsPaused(false);
                    setRecordingTime(0);
                    console.log('Recording started with Safari compatibility mode');
                } catch (safariError) {
                    console.error('Safari recording attempt failed:', safariError);

                    // Fallback for Safari: notify user of browser limitations
                    Alert.alert(
                        'Browser Limitation',
                        'Audio recording is not fully supported in Safari. Please try Chrome or Firefox for the best experience.',
                        [{ text: 'OK' }]
                    );
                    return;
                }
            } else {
                // Standard approach for other browsers and mobile
                const recording = new Audio.Recording();
                await recording.prepareToRecordAsync(
                    Audio.RecordingOptionsPresets.HIGH_QUALITY
                );
                await recording.startAsync();
                recordingRef.current = recording;
                setIsRecording(true);
                setIsPaused(false);
                setRecordingTime(0);
                console.log('Recording started');
            }
            setShowPauseResume(true);
        } catch (error) {
            console.error('Error starting recording:', error);
        }
    };

    const _pauseRecording = async () => {
        console.log('Pausing recording...');
        ToastService.show({
            message: "You recording has been paused.",
            type: "info",
            duration: 4000,
            position: "top",
            bottomOffset: 80,
            autoHide: true,
            backgroundColor: "#6A5AE0",
            icon: "checkmark-circle"

        });
        if (!recordingRef.current) return;

        try {
            await recordingRef.current.pauseAsync();

            setIsRecording(false);
            setIsPaused(true);
            setCancelRecording(true)
        } catch (err) {
            console.error('Failed to pause recording', err);
        }
    };

    const _resumeRecording = async () => {
        // ToastService.success("Your recording  started again.",3000,"top");
        ToastService.show({
            message: "Your recording has resumed.",
            type: "info",
            duration: 4000,
            position: "top",
            bottomOffset: 80,
            autoHide: true,
            backgroundColor: "#6A5AE0",
            icon: "checkmark-circle"

        });
        try {
            if (recordingRef.current) {
                await recordingRef.current.startAsync();
                setIsRecording(true);
                setIsPaused(false);
            }
        } catch (err) {
            console.error('Failed to resume recording', err);
        }
    };

    const _stopRecording = async () => {
        console.log('Stopping recording...');
        try {
            if (!recordingRef.current) {
                console.log('No active recording to stop.');
                return;
            }

            await recordingRef.current.stopAndUnloadAsync();
            const uri = recordingRef.current.getURI();
            if (!uri) {
                console.error('Failed to retrieve recording URI.');
                return;
            }

            setRecordedSegments(prev => [...prev, uri]);
            const finalRecording =
                recordedSegments.length > 0 ? [...recordedSegments, uri] : uri;

            setRecordingURI(finalRecording);
            console.log('Recording stopped and stored at', finalRecording);

            recordingRef.current = null;
            setRecordedSegments([]);
            setIsRecording(false);
            setIsPaused(false);
            console.log("Recording stopped....");
        } catch (error) {
            console.error('Error stopping recording:', error);
        }
    };

    const convertBlobToDataURI = async (blobURI) => {
        const blob = await fetch(blobURI).then((res) => res.blob());
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.readAsDataURL(blob);
        });
    };

    const firstRecording = () => {
        setIsFirstTimeRecording(false);
        _startRecording();
    };

    const _playAudio = async () => {
        console.log("_play Audio:", recordingURI);

        try {
            let sourceURI = recordingURI;

            if (Platform.OS === 'web') {
                sourceURI = await convertBlobToDataURI(recordingURI);
            }

            if (sound) {
                await sound.playFromPositionAsync(playbackPosition);
                setIsPlaying(true);
                return;
            }

            const { sound: newSound } = await Audio.Sound.createAsync(
                { uri: sourceURI },
                { shouldPlay: true },
                onPlaybackStatusUpdate
            );

            setSound(newSound);
            setIsPlaying(true);

            await newSound.playAsync();
        } catch (error) {
            console.error("Error playing audio:", error);
        }
    };

    // Add a function to handle playback status updates
    const onPlaybackStatusUpdate = (status) => {
        if (status.isLoaded) {
            setPlaybackPosition(status.positionMillis);

            if (status.didJustFinish) {
                // Audio finished playing
                setIsPlaying(false);
                setPlaybackPosition(0);
            }
        }
    };

    // Add function to pause audio playback
    const _pauseAudio = async () => {
        if (sound && isPlaying) {
            console.log("Pausing audio playback...");
            await sound.pauseAsync();
            setIsPlaying(false);
        }
    };

    const onModalCancel = () => {
        setShowModal(false);
        setRestartLoading(false);
        setCancelRecording(true);
        setShowPauseResume(false);

    };
    const onRestartModalCancel = () => {
        setRestartLoading(false);
        // setCancelRecording(true);
        setShowPauseResume(true);
    };

    const restartRecording = () => {

        setRestartLoading(true);
        setShowPauseResume(false);
        setShowModal(false);
    };

    const onRestartRecording = () => {
        _stopRecording()
        setIsFirstTimeRecording(true);
        setRecordingURI(null);
        setRecordingTime(0);
        setCancelRecording(false);
        setRestartLoading(false);
        setShowPauseResume(false);
        setSound(null);
    };

    // Function to toggle checkbox state
    const toggleCheckbox = (categoryIndex, questionId) => {
        const updatedCategories = [...categories];
        const questionIndex = updatedCategories[categoryIndex].items.findIndex(q => q.questionCode === questionId);

        if (questionIndex !== -1) {
            let isChecked = !updatedCategories[categoryIndex].items[questionIndex].checked;
            updatedCategories[categoryIndex].items[questionIndex].checked = isChecked;

            let count = checkedCount;
            if (isChecked) {
                count++;
            } else {
                count--;
            }

            if (totalQuestions != 0 && count != 0) {
                setProgress((count / totalQuestions) * 100);
            } else {
                setProgress(0);
            }

            setCheckedCount(count);
            setCategories(updatedCategories);
        }
    };

    const onBackClick = () => {
        navigation.goBack();
    };

    const callServer = async () => {
        try {
            let result = await uploadRecord(recordingURI, assesmentId);
            // console.log(JSON.stringify(result));
            if (result.status == "ok") {
                ToastService.show({
                    message: "We have successfully completed the form.",
                    type: "info",
                    duration: 4000,
                    position: "bottom",
                    bottomOffset: 80,
                    autoHide: false,
                    backgroundColor: "#4F47E5",
                    onPress: () => {
                        navigation.navigate('Assesment', { assesId: assesId });
                    },
                    customContent: (

                        <View style={{ height: 40, width: 40, backgroundColor: "#fff", borderRadius: 25, justifyContent: "center", alignItems: "center" }}>
                            {/* <Ionicons name="sparkles" size={20} color="#6A5AE0" /> */}
                            <Image
                                source={require('assets/images/sparkles.svg')}
                            />
                        </View>
                    ),
                });
            } else {
                Alert.alert(result.errorMessage);
            }
        } catch (error) {
            console.log(error);
            //     Alert.alert('Something went wrong. Please try again later');
        }
    };

    const submitToAI = () => {
        setShowModal(false);
        callServer();
        ToastService.show({
            message: "Hold tight, Goodly AI is processing your request.",
            type: "info",
            duration: 4000,
            position: "bottom",
            bottomOffset: 80,
            autoHide: false,
            backgroundColor: "#4F47E5",
            onPress: () => {
                console.log("Toast clicked");
            },
            customContent: (
                <View style={{ height: 40, width: 40, backgroundColor: "#fff", borderRadius: 25, justifyContent: "center", alignItems: "center" }}>
                    {/* <Ionicons name="sparkles" size={20} color="#6A5AE0" /> */}
                    <Image
                        source={require('assets/images/sparkles.svg')}
                    />
                </View>
            ),
        });
        navigation.navigate('Dashboard');
    };

    const stopRecording = () => {
        _stopRecording();
        setShowModal(true);
        setShowPauseResume(false);
    };

    const calculateUnanswered = () => {
        return totalQuestions - checkedCount;
    };
    const renderWebContent = () => {
        return (
            <div
                data-testid="visit-scrollable-container"
                style={{
                    overflowY: 'auto',
                    height: '60vh',
                }}
            >
                {categories.map((category, categoryIndex) => (
                    <View key={categoryIndex} style={styles.categoryContainer}>
                        {/* {console.log(JSON.stringify(category))} */}
                        <Text style={styles.categoryTitle}>{category?.container.subHeading}</Text>

                        {category.items.map((question) => {
                            return (
                                <>
                                    {!question.isContainer && question.isDisplayCheatSheet &&
                                        <TouchableOpacity
                                            key={question.questionCode}
                                            style={styles.questionRow}
                                            onPress={() => toggleCheckbox(categoryIndex, question.questionCode)}
                                            activeOpacity={0.7}
                                        >
                                            <View style={[
                                                styles.checkbox,
                                                question.checked ? styles.checkboxChecked : styles.checkboxUnchecked
                                            ]}>
                                                {question.checked && (
                                                    <Ionicons name="checkmark" size={16} color="white" />
                                                )}
                                            </View>
                                            <Text style={styles.questionText}>{question.description}</Text>
                                        </TouchableOpacity>
                                    }
                                </>
                            )
                        }
                        )}

                        {categoryIndex < categories.length - 1 && <View style={styles.divider} />}
                    </View>
                ))}
                {/* <View style={{ height: 20 }} /> */}
            </div>
        )
    }
    const renderNativeContent = () => {
        return (
            <ScrollView style={styles.scrollView}>
                {categories.map((category, categoryIndex) => (
                    <View key={categoryIndex} style={styles.categoryContainer}>
                        <Text style={styles.categoryTitle}>{category?.container.subHeading}</Text>

                        {category.items.map((question) => {
                            return (
                                <>
                                    {!question.isContainer &&
                                        <TouchableOpacity
                                            key={question.questionCode}
                                            style={styles.questionRow}
                                            onPress={() => toggleCheckbox(categoryIndex, question.questionCode)}
                                            activeOpacity={0.7}
                                        >
                                            <View style={[
                                                styles.checkbox,
                                                question.checked ? styles.checkboxChecked : styles.checkboxUnchecked
                                            ]}>
                                                {question.checked && (
                                                    <Ionicons name="checkmark" size={16} color="white" />
                                                )}
                                            </View>
                                            <Text style={styles.questionText}>{question.description}</Text>
                                        </TouchableOpacity>
                                    }
                                </>
                            )
                        }
                        )}

                        {categoryIndex < categories.length - 1 && <View style={styles.divider} />}
                    </View>
                ))}
                {/* <View style={{ height: 20 }} /> */}
            </ScrollView>
        )
    }
    // Here's our simplified layout approach
    return (
        <SafeAreaView style={styles.safeArea}>
            <StatusBar barStyle="dark-content" />
            <FullScreenLoader visible={showModal} onCancel={onModalCancel} onConfirm={submitToAI} value={calculateUnanswered()} />
            <RestartRecordModal visible={restartLoading} onCancel={onRestartModalCancel} onConfirm={onRestartRecording} />

            {/* Fixed Header */}
            <View style={styles.header}>
                <TouchableOpacity style={styles.backButton} onPress={onBackClick}>
                    <Ionicons name="chevron-back" size={24} color="black" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Recording In Progress</Text>
            </View>

            {/* Progress Section */}
            {!loading && categories.length > 0 && (
                <View style={styles.progressSection}>
                    <View style={styles.progressContainer}>
                        <Text style={styles.progressText}>Questions</Text>
                        <Text style={styles.progressCount}>{checkedCount}/{totalQuestions}</Text>
                    </View>
                    <View style={styles.progressBarContainer}>
                        <View style={[styles.progressBar, { width: `${progress}%` }]} />
                    </View>
                </View>
            )}

            {/* Scrollable Content */}
            <View style={styles.contentContainer}>
                {loading ? (
                    <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
                ) : categories.length > 0 ? (
                    Platform.OS === 'web' ? renderWebContent() : renderNativeContent()

                ) : (
                    <View style={styles.blankTextContainer}>
                        <Text style={styles.blankText}>No Question available</Text>
                    </View>
                )}
            </View>

            {/* Fixed Footer */}
            <View style={styles.footer}>
                <View style={styles.timerContainer}>
                    <Text style={styles.timerText}>{formatTime(recordingTime)}</Text>

                    {/* Control buttons */}
                    <View style={styles.controlsContainer}>
                        {isFirstTimeRecording ? (
                            <TouchableOpacity style={styles.startButton} onPress={firstRecording}>
                                <View style={styles.startButtonInner}>
                                    <FontAwesome name="microphone" size={20} color="#ffffff" />
                                    <Text style={styles.startText}>Start Recording</Text>
                                </View>
                            </TouchableOpacity>
                        ) : (
                            <>
                                {recordingURI && isPlaying &&
                                    <TouchableOpacity style={styles.controlButton} onPress={_pauseAudio}>
                                        <FontAwesome name="microphone-slash" size={20} color="#ffffff" />
                                    </TouchableOpacity>
                                }
                                {recordingURI && !isPlaying &&
                                    <TouchableOpacity style={styles.controlButton} onPress={_playAudio}>
                                        <FontAwesome name="microphone" size={20} color="#ffffff" />
                                    </TouchableOpacity>
                                }
                                {showPauseResume &&
                                    <TouchableOpacity style={styles.controlButton} onPress={
                                        isRecording
                                            ? _pauseRecording
                                            : isPaused
                                                ? _resumeRecording
                                                : _startRecording
                                    }>
                                        <View style={styles.pauseButton}>
                                            {isRecording &&
                                                <FontAwesome name="pause" size={18} color="#ffffff" />
                                            }
                                            {isPaused &&
                                                <FontAwesome name="play" size={18} color="#ffffff" />
                                                // <PlayCircle size={30} color="#ffffff" />
                                            }
                                        </View>
                                    </TouchableOpacity>
                                }
                                {cancelRecording &&
                                    <TouchableOpacity style={styles.controlButton} onPress={restartRecording}>
                                        {/* <RefreshCcwIcon size={28} color="#ffffff" /> */}
                                    </TouchableOpacity>
                                }
                                <TouchableOpacity style={styles.controlButton} onPress={stopRecording}>
                                    <FontAwesome name="check" size={18} color="#ffffff" />
                                </TouchableOpacity>
                            </>
                        )}
                    </View>
                </View>
            </View>
        </SafeAreaView >
    );
}

const styles = StyleSheet.create({
    icon: {
        width: 60,
        height: 60,
        backgroundColor: '#EFF6FF',
        borderRadius: 30,
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex',
    },
    safeArea: {
        flex: 1,
        backgroundColor: '#FFFFFF',
    },
    // Header
    header: {
        height: HEADER_HEIGHT,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        backgroundColor: '#FFFFFF',
        borderBottomWidth: 1,
        borderBottomColor: '#EAEAEA',
    },
    backButton: {
        marginRight: 8,
    },
    headerTitle: {
        fontSize: 20,
        fontFamily: "Poppins_500Medium",
        color: '#000'
    },

    // Progress section
    progressSection: {
        height: PROGRESS_HEIGHT,
        backgroundColor: '#FFFFFF',
    },
    progressContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        marginBottom: 8,
    },
    progressText: {
        fontSize: 14,
        color: '#000',
        fontFamily: "Poppins_400Regular",
    },
    progressCount: {
        fontSize: 14,
        color: '#000',
        fontFamily: "Poppins_400Regular",
    },
    progressBarContainer: {
        height: 6,
        backgroundColor: '#EAEAEA',
        marginLeft: 15,
        marginRight: 15,
        borderRadius: 10
    },
    progressBar: {
        height: 6,
        backgroundColor: '#0079FE',
        borderRadius: 10,
    },

    // Content
    contentContainer: {
        flex: 1,
        paddingTop: 10, // Add a little spacing from progress bar
    },
    scrollView: {
        flex: 1,
    },
    categoryContainer: {
        paddingHorizontal: 15,
    },
    categoryTitle: {
        fontSize: 18,
        fontFamily: "Poppins_500Medium",
        marginBottom: 12,
        marginTop: 18,
        color: '#000',
    },
    questionRow: {
        flexDirection: 'row',
        marginBottom: 16,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderRadius: 11,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    checkboxChecked: {
        backgroundColor: '#007AFF',
    },
    checkboxUnchecked: {
        borderWidth: 1,
        borderColor: '#999',
    },
    questionText: {
        fontSize: 14,
        flex: 1,
        fontFamily: "Poppins_500Medium",
        color: '#000'
    },
    divider: {
        height: 1,
        backgroundColor: '#E0E0E0',
    },
    blankTextContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center"
    },
    blankText: {
        textAlign: "center",
        fontSize: 20,
        fontWeight: "bold"
    },

    // Footer
    footer: {
        height: FOOTER_HEIGHT,
        backgroundColor: '#FFFFFF',
        borderTopWidth: 1,
        borderTopColor: '#EAEAEA',
        paddingVertical: 10,
        paddingHorizontal: 15,
    },
    timerContainer: {
        alignItems: 'center',
    },
    timerText: {
        fontSize: 30,
        fontWeight: 'bold',
        marginBottom: 6,
    },
    controlsContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
    },
    controlButton: {
        width: 40,
        height: 40,
        borderRadius: 32,
        backgroundColor: '#4F47E5', //'#007BFF',
        justifyContent: 'center',
        alignItems: 'center',
        marginHorizontal: 8,
    },
    pauseButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: 30,
        height: 30,
    },
    startButton: {
        width: 180,
        height: 40,
        backgroundColor: "#4F47E5",
        borderRadius: 40,
        color: "#FFFFFF"
    },
    startButtonInner: {
        flex: 1,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        gap: 10
    },
    startText: {
        color: "#FFFFFF",
        fontSize: 16,
        fontWeight: "bold"
    },

    // Modal styles
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        width: width * 0.85,
        backgroundColor: '#FFF',
        paddingVertical: 24,
        paddingHorizontal: 20,
        borderRadius: 16,
        alignItems: 'center',
        elevation: 5,
    },
    iconContainer: {
        marginBottom: 16,
    },
    title: {
        color: "#000",
        textAlign: "center",
        fontFamily: "Poppins",
        fontSize: 24,
        fontStyle: "normal",
        fontWeight: "bold",
    },
    message: {
        color: "#7C7887",
        textAlign: "center",
        fontFamily: "Poppins",
        fontSize: 18,
        fontStyle: "normal",
        fontWeight: 400,
    },
    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
        marginTop: 15
    },
    cancelButton: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#1D75F5',
        paddingVertical: 12,
        borderRadius: 24,
        marginRight: 8,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: '#1D75F5',
        fontSize: 16,
        fontWeight: '500',
    },
    confirmButton: {
        flex: 1,
        backgroundColor: '#1D75F5',
        paddingVertical: 12,
        borderRadius: 24,
        marginLeft: 8,
        alignItems: 'center',
    },
    confirmButtonText: {
        color: '#FFF',
        fontSize: 16,
        fontWeight: '500',
    },
});
