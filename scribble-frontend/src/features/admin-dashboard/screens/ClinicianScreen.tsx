import React, { useState, useEffect } from 'react';
import {
  StyleSheet, View, Text, TouchableOpacity, TextInput, ScrollView,
  SafeAreaView, Modal, FlatList, Platform,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { Feather, Ionicons, MaterialIcons } from '@expo/vector-icons';
import UserFormModal from '../components/UserFormModal';

import { fetchDiscipline } from '../../profile/profile-api';
import { fetchRoles, fetchUserList, saveUser, updateUser } from '../admin-api';
import FullScreenLoader from '@/src/utils/MobileLoader';
import { MessageModal } from '@/src/utils/MessageModal';
import { setPageTitle } from '@/src/utils/GeneralUtils';
const { width, height } = Dimensions.get('window');
// Sample data for users

// Define all available columns 
const allColumns = [
  { id: 'fullName', label: 'Full Name | Id', isVisible: true },
  { id: 'role', label: 'Role', isVisible: true },
  { id: 'email', label: 'Email', isVisible: true },
  { id: 'dob', label: 'DOB', isVisible: true },
  { id: 'gender', label: 'Gender', isVisible: true },
  { id: 'addressLine', label: 'Address', isVisible: true },
  { id: 'primaryPhone', label: 'Mobile Number', isVisible: true },
  { id: 'created', label: 'Created Date', isVisible: true },
  { id: 'updated', label: 'Last Updated Date', isVisible: true },
  { id: 'status', label: 'Status', isVisible: true },
  { id: 'disciplineName', label: 'Discipline', isVisible: true },
  { id: 'location', label: 'Location', isVisible: true },
  { id: 'payrollLocation', label: 'Payroll Location', isVisible: true },
  { id: 'actions', label: 'Actions', isVisible: true }
];
const filterColumns = [
  { id: 'name', label: 'Full Name ' },

];

// Role badge component
const RoleBadge = ({ role }) => {
  let bgColor = '#DBEAFE'; // Default light blue (Clinician)
  let textColor = '#3B82F6'; // Default blue

  if (role === 'user') {
    bgColor = '#F1F5F9'; // Light gray
    textColor = '#64748B'; // Gray
  }

  return (
    <View style={[styles.roleBadge, { backgroundColor: bgColor }]}>
      <Text style={[styles.roleText, { color: textColor }]}>{role}</Text>
    </View>
  );
};
const DeleteConfirmModal = ({ visible, onCancel, onConfirm }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
    >
      <View style={confStyle.overlay}>
        <View style={confStyle.modalContainer}>
          <View style={confStyle.iconContainer}>
            {/* <Ionicons name="user" size={48} color="#1D75F5" /> */}
            <Feather name="user-x" size={48} color="#1D75F5" />
          </View>
          <Text style={confStyle.title}>Deactivate User?</Text>
          <Text style={confStyle.message}>
            Are you sure you want to deactivate the user? They will no longerbe able to access the system.
          </Text>
          <View style={confStyle.buttonContainer}>
            <TouchableOpacity style={confStyle.cancelButton} onPress={onCancel}>
              <Text style={confStyle.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={confStyle.confirmButton} onPress={onConfirm}>
              <Text style={confStyle.confirmButtonText}>Deactivate</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
const confStyle = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.26,
    backgroundColor: '#FFF',
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    color: "#000",
    textAlign: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 24,
    fontStyle: "normal",
  },
  message: {
    color: "#7C7887",
    textAlign: "center",
    fontFamily: "Poppins_400Regular",
    fontSize: 18,
    fontStyle: "normal",
    fontWeight: 400,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 15
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#1D75F5',
    paddingVertical: 12,
    borderRadius: 24,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#1D75F5',
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButton: {
    flex: 1,
    backgroundColor: '#1D75F5',
    paddingVertical: 12,
    borderRadius: 24,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '500',
  },
})
const UsersTableLayout = () => {
  setPageTitle('Clinicians');
  const [data, setData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isFiltersModalVisible, setFiltersModalVisible] = useState(false);
  const [isColumnsModalVisible, setColumnsModalVisible] = useState(false);
  const [isUserFormModalVisible, setUserFormModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState("");
  const [columns, setColumns] = useState(allColumns);
  const [filterCoulmns, setFilterCoulmns] = useState(filterColumns)
  const [filters, setFilters] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [expandedRows, setExpandedRows] = useState({});
  const [maxCoreColumns, setMaxCoreColumns] = useState(11); // Configurable value for max core columns
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false)

  const [showMessageModal, setShowMessageModal] = useState(false)
  const [message, setMessage] = useState("")
  const [messageType, setMessageType] = useState("success")

  const [disciplines, setDisciplines] = useState([])
  const [roles, setRoles] = useState([])

  const [initial, setInitial] = useState({});

  const onModalCancel = () => {
    setShowMessageModal(false)
  };

  const toggleExpand = (id) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };
  const fetchAllDiscipline = () => {
    fetchDiscipline()
      .then((result: any) => {
        if (result.status == "ok") {
          let discipline = result.data.map((dis) => {
            return { label: dis.name, value: dis._id }
          })
          setDisciplines(discipline)
        }
      })
      .catch((error) => {
        console.log(error);
      })
  }
  const fetchAllRoles = () => {
    fetchRoles()
      .then((result: any) => {
        if (result.status == "ok") {
          let role = result.data.map((rol) => {
            return { label: rol.roleName, value: rol._id }
          })
          setRoles(role)
        }
      })
      .catch((error) => {
        console.log(error);
      })
  }
  useEffect(() => {
    fetchAllDiscipline()
    fetchAllRoles()
  }, [])
  // State for server-side pagination
  const [totalRecords, setTotalRecords] = useState(data.length);
  const [loading, setLoading] = useState(false);
  const [fullLoading, setFullLoading] = useState(false);

  // Simulated server request for data
  const fetchData = async (page, perPage, searchTerm, columnFilters) => {
    // In a real application, this would be an API call
    // console.log(JSON.stringify(columnFilters))

    let filters = ""
    for (const key in columnFilters) {
      if (columnFilters.hasOwnProperty(key)) {
        const value = columnFilters[key];
        filters = `&${key}=${value}`;
      }
    }
    setLoading(true);
    try {
      let result = await fetchUserList(perPage, page, filters)
      if (result.status == "ok") {
        let initialData = result.data;

        let modifiedData=initialData.map((val:any)=>{
          // console.log("\n\n Before : ",JSON.stringify(val))
           val.addressLine = val.address?.addressLine1;
          //  console.log("After : ",JSON.stringify(val))
           return val
        })
        let filtered = [...modifiedData];

        // For demo purposes, we'll filter the data client-side to simulate server behavior

        // Apply global search
        // if (searchTerm.trim() !== '') {
        //   filtered = filtered.filter(item => {
        //     return Object.values(item).some(val =>
        //       typeof val === 'string' && val.toLowerCase().includes(searchTerm.toLowerCase())
        //     );
        //   });
        // }


        // Apply column-specific filters
        // Object.keys(columnFilters).forEach(columnId => {
        //   const filterValue = columnFilters[columnId].toLowerCase();
        //   if (filterValue.trim() !== '') {
        //     filtered = filtered.filter(item => {
        //       const value = String(item[columnId] || '').toLowerCase();
        //       return value.includes(filterValue);
        //     });
        //   }
        // });

        // Set total records (for pagination)
        setTotalRecords(result.total_records_available);

        // Get subset of data for current page
        // const startIndex = (page - 1) * perPage;
        // const endIndex = startIndex + perPage;
        // const paginatedData = filtered.slice(startIndex, endIndex);

        setData(filtered);
      } else {
        setShowMessageModal(true)
        setMessage(result.errorMessage)
        setMessageType("error")
      }

    } catch (e) {
      setShowMessageModal(true)
      setMessage(e + "")
      setMessageType("error")
    } finally {
      setLoading(false);
    }

  };

  // Effect to load data when pagination or filters change
  useEffect(() => {
    fetchData(currentPage, itemsPerPage, searchQuery, filters);
  }, [currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  // Handle page change
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Toggle column visibility
  const toggleColumnVisibility = (columnId) => {
    // Don't allow disabling the actions column
    if (columnId === 'actions') return;

    setColumns(
      columns.map(col =>
        col.id === columnId ? { ...col, isVisible: !col.isVisible } : col
      )
    );
  };

  // Update filter for a specific column
  const updateFilter = (columnId, value) => {
    setFilters({
      ...filters,
      [columnId]: value
    });
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Get visible columns and determine core vs extended columns based on maxCoreColumns
  const getVisibleColumns = () => {
    // Get all visible columns
    const visibleColumns = columns.filter(col => col.isVisible);

    // Make sure 'actions' is always included in visible columns
    const actionsColumn = visibleColumns.find(col => col.id === 'actions');

    // Separate 'actions' column from other columns
    const nonActionColumns = visibleColumns.filter(col => col.id !== 'actions');

    // Get the first (maxCoreColumns-1) non-action columns (to make room for actions)
    let coreColumnsWithoutActions = [];
    if (nonActionColumns.length <= maxCoreColumns - 1) {
      // If we have fewer visible columns than maxCoreColumns, all go in the core
      coreColumnsWithoutActions = nonActionColumns;
    } else {
      // Otherwise, take the first (maxCoreColumns-1) columns
      coreColumnsWithoutActions = nonActionColumns.slice(0, maxCoreColumns - 1);
    }

    // Add actions column to core columns (always included)
    const coreColumns = [...coreColumnsWithoutActions];
    if (actionsColumn) {
      coreColumns.push(actionsColumn);
    }

    // Get extended columns (displayed in expanded view)
    const extendedColumns = nonActionColumns.slice(maxCoreColumns - 1);

    return { coreColumns, extendedColumns };
  };

  const { coreColumns, extendedColumns } = getVisibleColumns();

  // Handle edit and delete actions
  const handleEdit = (userId) => {
    const userToEdit = data.find(user => user._id === userId);
    if (userToEdit) {
      setSelectedUser(userId);
      // console.log(JSON.stringify(userToEdit))
      setInitial(userToEdit)
      setUserFormModalVisible(true);
    }
  };

  const handleDelete = (userId) => {
    console.log(`Delete user with ID: ${userId}`);
    setSelectedUser(userId)
    setIsDeleteModalVisible(true)

    // // Implement delete functionality - show confirmation dialog and then delete
    // if (confirm('Are you sure you want to delete this user?')) {
    //   // Remove from data
    //   const updatedData = data.filter(user => user.id !== userId);
    //   setData(updatedData);

    //   // In a real app, you would call an API here
    //   // fetchData(currentPage, itemsPerPage, searchQuery, filters);
    // }
  };

  // Handle adding a new user
  const handleAddUser = () => {
    setSelectedUser(null); // Clear any selected user (for edit mode)
    setInitial({})
    setUserFormModalVisible(true);
  };

  // Handle save from the modal
  // const handleSaveUser = (userData, isEdit) => {
  //   if (isEdit) {
  //     // Update existing user
  //     const updatedData = data.map(user =>
  //       user.id === userData.id ? { ...user, ...userData } : user
  //     );
  //     setData(updatedData);
  //   } else {
  //     // Add new user with default values for all fields
  //     const newUser = {
  //       ...userData,
  //       discipline: userData.role === 'Clinician' ? (userData.discipline || 'N/A') : 'N/A',
  //       location: userData.role === 'Clinician' ? (userData.location || 'N/A') : 'N/A',
  //       payrollLocation: userData.role === 'Clinician' ? (userData.payrollLocation || 'N/A') : 'N/A'
  //     };
  //     setData([...data, newUser]);
  //   }

  //   // In a real application, you would call an API here and then refetch data
  //   // fetchData(currentPage, itemsPerPage, searchQuery, filters);
  // };
  const renderWebContent = () => {
    return (
      <div style={{
        overflowY: 'auto',
        height: '60vh',
      }}>
        {data.map((row) => (
          <View key={row._id}>
            {/* Main row */}
            <TouchableOpacity
              style={styles.tableRow}
              onPress={() => extendedColumns.length > 0 && toggleExpand(row._id)}
              activeOpacity={0.7}
            >
              {/* Expansion toggle */}
              {extendedColumns.length > 0 && (
                <View style={[styles.cell, { width: 50 }]}>
                  <MaterialIcons
                    name={expandedRows[row._id] ? 'keyboard-arrow-down' : 'keyboard-arrow-right'}
                    size={20}
                    color="#94A3B8"
                  />
                </View>
              )}

              {/* Core columns data */}
              {coreColumns.map(column => renderCell(column.id, row))}
            </TouchableOpacity>

            {/* Expanded details (if there are extended columns) */}
            {expandedRows[row._id] && extendedColumns.length > 0 && (
              <View style={styles.expandedContainer}>
                {/* Extended columns header */}
                <View style={styles.detailsHeaderRow}>
                  {extendedColumns.map(column => (
                    <View key={column.id} style={styles.detailsHeaderCell}>
                      <Text style={styles.detailsHeaderText}>{column.label}</Text>
                    </View>
                  ))}
                </View>

                {/* Extended columns data */}
                <View style={styles.detailsRow}>
                  {extendedColumns.map(column => (
                    <View key={column.id} style={styles.detailsCell}>
                      <Text style={styles.detailsCellText}>{row[column.id] || 'N/A'}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        ))}
      </div>
    )
  }
  const renderNativeContent = () => {
    return (
      <ScrollView style={styles.tableBody}>
        {data.map((row) => (
          <View key={row._id}>
            {/* Main row */}
            <TouchableOpacity
              style={styles.tableRow}
              onPress={() => extendedColumns.length > 0 && toggleExpand(row._id)}
              activeOpacity={0.7}
            >
              {/* Expansion toggle */}
              {extendedColumns.length > 0 && (
                <View style={[styles.cell, { width: 50 }]}>
                  <MaterialIcons
                    name={expandedRows[row._id] ? 'keyboard-arrow-down' : 'keyboard-arrow-right'}
                    size={20}
                    color="#94A3B8"
                  />
                </View>
              )}

              {/* Core columns data */}
              {coreColumns.map(column => renderCell(column.id, row))}
            </TouchableOpacity>

            {/* Expanded details (if there are extended columns) */}
            {expandedRows[row._id] && extendedColumns.length > 0 && (
              <View style={styles.expandedContainer}>
                {/* Extended columns header */}
                <View style={styles.detailsHeaderRow}>
                  {extendedColumns.map(column => (
                    <View key={column.id} style={styles.detailsHeaderCell}>
                      <Text style={styles.detailsHeaderText}>{column.label}</Text>
                    </View>
                  ))}
                </View>

                {/* Extended columns data */}
                <View style={styles.detailsRow}>
                  {extendedColumns.map(column => (
                    <View key={column.id} style={styles.detailsCell}>
                      <Text style={styles.detailsCellText}>{row[column.id] || 'N/A'}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    )
  }
  const onClose = () => {
    setUserFormModalVisible(false);  // User creation Modal
    setIsDeleteModalVisible(false); // Delete confirm modal
  }
  const onSave = (userData: any, type: string) => {
    // console.log(JSON.stringify(userData));
    if (type == 'create') {
      setFullLoading(true)
      saveUser(userData)
        .then((result: any) => {
          if (result.status == "ok") {
            fetchData(currentPage, itemsPerPage, searchQuery, filters);
          } else {
            setShowMessageModal(true)
            setMessage(result.errorMessage)
            setMessageType("error")
          }
        })
        .catch((error) => {
          console.log(error);
        }).finally(() => setFullLoading(false));
    } else {
      let userId = userData._id;
      setFullLoading(true)
      updateUser(userData, selectedUser)
        .then((result: any) => {
          if (result.status == "ok") {
            fetchData(currentPage, itemsPerPage, searchQuery, filters);
          } else {
            setShowMessageModal(true)
            setMessage(result.errorMessage)
            setMessageType("error")
          }
        })
        .catch((error) => {
          console.log(error);
        }).finally(() => setFullLoading(false));
    }
  }
  const deleteUserData = () => {

    setIsDeleteModalVisible(false)
    setFullLoading(true)
    updateUser({ "isDeleted": true }, selectedUser)
      .then((result: any) => {
        if (result.status == "ok") {
          fetchData(currentPage, itemsPerPage, searchQuery, filters);
        }
      })
      .catch((error) => {
        console.log(error);
      }).finally(() => setFullLoading(false));
  }
  return (
    <SafeAreaView style={styles.container}>
      <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} />
      <FullScreenLoader visible={fullLoading} />
      <View style={styles.header}>
        <Text style={styles.title}>Users</Text>
        <View style={styles.controlsContainer}>
          <View style={styles.searchBox}>
            <Ionicons name="search" size={18} color="#94A3B8" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search"
              placeholderTextColor="#94A3B8"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>

          {/* Filters Button */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setFiltersModalVisible(true)}
          >
            {/* <MaterialIcons name="filter-list" size={18} color="#64748B" /> */}
            <Feather name="filter" size={18} color="#64748B" />
            <Text style={styles.actionButtonText}>Filters</Text>
          </TouchableOpacity>

          {/* Columns Button */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setColumnsModalVisible(true)}
          >
            {/* <MaterialIcons name="view-column" size={18} color="#64748B" /> */}
            <Feather name="settings" size={18} color="#64748B" />
            <Text style={styles.actionButtonText}>Columns</Text>
          </TouchableOpacity>

          {/* Add User Button */}
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddUser}
          >
            <MaterialIcons name="add" size={18} color="#FFFFFF" />
            <Text style={styles.addButtonText}>Add User</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.mainContent}>
        <ScrollView horizontal contentContainerStyle={styles.horizontalScrollContent}>
          <View style={styles.tableContainer}>
            {/* Header row */}
            <View style={styles.tableHeaderRow}>
              {/* Expansion toggle column */}
              {extendedColumns.length > 0 && (
                <View style={[styles.headerCell, { width: 50 }]}>
                  <Text style={styles.headerText}></Text>
                </View>
              )}

              {/* Core columns headers */}
              {coreColumns.map((column) => (
                <View key={column.id} style={[styles.headerCell, getColumnWidth(column.id)]}>
                  <Text style={styles.headerText}>{column.label}</Text>
                </View>
              ))}
            </View>

            {/* Table body */}
            {loading ? (
              <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
            ) : data.length > 0 ? (
              Platform.OS === 'web' ? renderWebContent() : renderNativeContent()
            ) : (
              <View style={styles.blankTextContainer}>
                {/* <Text style={styles.blankText}>No Question available</Text> */}
                <Text style={styles.blankText}> {Object.keys(filters).length != 0 ? "No data found for selected filter" : "No data found"}</Text>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Pagination */}
        <View style={styles.paginationContainer}>
          <View style={styles.paginationInfo}>
            <Text style={styles.paginationText}>
              Showing {totalRecords > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0} to {Math.min(currentPage * itemsPerPage, totalRecords)} of {totalRecords} entries
            </Text>
          </View>

          <View style={styles.paginationControls}>
            <TouchableOpacity
              style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
              onPress={() => goToPage(1)}
              disabled={currentPage === 1}
            >
              <Text style={styles.paginationButtonText}>«</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
              onPress={() => goToPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <Text style={styles.paginationButtonText}>‹</Text>
            </TouchableOpacity>

            <View style={styles.pageNumberContainer}>
              <Text style={styles.paginationText}>
                Page {currentPage} of {totalPages || 1}
              </Text>
            </View>

            <TouchableOpacity
              style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
              onPress={() => goToPage(currentPage + 1)}
              disabled={currentPage === totalPages || totalPages === 0}
            >
              <Text style={styles.paginationButtonText}>›</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
              onPress={() => goToPage(totalPages)}
              disabled={currentPage === totalPages || totalPages === 0}
            >
              <Text style={styles.paginationButtonText}>»</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.itemsPerPageContainer}>
            <Text style={styles.paginationText}>Show:</Text>
            {[10, 25, 50].map(num => (
              <TouchableOpacity
                key={num}
                style={[
                  styles.itemsPerPageButton,
                  itemsPerPage === num && styles.itemsPerPageButtonActive
                ]}
                onPress={() => handleItemsPerPageChange(num)}
              >
                <Text style={[
                  styles.itemsPerPageButtonText,
                  itemsPerPage === num && styles.itemsPerPageButtonTextActive
                ]}>
                  {num}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Filters Modal */}
      <Modal
        visible={isFiltersModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setFiltersModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filter Data</Text>
              <TouchableOpacity onPress={() => setFiltersModalVisible(false)}>
                <MaterialIcons name="close" size={24} color="#64748B" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {filterCoulmns.map(column => (
                <View key={column.id} style={styles.filterItem}>
                  <Text style={styles.filterLabel}>{column.label}</Text>
                  <TextInput
                    style={styles.filterInput}
                    placeholder={`Search ${column.label}`}
                    value={filters[column.id] || ''}
                    onChangeText={(text) => updateFilter(column.id, text)}
                  />
                </View>
              ))}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.clearButton}
                onPress={() => setFilters({})}
              >
                <Text style={styles.clearButtonText}>Clear All</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.applyButton}
                onPress={applyFilters}
              >
                <Text style={styles.applyButtonText}>Apply Filters</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Columns Modal */}
      <Modal
        visible={isColumnsModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setColumnsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Columns</Text>
              <TouchableOpacity onPress={() => setColumnsModalVisible(false)}>
                <MaterialIcons name="close" size={24} color="#64748B" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {columns.map(column => (
                <View key={column.id} style={styles.columnItem}>
                  <TouchableOpacity
                    style={styles.checkboxContainer}
                    onPress={() => toggleColumnVisibility(column.id)}
                    disabled={column.id === 'actions'} // Disable toggle for actions column
                  >
                    <View style={[
                      styles.checkbox,
                      column.isVisible && styles.checkboxChecked,
                      column.id === 'actions' && styles.checkboxDisabled // Style for disabled checkbox
                    ]}>
                      {column.isVisible && (
                        <MaterialIcons name="check" size={16} color="#FFFFFF" />
                      )}
                    </View>
                    <Text style={styles.columnLabel}>
                      {column.label}
                      {column.id === 'actions' && <Text style={styles.requiredText}> (Required)</Text>}
                    </Text>
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.applyButton}
                onPress={() => setColumnsModalVisible(false)}
              >
                <Text style={styles.applyButtonText}>Apply Changes</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* User Form Modal would go here */}
      <UserFormModal isVisible={isUserFormModalVisible} onClose={onClose} onSave={onSave} initialData={initial}
        disciplineOptions={disciplines} roleOptions={roles}></UserFormModal>
      <DeleteConfirmModal visible={isDeleteModalVisible} onCancel={onClose} onConfirm={deleteUserData}></DeleteConfirmModal>
    </SafeAreaView>
  );

  // Helper function to render cell content based on column ID
  function renderCell(columnId, row) {
    const cellStyle = [styles.cell, getColumnWidth(columnId)];

    switch (columnId) {
      case 'fullName':
        return (
          <View key={columnId} style={cellStyle}>
            <Text style={styles.primaryText}>{row.firstName} {row.lastName}</Text>
            <Text style={styles.secondaryText}>{row.id}</Text>
          </View>
        );
      case 'role':
        return (
          <View key={columnId} style={cellStyle}>
            <RoleBadge role={row.roleName} />
          </View>
        );
      case 'status':
        return (
          <View key={columnId} style={cellStyle}>
            <View style={[
              styles.statusBadge,
              row.isDeleted === 'false' ? styles.activeBadge : styles.inactiveBadge
            ]}>
              <Text style={[
                styles.statusText,
                row.isDeleted === 'false' ? styles.activeText : styles.inactiveText
              ]}>
                {row.isDeleted === 'false' ? 'Active' : 'Inactive'}
              </Text>
            </View>
          </View>
        );
      case 'actions':
        return (
          <View key={columnId} style={[cellStyle, styles.actionsCell]}>
            <TouchableOpacity style={styles.actionIcon} onPress={() => handleEdit(row._id)}>
              <MaterialIcons name="edit" size={18} color="#3B82F6" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionIcon} onPress={() => handleDelete(row._id)}>
              {/* <MaterialIcons name="delete" size={18} color="#3B82F6" /> */}
              <Ionicons name="key-outline" size={18} color="#3B82F6" />
            </TouchableOpacity>
          </View>
        );
      case 'address':
        return (
          <View key={columnId} style={cellStyle}>
            <Text style={styles.cellText}>{row.address1}</Text>
          </View>
        )
      case 'created':
        return (
          <View key={columnId} style={cellStyle}>
            <Text style={styles.cellText}>{formatDate(row.created, 1)}</Text>
          </View>
        )
      case 'updated':
        return (
          <View key={columnId} style={cellStyle}>
            <Text style={styles.cellText}>{formatDate(row.updated, 1)}</Text>
          </View>
        )
      case 'dob':
        return (
          <View key={columnId} style={cellStyle}>
            {row.dob != "" ?
              <Text style={styles.cellText}>{formatDate(row.dob, 2)}</Text>
              :
              <Text style={styles.cellText}></Text>
            }
          </View>
        )
      default:
        return (
          <View key={columnId} style={cellStyle}>
            <Text style={styles.cellText}>{row[columnId]}</Text>
          </View>
        );
    }
  }
  function applyFilters() {
    setFiltersModalVisible(false);
    setCurrentPage(1)
    fetchData(1, itemsPerPage, searchQuery, filters);
  }
  function formatDate(dateStr: string, type: number) {
    const date = new Date(dateStr);

    const padZero = (num) => num.toString().padStart(2, '0');

    const month = padZero(date.getMonth() + 1); // Months are 0-indexed
    const day = padZero(date.getDate());
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = padZero(date.getMinutes());
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const formattedHours = padZero(hours);
    if (type == 1) {
      return `${month}/${day}/${year} ${formattedHours}:${minutes} ${ampm}`;
    } else {
      return `${month}/${day}/${year} `;
    }
  }
  // Helper function to get column width
  function getColumnWidth(columnId) {
    switch (columnId) {
      case 'fullName': return { width: 180 };
      case 'role': return { width: 80 };
      case 'email': return { width: 200 };
      case 'dob': return { width: 100 };
      case 'gender': return { width: 80 };
      case 'address': return { width: 250 };
      case 'primaryPhone': return { width: 150 };
      case 'createdDate': return { width: 150 };
      case 'actions': return { width: 100 };
      case 'status': return { width: 100 };
      default: return { width: 150 };
    }
  }
};

const styles = StyleSheet.create({
  blankTextContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  },
  blankText: {
    textAlign: "center",
    fontSize: 20,
    fontWeight: "bold"
  },
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0F172A',
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 8,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  searchIcon: {
    marginRight: 6,
  },
  searchInput: {
    color: '#0F172A',
    width: 200,
    fontSize: 14,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 10,
  },
  actionButtonText: {
    color: '#64748B',
    fontSize: 14,
    marginLeft: 6,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4F46E5',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 3px rgba(0, 0, 0, 0.19)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
      elevation: 2,
    }),
  },
  horizontalScrollContent: {
    flexGrow: 1,
  },
  tableContainer: {
    flex: 1,
    minWidth: '100%',
  },
  tableHeaderRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  headerCell: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  headerText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  tableBody: {
    flex: 1,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
  },
  cell: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    justifyContent: 'center',
  },
  primaryText: {
    fontSize: 14,
    color: '#0F172A',
    fontWeight: '500',
  },
  secondaryText: {
    fontSize: 12,
    color: '#64748B',
    marginTop: 4,
  },
  cellText: {
    fontSize: 14,
    color: '#0F172A',
    fontFamily: 'Poppins_400Regular',
  },
  roleBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  activeBadge: {
    backgroundColor: '#DCFCE7',
  },
  inactiveBadge: {
    backgroundColor: '#FEE2E2',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  activeText: {
    color: '#22C55E',
  },
  inactiveText: {
    color: '#EF4444',
  },
  actionsCell: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  actionIcon: {
    marginRight: 16,
  },
  // Expanded section styles
  expandedContainer: {
    backgroundColor: '#F8FAFC',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  expandedTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0F172A',
    marginBottom: 12,
  },
  detailsHeaderRow: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: '#E2E8F0',
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
  },
  detailsHeaderCell: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    // borderRightWidth: 1,
    // borderRightColor: '#E2E8F0',
  },
  detailsHeaderText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  detailsRow: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderColor: '#E2E8F0',
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
  },
  detailsCell: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    // borderRightWidth: 1,
    // borderRightColor: '#E2E8F0',
  },
  detailsCellText: {
    fontSize: 14,
    color: '#0F172A',
  },
  // Pagination styles
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  paginationInfo: {
    flex: 1,
  },
  paginationText: {
    fontSize: 14,
    color: '#64748B',
  },
  paginationControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paginationButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 4,
    marginHorizontal: 4,
  },
  paginationButtonDisabled: {
    borderColor: '#E2E8F0',
    backgroundColor: '#F1F5F9',
  },
  paginationButtonText: {
    fontSize: 14,
    color: '#0F172A',
  },
  pageNumberContainer: {
    paddingHorizontal: 12,
  },
  itemsPerPageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemsPerPageButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 4,
    marginLeft: 8,
  },
  itemsPerPageButtonActive: {
    backgroundColor: '#4F46E5',
    borderColor: '#4F46E5',
  },
  itemsPerPageButtonText: {
    fontSize: 14,
    color: '#0F172A',
  },
  itemsPerPageButtonTextActive: {
    color: '#FFFFFF',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '60%',
    maxWidth: 600,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    overflow: 'hidden',
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.1)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 3,
    }),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0F172A',
  },
  modalBody: {
    padding: 16,
    maxHeight: 400,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  filterItem: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
    marginBottom: 8,
  },
  filterInput: {
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
  },
  columnItem: {
    paddingVertical: 8,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#4F46E5',
    borderColor: '#4F46E5',
  },
  checkboxDisabled: {
    opacity: 0.7,
  },
  columnLabel: {
    fontSize: 14,
    color: '#0F172A',
  },
  requiredText: {
    fontSize: 12,
    fontStyle: 'italic',
    color: '#64748B',
  },
  clearButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    marginRight: 12,
  },
  clearButtonText: {
    fontSize: 14,
    color: '#64748B',
  },
  applyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#4F46E5',
    borderRadius: 6,
  },
  applyButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  }
})
export default UsersTableLayout