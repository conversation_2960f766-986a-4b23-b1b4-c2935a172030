import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Image,
  Linking,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
// import { ChevronLeft, Map, ExternalLink } from 'lucide-react-native';
import { StatusBar } from 'expo-status-bar';
import { screenHeight, screenWidth } from 'src/utils/ScreenUtils';
import theme from 'src/theme';

export default function ClinicianDetailsScreen({ navigation, route }) {
  // For demo purposes, this would typically come from route.params or API
  const clinicianData = route.params?.clinicianData;
  const formConfig = route.params?.formConfig;
  //   {
  //   staffId: '1266',
  //   name: 'SanDeep <PERSON>',
  //   firstName: 'SanDeep',
  //   lastName: 'Banga',
  //   status: 'Active',
  //   dob: '01/01/1980',
  //   address1: '4531 N. 16th Street Suite 102',
  //   city: 'Phoenix',
  //   state: 'AZ',
  //   zip: '85016',
  //   primaryPhone: '(*************',
  //   cellPhone: '',
  //   email: '<EMAIL>',
  //   location: 'Haggai Healthcare Corporation',
  //   gender: 'Male',
  //   jobTitle: 'Contractor Admin',
  // };

  const excludeFields = [
    'firstName',
    'lastName',
    'name',
    'staffId',
    'status',
    'state',
    'address1',
    'city',
    'state',
    'zip',
  ];

  const handleBack = () => {
    navigation.goBack();
  };

  const handleEdit = () => {
    // Navigate to edit screen with clinician data
    // navigation.navigate('EditClinicianScreen', { clinicianData });
    alert('Coming Soon...');
  };

  const navigateToMap = () => {
    const latitude = 40.74958102694104;
    const longitude = -73.98537418664596;
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`;

    Linking.openURL(url);
  };

  const groupFields = () => {
    const groups = {};

    Object.entries(formConfig).forEach(([fieldId, fieldConfig]) => {
      const { fieldGroupName } = fieldConfig;
      if (!groups[fieldGroupName]) {
        groups[fieldGroupName] = [];
      }
      if (!excludeFields.includes(fieldId)) {
        groups[fieldGroupName].push({ fieldId, ...fieldConfig });
      }
    });

    return groups;
  };

  const renderInfoItem = (item, index, isLast = false) => (
    <View key={index} style={[styles.infoRow, !isLast && styles.borderBottom]}>
      <Text style={styles.infoLabel}>{item.label}</Text>
      <Text style={styles.infoValue}>
        {clinicianData[item.fieldId] || 'N/A'}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Header */}
      <View style={styles.headerBar}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          {/* <ChevronLeft color="#1F2937" size={24} /> */}
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Clinician Details</Text>
        {/*<TouchableOpacity style={styles.editButton} onPress={handleEdit}>*/}
        {/*  <LinearGradient*/}
        {/*    colors={['#4C51BF', '#6B46C1']}*/}
        {/*    style={styles.editGradient}*/}
        {/*  >*/}
        {/*    <Text style={styles.editButtonText}>Edit</Text>*/}
        {/*  </LinearGradient>*/}
        {/*</TouchableOpacity>*/}
      </View>

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.contentContainer}>
          {/* Personal Information Section with Photo */}
          <View style={styles.personalInfoSection}>
            <View style={styles.photoNameSection}>
              <View style={styles.photoContainer}>
                <Text style={styles.photoPlaceholder}>Photo Not Available</Text>
              </View>
              <View style={styles.nameInfoContainer}>
                <Text
                  style={styles.fullName}
                >{`${clinicianData.lastName}, ${clinicianData.firstName}`}</Text>
                <View style={styles.idDisciplineRow}>
                  <Text style={styles.idLabel}>Clinician ID:</Text>
                  <Text style={styles.idValue}>{clinicianData.staffId}</Text>
                </View>
                {/*<View style={styles.idDisciplineRow}>*/}
                {/*  <Text style={styles.idLabel}>Discipline:</Text>*/}
                {/*  <Text style={styles.idValue}>{clinicianData.discipline}</Text>*/}
                {/*</View>*/}
                <View style={styles.idDisciplineRow}>
                  <Text style={styles.idLabel}>Status:</Text>
                  <Text
                    style={[
                      styles.statusValue,
                      {
                        color:
                          clinicianData.status === 'Active'
                            ? '#10B981'
                            : '#EF4444',
                      },
                    ]}
                  >
                    {clinicianData.status}
                  </Text>
                </View>
              </View>
            </View>

            {/* Address Section */}
            <View style={styles.addressSection}>
              <Text style={styles.sectionLabel}>Address:</Text>
              <Text style={styles.addressText}>{clinicianData.address1}</Text>
              {/*{clinicianData.address2 ? (*/}
              {/*  <Text style={styles.addressText}>{clinicianData.address2}</Text>*/}
              {/*) : null}*/}
              <Text style={styles.addressText}>{`${clinicianData.city}`}</Text>
              <Text
                style={styles.addressText}
              >{`${clinicianData.state} - ${clinicianData.zip}`}</Text>
              {/*<Text style={styles.addressText}>*/}
              {/*  State: {clinicianData.state || ''}*/}
              {/*</Text>*/}

              <View style={styles.mapButtonsContainer}>
                <TouchableOpacity
                  style={styles.mapButton}
                  onPress={navigateToMap}
                >
                  {/* <Map size={16} color="#4C51BF" /> */}
                  <Text style={styles.mapButtonText}>Map</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Contact Info */}
            {/*<View style={styles.contactInfoSection}>*/}
            {/*  {personalInfo.map((item, index) =>*/}
            {/*    renderInfoItem(item, index, index === personalInfo.length - 1),*/}
            {/*  )}*/}
            {/*</View>*/}
          </View>

          {Object.entries(groupFields()).map(([groupName, fields]) => (
            <View key={groupName} style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>{groupName}</Text>
              <View style={styles.sectionContent}>
                {fields.map((field, index) =>
                  renderInfoItem(field, index, index === fields.length - 1),
                )}
              </View>
            </View>
          ))}

          {/*/!* Other Information *!/*/}
          {/*<View style={styles.sectionContainer}>*/}
          {/*  <Text style={styles.sectionTitle}>Other Information</Text>*/}
          {/*  <View style={styles.sectionContent}>*/}
          {/*    {otherInfo.map((item, index) =>*/}
          {/*      renderInfoItem(item, index, index === otherInfo.length - 1),*/}
          {/*    )}*/}
          {/*  </View>*/}
          {/*</View>*/}

          {/*/!* Organization Role *!/*/}
          {/*<View style={styles.sectionContainer}>*/}
          {/*  <Text style={styles.sectionTitle}>Organization Role</Text>*/}
          {/*  <View style={styles.sectionContent}>*/}
          {/*    {organizationRoles.map((item, index) =>*/}
          {/*      renderInfoItem(*/}
          {/*        item,*/}
          {/*        index,*/}
          {/*        index === organizationRoles.length - 1,*/}
          {/*      ),*/}
          {/*    )}*/}
          {/*  </View>*/}
          {/*</View>*/}

          {/* Bottom spacing */}
          <View style={{ height: 40 }} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background || '#F1F5F9',
  },
  headerBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    height: 65,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
    position: 'relative',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#1F2937',
  },
  editButton: {
    flex: 1,
    height: 36,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'absolute',
    right: 24,
  },
  editGradient: {
    height: '100%',
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButtonText: {
    fontFamily: 'Poppins_500Medium',
    color: '#FFFFFF',
    fontSize: 14,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    width: screenWidth,
    alignItems: 'center',
    height: screenHeight - 65,
  },
  personalInfoSection: {
    width: '95%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  photoNameSection: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  photoContainer: {
    width: 120,
    height: 120,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    padding: 16,
  },
  photoPlaceholder: {
    color: '#9CA3AF',
    fontSize: 12,
    textAlign: 'center',
    fontFamily: 'Poppins_400Regular',
  },
  nameInfoContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  fullName: {
    fontSize: 18,
    fontFamily: 'Poppins_600SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  idDisciplineRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  idLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    color: '#4B5563',
    marginRight: 4,
  },
  idValue: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
  },
  statusValue: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
  },
  addressSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionLabel: {
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    color: '#4B5563',
    marginBottom: 8,
  },
  addressText: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
    lineHeight: 22,
  },
  mapButtonsContainer: {
    flexDirection: 'row',
    marginTop: 12,
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  directionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  mapButtonText: {
    color: '#4C51BF',
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    marginLeft: 6,
  },
  contactInfoSection: {
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    alignItems: 'flex-start',
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  infoLabel: {
    width: '40%',
    fontSize: 14,
    fontFamily: 'Poppins_500Medium',
    color: '#4B5563',
  },
  infoValue: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#1F2937',
  },
  sectionContainer: {
    width: '95%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Poppins_600SemiBold',
    color: '#1F2937',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  roleSubtitle: {
    fontSize: 14,
    fontFamily: 'Poppins_400Regular',
    color: '#4B5563',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionContent: {
    padding: 16,
  },
});
