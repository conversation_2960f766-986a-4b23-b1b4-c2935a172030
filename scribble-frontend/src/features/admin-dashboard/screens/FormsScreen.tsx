import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { useResponsive } from 'src/hooks/useResponsive';
import { Trash, Plus, Edit, ClipboardList } from 'lucide-react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { globalStyles } from 'src/styles';
import { fetchFormList,fetchTemplateList } from '../../form-builder/form-api';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { screenHeight } from '@/src/utils/ScreenUtils';
import theme from '@/src/theme';
import { FormList } from '../components/FormList';
import { TemplateList } from '../components/TemplateList';
import { setPageTitle } from '@/src/utils/GeneralUtils';

type FormData = {
  id: string;
  name: string;
  status: 'Active' | 'Draft' | 'Inactive';
  assessments: number;
};

const colorThemes = [
  { background: '#F1E8FF', color: '#7B3FF2' }, // Purple
  { background: '#E8F7FF', color: '#219EBC' }, // Teal Blue
  { background: '#FFF4E6', color: '#FF7F11' }, // Orange
  { background: '#E6FFFA', color: '#00B894' }, // Mint Green
  { background: '#FFEFF7', color: '#D72638' }, // Rose Red
  { background: '#F6FFE6', color: '#6A994E' }, // Olive Green
  { background: '#E6F0FF', color: '#3A86FF' }, // Royal Blue
  { background: '#FFFDE7', color: '#FFB703' }, // Golden Yellow
  { background: '#FBE7FF', color: '#9D4EDD' }, // Violet
  { background: '#E7FFF7', color: '#06D6A0' }, // Seafoam Green
];

console.log(colorThemes);
const Tab = createMaterialTopTabNavigator();
export default function FormsScreen() {
  
  
  // const [forms, setForms] = useState<FormData[]>([
  //   { id: 'f1', name: 'SOC Assessment', status: 'Active', assessments: 150 },
  //   { id: 'f2', name: 'ROC Form', status: 'Draft', assessments: 0 },
  //   { id: 'f3', name: 'Recertification', status: 'Active', assessments: 10 },
  //   { id: 'f4', name: 'Discharge', status: 'Inactive', assessments: 25 },
  // ]);
  
  
  return (
    <View style={tabStyles.root}>
      {/* Header */}
      {/* <Text style={styles.headerTitle}>Forms</Text> */}
      <View style={tabStyles.tabContainer}>
        <Tab.Navigator 
          screenOptions={({ route }) => ({
            tabBarStyle: tabStyles.tabBar,
            tabBarLabelStyle: tabStyles.tabLabel,
            tabBarLabel: ({ focused }) =>
              focused ? (
                <Text style={[tabStyles.tabLabel, tabStyles.tabLabelSelected]}>
                  {route.name}
                </Text>
              ) : (
                <Text style={tabStyles.tabLabel}>{route.name}</Text>
              ),
            tabBarIndicatorStyle: tabStyles.tabIndicator,
            lazy: true,
          })}
        >
          <Tab.Screen name="Forms"  component={FormList} />
          <Tab.Screen name="Templates"  component={TemplateList} />
          
        </Tab.Navigator>
      </View>
      
    </View>
  );
}

/** Helper to get color-coded status badges */
function getStatusBadgeStyle(status: string) {
  switch (status) {
    case 'Active':
      return { backgroundColor: '#D1FAE5', color: '#065F46' }; // Green
    case 'Draft':
      return { backgroundColor: '#FDE68A', color: '#92400E' }; // Orange
    case 'Inactive':
      return { backgroundColor: '#F3F4F6', color: '#6B7280' }; // Gray
    default:
      return { backgroundColor: '#E5E7EB', color: '#374151' };
  }
}
const tabStyles=StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },tabContainer: {
    display: "flex",
    flex: 1,
    height: screenHeight - 65,
  },tabBar: {
    backgroundColor: "#fff",
  },
  tabLabel: {
    color: theme.colors.ternaryText,
    fontFamily: "Poppins_500Medium",
    fontSize: 13,
  },
  tabLabelSelected: {
    color: theme.colors.secondary,
  },
  tabIndicator: {
    backgroundColor: theme.colors.secondary,
    height: 3,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
})

