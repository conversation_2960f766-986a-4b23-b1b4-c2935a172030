import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Calendar,
  User,
  MessageCircle,
  Settings,
  Home,
  PlusCircle,
  ChevronLeft,
  ChevronRight,
  Users,
  File,
  LayoutDashboard,
  PanelLeftClose,
  PanelLeftOpen,
  Stethoscope,
  Briefcase,
  BriefcaseMedical,
  ClipboardList,
} from 'lucide-react-native';
import { globalStyles } from 'src/styles';
import { StatusBar } from 'expo-status-bar';
import { useResponsive } from 'src/hooks/useResponsive';
import { getFormattedDate } from 'src/utils/DateUtils';
import { screenHeight } from 'src/utils/ScreenUtils';

export default function DashboardLayout({ navigation }) {
  const { isTabletOrMobileDevice } = useResponsive();
  const styles = dashboardStyles(isTabletOrMobileDevice);
  const [currentMonth, setCurrentMonth] = useState('January 2021');

  const onAddClinician = () => {
    navigation.navigate('CreateStaffScreen');
  };

  // Sample data
  const stats = [
    // {
    //   title: 'Clinicians',
    //   count: '170',
    //   color: '#4361ee',
    //   icon: <Stethoscope size={20} color="#fff" />,
    // },
    {
      title: 'Active Clinicians',
      count: '150',
      icon: <Users size={20} color="#fff" />,
      color: '#4361ee',
    },
    {
      title: 'In-Active Clinicians',
      count: '150',
      icon: <Users size={20} color="#fff" />,
      color: '#f15887',
    },
    // {
    //   title: 'Visits',
    //   count: '128',
    //   icon: <BriefcaseMedical size={20} color="#fff" />,
    //   color: '#f15887',
    // },
    // {
    //   title: 'Not Started Visits',
    //   count: '50',
    //   icon: <BriefcaseMedical size={20} color="#fff" />,
    //   color: '#f15887',
    // },
    // {
    //   title: 'In-Progress Visits',
    //   count: '20',
    //   icon: <BriefcaseMedical size={20} color="#fff" />,
    //   color: '#f15887',
    // },
    // {
    //   title: 'In-Review Visits',
    //   count: '25',
    //   icon: <BriefcaseMedical size={20} color="#fff" />,
    //   color: '#f15887',
    // },
    // {
    //   title: 'Completed Visits',
    //   count: '37',
    //   icon: <BriefcaseMedical size={20} color="#fff" />,
    //   color: '#f15887',
    // },
    {
      title: 'Clients',
      count: '250',
      icon: <BriefcaseMedical size={20} color="#fff" />,
      color: '#3fcad9',
    },
    {
      title: 'Episodes',
      count: '10',
      icon: <ClipboardList size={20} color="#fff" />,
      color: '#6A994E',
    },
  ];

  const patients = [
    {
      name: 'Arya Wijaya Kusuma',
      date: 'Jan 28, 2020',
      timeStart: '9 AM',
      timeEnd: '11 AM',
      avatar: require('assets/images/icon.png'),
    },
    {
      name: 'Sherly Indriani',
      date: 'Jan 27, 2020',
      timeStart: '10 AM',
      timeEnd: '11 AM',
      avatar: require('assets/images/icon.png'),
    },
    {
      name: 'Nafiu efandyar maulidy',
      date: 'Jan 26, 2020',
      timeStart: '7 AM',
      timeEnd: '9 AM',
      avatar: require('assets/images/icon.png'),
    },
  ];

  const appointments = [
    {
      title: 'Assessments Past 48 Hours',
      subtitle: '30 Assessments',
      time: '10:00 AM',
      color: '#4361ee',
      attendees: [
        require('assets/images/icon.png'),
        require('assets/images/icon.png'),
        require('assets/images/icon.png'),
      ],
    },
    {
      title: 'Assessment Pending Submission',
      subtitle: '8 Assessments',
      time: '1:00 PM',
      color: '#f15887',
      attendees: [
        require('assets/images/icon.png'),
        require('assets/images/icon.png'),
      ],
    },
    {
      title: 'Upcoming Assessments - SOC',
      subtitle: '8 Assessments',
      time: '3:30 PM',
      color: '#3fcad9',
      attendees: [
        require('assets/images/icon.png'),
        require('assets/images/icon.png'),
        require('assets/images/icon.png'),
      ],
    },
    {
      title: 'Upcoming Assessments - RECERT',
      subtitle: '2 Assessments',
      time: '5:00 PM',
      color: '#6A994E',
      attendees: [require('assets/images/icon.png')],
    },
  ];

  const dataHierarchy = [
    {
      title: 'Client',
      description:
        'A client can have multiple episodes, each covering a specific care period.',
    },
    {
      title: 'Episodes',
      description:
        'Each episode lasts for a 60-day period and consists of multiple visits.',
    },
    {
      title: 'Visits',
      description:
        'Visits are scheduled appointments during an episode for patient care.',
    },
    {
      title: 'Forms',
      description:
        'Each visit contains multiple forms for documentation and care details.',
    },
  ];

  const weekdays = ['Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <View style={styles.container}>
      {/* Left sidebar */}
      {/*
        <View style={styles.sidebar}>
          <LinearGradient
            colors={['#4C51BF', '#6B46C1']}
            style={styles.sidebarGradient}
          >
            <TouchableOpacity style={styles.sidebarIcon}>
              <Home color="#FFFFFF" size={24} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.sidebarIcon}>
              <Calendar color="#FFFFFF" size={24} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.sidebarIcon}>
              <MessageCircle color="#FFFFFF" size={24} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.sidebarIcon}>
              <User color="#FFFFFF" size={24} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.sidebarIcon}>
              <Settings color="#FFFFFF" size={24} />
            </TouchableOpacity>
          </LinearGradient>
        </View>
*/}

      {/* Main content */}
      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.headerTitle}>Dashboard</Text>
            <Text style={styles.headerDate}>{getFormattedDate()}</Text>
          </View>
          <View style={styles.headerRight}>
            {/*<TouchableOpacity style={styles.historyButton}>*/}
            {/*  <Text style={styles.historyButtonText}>Appointment History</Text>*/}
            {/*  <Text style={styles.plusIcon}>+</Text>*/}
            {/*</TouchableOpacity>*/}
            <TouchableOpacity
              style={styles.addPatientButton}
              onPress={onAddClinician}
            >
              <LinearGradient
                colors={['#4C51BF', '#6B46C1']}
                style={styles.addPatientGradient}
              >
                <Text style={styles.addPatientText}>Add Clinician</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>

        {/* Welcome card */}
        {/* <LinearGradient
          colors={['#4361ee', '#3a56e4']}
          style={styles.welcomeCard}
        >
          <View style={styles.welcomeContent}>
            <Image
              source={require('assets/images/doctor-dashboard.png')}
              style={styles.doctorImage}
            />
            <View style={styles.welcomeText}>
              <Text style={styles.welcomeTitle}>Welcome, Dr. Shabrina</Text>
              <Text style={styles.welcomeSubtitle}>
                Have a nice day at work
              </Text>
            </View>
          </View>
          <View style={styles.loadingCircle}>
            <View style={styles.loadingArc}></View>
          </View>
        </LinearGradient>*/}

        {/* Statistics */}
        <View style={styles.statsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Report</Text>
            <TouchableOpacity style={styles.filterButton}>
              <Text style={styles.filterText}>Today</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <View key={index} style={styles.statCard}>
                <View
                  style={[
                    styles.statIconContainer,
                    { backgroundColor: stat.color },
                  ]}
                >
                  {stat.icon}
                </View>
                <Text style={styles.statTitle}>{stat.title}</Text>
                <Text style={styles.statCount}>{stat.count}</Text>
              </View>
            ))}
            {/*   <TouchableOpacity style={styles.addStatCard}>
              <PlusCircle color="#6B7280" size={24} />
            </TouchableOpacity>*/}
          </View>
        </View>

        {/* Calendar section */}
        <View style={styles.calendarSection}>
          <View style={styles.calendarHeader}>
            {/*<TouchableOpacity>*/}
            {/*  <ChevronLeft color="#374151" size={20} />*/}
            {/*</TouchableOpacity>*/}
            <Text style={styles.calendarTitle}>Visits Report</Text>
            {/*<TouchableOpacity>*/}
            {/*  <ChevronRight color="#374151" size={20} />*/}
            {/*</TouchableOpacity>*/}
          </View>

          {/*<View style={styles.dayLabelContainer}>*/}
          {/*  <Text style={styles.dayLabelText}>Thu</Text>*/}
          {/*  <Text style={styles.dayNumber}>28</Text>*/}
          {/*</View>*/}

          <ScrollView
            style={styles.appointmentsContainer}
            showsVerticalScrollIndicator={false}
          >
            {appointments.map((appointment, index) => (
              <View
                key={index}
                style={[
                  styles.appointmentCard,
                  { backgroundColor: appointment.color + '15' },
                ]}
              >
                <View style={styles.appointmentContent}>
                  <Text
                    style={[
                      styles.appointmentTitle,
                      { color: appointment.color },
                    ]}
                  >
                    {appointment.title}
                  </Text>
                  <Text style={styles.appointmentSubtitle}>
                    {appointment.subtitle}
                  </Text>
                </View>
                <View style={styles.attendeesContainer}>
                  {appointment.attendees.map((attendee, idx) => (
                    <Image
                      key={idx}
                      source={attendee}
                      style={[
                        styles.attendeeAvatar,
                        { marginLeft: idx > 0 ? -10 : 0 },
                      ]}
                    />
                  ))}
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Right panel */}
      {/*<View style={styles.rightPanel}>
          <View style={styles.profileSection}>
            <Text style={styles.profileTitle}>My Profile</Text>
            <TouchableOpacity style={styles.notificationIcon}>
              <Text style={styles.notificationDot}>•</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.profileCardContainer}>
            <View style={styles.profileImageContainer}>
              <Image
                source={require('assets/images/splash-icon.png')}
                style={styles.profileImage}
              />
            </View>
            <Text style={styles.doctorName}>Shabrina</Text>
          </View>

          <View style={styles.patientListSection}>
            <View style={styles.patientListHeader}>
              <Text style={styles.patientListTitle}>Last Patient</Text>
              <TouchableOpacity>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>

            {patients.map((patient, index) => (
              <View key={index} style={styles.patientCard}>
                <Image source={patient.avatar} style={styles.patientAvatar} />
                <View style={styles.patientInfo}>
                  <Text style={styles.patientName}>{patient.name}</Text>
                  <Text style={styles.patientDate}>
                    {patient.date} • {patient.timeStart} - {patient.timeEnd}
                  </Text>
                </View>
              </View>
            ))}
          </View>

          <View style={styles.patientStatsCard}>
            <View style={styles.patientStatsHeader}>
              <Text style={styles.patientStatsTitle}>Number of patients</Text>
            </View>
            <View style={styles.graphContainer}>
              <View style={styles.graph}>
                <View style={styles.graphLine}></View>
                <View style={styles.graphDot}></View>
              </View>
            </View>
            <View style={styles.statsFooter}>
              <View style={styles.statCount}>
                <Text style={styles.bigNumber}>134</Text>
                <Text style={styles.statLabel}>Patients</Text>
              </View>
              <View style={styles.weekDays}>
                {weekdays.map((day, index) => (
                  <View key={index} style={styles.weekDay}>
                    <Text
                      style={[
                        styles.dayText,
                        day === 'Thu' ? styles.activeDay : {},
                      ]}
                    >
                      {day}
                    </Text>
                    {day === 'Thu' && (
                      <Text style={styles.activeDayNumber}>28</Text>
                    )}
                  </View>
                ))}
              </View>
            </View>
          </View>
        </View>*/}
    </View>
  );
}

const dashboardStyles = isMobile =>
  StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
    },
    sidebar: {
      width: 80,
      backgroundColor: '#4361ee',
    },
    sidebarGradient: {
      flex: 1,
      alignItems: 'center',
      paddingTop: 20,
      paddingBottom: 20,
    },
    sidebarIcon: {
      width: 40,
      height: 40,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: 15,
    },
    content: {
      flex: 2,
      paddingHorizontal: 20,
      height: screenHeight - 80,
      paddingBottom: 40,
    },
    rightPanel: {
      flex: 1,
      backgroundColor: '#1a1f37',
      borderTopLeftRadius: 20,
      borderBottomLeftRadius: 20,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    headerTitle: {
      fontSize: 22,
      fontFamily: 'Poppins_600SemiBold',
      color: '#1F2937',
    },
    headerDate: {
      fontSize: 14,
      fontFamily: 'Poppins_400Regular',
      color: '#6B7280',
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    historyButton: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 15,
    },
    historyButtonText: {
      fontFamily: 'Poppins_400Regular',
      color: '#4361ee',
      marginRight: 5,
    },
    plusIcon: {
      fontSize: 16,
      color: '#4361ee',
    },
    addPatientButton: {
      borderRadius: 8,
      overflow: 'hidden',
    },
    addPatientGradient: {
      paddingVertical: 8,
      paddingHorizontal: 16,
    },
    addPatientText: {
      color: '#FFFFFF',
      fontFamily: 'Poppins_500Medium',
      fontSize: 14,
    },
    welcomeCard: {
      flexDirection: 'row',
      borderRadius: 16,
      padding: 20,
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    welcomeContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    doctorImage: {
      width: 80,
      height: 80,
      marginRight: 15,
    },
    welcomeText: {
      justifyContent: 'center',
    },
    welcomeTitle: {
      color: '#FFFFFF',
      fontSize: 20,
      fontFamily: 'Poppins_600SemiBold',
      marginBottom: 4,
    },
    welcomeSubtitle: {
      color: '#E5E7EB',
      fontSize: 14,
      fontFamily: 'Poppins_400Regular',
    },
    loadingCircle: {
      width: 50,
      height: 50,
      borderRadius: 25,
      borderWidth: 3,
      borderColor: 'rgba(255, 255, 255, 0.2)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingArc: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 3,
      borderTopColor: '#FFFFFF',
      borderRightColor: 'transparent',
      borderBottomColor: 'transparent',
      borderLeftColor: 'transparent',
      transform: [{ rotate: '45deg' }],
    },
    statsSection: {
      marginBottom: 20,
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 15,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: 'Poppins_600SemiBold',
      color: '#1F2937',
    },
    filterButton: {
      backgroundColor: '#F3F4F6',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 8,
    },
    filterText: {
      fontSize: 12,
      color: '#4B5563',
      fontFamily: 'Poppins_400Regular',
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    statCard: {
      width: '23%',
      backgroundColor: '#FFFFFF',
      borderRadius: 12,
      padding: 15,
      marginBottom: 10,
    },
    statIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 10,
    },
    statTitle: {
      fontSize: 12,
      color: '#6B7280',
      fontFamily: 'Poppins_400Regular',
      marginBottom: 5,
    },
    statCount: {
      fontSize: 20,
      color: '#1F2937',
      fontFamily: 'Poppins_700Bold',
    },
    addStatCard: {
      width: '23%',
      height: 120,
      backgroundColor: '#FFFFFF',
      borderRadius: 12,
      borderStyle: 'dashed',
      borderWidth: 1,
      borderColor: '#D1D5DB',
      justifyContent: 'center',
      alignItems: 'center',
    },
    calendarSection: {
      backgroundColor: '#FFFFFF',
      borderRadius: 12,
      padding: 15,
      flex: 1,
    },
    calendarHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 15,
      paddingHorizontal: 10,
    },
    calendarTitle: {
      fontSize: 16,
      fontFamily: 'Poppins_600SemiBold',
      color: '#1F2937',
    },
    dayLabelContainer: {
      alignItems: 'center',
      marginVertical: 10,
    },
    dayLabelText: {
      fontSize: 14,
      color: '#6B7280',
      fontFamily: 'Poppins_400Regular',
    },
    dayNumber: {
      fontSize: 16,
      color: '#1F2937',
      fontFamily: 'Poppins_600SemiBold',
      marginTop: 5,
    },
    appointmentsContainer: {
      flex: 1,
    },
    appointmentCard: {
      flexDirection: 'row',
      borderRadius: 12,
      padding: 15,
      marginBottom: 10,
      justifyContent: 'space-between',
    },
    appointmentContent: {
      flex: 1,
    },
    appointmentTitle: {
      fontSize: 14,
      fontFamily: 'Poppins_600SemiBold',
      marginBottom: 4,
    },
    appointmentSubtitle: {
      fontSize: 12,
      color: '#6B7280',
      fontFamily: 'Poppins_400Regular',
    },
    attendeesContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    attendeeAvatar: {
      width: 30,
      height: 30,
      borderRadius: 15,
      borderWidth: 2,
      borderColor: '#FFFFFF',
    },
    profileSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    profileTitle: {
      fontSize: 18,
      fontFamily: 'Poppins_600SemiBold',
      color: '#FFFFFF',
    },
    notificationIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    notificationDot: {
      color: '#FF5C5C',
      fontSize: 18,
    },
    profileCardContainer: {
      alignItems: 'center',
      marginBottom: 30,
    },
    profileImageContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: '#2A3158',
      padding: 5,
      marginBottom: 10,
    },
    profileImage: {
      width: 70,
      height: 70,
      borderRadius: 35,
    },
    doctorName: {
      fontSize: 16,
      color: '#FFFFFF',
      fontFamily: 'Poppins_500Medium',
    },
    patientListSection: {
      marginBottom: 20,
    },
    patientListHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 15,
    },
    patientListTitle: {
      fontSize: 16,
      color: '#FFFFFF',
      fontFamily: 'Poppins_600SemiBold',
    },
    viewAllText: {
      fontSize: 12,
      color: '#6B7280',
      fontFamily: 'Poppins_400Regular',
    },
    patientCard: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 15,
    },
    patientAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: 10,
    },
    patientInfo: {
      flex: 1,
    },
    patientName: {
      fontSize: 14,
      color: '#FFFFFF',
      fontFamily: 'Poppins_500Medium',
      marginBottom: 2,
    },
    patientDate: {
      fontSize: 12,
      color: '#9CA3AF',
      fontFamily: 'Poppins_400Regular',
    },
    patientStatsCard: {
      backgroundColor: '#4361ee',
      borderRadius: 12,
      padding: 15,
    },
    patientStatsHeader: {
      marginBottom: 15,
    },
    patientStatsTitle: {
      fontSize: 14,
      color: '#FFFFFF',
      fontFamily: 'Poppins_500Medium',
    },
    graphContainer: {
      height: 80,
      justifyContent: 'center',
      marginBottom: 10,
    },
    graph: {
      position: 'relative',
    },
    graphLine: {
      height: 2,
      backgroundColor: '#FF5C5C',
      width: '100%',
      position: 'absolute',
      top: '50%',
      borderRadius: 1,
    },
    graphDot: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#FFFFFF',
      position: 'absolute',
      top: '42%',
      right: '25%',
      borderWidth: 2,
      borderColor: '#FF5C5C',
    },
    statsFooter: {
      marginTop: 10,
    },
    statCount: {
      marginBottom: 15,
    },
    bigNumber: {
      fontSize: 24,
      color: '#FFFFFF',
      fontFamily: 'Poppins_700Bold',
    },
    statLabel: {
      fontSize: 14,
      color: '#E5E7EB',
      fontFamily: 'Poppins_400Regular',
    },
    weekDays: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    weekDay: {
      alignItems: 'center',
    },
    dayText: {
      fontSize: 12,
      color: '#E5E7EB',
      fontFamily: 'Poppins_400Regular',
    },
    activeDay: {
      color: '#FFFFFF',
      fontFamily: 'Poppins_600SemiBold',
    },
    activeDayNumber: {
      fontSize: 14,
      color: '#FFFFFF',
      fontFamily: 'Poppins_600SemiBold',
      marginTop: 4,
    },
  });
