import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, SafeAreaView, TextInput, Platform } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';

// Sample data for the dashboard
const initialData = [
  {
    id: '1',
    clientName: '<PERSON> Dick<PERSON>',
    clientId: '8209',
    dob: '03/25/1965',
    visitDate: 'Aug 20, 2025',
    time: '8:00 AM - 10:00 AM',
    clinician: '<PERSON>',
    discipline: 'RN',
    type: 'ROC',
    status: 'In Progress',
    forms: '02',
    isExpanded: true,
    subRows: [
      {
        name: 'Haggai Health - SOC',
        type: 'SOC',
        date: 'Aug 20, 2025, 11:15 AM',
        episode: '#1 - 03/04/2025-05/03/2025',
        status: 'New',
      },
      {
        name: 'Haggai Health - Wound Care',
        type: 'Wound Care',
        date: 'Aug 20, 2025, 11:15 AM',
        episode: '#1 - 03/04/2025-05/03/2025',
        status: 'Submitted to E<PERSON>',
      },
    ],
  },
  {
    id: '2',
    clientName: '<PERSON><PERSON>',
    clientId: '8210',
    dob: '03/25/1965',
    visitDate: 'Apr 01, 2025',
    time: '6:00 AM - 5:00 AM',
    clinician: 'Regina Mohr',
    discipline: 'OT',
    type: 'Follow Up',
    status: 'New',
    forms: '01',
    isExpanded: false,
    subRows: [],
  },
  {
    id: '3',
    clientName: 'Candace Dicki',
    clientId: '8211',
    dob: '11/20/1980',
    visitDate: 'Feb 01, 2025',
    time: '8:00 AM - 10:00 AM',
    clinician: 'John Smith',
    discipline: 'PT',
    type: 'ROC',
    status: 'New',
    forms: '02',
    isExpanded: false,
    subRows: [],
  },
  {
    id: '4',
    clientName: 'Candace Dicki',
    clientId: '8212',
    dob: '06/01/1950',
    visitDate: 'Jan 10, 2025',
    time: '8:00 AM - 10:00 AM',
    clinician: 'John Smith',
    discipline: 'RN',
    type: 'Wound Care',
    status: 'Past Due',
    forms: '0',
    isExpanded: false,
    subRows: [],
  },
  {
    id: '5',
    clientName: 'Candace Dicki',
    clientId: '8213',
    dob: '01/05/1978',
    visitDate: 'Mar 03, 2025',
    time: '8:00 AM - 10:00 AM',
    clinician: 'John Smith',
    discipline: 'MSW',
    type: 'SOC',
    status: 'In Progress',
    forms: '02',
    isExpanded: false,
    subRows: [],
  },
  {
    id: '6',
    clientName: 'Candace Dicki',
    clientId: '8214',
    dob: '03/21/1985',
    visitDate: 'Aug 14, 2025',
    time: '8:00 AM - 10:00 AM',
    clinician: 'John Smith',
    discipline: 'SLP',
    type: 'Follow Up',
    status: 'Completed',
    forms: '0',
    isExpanded: false,
    subRows: [],
  },
  {
    id: '7',
    clientName: 'Candace Dicki',
    clientId: '8215',
    dob: '03/25/1965',
    visitDate: 'Aug 21, 2025',
    time: '8:00 AM - 10:00 AM',
    clinician: 'John Smith',
    discipline: 'SN',
    type: 'ROC',
    status: 'New',
    forms: '01',
    isExpanded: false,
    subRows: [],
  },
];

// Custom status badge renderer
const StatusBadge = ({ status }) => {
  let bgColor = '#DBEAFE'; // Default light blue (New)
  let textColor = '#3B82F6'; // Default blue

  if (status === 'In Progress') {
    bgColor = '#FEF9C3'; // Light yellow
    textColor = '#EAB308'; // Amber
  } else if (status === 'Past Due') {
    bgColor = '#FEE2E2'; // Light red
    textColor = '#EF4444'; // Red
  } else if (status === 'Completed') {
    bgColor = '#DBEAFE'; // Light blue 
    textColor = '#3B82F6'; // Blue
  } else if (status === 'Submitted to EMR') {
    bgColor = '#FEF9C3'; // Light yellow
    textColor = '#EAB308'; // Amber
  }

  return (
    <View style={[styles.statusBadge, { backgroundColor: bgColor }]}>
      <Text style={[styles.statusText, { color: textColor }]}>{status}</Text>
    </View>
  );
};

const DashboardLayout = () => {
  const [data, setData] = useState(initialData);
  const [selectedStatus, setSelectedStatus] = useState('All Statuses');

  const toggleExpand = (id) => {
    setData(
      data.map((item) => {
        if (item.id === id) {
          return { ...item, isExpanded: !item.isExpanded };
        }
        return item;
      })
    );
  };

  const tableHeaders = [
    { id: 'arrow', label: '', flex: 0.5 },
    { id: 'clientName', label: 'Client Name | Id', flex: 2 },
    { id: 'dob', label: 'DOB', flex: 1 },
    { id: 'visitDate', label: 'Visit Date', flex: 1.2 },
    { id: 'time', label: 'Time', flex: 1.5 },
    { id: 'clinician', label: 'Clinician', flex: 1.2 },
    { id: 'discipline', label: 'Discipline', flex: 1 },
    { id: 'type', label: 'Type', flex: 1 },
    { id: 'status', label: 'Status', flex: 1.5 },
    { id: 'forms', label: 'Forms', flex: 0.8 },
  ];

  const formHeaders = [
    { id: 'name', label: 'Form Name', flex: 2 },
    { id: 'type', label: 'Type', flex: 1.2 },
    { id: 'date', label: 'Date', flex: 1.8 },
    { id: 'episode', label: 'Episode', flex: 2.2 },
    { id: 'status', label: 'Status', flex: 1.5 },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Dashboard</Text>
        <View style={styles.searchContainer}>
          <View style={styles.searchBox}>
            <Ionicons name="search" size={18} color="#94A3B8" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search"
              placeholderTextColor="#94A3B8"
            />
          </View>
          <TouchableOpacity style={styles.filterButton}>
            <MaterialIcons name="filter-list" size={18} color="#64748B" />
            <Text style={styles.filterText}>{selectedStatus}</Text>
            <MaterialIcons name="keyboard-arrow-down" size={18} color="#64748B" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.mainContent}>
        <View style={styles.tableWrapper}>
          <ScrollView horizontal contentContainerStyle={styles.horizontalScrollContent}>
            <View style={styles.tableContainer}>
              {/* Header row */}
              <View style={styles.tableHeaderRow}>
                {tableHeaders.map((header) => (
                  <View key={header.id} style={[styles.headerCell, { flex: header.flex }]}>
                    {header.label && <Text style={styles.headerText}>{header.label}</Text>}
                  </View>
                ))}
              </View>

              {/* Table body */}
              <ScrollView style={styles.tableBody}>
                {data.map((row) => (
                  <View key={row.id}>
                    {/* Main row */}
                    <TouchableOpacity
                      style={styles.tableRow}
                      onPress={() => toggleExpand(row.id)}
                      activeOpacity={0.7}
                    >
                      <View style={[styles.cell, { flex: 0.5 }]}>
                        <MaterialIcons
                          name={row.isExpanded ? 'keyboard-arrow-down' : 'keyboard-arrow-right'}
                          size={20}
                          color="#94A3B8"
                        />
                      </View>
                      <View style={[styles.cell, { flex: 2 }]}>
                        <Text style={styles.primaryText}>{row.clientName}</Text>
                        <Text style={styles.secondaryText}>{row.clientId}</Text>
                      </View>
                      <View style={[styles.cell, { flex: 1 }]}>
                        <Text style={styles.cellText}>{row.dob}</Text>
                      </View>
                      <View style={[styles.cell, { flex: 1.2 }]}>
                        <Text style={styles.cellText}>{row.visitDate}</Text>
                      </View>
                      <View style={[styles.cell, { flex: 1.5 }]}>
                        <Text style={styles.cellText}>{row.time}</Text>
                      </View>
                      <View style={[styles.cell, { flex: 1.2 }]}>
                        <Text style={styles.cellText}>{row.clinician}</Text>
                      </View>
                      <View style={[styles.cell, { flex: 1 }]}>
                        <Text style={styles.cellText}>{row.discipline}</Text>
                      </View>
                      <View style={[styles.cell, { flex: 1 }]}>
                        <Text style={styles.cellText}>{row.type}</Text>
                      </View>
                      <View style={[styles.cell, { flex: 1.5 }]}>
                        <StatusBadge status={row.status} />
                      </View>
                      <View style={[styles.cell, { flex: 0.8, alignItems: 'center' }]}>
                        <Text style={styles.cellText}>{row.forms}</Text>
                      </View>
                    </TouchableOpacity>

                    {/* Expanded forms section */}
                    {row.isExpanded && row.subRows.length > 0 && (
                      <View style={styles.expandedContainer}>
                        <Text style={styles.expandedTitle}>Forms</Text>

                        {/* Forms table header */}
                        <View style={styles.formsHeaderRow}>
                          {formHeaders.map((header) => (
                            <View key={header.id} style={[styles.formsHeaderCell, { flex: header.flex }]}>
                              <Text style={styles.formsHeaderText}>{header.label}</Text>
                            </View>
                          ))}
                        </View>

                        {/* Forms table rows */}
                        {row.subRows.map((subRow, index) => (
                          <View key={index} style={styles.formsRow}>
                            <View style={[styles.formsCell, { flex: 2 }]}>
                              <Text style={styles.formsCellText}>{subRow.name}</Text>
                            </View>
                            <View style={[styles.formsCell, { flex: 1.2 }]}>
                              <Text style={styles.formsCellText}>{subRow.type}</Text>
                            </View>
                            <View style={[styles.formsCell, { flex: 1.8 }]}>
                              <Text style={styles.formsCellText}>{subRow.date}</Text>
                            </View>
                            <View style={[styles.formsCell, { flex: 2.2 }]}>
                              <Text style={styles.formsCellText}>{subRow.episode}</Text>
                            </View>
                            <View style={[styles.formsCell, { flex: 1.5 }]}>
                              <StatusBadge status={subRow.status} />
                            </View>
                          </View>
                        ))}
                      </View>
                    )}
                  </View>
                ))}
              </ScrollView>
            </View>
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#0F172A',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 10,
  },
  searchIcon: {
    marginRight: 6,
  },
  searchInput: {
    color: '#0F172A',
    width: 200,
    height: 24,
    fontSize: 14,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  filterText: {
    color: '#64748B',
    fontSize: 14,
    marginHorizontal: 8,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 3px rgba(0, 0, 0, 0.19)',
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
      elevation: 3,
    }),
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15,
    width: '100%',
  },
  tableWrapper: {
    flex: 1,
    width: '100%',
  },
  horizontalScrollContent: {
    flexGrow: 1,
    minWidth: '100%',
  },
  tableContainer: {
    flex: 1,
    width: '100%',
  },
  tableHeaderRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15,
    width: '100%',
  },
  headerCell: {
    paddingVertical: 16,
    paddingHorizontal: 10,
    justifyContent: 'center',
  },
  headerText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  tableBody: {
    flex: 1,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
    width: '100%',
  },
  cell: {
    paddingVertical: 16,
    paddingHorizontal: 10,
    justifyContent: 'center',
  },
  primaryText: {
    fontSize: 14,
    color: '#0F172A',
    fontWeight: '400',
  },
  secondaryText: {
    fontSize: 12,
    color: '#64748B',
    marginTop: 2,
  },
  cellText: {
    fontSize: 14,
    color: '#0F172A',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Expanded section styles
  expandedContainer: {
    backgroundColor: '#EFF3F6',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  expandedTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#0F172A',
    marginBottom: 12,
  },
  formsHeaderRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
  },
  formsHeaderCell: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    justifyContent: 'center',
  },
  formsHeaderText: {
    fontSize: 14,
    color: '#64748B',
    fontFamily: 'Poppins_400Regular',
  },
  formsRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
  },
  formsCell: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    justifyContent: 'center',
  },
  formsCellText: {
    fontSize: 14,
    color: '#0F172A',
    fontFamily: 'Poppins_400Regular',
  }
});

export default DashboardLayout;