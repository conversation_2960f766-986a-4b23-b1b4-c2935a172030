import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  TextInput,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
// import {
//   Settings,
//   Bell,
//   Globe,
//   Shield,
//   User,
//   Lock,
//   HelpCircle,
//   ChevronRight,
//   LogOut,
//   Clock,
//   MessageCircle,
// } from 'lucide-react-native';
import { StatusBar } from 'expo-status-bar';
import { useResponsive } from 'src/hooks/useResponsive';
import { getFormattedDate } from 'src/utils/DateUtils';
import { screenHeight } from 'src/utils/ScreenUtils';
import { logout } from 'src/redux/slices/authSlice';
import { useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { setPageTitle } from '@/src/utils/GeneralUtils';

export default function SettingsScreen({ navigation }) {
  setPageTitle('Settings');
  const { isTabletOrMobileDevice } = useResponsive();
  const styles = settingsStyles(isTabletOrMobileDevice);

  const dispatch = useDispatch();

  // State for switch components
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [emailUpdates, setEmailUpdates] = useState(true);
  const [twoFactorAuth, setTwoFactorAuth] = useState(false);
  const [autoLogout, setAutoLogout] = useState(true);
  
  // State for session timeout
  const [sessionTimeout, setSessionTimeout] = useState('30');

  const onLogout = async () => {
    await AsyncStorage.clear();
    dispatch(logout());
    navigation.replace('Login');
  };

  const navigateToChangePassword = () => {
    navigation.navigate('ResetPassword');
  };

  const settingsSections = [
    {
      title: 'Account',
      items: [
        // {
        //   title: 'Profile Information',
        //   icon: <User size={20} color="#4361ee" />,
        //   action: 'navigate',
        //   destination: 'ProfileScreen',
        // },
        // {
        //   title: 'Change Password',
        //   icon: <Lock size={20} color="#4361ee" />,
        //   action: 'navigate',
        //   onPress: navigateToChangePassword,
        // },
        
        // {
        //   title: 'Security',
        //   icon: <Shield size={20} color="#3fcad9" />,
        //   action: 'navigate',
        //   destination: 'SecurityScreen',
        // },
        // {
        //   title: 'Notifications',
        //   icon: <Bell size={20} color="#f15887" />,
        //   action: 'toggle',
        //   state: notifications,
        //   setState: setNotifications,
        // },
      ],
    },
    // {
    //   title: 'Preferences',
    //   items: [
    //     // {
    //     //   title: 'Dark Mode',
    //     //   icon: <Globe size={20} color="#6A994E" />,
    //     //   action: 'toggle',
    //     //   state: darkMode,
    //     //   setState: setDarkMode,
    //     // },
    //     {
    //       title: 'Email Updates',
    //       icon: <Bell size={20} color="#f15887" />,
    //       action: 'toggle',
    //       state: emailUpdates,
    //       setState: setEmailUpdates,
    //     },
    //   ],
    // },
    // {
    //   title: 'Security',
    //   items: [
    //     {
    //       title: 'Change Password',
    //       icon: <Lock size={20} color="#4361ee" />,
    //       action: 'navigate',
    //       onPress: navigateToChangePassword,
    //     },
    //     {
    //       title: 'Auto Logout',
    //       icon: <LogOut size={20} color="#f15887" />,
    //       action: 'toggle',
    //       state: autoLogout,
    //       setState: setAutoLogout,
    //     },
    //     // {
    //     //   title: 'Session Timeout (minutes)',
    //     //   icon: <Clock size={20} color="#f15887" />,
    //     //   action: 'input',
    //     //   value: sessionTimeout,
    //     //   setValue: setSessionTimeout,
    //     // },
    //   ],
    // },
    // {
    //   title: 'Support',
    //   items: [
    //     {
    //       title: 'Help Center',
    //       icon: <HelpCircle size={20} color="#6A994E" />,
    //       action: 'navigate',
    //       destination: 'HelpCenter',
    //     },
    //     {
    //       title: 'Contact Support',
    //       icon: <MessageCircle size={20} color="#4361ee" />,
    //       action: 'navigate',
    //       destination: 'ContactSupport',
    //     },
    //   ],
    // },
  ];

  const renderSettingItem = item => {
    return (
      <TouchableOpacity
        style={styles.settingItem}
        onPress={() => item?.onPress()}
      >
        <View style={styles.settingItemLeft}>
          <View style={styles.settingIconContainer}>{item.icon}</View>
          <Text style={styles.settingItemTitle}>{item.title}</Text>
        </View>

        {item.action === 'toggle' && (
          <Switch
            value={item.state}
            onValueChange={item.setState}
            trackColor={{ false: '#D1D5DB', true: '#4361ee' }}
            thumbColor={item.state ? '#FFFFFF' : '#FFFFFF'}
          />
        )}

        {/* {item.action === 'navigate' && (
          <ChevronRight size={20} color="#6B7280" />
        )} */}

        {item.action === 'input' && (
          <TextInput
            style={styles.inputField}
            value={item.value}
            onChangeText={item.setValue}
            keyboardType="numeric"
            maxLength={3}
          />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      
      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.headerTitle}>Settings</Text>
            <Text style={styles.headerDate}>{getFormattedDate()}</Text>
          </View>
        </View>

        {/* Settings Sections */}
        {settingsSections.map((section, index) => (
          <View key={index} style={styles.settingsSection}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.settingsCard}>
              {section.items.map((item, idx) => (
                <React.Fragment key={idx}>
                  {renderSettingItem(item)}
                  {idx < section.items.length - 1 && (
                    <View style={styles.divider} />
                  )}
                </React.Fragment>
              ))}
            </View>
          </View>
        ))}

        {/* Version info */}
        <View style={styles.versionInfo}>
          <Text style={styles.versionText}>App Version: 1.0.0</Text>
        </View>

        {/* Logout button */}
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={() => onLogout()}
        >
          <LinearGradient
            colors={['#f15887', '#e74c6f']}
            style={styles.logoutGradient}
          >
            {/* <LogOut size={20} color="#FFFFFF" style={styles.logoutIcon} /> */}
            <Text style={styles.logoutText}>Logout</Text>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const settingsStyles = isMobile =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingBottom: 40,
      height: screenHeight - 100,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    headerTitle: {
      fontSize: 22,
      fontFamily: 'Poppins_600SemiBold',
      color: '#1F2937',
    },
    headerDate: {
      fontSize: 14,
      fontFamily: 'Poppins_400Regular',
      color: '#6B7280',
    },
    settingsSection: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: 'Poppins_600SemiBold',
      color: '#1F2937',
      marginBottom: 10,
    },
    settingsCard: {
      backgroundColor: '#FFFFFF',
      borderRadius: 12,
      padding: 15,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 3.84,
      elevation: 5,
    },
    settingItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
    },
    settingItemLeft: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    settingIconContainer: {
      width: 36,
      height: 36,
      borderRadius: 8,
      backgroundColor: '#F3F4F6',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    settingItemTitle: {
      fontSize: 16,
      fontFamily: 'Poppins_500Medium',
      color: '#1F2937',
    },
    divider: {
      height: 1,
      backgroundColor: '#E5E7EB',
    },
    inputField: {
      borderWidth: 1,
      borderColor: '#D1D5DB',
      borderRadius: 6,
      paddingHorizontal: 12,
      paddingVertical: 6,
      width: 60,
      textAlign: 'center',
      fontFamily: 'Poppins_400Regular',
    },
    versionInfo: {
      alignItems: 'center',
      marginVertical: 20,
    },
    versionText: {
      fontSize: 14,
      fontFamily: 'Poppins_400Regular',
      color: '#6B7280',
    },
    logoutButton: {
      borderRadius: 12,
      overflow: 'hidden',
      marginBottom: 30,
    },
    logoutGradient: {
      flexDirection: 'row',
      paddingVertical: 15,
      paddingHorizontal: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    logoutIcon: {
      marginRight: 10,
    },
    logoutText: {
      color: '#FFFFFF',
      fontFamily: 'Poppins_600SemiBold',
      fontSize: 16,
    },
  });
