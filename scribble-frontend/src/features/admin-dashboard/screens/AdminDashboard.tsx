import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
} from 'react-native';
// import {
//   Bell,
//   Search as SearchIcon,
//   User,
//   Book,
//   File,
//   LayoutDashboard,
//   PanelLeftClose,
//   PanelLeftOpen,
//   Settings,
//   Users,
//   Stethoscope,
//   LogOut,
//   BriefcaseMedical,
//   ClipboardList,
//   CalendarCheck2,
//   LifeBuoy,
// } from 'lucide-react-native';

import { useResponsive } from 'src/hooks/useResponsive';
import '@expo/match-media';
import { globalStyles } from 'src/styles';
import { getInitials } from 'src/utils/ProfileUtils';
import FormsScreen from 'src/features/admin-dashboard/screens/FormsScreen';
import { useDispatch } from 'react-redux';
import { login, logout } from 'src/redux/slices/authSlice';
import ClinicianScreen from 'src/features/admin-dashboard/screens/ClinicianScreen';
import PatientsScreen from 'src/features/admin-dashboard/screens/PatientsScreen';
import DashboardLayout from 'src/features/admin-dashboard/screens/DashboardLayout';
import AssessmentScreen from 'src/features/admin-dashboard/screens/AssessmentScreen';
import AdminFAQScreen from 'src/features/admin-dashboard/screens/AdminFAQScreen';
import SettingsScreen from 'src/features/admin-dashboard/screens/SettingsScreen';
import { useNavigation } from '@react-navigation/native';
import { setPageTitle } from '@/src/utils/GeneralUtils';
import { AntDesign, Feather, FontAwesome, MaterialIcons } from '@expo/vector-icons';

const adminName = 'John Smith';

function Header({
  onToggleSidebar,
  sidebarOpen,
}: {
  onToggleSidebar: () => void;
  sidebarOpen: boolean;
}) {
  const [searchValue, setSearchValue] = useState('');

  return (
    <View style={styles.header}>
      {/* Left side: optional burger for mobile to toggle sidebar */}
      <TouchableOpacity style={styles.burgerButton} onPress={onToggleSidebar}>
        {sidebarOpen ? (
          <AntDesign name="leftcircleo" size={20} color="#374151" />
        ) : (
          <AntDesign name="rightcircleo" size={20} color="#374151" />
        )}
      </TouchableOpacity>

      <Text style={styles.headerTitle}> Admin</Text>
      <View style={styles.headerRight}>
        <TouchableOpacity style={styles.avatarContainer}>
          <Text style={styles.avatarText}>{getInitials(adminName)}</Text>
        </TouchableOpacity>
        <Text style={styles.adminLabel}>{adminName}</Text>
      </View>

      {/* Search field */}
      {/*<View style={styles.searchContainer}>*/}
      {/*  <SearchIcon color="#6B7280" size={18} style={styles.searchIcon} />*/}
      {/*  <TextInput*/}
      {/*    style={styles.searchInput}*/}
      {/*    placeholder="Search..."*/}
      {/*    placeholderTextColor="#9CA3AF"*/}
      {/*    value={searchValue}*/}
      {/*    onChangeText={setSearchValue}*/}
      {/*  />*/}
      {/*</View>*/}

      {/* Right side: notifications (no avatar here) */}
      {/*<View style={styles.headerRight}>*/}
      {/*  <TouchableOpacity style={styles.headerIconButton}>*/}
      {/*    <Bell color="#1F2937" size={24} />*/}
      {/*  </TouchableOpacity>*/}
      {/*  <TouchableOpacity style={styles.avatarContainer}>*/}
      {/*    <Text style={styles.avatarText}>{getInitials(adminName)}</Text>*/}
      {/*  </TouchableOpacity>*/}
      {/*</View>*/}
    </View>
  );
}

function SideMenu({
  isOpen,
  onClose,
  activeItem, // for the currently selected menu item
  onSelectItem,
}: {
  isOpen: boolean;
  onClose: () => void;
  activeItem: string; // e.g. "dashboard", "nurses", etc.
  onSelectItem: (id: string) => void;
}) {
  const navigation = useNavigation();

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon:<MaterialIcons name="dashboard" size={20} color="#374151" />
    },
    {
      id: 'nurses',
      label: 'Clinicians',
      icon: <FontAwesome name="stethoscope" size={20} color="#374151" />,
    },
    {
      id: 'patients',
      label: 'Clients',
      icon: <Feather name="users" size={20} color="#374151" />,
    },
   
    {
      id: 'forms',
      label: 'Forms',
      icon: <AntDesign name="form" size={20} color="#374151" />,
    },
    
    {
      id: 'settings',
      label: 'Settings',
      icon: <AntDesign name="setting" size={20} color="#374151" />,
    },
  ];

  const onNavigateToProfile = (): void => {
    // navigation.navigate('ProfileDetails');
  };

  return (
    <View style={[styles.sidebar, !isOpen && styles.sidebarClosed]}>
      {/* Avatar + Admin text */}
      <TouchableOpacity
        style={styles.profileContainer}

      >
        {/* <TouchableOpacity style={styles.avatarContainer}>
          <Text style={styles.avatarText}>{getInitials(adminName)}</Text>
        </TouchableOpacity>
        <Text style={styles.adminLabel}>{adminName}</Text> */}
        <View style={styles.iconContainer}>
          <Image
            source={require('assets/images/icon.png')}
            style={styles.logo}
          />
        </View>
      </TouchableOpacity>

      {menuItems.map(item => (
        <NavItem
          key={item.id}
          label={item.label}
          icon={item.icon}
          isSelected={activeItem === item.id}
          onPress={() => onSelectItem(item.id)}
        />
      ))}
    </View>
  );
}

/** Nav item with hover + selected (active) styles (left border) */
function NavItem({
  label,
  icon,
  isSelected,
  onPress,
}: {
  label: string;
  icon: JSX.Element;
  isSelected: boolean;
  onPress: () => void;
}) {
  const [hovered, setHovered] = useState(false);

  return (
    <TouchableOpacity
      style={[
        styles.navItem,
        hovered && styles.navItemHover,
        isSelected && styles.navItemSelected, // active style
      ]}
      onPress={onPress}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {isSelected && <View style={styles.navItemBar} />}

      <View style={styles.navItemContent}>
        {icon}
        <Text style={styles.navItemText}>{label}</Text>
      </View>
    </TouchableOpacity>
  );
}

/**
 * The main AdminDashboard that uses the sub-components.
 */
export default function AdminDashboard({ navigation }) {
  setPageTitle('Home | Scribble');
  const { isTabletOrMobileDevice } = useResponsive();
  const [sidebarOpen, setSidebarOpen] = useState(!isTabletOrMobileDevice);

  const dispatch = useDispatch();

  // Track the selected menu item
  const [activeItem, setActiveItem] = useState('dashboard');

  const toggleSidebar = () => {
    setSidebarOpen(prev => !prev);
  };

  const handleSelectItem = (id: string) => {
    setActiveItem(id);
  };

  const renderContent = () => {
    switch (activeItem) {
      case 'forms':
        setPageTitle('Forms');
        return <FormsScreen  />;
      case 'nurses':
        return <ClinicianScreen navigation={navigation} />;
      case 'patients':
        return <PatientsScreen navigation={navigation} />;
      case 'dashboard':
        return <DashboardLayout navigation={navigation} />;
      case 'assessments':
        return <AssessmentScreen navigation={navigation} />;
      case 'faq':
        return <AdminFAQScreen navigation={navigation} />;
      case 'settings':
        setPageTitle('Settings');
        return <SettingsScreen navigation={navigation} />;
    }
  };

  return (
    <View style={styles.root}>
      {(!isTabletOrMobileDevice || sidebarOpen) && (
        <SideMenu
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          activeItem={activeItem}
          onSelectItem={handleSelectItem}
        />
      )}

      <View style={styles.mainArea}>
        <Header onToggleSidebar={toggleSidebar} sidebarOpen={sidebarOpen} />

        {/* Render content based on activeItem, etc. */}
        <View style={styles.contentContainer}>
          {/* Maybe conditionally show different components */}
          {renderContent()}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  root: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#F8FAFC',
  },
  // SIDEBAR
  sidebar: {
    width: 240,
    backgroundColor: '#FFFFFF',
    borderRightColor: '#E2E8F0',
    borderRightWidth: 1,
    padding: 16,
  },
  sidebarClosed: {
    width: 0,
    overflow: 'hidden',
    padding: 0,
  },
  // The new profile container at top of sidebar
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // marginBottom: 40,
  },
  avatarContainer: {
    width: 36,
    height: 36,
    borderRadius: 20,
    backgroundColor: '#4C51BF', // brand color or any
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
  },
  adminLabel: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 18,
    color: '#1F2937',
    marginLeft: 8,
  },

  // Nav items
  navItem: {
    position: 'relative',
    paddingVertical: 10,
    marginBottom: 8,
    backgroundColor: 'transparent',
    borderRadius: 8,
    justifyContent: 'center', // center the icon+label
  },
  navItemHover: {
    backgroundColor: '#F1F5F9',
  },
  navItemSelected: {
    // No left border or background color here,
    // the bar is separate. You can add a highlight
    backgroundColor: '#F1F5F9',
  },
  navItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  navItemText: {
    fontFamily: 'PlusJakartaSans_400Regular',
    fontSize: 14,
    color: '#374151',
    marginLeft: 6,
  },

  // The tall bar (about 10cm => ~378px IRL,
  // but we'll approximate ~100–120px to avoid extreme size).
  navItemBar: {
    position: 'absolute',
    left: -2,
    width: 4,
    height: 20,
    backgroundColor: '#4C51BF', // brand color
    borderRadius: 2,
  },

  // MAIN AREA
  mainArea: {
    flex: 1,
    flexDirection: 'column',
  },
  // HEADER
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
    position: 'relative',
    height: 65,
  },
  burgerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 22,
    color: '#1F2937',
    marginLeft: 24,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 8,
    marginHorizontal: 16,
    width: '50%',
  },
  searchIcon: {
    marginRight: 4,
  },
  searchInput: {
    flex: 1,
    height: 36,
    fontFamily: 'PlusJakartaSans_400Regular',
    fontSize: 14,
    color: '#000000',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    right: 24,
  },
  headerIconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },

  // CONTENT
  contentContainer: {
    flex: 1,
    padding: 16,
  },

  // FORMLIST
  formListContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    ...globalStyles.shadow,
  },
  sectionTitle: {
    fontFamily: 'Poppins_600SemiBold',
    fontSize: 16,
    color: '#1F2937',
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  formName: {
    fontFamily: 'PlusJakartaSans_400Regular',
    fontSize: 14,
    color: '#374151',
  },
  formStatus: {
    fontFamily: 'PlusJakartaSans_400Regular',
    fontSize: 14,
    color: '#6B7280',
  },
  iconContainer: {
    display: "flex",
    justifyContent: "center",
    marginBottom: 20,
    alignItems: "center",
  },
  logo: {
    width: 60,
    height: 60,
    resizeMode: "contain",
  },
});
