import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Alert,
    ScrollView,
    ActivityIndicator,
    Platform,
    Dimensions, Modal
} from 'react-native';
import { useResponsive } from 'src/hooks/useResponsive';
// import { Trash, Plus, Edit, ClipboardList } from 'lucide-react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { globalStyles } from 'src/styles';
import { fetchFormList, deleteFormData } from '../../form-builder/form-api';
import { AntDesign, Feather, FontAwesome5, Ionicons } from '@expo/vector-icons';
const { width, height } = Dimensions.get('window');
import { MessageModal } from '@/src/utils/MessageModal';
import { setPageTitle } from '@/src/utils/GeneralUtils';

const ConfirmationModal = ({ visible, onCancel, onConfirm }) => {
    // Modal content remains the same
    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={visible}
        >
            <View style={confModalStyle.overlay}>
                <View style={confModalStyle.modalContainer}>
                    <View style={styles.iconContainer}>
                        <Ionicons name="checkmark-circle" size={48} color="#1D75F5" />
                    </View>
                    {/* <Text style={styles.title}>Submit Recording?</Text> */}
                    <Text style={confModalStyle.message}>
                        Do you want to delete the form permanently?
                    </Text>
                    <View style={confModalStyle.buttonContainer}>
                        <TouchableOpacity style={confModalStyle.cancelButton} onPress={onCancel}>
                            <Text style={confModalStyle.cancelButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={confModalStyle.confirmButton} onPress={onConfirm}>
                            <Text style={confModalStyle.confirmButtonText}>Confirm</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
};
export const FormList = () => {
    setPageTitle('Forms');
    const navigation = useNavigation();
    const [loading, setLoading] = useState(true);
    const { isTabletOrMobileDevice } = useResponsive();
    const [hoverItemId, setHoverItemId] = useState<string | null>(null);

    const [showMessageModal, setShowMessageModal] = useState(false)
    const [message, setMessage] = useState("")
    const [messageType, setMessageType] = useState("success")

    const FORM_LIMIT = 40

    const isWeb = Platform.OS === 'web';

    const [forms, setForms] = useState([]);
    const [showConfModal, setShowConfModal] = useState(false);
    const [deletedFormId, setDeletedFormId] = useState("")
    const [loadingMore, setLoadingMore] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const containerRef = useRef(null);
    const scrollTimeout = useRef(null);


    const fetchList = async (pageNumber = 1) => {
        if (loadingMore || !hasMore) return;

        try {
            if (pageNumber === 1) setLoading(true);
            else setLoadingMore(true);

            const result = await fetchFormList(FORM_LIMIT, pageNumber);
            console.log("data length:" + result.data.length);
            if (result.data.length > 0) {
                setForms((prev) => (pageNumber === 1 ? result.data : [...prev, ...result.data]));
                setPage(pageNumber + 1);
                if (result.data.length < FORM_LIMIT || result.total_records_available <= FORM_LIMIT * pageNumber) {
                    setHasMore(false);
                }
            } else {
                setHasMore(false);
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
            setLoadingMore(false);
        }
    };
    useFocusEffect(
        useCallback(() => {
            setHasMore(true);
            setPage(page);
            fetchList(page);
        }, [])
    );
    const handleLoadMore = () => {
        if (!loadingMore && hasMore) {
            fetchList(page);
        }
    };
    /** Helper to get color-coded status badges */
    function getStatusBadgeStyle(isPublish: boolean) {
        // switch (status) {
        //     case 'Active':
        //         return { backgroundColor: '#D1FAE5', color: '#065F46' }; // Green
        //     case 'Draft':
        //         return { backgroundColor: '#FDE68A', color: '#92400E' }; // Orange
        //     case 'Inactive':
        //         return { backgroundColor: '#F3F4F6', color: '#6B7280' }; // Gray
        //     default:
        //         return { backgroundColor: '#E5E7EB', color: '#374151' };
        // }
        switch (isPublish) {
            case true:
                return { backgroundColor: '#D1FAE5', color: '#065F46' }; // Green
            default:
                return { backgroundColor: '#F3F4F6', color: '#6B7280' }; // Gray 
        }
    }
    const handleWebScroll = (event) => {
        if (scrollTimeout.current) clearTimeout(scrollTimeout.current);

        scrollTimeout.current = setTimeout(() => {
            if (event.target) {
                const { scrollTop, scrollHeight, clientHeight } = event.target;
                // console.log(`ScrollTop: ${scrollTop}, ScrollHeight: ${scrollHeight}, ClientHeight: ${clientHeight}`);
                if (scrollHeight - scrollTop <= clientHeight + 20) {
                    handleLoadMore();
                }
            }
        }, 100);
    };
    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleWebScroll);
        }

        // Check for null before removing the event listener
        return () => {
            if (container) {
                container.removeEventListener('scroll', handleWebScroll);
            }
            if (scrollTimeout.current) {
                clearTimeout(scrollTimeout.current);
            }
        };
    }, [page, loadingMore, hasMore]);



    const handleDeleteForm = (id: string) => {

        setDeletedFormId(id)
        setShowConfModal(true)
    };

    const handleFormPress = (id: string) => {
        console.log("id : " + id)
        navigation.navigate('FormCreation', { formId: id, type: "form" });
    };

    const handleStartFromScratch = () => {
        navigation.navigate('FormCreation', { formId: 'new', type: "form" });
    };
    const onConfModalCancel = () => {
        setShowConfModal(false);
        setShowMessageModal(false)
    }
    const onConfModalConfirm = () => {
        onConfModalCancel()
        // console.log(deletedFormId)
        deleteFormData(deletedFormId)
            .then((result) => {
                if (result.status == "ok") {
                    setForms(prev => prev.filter(f => f._id !== deletedFormId));
                    // alert("Form Deleted Successfully.")
                    setShowMessageModal(true)
                    setMessage("Form Deleted Successfully.")
                    setMessageType("success")
                } else {
                    setShowMessageModal(true)
                    setMessage(result.errorMessage)
                    setMessageType("error")
                }
            })
            .catch((error) => {
                console.log(error);
            })
    }
    const renderNativeContent = () => {
        return (
            <ScrollView contentContainerStyle={styles.scrollContent}>
                <View
                    style={[
                        styles.gridContainer,
                        isTabletOrMobileDevice && styles.gridContainerMobile,
                    ]}
                >
                    {/* "Start From Scratch" card */}
                    <TouchableOpacity
                        style={[
                            styles.scratchCard,
                            hoverItemId === 'new' && styles.cardHover,
                        ]}
                        onPress={handleStartFromScratch}
                        onMouseEnter={() => setHoverItemId('new')}
                        onMouseLeave={() => setHoverItemId(null)}
                    >
                        <AntDesign name="plus" size={40} color="#4C51BF" />
                        {/* <Plus size={40} color="#4C51BF" /> */}
                        <Text style={styles.scratchText}>Create Form</Text>
                    </TouchableOpacity>

                    {/* Existing Forms */}
                    {loading &&
                        <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
                    }
                    {forms.map(form => (
                        <TouchableOpacity
                            key={form._id}
                            style={[
                                styles.formCard,
                                hoverItemId === form._id && styles.cardHover,
                            ]}
                            onPress={() => handleFormPress(form._id)}
                            onMouseEnter={() => setHoverItemId(form._id)}
                            onMouseLeave={() => setHoverItemId(null)}
                        >
                            <View
                                style={[
                                    styles.badgeContainer,
                                    getStatusBadgeStyle(form.isPublished),
                                ]}
                            >
                                <Text style={styles.badgeText}>{form.isPublished ? 'Published' : 'Draft'}</Text>
                            </View>
                            <View style={styles.iconContainer}>
                                {/* <ClipboardList size={40} color="#4C51BF" /> */}
                            </View>
                            <Text style={styles.formName}>{form.name}</Text>
                            {/* <View style={styles.countBadge}>
                <Text
                  style={styles.countText}
                >{`${form.assessments} Assessments`}</Text>
              </View> */}

                            <View style={styles.cardActions}>
                                <TouchableOpacity style={styles.actionBtn}>
                                    {/* <Edit size={18} color="#4B5563" /> */}
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={styles.actionBtn}
                                    onPress={() => handleDeleteForm(form.id)}
                                >
                                    {/* <Trash size={18} color="#EF4444" /> */}
                                </TouchableOpacity>
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>
            </ScrollView>
        )
    }
    const renderWebContent = () => {
        return (
            <div ref={containerRef}
                style={{
                    overflowY: 'auto',
                    height: '70vh',
                    paddingTop: '10px'
                }}
            >
                <View
                    style={[
                        styles.gridContainer,
                        isTabletOrMobileDevice && styles.gridContainerMobile,
                    ]}
                >
                    {/* "Start From Scratch" card */}
                    <TouchableOpacity
                        style={[
                            styles.scratchCard,
                            hoverItemId === 'new' && styles.cardHover,
                        ]}
                        onPress={handleStartFromScratch}
                        onMouseEnter={() => setHoverItemId('new')}
                        onMouseLeave={() => setHoverItemId(null)}
                    >
                        {/* <Plus size={40} color="#4C51BF" /> */}
                        <AntDesign name="plus" size={40} color="#4C51BF" />
                        <Text style={styles.scratchText}>Create Form</Text>
                    </TouchableOpacity>

                    {/* Existing Forms */}
                    {loading &&
                        <ActivityIndicator size="large" color="#0000ff" style={{ marginTop: 20 }} />
                    }

                    {forms.map(form => (
                        <TouchableOpacity
                            key={form._id}
                            style={[
                                styles.formCard,
                                hoverItemId === form._id && styles.cardHover,
                            ]}
                            onPress={() => handleFormPress(form._id)}
                            onMouseEnter={() => setHoverItemId(form._id)}
                            onMouseLeave={() => setHoverItemId(null)}
                        >
                            <View
                                style={[
                                    styles.badgeContainer,
                                    getStatusBadgeStyle(form.isPublished),
                                ]}
                            >
                                <Text style={styles.badgeText}>{form.isPublished ? 'Published' : 'Draft'}</Text>
                            </View>
                            <View style={styles.iconContainer}>
                            <FontAwesome5 name="clipboard-list" size={40} color="#4C51BF" />
                                {/* <ClipboardList size={40} color="#4C51BF" /> */}
                            </View>
                            <Text style={styles.formName}>{form.name}</Text>
                            {/* <View style={styles.countBadge}>
                <Text
                  style={styles.countText}
                >{`${form.assessments} Assessments`}</Text>
              </View> */}

                            <View style={styles.cardActions}>
                                <TouchableOpacity style={styles.actionBtn}>
                                <Feather name="edit" size={18} color="#4B5563" />
                                    {/* <Edit size={18} color="#4B5563" /> */}
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={styles.actionBtn}
                                    onPress={() => handleDeleteForm(form._id)}
                                >
                                    <Feather name="trash-2" size={18} color="#EF4444" />
                                    {/* <Trash size={18} color="#EF4444" /> */}
                                </TouchableOpacity>
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>
                {loadingMore && (
                    <ActivityIndicator size="small" color="#0000ff" style={{ marginTop: 10, marginBottom: 10 }} />
                )}
            </div>
        )
    }
    return (
        <View style={styles.root}>
            <ConfirmationModal visible={showConfModal} onCancel={onConfModalCancel} onConfirm={onConfModalConfirm} />
            <MessageModal visible={showMessageModal} onCancel={onConfModalCancel} message={message} type={messageType} />
            {/* Header */}
            {/* <Text style={styles.headerTitle}>Forms</Text> */}
            {isWeb ? renderWebContent() : renderNativeContent()}

        </View>
    );
}
const confModalStyle = StyleSheet.create({
    message: {
        color: "#7C7887",
        textAlign: "center",
        fontFamily: "Poppins",
        fontSize: 18,
        fontStyle: "normal",
        fontWeight: 400,
    },
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        width: width * 0.26,
        backgroundColor: '#FFF',
        paddingVertical: 24,
        paddingHorizontal: 20,
        borderRadius: 16,
        alignItems: 'center',
        elevation: 5,
    },
    iconContainer: {
        marginBottom: 16,
    },
    title: {
        color: "#000",
        textAlign: "center",
        fontFamily: "Poppins",
        fontSize: 24,
        fontStyle: "normal",
        fontWeight: "bold",
    },

    buttonContainer: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
        marginTop: 15
    },
    cancelButton: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#1D75F5',
        paddingVertical: 12,
        borderRadius: 24,
        marginRight: 8,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: '#1D75F5',
        fontSize: 16,
        fontWeight: '500',
    },
    confirmButton: {
        flex: 1,
        backgroundColor: '#1D75F5',
        paddingVertical: 12,
        borderRadius: 24,
        marginLeft: 8,
        alignItems: 'center',
    },
    confirmButtonText: {
        color: '#FFF',
        fontSize: 16,
        fontWeight: '500',
    },
})
const styles = StyleSheet.create({
    root: {
        flex: 1,
        backgroundColor: '#F8FAFC',

    },
    headerTitle: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 22,
        color: '#1F2937',
        marginLeft: 16,
    },
    scrollContent: {
        flexGrow: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
    },
    gridContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        width: '80%',
    },
    gridContainerMobile: {
        justifyContent: 'center',
    },
    // FORM CARDS
    formCard: {
        width: 260,
        height: 220,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#E5E7EB',
        borderRadius: 16,
        padding: 16,
        marginRight: 16,
        marginBottom: 16,
        backgroundColor: '#FFFFFF',
        position: 'relative',
    },
    formName: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 16,
        color: '#1F2937',
        marginBottom: 8,
    },
    countBadge: {
        borderRadius: 6,
        paddingHorizontal: 8,
        paddingVertical: 4,
        alignSelf: 'center',
        marginBottom: 12,
        backgroundColor: '#F1F5F9',
    },
    countText: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 12,
    },
    cardActions: {
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 12,
        marginTop: 12,
    },
    actionBtn: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: '#F9FAFB',
        alignItems: 'center',
        justifyContent: 'center',
    },
    // START FROM SCRATCH CARD
    scratchCard: {
        width: 260,
        height: 220,
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        borderStyle: 'dashed',
        borderWidth: 2,
        borderColor: '#4C51BF',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 16,
        marginBottom: 16,
    },
    scratchText: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 14,
        color: '#4C51BF',
        marginTop: 8,
    },
    // Hover effect
    cardHover: {
        ...globalStyles.shadow,
        transform: [{ scale: 1.01 }],
    },
    badgeContainer: {
        position: 'absolute',
        right: 12,
        top: 12,
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 10,
    },
    badgeText: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 12,
        color: '#111827',
    },
    iconContainer: {
        marginBottom: 12,
    },
});