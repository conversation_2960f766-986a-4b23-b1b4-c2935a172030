import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Role } from "src/enums/Role";
import { validateEmail, validateMobile, formatPhoneNumber } from 'src/utils/ProfileUtils';
import { MessageModal } from '@/src/utils/MessageModal';
import stateOptions from '../../../data/location.json'
// Include the form components directly in this file
const Dropdown = ({
    label = "",
    options = [],
    initialValue = "",
    onChange,
    placeholder = "Select an option",
    valueKey = "value",
    labelKey = "label",
    isDisabled = false
}) => {
    const [value, setValue] = useState(initialValue);
    const [isOpen, setIsOpen] = useState(false);
    const [focused, setFocused] = useState(false);
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    const dropdownRef = useRef(null);
    const triggerRef = useRef(null);
    const optionsRef = useRef([]);
    const lastKeyPressTime = useRef(0);
    const searchBuffer = useRef('');

    // Update state when initialValue prop changes
    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    // Reset highlighted index when options change or dropdown opens
    useEffect(() => {
        if (isOpen) {
            // Find current selected option index
            const currentIndex = options.findIndex(option => {
                if (typeof value === 'string' || typeof value === 'number') {
                    return option[valueKey] === value;
                }
                return option[valueKey] === (value ? value[valueKey] : null);
            });
            setHighlightedIndex(currentIndex >= 0 ? currentIndex : 0);
        }
    }, [isOpen, options, value, valueKey]);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
                setFocused(false);
                setHighlightedIndex(-1);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Handle keyboard navigation
    const handleKeyDown = (event) => {
        if (isDisabled) return;

        // Always handle these keys when dropdown has focus
        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                if (!isOpen) {
                    setIsOpen(true);
                    setFocused(true);
                    setHighlightedIndex(0);
                } else {
                    setHighlightedIndex(prev => 
                        prev < options.length - 1 ? prev + 1 : 0
                    );
                }
                break;

            case 'ArrowUp':
                event.preventDefault();
                if (!isOpen) {
                    setIsOpen(true);
                    setFocused(true);
                    setHighlightedIndex(options.length - 1);
                } else {
                    setHighlightedIndex(prev => 
                        prev > 0 ? prev - 1 : options.length - 1
                    );
                }
                break;

            case 'Enter':
            case ' ':
                event.preventDefault();
                if (!isOpen) {
                    setIsOpen(true);
                    setFocused(true);
                    setHighlightedIndex(0);
                } else if (highlightedIndex >= 0 && highlightedIndex < options.length) {
                    handleOptionSelect(options[highlightedIndex]);
                }
                break;

            case 'Escape':
                event.preventDefault();
                setIsOpen(false);
                setFocused(false);
                setHighlightedIndex(-1);
                if (triggerRef.current) {
                    triggerRef.current.blur();
                }
                break;

            case 'Tab':
                setIsOpen(false);
                setFocused(false);
                setHighlightedIndex(-1);
                break;

            default:
                // Handle letter/number keys for first-letter jumping
                if (event.key.length === 1 && /[a-zA-Z0-9]/.test(event.key)) {
                    event.preventDefault();
                    if (!isOpen) {
                        setIsOpen(true);
                        setFocused(true);
                    }
                    handleLetterSearch(event.key.toLowerCase());
                }
                break;
        }
    };

    // Handle keyboard navigation - attach to trigger element
    useEffect(() => {
        const triggerElement = triggerRef.current;
        if (triggerElement) {
            triggerElement.addEventListener('keydown', handleKeyDown);
            return () => {
                triggerElement.removeEventListener('keydown', handleKeyDown);
            };
        }
    }, [isOpen, highlightedIndex, options, isDisabled]);

    // Scroll highlighted option into view
    useEffect(() => {
        if (isOpen && highlightedIndex >= 0 && optionsRef.current[highlightedIndex]) {
            const highlightedElement = optionsRef.current[highlightedIndex];
            const container = highlightedElement.parentElement;
            
            if (container) {
                const containerRect = container.getBoundingClientRect();
                const elementRect = highlightedElement.getBoundingClientRect();
                
                if (elementRect.bottom > containerRect.bottom) {
                    container.scrollTop += elementRect.bottom - containerRect.bottom;
                } else if (elementRect.top < containerRect.top) {
                    container.scrollTop -= containerRect.top - elementRect.top;
                }
            }
        }
    }, [highlightedIndex, isOpen]);

    const handleLetterSearch = (letter) => {
        const currentTime = Date.now();
        
        // If more than 1 second has passed, start a new search
        if (currentTime - lastKeyPressTime.current > 1000) {
            searchBuffer.current = '';
        }
        
        lastKeyPressTime.current = currentTime;
        searchBuffer.current += letter;
        
        // Find option that starts with the search buffer
        const matchingIndex = options.findIndex(option => 
            option[labelKey].toLowerCase().startsWith(searchBuffer.current)
        );
        
        if (matchingIndex >= 0) {
            setHighlightedIndex(matchingIndex);
        }
        
        // Clear search buffer after 2 seconds
        setTimeout(() => {
            if (Date.now() - lastKeyPressTime.current >= 2000) {
                searchBuffer.current = '';
            }
        }, 2000);
    };

    const handleOptionSelect = (option) => {
        setValue(option);
        setIsOpen(false);
        setFocused(false);
        setHighlightedIndex(-1);
        if (onChange) {
            onChange(option[valueKey]);
        }
    };

    const toggleDropdown = () => {
        if (isDisabled) return;
        
        const newIsOpen = !isOpen;
        setIsOpen(newIsOpen);
        setFocused(newIsOpen);
        
        if (newIsOpen) {
            // Set initial highlighted index to current selection or first item
            const currentIndex = options.findIndex(option => {
                if (typeof value === 'string' || typeof value === 'number') {
                    return option[valueKey] === value;
                }
                return option[valueKey] === (value ? value[valueKey] : null);
            });
            setHighlightedIndex(currentIndex >= 0 ? currentIndex : 0);
            
            // Focus the trigger for keyboard navigation
            if (triggerRef.current) {
                triggerRef.current.focus();
            }
        } else {
            setHighlightedIndex(-1);
        }
    };

    const handleMouseEnter = (index) => {
        setHighlightedIndex(index);
    };

    // Find selected option's label
    const getDisplayValue = () => {
        if (!value) return '';
        if (typeof value === 'string' || typeof value === 'number') {
            const found = options.find((opt) => {
                return opt[valueKey] === value
            });
            return found ? found[labelKey] : "";
        }
        return value[labelKey] || '';
    };

    return (
        <View style={dropdownStyles.container} ref={dropdownRef}>
            <div
                ref={triggerRef}
                tabIndex={isDisabled ? -1 : 0}
                style={{ outline: 'none', width: '100%' }}
                onFocus={() => setFocused(true)}
                onBlur={(e) => {
                    // Only blur if not clicking on dropdown options
                    if (!dropdownRef.current?.contains(e.relatedTarget)) {
                        setFocused(false);
                        setIsOpen(false);
                        setHighlightedIndex(-1);
                    }
                }}
            >
                <TouchableOpacity
                    style={[dropdownStyles.field, focused && dropdownStyles.fieldFocused, isDisabled && { backgroundColor: '#e0e0e0' }]}
                    activeOpacity={0.9}
                    onPress={toggleDropdown}
                >
                    <View style={dropdownStyles.fieldContent}>
                        <Text style={dropdownStyles.fieldLabel}>{label}</Text>
                        <View style={dropdownStyles.inputContainer}>
                            {getDisplayValue() ? (
                                <Text style={dropdownStyles.fieldInput}>{getDisplayValue()}</Text>
                            ) : (
                                <Text style={dropdownStyles.placeholder}>{placeholder}</Text>
                            )}
                        </View>
                    </View>
                    {!isDisabled && (
                        <View style={dropdownStyles.arrowIcon}>
                            <Ionicons
                                name={isOpen ? "chevron-up-outline" : "chevron-down-outline"}
                                size={18}
                                color="#999"
                            />
                        </View>
                    )}
                </TouchableOpacity>
            </div>

            {isOpen && !isDisabled && (
                <View style={dropdownStyles.dropdown}>
                    <View style={dropdownStyles.scrollView}>
                        {options.map((option, index) => (
                            <TouchableOpacity
                                key={index}
                                ref={el => optionsRef.current[index] = el}
                                style={[
                                    dropdownStyles.option,
                                    value && option[valueKey] === (typeof value === 'string' ? value : value[valueKey]) &&
                                    dropdownStyles.selectedOption,
                                    highlightedIndex === index && dropdownStyles.highlightedOption
                                ]}
                                onPress={() => handleOptionSelect(option)}
                                onMouseEnter={() => handleMouseEnter(index)}
                            >
                                <Text
                                    style={[
                                        dropdownStyles.optionText,
                                        value && option[valueKey] === (typeof value === 'string' ? value : value[valueKey]) &&
                                        dropdownStyles.selectedOptionText,
                                        highlightedIndex === index && dropdownStyles.highlightedOptionText
                                    ]}
                                >
                                    {option[labelKey]}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            )}
        </View>
    );
};
const TextField = React.forwardRef(({ label = "", initialValue = "", onChange, placeholder = "", iconname, style = {}, disabled = false }, ref) => {
    const [value, setValue] = useState(initialValue);
    const [focused, setFocused] = useState(false);
    const inputRef = useRef(null);

    //Useing  the forwarded ref or fallback to internal ref
    const actualInputRef = ref || inputRef;

    // Update state when initialValue prop changes
    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    const handleChange = (e) => {
        setValue(e.target.value);
        if (onChange) {
            onChange(e.target.value);
        }
    };

    const handleContainerClick = () => {
        if (inputRef.current) {
            inputRef.current.focus();
        }
    };

    return (
        <TouchableOpacity
            style={[dateStyles.field, focused && dateStyles.fieldFocused, style]}
            activeOpacity={0.9}
            onPress={handleContainerClick}
        >
            {/* <View style={dateStyles.fieldIcon}>
        <Ionicons name={iconname} size={18} color="#999" />
      </View> */}
            <View style={dateStyles.fieldContent}>
                <Text style={dateStyles.fieldLabel}>{label}</Text>
                <View style={dateStyles.inputContainer}>
                    {/* The input is always visible but styled to match design */}
                    <input
                        ref={actualInputRef}
                        type="text"
                        value={value}
                        onChange={handleChange}
                        onFocus={() => setFocused(true)}
                        onBlur={() => setFocused(false)}
                        placeholder={placeholder}
                        disabled={disabled}
                        style={{
                            fontSize: 16,
                            color: '#333',
                            padding: 0,
                            border: 'none',
                            outline: 'none',
                            width: '100%',
                            height: '100%',
                            backgroundColor: 'transparent',
                            fontFamily: "Poppins_400Regular",
                            // Show placeholder with correct styling
                            '::placeholder': {
                                color: '#aaa',
                                opacity: 1,
                            }
                        }}
                    />
                </View>
            </View>
        </TouchableOpacity>
    );
});

const DatePickerField = ({ label = "DOB", initialValue = "", onChange, placeholder = "MM/DD/YYYY" }) => {
    const [value, setValue] = useState("");
    const [displayValue, setDisplayValue] = useState("");
    const [focused, setFocused] = useState(false);
    const [isManualInput, setIsManualInput] = useState(false);
    const inputRef = useRef(null);
    const hiddenDateInputRef = useRef(null);

    // Format date for display (MM/DD/YYYY)
    const formatDisplayDate = (dateString) => {
        if (!dateString) return "";
        
        // If it's already in MM/DD/YYYY format, return as is
        if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateString)) {
            return dateString;
        }
        
        // If it's in YYYY-MM-DD format (from date input), convert to MM/DD/YYYY
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            const [year, month, day] = dateString.split('-');
            return `${month}/${day}/${year}`;
        }
        
        // Try to parse as date
        const date = new Date(dateString);
        if (!isNaN(date.getTime())) {
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const year = date.getFullYear();
            return `${month}/${day}/${year}`;
        }
        
        return dateString;
    };

    // Convert MM/DD/YYYY to YYYY-MM-DD for date input
    const convertToDateInputFormat = (displayDate) => {
        if (!displayDate || !displayDate.includes('/')) return "";
        
        const parts = displayDate.split('/');
        if (parts.length === 3) {
            const [month, day, year] = parts;
            if (month.length <= 2 && day.length <= 2 && year.length === 4) {
                return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            }
        }
        return "";
    };

    // Initialize display value
    useEffect(() => {
        if (initialValue) {
            const formatted = formatDisplayDate(initialValue);
            setDisplayValue(formatted);
            setValue(convertToDateInputFormat(formatted) || initialValue);
        }
    }, [initialValue]);

    // Format input as user types MM/DD/YYYY
    const formatDateInput = (input) => {
        // Remove all non-numeric characters
        const numbers = input.replace(/\D/g, '');
        
        // Apply MM/DD/YYYY formatting
        if (numbers.length <= 2) {
            return numbers;
        } else if (numbers.length <= 4) {
            return `${numbers.slice(0, 2)}/${numbers.slice(2)}`;
        } else if (numbers.length <= 8) {
            return `${numbers.slice(0, 2)}/${numbers.slice(2, 4)}/${numbers.slice(4)}`;
        } else {
            return `${numbers.slice(0, 2)}/${numbers.slice(2, 4)}/${numbers.slice(4, 8)}`;
        }
    };

    // Validate MM/DD/YYYY date
    const isValidDate = (dateString) => {
        if (!/^\d{2}\/\d{2}\/\d{4}$/.test(dateString)) return false;
        
        const [month, day, year] = dateString.split('/').map(Number);
        const date = new Date(year, month - 1, day);
        
        return date.getFullYear() === year && 
               date.getMonth() === month - 1 && 
               date.getDate() === day;
    };

    const handleManualInputChange = (e) => {
        const formattedValue = formatDateInput(e.target.value);
        setDisplayValue(formattedValue);
        
        // If complete and valid date, update the hidden date input and call onChange
        if (formattedValue.length === 10 && isValidDate(formattedValue)) {
            const dateInputValue = convertToDateInputFormat(formattedValue);
            setValue(dateInputValue);
            if (onChange) {
                onChange(dateInputValue);
            }
        }
    };

    const handleDateInputChange = (e) => {
        const dateValue = e.target.value;
        setValue(dateValue);
        setDisplayValue(formatDisplayDate(dateValue));
        if (onChange) {
            onChange(dateValue);
        }
    };

    const handleContainerClick = () => {
        if (inputRef.current) {
            inputRef.current.focus();
            setIsManualInput(true);
        }
    };

    const openDatePicker = (e) => {
        e.stopPropagation();
        if (hiddenDateInputRef.current) {
            hiddenDateInputRef.current.showPicker();
            setIsManualInput(false);
        }
    };

    const handleKeyDown = (e) => {
        // Handle Enter/Space on calendar icon to open picker
        if (e.key === 'Enter' || e.key === ' ') {
            if (e.target === hiddenDateInputRef.current || 
                (e.target.closest && e.target.closest('[data-calendar-icon]'))) {
                e.preventDefault();
                openDatePicker(e);
            }
        }
    };

    const handleCalendarKeyDown = (e) => {
        // Handle Enter/Space on calendar icon
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            openDatePicker(e);
        }
    };

    const handleFocus = () => {
        setFocused(true);
        setIsManualInput(true);
    };

    const handleBlur = () => {
        setFocused(false);
        setIsManualInput(false);
        
        // Validate and clean up the input on blur
        if (displayValue && displayValue.length === 10) {
            if (isValidDate(displayValue)) {
                const dateInputValue = convertToDateInputFormat(displayValue);
                setValue(dateInputValue);
                if (onChange) {
                    onChange(dateInputValue);
                }
            }
        }
    };

    return (
        <div style={{ width: '100%' }}>
            <TouchableOpacity
                style={[dateStyles.field, focused && dateStyles.fieldFocused]}
                onPress={handleContainerClick}
                activeOpacity={0.9}
            >
                {/* Calendar icon - focusable via tab */}
                <div 
                    data-calendar-icon
                    tabIndex={0}
                    onClick={openDatePicker}
                    onKeyDown={handleCalendarKeyDown}
                    onFocus={() => setFocused(true)}
                    onBlur={() => setFocused(false)}
                    style={{ 
                        ...dateStyles.fieldIcon, 
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        outline: 'none',
                        borderRadius: '4px',
                    }}
                >
                    <Ionicons name="calendar-outline" size={18} color="#999" />
                </div>
                
                <View style={dateStyles.fieldContent}>
                    <Text style={dateStyles.fieldLabel}>{label}</Text>
                    <View style={dateStyles.inputContainer}>
                        {/* Manual input field - visible and focusable */}
                        <input
                            ref={inputRef}
                            type="text"
                            value={displayValue}
                            onChange={handleManualInputChange}
                            onFocus={handleFocus}
                            onBlur={handleBlur}
                            onKeyDown={handleKeyDown}
                            placeholder={placeholder}
                            maxLength={10}
                            tabIndex={0}
                            style={{
                                fontSize: 16,
                                color: displayValue ? '#333' : '#aaa',
                                padding: 0,
                                border: 'none',
                                outline: 'none',
                                width: '100%',
                                height: '100%',
                                backgroundColor: 'transparent',
                                fontFamily: "Poppins_400Regular",
                            }}
                        />
                        
                        {/* Hidden date input for calendar picker - not focusable */}
                        <input
                            ref={hiddenDateInputRef}
                            type="date"
                            value={value}
                            onChange={handleDateInputChange}
                            tabIndex={-1}
                            style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                opacity: 0,
                                pointerEvents: 'none',
                                zIndex: -1,
                            }}
                        />
                    </View>
                </View>
            </TouchableOpacity>
        </div>
    );
};
const PhoneTextField = ({ label = "", initialValue = "", onChange, placeholder = "", iconname, style = {} }) => {
    const [value, setValue] = useState(initialValue);
    const [focused, setFocused] = useState(false);
    const inputRef = useRef(null);

    // Format phone number as user types
    const formatPhoneNumber = (input) => {
        // Strip all non-numeric characters
        const phoneNumber = input.replace(/\D/g, '');

        // Format based on input length
        if (phoneNumber.length < 4) {
            return phoneNumber;
        } else if (phoneNumber.length < 7) {
            return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
        } else {
            return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
        }
    };

    // Update state when initialValue prop changes
    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    const handleChange = (e) => {
        const formattedValue = formatPhoneNumber(e.target.value);
        setValue(formattedValue);
        if (onChange) {
            onChange(formattedValue);
        }
    };

    const handleContainerClick = () => {
        if (inputRef.current) {
            inputRef.current.focus();
        }
    };

    return (
        <TouchableOpacity
            style={[dateStyles.field, focused && dateStyles.fieldFocused, style]}
            activeOpacity={0.9}
            onPress={handleContainerClick}
        >
            <View style={dateStyles.fieldIcon}>
                <Ionicons name={iconname} size={18} color="#999" />
            </View>
            <View style={dateStyles.fieldContent}>
                <Text style={dateStyles.fieldLabel}>{label}</Text>
                <View style={dateStyles.inputContainer}>
                    <input
                        ref={inputRef}
                        type="tel"
                        value={value}
                        onChange={handleChange}
                        onFocus={() => setFocused(true)}
                        onBlur={() => setFocused(false)}
                        placeholder={placeholder}
                        style={{
                            fontSize: 16,
                            color: '#333',
                            padding: 0,
                            border: 'none',
                            outline: 'none',
                            width: '100%',
                            height: '100%',
                            backgroundColor: 'transparent',
                            '::placeholder': {
                                color: '#aaa',
                                opacity: 1,
                            }
                        }}
                    />
                </View>
            </View>
        </TouchableOpacity>
    );
};

// UserFormModal component
const UserFormModal = ({ isVisible, onClose, initialData = {}, onSave, disciplineOptions = [], roleOptions = [] }) => {
    // console.log(JSON.stringify(initialData))
    const [userData, setUserData] = useState({
        firstName: '',
        lastName: '',
        roleId: '',
        email: '',
        dob: '',
        employeeId: '',
        gender: '',
        primaryPhone: '',
        isDeleted: '',
        disciplineId: '',
        location: '',
        payrollLocation: '',
        address1: '',
        address2:'',
        city: '',
        state: '',
        zip: ''
    });

    const [showMessageModal, setShowMessageModal] = useState(false)
    const [message, setMessage] = useState("")
    const [messageType, setMessageType] = useState("success")

    const [type, setType] = useState("")
    const [roleName, setRoleName] = useState("")

    const firstNameInputRef = useRef(null);

    const saveRoleName = (value) => {
        const selectedOption = roleOptions.find(option => option.value === value);
        if (selectedOption) {
            setRoleName(selectedOption.label);
        }
    }
    useEffect(() => {
        if (isVisible && firstNameInputRef.current) {
            setTimeout(() => {
                firstNameInputRef.current.focus();
            }, 100);
        }
    }, [isVisible]);
    useEffect(() => {

        if (initialData && Object.keys(initialData).length > 0) {
            setUserData(prev => ({
                ...prev,
                firstName: initialData.firstName || '',
                lastName: initialData.lastName || '',
                roleId: initialData.roleId || '',
                email: initialData.email || '',
                dob: initialData.dateOfBirth || '',
                employeeId: initialData.staffId || '',
                gender: initialData.gender || '',
                primaryPhone: initialData.phone || '',
                isDeleted: initialData.isDeleted || '',
                disciplineId: initialData.disciplineId || '',
                location: initialData.location || '',
                payrollLocation: initialData.payrollLocation || '',
                address1: initialData.address?.addressLine1 || '',
                address2: initialData.address?.addressLine2 || '',
                city: initialData.address?.city || '',
                state: initialData.address?.state || '',
                zip: initialData.address?.zipcode || ''
            }));
            saveRoleName(initialData.roleId)
            setType("edit")
        } else {
            setUserData({
                firstName: '',
                lastName: '',
                roleId: '',
                email: '',
                dob: '',
                employeeId: '',
                gender: '',
                primaryPhone: '',
                isDeleted: '',
                disciplineId: '',
                location: '',
                payrollLocation: '',
                address1: '',
                address2:'',
                city: '',
                state: '',
                zip: ''
            })
            setType("create")
        }
    }, [initialData]);
    const handleChange = (field, value) => {
        setUserData(prev => ({ ...prev, [field]: value }));
    };

    const handleSave = () => {
        if (onSave) {
            if (!validateEmail(userData.email)) {
                setShowMessageModal(true)
                setMessage("Invalid Email Address.Please provide valid Email.")
                setMessageType("error")
            } else if (!validateMobile(userData.primaryPhone)) {
                setShowMessageModal(true)
                setMessage("Invalid Mobile Number.Please provide valid mobile with this format (************* .")
                setMessageType("error")
            } else {
                onSave(userData, type);
                if (onClose) {
                    onClose();
                }
            }
        }
        
    };

    if (!isVisible) return null;

    const genderOptions = [
        { value: 'Male', label: 'Male' },
        { value: 'Female', label: 'Female' },
        { value: 'Other', label: 'Other' }
    ];

    const statusOptions = [
        { value: "false", label: 'Active' },
        { value: "true", label: 'Inactive' }
    ];


    const locationOptions = [
        { value: 'Haggai Healthcare', label: 'Haggai Healthcare' },
        { value: 'Other Location', label: 'Other Location' }
    ];



    // const stateOptions = ;

    const onModalCancel = () => {
        setShowMessageModal(false)
    };
    return (
        <View style={modalStyles.modalOverlay}>
            <MessageModal visible={showMessageModal} onCancel={onModalCancel} message={message} type={messageType} />
            <View style={modalStyles.modalContainer}>
                <View style={modalStyles.modalHeader}>
                    <Text style={modalStyles.modalTitle}>{type == "create" ? "Create User" : "Edit Details"}</Text>
                    <TouchableOpacity style={modalStyles.closeButton} onPress={onClose}>
                        <Text style={modalStyles.closeButtonText}>×</Text>
                    </TouchableOpacity>
                </View>

                <View style={modalStyles.modalContent}>
                    <View style={modalStyles.formRow}>
                        <View style={modalStyles.formColumn}>
                            <TextField
                                label="First Name"
                                ref={firstNameInputRef}
                                initialValue={userData.firstName}
                                onChange={(value) => handleChange('firstName', value)}
                                placeholder="Enter first name"
                                iconname="person-outline"
                            />
                        </View>
                        <View style={modalStyles.formColumn}>
                            <TextField
                                label="Last Name"
                                initialValue={userData.lastName}
                                onChange={(value) => handleChange('lastName', value)}
                                placeholder="Enter last name"
                                iconname="person-outline"
                            />
                        </View>
                    </View>

                    <View style={modalStyles.formRow}>
                        <View style={modalStyles.formColumn}>
                            <Dropdown
                                label="Role"
                                options={roleOptions}
                                initialValue={userData.roleId}
                                onChange={(value) => { handleChange('roleId', value), saveRoleName(value) }}
                                placeholder="Select role"
                                iconname="briefcase-outline"
                                isDisabled={type === "edit"}
                            />
                        </View>
                        <View style={modalStyles.formColumn}>
                            <TextField
                                label="Email"
                                initialValue={userData.email}
                                onChange={(value) => handleChange('email', value)}
                                placeholder="Enter email"
                                iconname="mail-outline"
                            />
                        </View>
                    </View>

                    <View style={modalStyles.formRow}>
                        <View style={modalStyles.formColumn}>
                            <DatePickerField
                                label="DOB"
                                initialValue={userData.dob}
                                onChange={(value) => handleChange('dob', value)}
                                placeholder="Select date"
                            />
                        </View>
                        <View style={modalStyles.formColumn}>
                            <Dropdown
                                label="Gender"
                                options={genderOptions}
                                initialValue={userData.gender}
                                onChange={(value) => handleChange('gender', value)}
                                placeholder="Select gender"
                                iconname="person-outline"
                            />
                        </View>
                    </View>

                    <View style={modalStyles.formRow}>
                        <View style={modalStyles.formColumn}>
                            
                            <PhoneTextField
                                label="Primary Phone"
                                initialValue={userData.primaryPhone}
                                onChange={(value) => handleChange('primaryPhone', value)}
                                placeholder="(*************"
                                iconname="call-outline"
                            />
                        </View>
                        <View style={modalStyles.formColumn}>

                            {type == 'create' ?
                                (
                                    <TextField
                                        label="User Id"
                                        initialValue={userData.employeeId}
                                        onChange={(value) => handleChange('employeeId', value)}
                                        placeholder="Enter User Id"
                                        iconname="call-outline" />
                                ) : (
                                    <Dropdown
                                        label="Status"
                                        options={statusOptions}
                                        initialValue={userData.isDeleted}
                                        onChange={(value) => handleChange('isDeleted', value)}
                                        placeholder="Select status"
                                        iconname="checkmark-circle-outline"
                                    />
                                )}
                        </View>
                    </View>
                    {roleName === Role.CLINICIAN && (
                        <View style={modalStyles.formRow}>
                            <View style={modalStyles.formColumn}>
                                <Dropdown
                                    label="Discipline"
                                    options={disciplineOptions}
                                    initialValue={userData.disciplineId}
                                    onChange={(value) => handleChange('disciplineId', value)}
                                    placeholder="Select discipline"
                                    iconname="medical-outline"
                                />
                            </View>
                            
                        </View>)}

                    <View style={modalStyles.formRow}>
                        <View style={modalStyles.fullWidth}>
                            <TextField
                                label="Address 1"
                                initialValue={userData.address1}
                                onChange={(value) => handleChange('address1', value)}
                                placeholder="Enter address1"
                                iconname="home-outline"
                            />
                        </View>
                    </View>
                    <View style={modalStyles.formRow}>
                        <View style={modalStyles.fullWidth}>
                            <TextField
                                label="Address 2"
                                initialValue={userData.address2}
                                onChange={(value) => handleChange('address2', value)}
                                placeholder="Enter address 2"
                                iconname="home-outline"
                            />
                        </View>
                    </View>

                    <View style={modalStyles.formRow}>
                        <View style={modalStyles.formColumn}>
                            <TextField
                                label="City"
                                initialValue={userData.city}
                                onChange={(value) => handleChange('city', value)}
                                placeholder="Enter city"
                                iconname="business-outline"
                            />
                        </View>
                        <View style={modalStyles.formColumn}>
                            <Dropdown
                                label="State"
                                options={stateOptions}
                                initialValue={userData.state}
                                onChange={(value) => handleChange('state', value)}
                                placeholder="Select state"
                                iconname="map-outline"
                            />
                        </View>
                        <View style={modalStyles.formColumn}>
                            <TextField
                                label="Zip Code"
                                initialValue={userData.zip}
                                onChange={(value) => handleChange('zip', value)}
                                placeholder="Enter zip code"
                                iconname="pin-outline"
                            />
                        </View>
                    </View>

                    <View style={modalStyles.buttonRow}>
                        <TouchableOpacity style={modalStyles.cancelButton} onPress={onClose}>
                            <Text style={modalStyles.cancelButtonText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={modalStyles.saveButton} onPress={handleSave}>
                            <Text style={modalStyles.saveButtonText}>Save</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </View>
    );
};

// Styles for the dropdown component
const dropdownStyles = StyleSheet.create({
    highlightedOption: {
        backgroundColor: '#f0f0ff',
    },
    highlightedOptionText: {
        color: '#5050ff',
        fontWeight: '500',
    },
    container: {
        position: 'relative',
        zIndex: 'unset',
        marginBottom: 10,
    },
    field: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: '#f0f0f0',
        paddingHorizontal: 6,
        paddingVertical: 6,
        borderRadius: 10,
        backgroundColor: 'white',
        alignItems: 'center',
    },
    fieldFocused: {
        borderColor: '#d0d0ff',
        backgroundColor: '#fafafe',
    },
    fieldIcon: {
        width: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
    },
    fieldContent: {
        flex: 1,
        position: 'relative',
    },
    fieldLabel: {
        fontSize: 12,
        color: '#888',
        marginBottom: 4,
    },
    inputContainer: {
        position: 'relative',
        height: 24,
        justifyContent: 'center',
    },
    fieldInput: {
        fontSize: 16,
        color: '#333',
        padding: 0,
    },
    placeholder: {
        fontSize: 16,
        color: '#aaa',
        padding: 0,
    },
    arrowIcon: {
        width: 24,
        alignItems: 'center',
        justifyContent: 'center',
    },
    dropdown: {
        position: 'absolute',
        top: '100%',
        left: 0,
        right: 0,
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#d0d0ff',
        borderRadius: 10,
        marginTop: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        zIndex: 10,
    },
    scrollView: {
        maxHeight: 180,
        overflow: 'auto'
    },
    option: {
        paddingVertical: 10,
        paddingHorizontal: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#f5f5f5',
    },
    selectedOption: {
        backgroundColor: '#f0f0ff',
    },
    optionText: {
        fontSize: 16,
        color: '#333',
        fontFamily: "Poppins_400Regular",
    },
    selectedOptionText: {
        color: '#5050ff',
        fontWeight: '500',
    },
});

// Styles for the date and text fields
const dateStyles = StyleSheet.create({
    field: {
        flexDirection: 'row',
        marginBottom: 10,
        borderWidth: 1,
        borderColor: '#f0f0f0',
        paddingHorizontal: 6,
        paddingVertical: 6,
        borderRadius: 10,
        backgroundColor: 'white',
        alignItems: 'center',
    },
    fieldFocused: {
        borderColor: '#d0d0ff',
        backgroundColor: '#fafafe',
    },
    fieldIcon: {
        width: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
    },
    fieldContent: {
        flex: 1,
        position: 'relative',
    },
    fieldLabel: {
        fontSize: 12,
        color: '#888',
        marginBottom: 4,
        fontFamily: "Poppins_400Regular",
    },
    inputContainer: {
        position: 'relative',
        height: 24,
        justifyContent: 'center',
    },
    fieldInput: {
        fontSize: 16,
        color: '#333',
        padding: 0,
    },
    placeholder: {
        fontSize: 16,
        color: '#aaa',
        padding: 0,
    },
    calendarButton: {
        padding: 4,
    }
});

// Styles for the modal
const modalStyles = StyleSheet.create({
    modalOverlay: {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        width: '50%',
        // maxWidth: 600,
        maxHeight: '90%',
        overflow: 'auto',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 24,
        color: '#333',
        fontFamily: "Poppins_500Medium",
    },
    closeButton: {
        width: 30,
        height: 30,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButtonText: {
        fontSize: 28,
        color: '#999',
    },
    modalContent: {
        padding: 20,
    },
    formRow: {
        flexDirection: 'row',
        marginBottom: 15,
        flexWrap: 'wrap',
        zIndex: 'unset',
    },
    formColumn: {
        flex: 1,
        paddingHorizontal: 5,
        minWidth: 200,
        zIndex: 'unset',
    },
    fullWidth: {
        width: '100%',
        paddingHorizontal: 5,
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    cancelButton: {
        // paddingVertical: 12,
        // paddingHorizontal: 24,
        // borderWidth: 1,
        // borderColor: '#ddd',
        // borderRadius: 20,
        // marginRight: 10,
        flex: 1,
        borderWidth: 1,
        borderColor: '#ddd',
        paddingVertical: 12,
        borderRadius: 24,
        marginRight: 8,
        alignItems: 'center',
    },
    cancelButtonText: {
        color: '#666',
        fontWeight: '500',
        fontSize: 16,
    },
    saveButton: {
        // backgroundColor: '#624AF2',
        // paddingVertical: 12,
        // paddingHorizontal: 24,
        // borderRadius: 20,
        flex: 1,
        backgroundColor: '#624AF2',
        paddingVertical: 12,
        borderRadius: 24,
        marginLeft: 8,
        alignItems: 'center',
    },
    saveButtonText: {
        color: 'white',
        fontWeight: '500',
        fontSize: 16,
    }
});

export default UserFormModal;