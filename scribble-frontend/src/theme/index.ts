import { DefaultTheme } from 'react-native-paper';

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#6B46C1', // Dark blue from Scribble
    accent: '#f9a703', // Vibrant orange from Scribble
    background: '#F8FAFC',
    secondary: '#3b82f6',
    surface: '#F4F6F8', // Light gray for cards and surfaces
    text: '#1E293B', // Dark gray for primary text
    placeholder: '#6B7280', // Muted gray for placeholders
    disabled: '#D1D5DB', // Lighter gray for disabled elements
    error: '#D32F2F', // Red for error states
    border: '#CBD5E1',
    white: '#ffffff',
    lightGrey: '#f9fafb',
    secondaryText: '#475569',
    ternaryText: '#64748B',
    buttonColor :'#4E47DD',
  },
  fonts: {
    regular: {
      fontFamily: 'Roboto-Regular',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'Roboto-Medium',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'Roboto-Light',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'Roboto-Thin',
      fontWeight: '100',
    },
  },
  roundness: 8, // Rounded corners for buttons and inputs
};

export default theme;
