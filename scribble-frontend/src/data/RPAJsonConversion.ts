function convertImmunizationData(data) {
  const result = {};

  data.forEach(section => {
    section.questions.forEach(immunization => {
      immunization.questions.forEach(question => {
        const key = question.questionCode;
        result[key] = {
          id: question.id,
          questionCode: question.questionCode,
          questionType: question.questionType.replace(/_/g, ' ').toUpperCase(),
          questionText: question.questionText || '',
          description: question.placeholder || '',
          required: question.required || false,
          value: question.questionType === 'DROPDOWN' ? 'YES' : '',
        };
      });
    });
  });

  return result;
}
