{"patientId": {"label": "Patient ID", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": false}, "name": {"label": "Name", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": true}, "firstName": {"label": "First Name", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": true}, "lastName": {"label": "Last Name", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": true}, "status": {"label": "Status", "isRequired": true, "questionType": "DROPDOWN", "options": ["Active", "Inactive"], "fieldGroupName": "Personal Information", "isVisible": true}, "dob": {"label": "D.O.B.", "isRequired": true, "questionType": "DATE_PICKER", "fieldGroupName": "Personal Information", "isVisible": true}, "address1": {"label": "Address 1", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "city": {"label": "City", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "state": {"label": "State", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "zip": {"label": "Zip", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information"}, "primaryPhone": {"label": "Primary Phone", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "gender": {"label": "Gender", "isRequired": true, "questionType": "RADIO", "options": ["Male", "Female"], "fieldGroupName": "Personal Information"}, "placeOfService": {"label": "Place of Service", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Job Information"}}