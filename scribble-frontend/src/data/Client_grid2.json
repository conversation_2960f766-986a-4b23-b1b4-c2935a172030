{
  "patientId": {
    "label": "Patient ID",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Personal Information"
  },
  "name": {
    "label": "Name",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Personal Information"
  },
  "firstName": {
    "label": "First Name",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Personal Information"
  },
  "lastName": {
    "label": "Last Name",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Personal Information"
  },
  "status": {
    "label": "Status",
    "isRequired": true,
    "questionType": "DROPDOWN",
    "options": [
      "Active",
      "Inactive"
    ],
    "fieldGroupName": "Personal Information"
  },
  "dob": {
    "label": "D.O.B.",
    "isRequired": true,
    "questionType": "DATE_PICKER",
    "fieldGroupName": "Personal Information"
  },
  "address1": {
    "label": "Address 1",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Contact Information"
  },
  "city": {
    "label": "City",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Contact Information",
  },
  "state": {
    "label": "State",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Contact Information"
  },
  "zip": {
    "label": "Zip",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Contact Information"
  },
  "primaryPhone": {
    "label": "Primary Phone",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Contact Information"
  },
  "gender": {
    "label": "Gender",
    "isRequired": true,
    "questionType": "RADIO",
    "options": [
      "Male",
      "Female"
    ],
    "fieldGroupName": "Personal Information"
  },
  "placeOfService": {
    "label": "Place of Service",
    "isRequired": true,
    "questionType": "TEXT_INPUT",
    "fieldGroupName": "Job Information"
  }
}
