{"staffId": {"label": "Staff ID", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": true}, "name": {"label": "Name", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": true}, "firstName": {"label": "First Name", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": false}, "lastName": {"label": "Last Name", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Personal Information", "isVisible": false}, "status": {"label": "Status", "isRequired": true, "questionType": "DROPDOWN", "options": ["Active", "Inactive", "Terminated"], "fieldGroupName": "Job Information", "isVisible": true}, "dob": {"label": "D.O.B.", "isRequired": true, "questionType": "DATE_PICKER", "fieldGroupName": "Personal Information", "isVisible": true}, "address1": {"label": "Address 1", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "city": {"label": "City", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "state": {"label": "State", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": false}, "zip": {"label": "Zip", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": false}, "primaryPhone": {"label": "Primary Phone", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "cellPhone": {"label": "Cell Phone", "isRequired": false, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "email": {"label": "Email", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Contact Information", "isVisible": true}, "location": {"label": "Location", "isRequired": true, "questionType": "TEXT_INPUT", "fieldGroupName": "Job Information", "isVisible": false}, "gender": {"label": "Gender", "isRequired": true, "questionType": "RADIO", "options": ["Male", "Female", "Other"], "fieldGroupName": "Personal Information", "isVisible": false}, "jobTitle": {"label": "Job Title", "isRequired": true, "questionType": "DROPDOWN", "options": ["<PERSON><PERSON>", "Business Development", "Clerk", "Clinical Manager", "Contractor <PERSON><PERSON>", "Director of Clinical Services", "Director of Therapy Services", "DON", "HR Assist", "Office Staff", "President", "QAPI"], "fieldGroupName": "Job Information", "isVisible": false}}