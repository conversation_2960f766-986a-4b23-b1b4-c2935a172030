{"title": "Living Assistance", "fields": [{"id": "maritalStatus", "type": "radio", "label": "Patient's Marital Status", "options": [{"value": "single", "label": "Single"}, {"value": "married", "label": "Married"}, {"value": "widowed", "label": "Widowed"}, {"value": "divorced", "label": "Divorced"}, {"value": "separated", "label": "Separated"}, {"value": "unknown", "label": "Unknown"}]}, {"id": "householdSupports", "type": "repeatGroup", "label": "Other Living in Household / Available Supports", "maxRepeats": 3, "subQuestions": [{"id": "firstName", "type": "text", "label": "First Name"}, {"id": "lastName", "type": "text", "label": "Last Name"}, {"id": "age", "type": "number", "label": "Age"}, {"id": "gender", "type": "dropdown", "label": "Gender", "options": [{"value": "male", "label": "Male"}, {"value": "female", "label": "Female"}, {"value": "other", "label": "Other"}]}, {"id": "relationship", "type": "dropdown", "label": "Relationship", "options": [{"value": "spouse", "label": "Spouse"}, {"value": "child", "label": "Child"}, {"value": "parent", "label": "Parent"}, {"value": "sibling", "label": "Sibling"}, {"value": "friend", "label": "Friend"}, {"value": "other", "label": "Other"}]}, {"id": "ableAssist", "type": "dropdown", "label": "Able/Willing to Assist?", "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}, {"value": "unknown", "label": "Unknown"}]}, {"id": "considerRep", "type": "checkbox", "label": "Consider as Patient Representative"}, {"id": "courtAppointed<PERSON><PERSON><PERSON>", "type": "dropdown", "label": "Court Appointed Guardian?", "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}, {"value": "unknown", "label": "Unknown"}]}, {"id": "patientAppointedRep", "type": "dropdown", "label": "Patient Appointed Representative?", "options": [{"value": "yes", "label": "Yes"}, {"value": "no", "label": "No"}, {"value": "unknown", "label": "Unknown"}]}, {"id": "schedule", "type": "text", "label": "Schedule"}]}, {"id": "orgAssistance", "type": "textarea", "label": "Persons or Organizations Providing Assistance/Services"}]}