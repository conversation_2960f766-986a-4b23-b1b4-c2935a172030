{
  "title": "(M1021) Primary & (M1023) Other Diagnoses",
  "helpText": "Sequencing of Diagnoses should reflect the seriousness of each condition. ICD-10-C M codes and symptom control rating for each condition.",

  "fields": [
    {
      // row A => (M1021) Primary Diagnosis
      "id": "rowA",
      "type": "row",
      "label": "a. (M1021) Primary Diagnosis",
      "subFields": [
        {
          "id": "descriptionA",
          "type": "text",
          "label": "Primary Diagnosis Description"
        },
        {
          "id": "icdCodeA",
          "type": "text",
          "label": "ICD-10-C M Code (no V, W, X, Y)"
        },
        {
          "id": "symptomControlA",
          "type": "radio",
          "label": "Symptom Control (0–4)",
          "options": [
            { "value": "0", "label": "0" },
            { "value": "1", "label": "1" },
            { "value": "2", "label": "2" },
            { "value": "3", "label": "3" },
            { "value": "4", "label": "4" }
          ]
        },
        {
          "id": "dateA",
          "type": "date",
          "label": "Onset/Diagnosed Date",
          "optional": true
        },
        {
          "id": "extraDropdownA",
          "type": "dropdown",
          "label": "Extra Info (if needed)",
          "options": [
            { "value": "option1", "label": "Option 1" },
            { "value": "option2", "label": "Option 2" }
          ],
          "optional": true
        }
      ]
    },
    {
      // row B => (M1023) Other Diagnosis
      "id": "rowB",
      "type": "row",
      "label": "b. (M1023) Other Diagnosis",
      "subFields": [
        {
          "id": "descriptionB",
          "type": "text",
          "label": "Other Diagnosis Description"
        },
        {
          "id": "icdCodeB",
          "type": "text",
          "label": "ICD-10-C M Code"
        },
        {
          "id": "symptomControlB",
          "type": "radio",
          "label": "Symptom Control (0–4)",
          "options": [
            { "value": "0", "label": "0" },
            { "value": "1", "label": "1" },
            { "value": "2", "label": "2" },
            { "value": "3", "label": "3" },
            { "value": "4", "label": "4" }
          ]
        },
        {
          "id": "dateB",
          "type": "date",
          "label": "Onset/Diagnosed Date",
          "optional": true
        },
        {
          "id": "extraDropdownB",
          "type": "dropdown",
          "label": "Extra Info",
          "options": [
            { "value": "option1", "label": "Option 1" },
            { "value": "option2", "label": "Option 2" }
          ],
          "optional": true
        }
      ]
    },
    {
      // row C => (M1023) Another
      "id": "rowC",
      "type": "row",
      "label": "c. (M1023) Other Diagnosis",
      "subFields": [
        {
          "id": "descriptionC",
          "type": "text",
          "label": "Description"
        },
        {
          "id": "icdCodeC",
          "type": "text",
          "label": "ICD-10 Code"
        },
        {
          "id": "symptomControlC",
          "type": "radio",
          "label": "Symptom Control (0–4)",
          "options": [
            { "value": "0", "label": "0" },
            { "value": "1", "label": "1" },
            { "value": "2", "label": "2" },
            { "value": "3", "label": "3" },
            { "value": "4", "label": "4" }
          ]
        }
      ]
    },
    {
      // row D => (M1023)
      "id": "rowD",
      "type": "row",
      "label": "d. (M1023) Other Diagnosis",
      "subFields": [
        {
          "id": "descriptionD",
          "type": "text",
          "label": "Description"
        },
        {
          "id": "icdCodeD",
          "type": "text",
          "label": "ICD-10 Code"
        },
        {
          "id": "symptomControlD",
          "type": "radio",
          "label": "Symptom Control (0–4)",
          "options": [
            { "value": "0", "label": "0" },
            { "value": "1", "label": "1" },
            { "value": "2", "label": "2" },
            { "value": "3", "label": "3" },
            { "value": "4", "label": "4" }
          ]
        }
      ]
    },
    {
      // row E => (M1023)
      "id": "rowE",
      "type": "row",
      "label": "e. (M1023) Other Diagnosis",
      "subFields": [
        {
          "id": "descriptionE",
          "type": "text",
          "label": "Description"
        },
        {
          "id": "icdCodeE",
          "type": "text",
          "label": "ICD-10 Code"
        },
        {
          "id": "symptomControlE",
          "type": "radio",
          "label": "Symptom Control (0–4)",
          "options": [
            { "value": "0", "label": "0" },
            { "value": "1", "label": "1" },
            { "value": "2", "label": "2" },
            { "value": "3", "label": "3" },
            { "value": "4", "label": "4" }
          ]
        }
      ]
    },
    {
      // row F => (M1023)
      "id": "rowF",
      "type": "row",
      "label": "f. (M1023) Other Diagnosis",
      "subFields": [
        {
          "id": "descriptionF",
          "type": "text",
          "label": "Description"
        },
        {
          "id": "icdCodeF",
          "type": "text",
          "label": "ICD-10 Code"
        },
        {
          "id": "symptomControlF",
          "type": "radio",
          "label": "Symptom Control (0–4)",
          "options": [
            { "value": "0", "label": "0" },
            { "value": "1", "label": "1" },
            { "value": "2", "label": "2" },
            { "value": "3", "label": "3" },
            { "value": "4", "label": "4" }
          ]
        }
      ]
    },
    {
      // notes area
      "id": "notes",
      "type": "textarea",
      "label": "Notes"
    }
  ]
}
