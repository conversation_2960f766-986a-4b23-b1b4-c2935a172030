import React, { useEffect } from 'react';
import { Linking, Platform } from 'react-native';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from './types'; // Create this type file if not exists

type DeeplinkNavigationProp = NavigationProp<RootStackParamList>;

const DeepLinkHandler = () => {
  // Properly type the navigation object
  const navigation = useNavigation<DeeplinkNavigationProp>();

  useEffect(() => {
    // Function to handle the deeplink
    const handleDeepLink = (url: string) => {
      console.log('Handling deeplink URL:', url);
      
      if (url && url.includes('recover-password')) {
        try {
          const urlObj = new URL(url);
          const pathParts = urlObj.pathname.split('/').filter(Boolean);
          
          // Find "recover-password" index in the path
          const recoveryIndex = pathParts.findIndex(part => part === 'recover-password');
          
          if (recoveryIndex !== -1 && pathParts.length > recoveryIndex + 2) {
            const email = pathParts[recoveryIndex + 1];
            const token = pathParts[recoveryIndex + 2];
            
            // console.log('Parsed email:', email);
            // console.log('Parsed token:', token);
            
            // Navigate to the reset password screen
            navigation.navigate('ResetPassword', {
              email,
              token,
              isForgotPassword: true,
            });
          } else {
            console.log('Invalid recovery URL format');
          }
        } catch (error) {
          console.error('Error parsing URL:', error);
        }
      }
    };

    // For web, handle the current URL when component mounts
    if (Platform.OS === 'web') {
      const url = window.location.href;
      handleDeepLink(url);
    }

    // For native platforms, listen for deeplink events (not needed for your web-only case)
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    // Cleanup
    return () => {
      if (Platform.OS !== 'web') {
        subscription.remove();
      }
    };
  }, [navigation]);

  return null;
};

export default DeepLinkHandler;