import React, { createContext, useContext, useState } from 'react';

const NotificationContext = createContext();

export const NotificationProvider = ({ children }) => {
  const [showPopup, setShowPopup] = useState(false);
  const [message, setMessage] = useState('');

  const showNotification = (msg) => {
    setMessage(msg);
    setShowPopup(true);
  };

  const hideNotification = () => {
    setShowPopup(false);
    setMessage('');
  };

  return (
    <NotificationContext.Provider value={{ showPopup, message, showNotification, hideNotification }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => useContext(NotificationContext);
