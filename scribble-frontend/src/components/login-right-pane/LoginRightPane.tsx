import React from 'react';
import { Text, StyleSheet, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useResponsive } from 'src/hooks/useResponsive';

const LoginRightPane = () => {
  const { isTabletOrMobileDevice } = useResponsive();

  const styles = loginScreenStyles(isTabletOrMobileDevice);

  return (
    <LinearGradient
      colors={['#4C51BF', '#6B46C1']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.rightPane}
    >
      <Image
        source={require('assets/images/banner-image.png')}
        style={styles.heroImage}
      />
      <Text style={styles.heroTitle}>
        AI-Powered Clinician <Text style={styles.assistantText}>Assistant</Text>
      </Text>
      <Text style={styles.heroSubtitle}>
        Revolutionize patient care with real-time, intelligent documentation
      </Text>
    </LinearGradient>
  );
};

const loginScreenStyles = (isMobile: boolean) =>
  StyleSheet.create({
    rightPane: {
      width: '50%',
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      borderTopRightRadius: 16,
      borderBottomRightRadius: 16,
      backgroundColor: '#6B46C1',
      padding: 24,
    },
    heroTitle: {
      fontFamily: 'Poppins_600SemiBold',
      fontSize: 26,
      color: '#FFFFFF',
      marginBottom: 8,
      textAlign: 'center',
      marginTop: 24,
    },
    heroSubtitle: {
      fontFamily: 'Poppins_400Regular',
      fontSize: 16,
      color: '#FFFFFF',
      marginBottom: 16,
      textAlign: 'center',
    },
    assistantText: {
      color: '#FFD700',
    },
    heroImage: {
      width: 350,
      height: 350,
      resizeMode: 'contain',
      marginTop: 16,
      backgroundColor: 'transparent',
    },
  });

export default LoginRightPane;
