import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
//import { Mic } from 'lucide-react-native';
import { globalStyles } from 'src/styles';

const FabButton = ({ onPress }: { onPress: () => void }) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      {/* <Mic size={28} color={'white'} /> */}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 56,
    height: 56,
    backgroundColor: '#DC2626',
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 24,
    right: 24,
    ...globalStyles.shadow,
  },
});

export default FabButton;
