import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Animated,
  Platform,
  StatusBar,
  PanResponder,
  TouchableWithoutFeedback,
  Pressable,
  View,
  Text
} from "react-native";
import { Ionicons } from '@expo/vector-icons';
import ToastService from "@/src/utils/toastService";

interface ToastProps {
  children: React.ReactNode;
}

interface ToastState {
  visible: boolean;
  message: string;
  type: "success" | "error" | "info" | "warning";
  position: "top" | "bottom";
  bottomOffset?: number;
  icon?: string;
  autoHide: boolean;
  onPress?: () => void;
  backgroundColor?: string;
  customContent?: React.ReactNode;
}

export const ToastProvider: React.FC<ToastProps> = ({ children }) => {
  const [toast, setToast] = useState<ToastState>({
    visible: false,
    message: "",
    type: "info",
    position: "top",
    bottomOffset: 20,
    icon: undefined,
    autoHide: true,
    onPress: undefined,
    backgroundColor: undefined,
    customContent: undefined,
  });

  const fadeAnim = useState(new Animated.Value(0))[0];
  // Initialize animation value based on position
  const translateYAnim = useState(new Animated.Value(toast.position === "top" ? -100 : 100))[0];

  // Default toast duration of 3 seconds (3000ms)
  const DEFAULT_TOAST_DURATION = 3000;

  // Threshold for swipe to dismiss (in pixels)
  const SWIPE_THRESHOLD = 50;

  // Get status bar height
  const STATUSBAR_HEIGHT =
    Platform.OS === "ios" ? 44 : StatusBar.currentHeight || 0;

  // Configure pan responder for swipe gestures (primarily vertical for this design)
  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Respond to vertical swipes
        return Math.abs(gestureState.dy) > 5;
      },
      onPanResponderMove: (_, gestureState) => {
        // Handle vertical swipes (in appropriate direction for position)
        if ((toast.position === "top" && gestureState.dy < 0) || 
            (toast.position === "bottom" && gestureState.dy > 0)) {
          translateYAnim.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        // Vertical swipe dismissal - direction depends on position
        if ((toast.position === "top" && gestureState.dy < -SWIPE_THRESHOLD) || 
            (toast.position === "bottom" && gestureState.dy > SWIPE_THRESHOLD)) {
          
          // Swipe direction based on position
          const dismissValue = toast.position === "top" ? -100 : 100;
          
          Animated.timing(translateYAnim, {
            toValue: dismissValue,
            duration: 200,
            useNativeDriver: true,
          }).start(() => {
            hideToast();
          });
        }
        // Threshold not met, snap back
        else {
          Animated.spring(translateYAnim, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    }),
  ).current;

  useEffect(() => {
    const showListener = (config: any) => {
      setToast({
        visible: true,
        message: config.message,
        type: config.type,
        position: config.position || "top",
        bottomOffset: config.bottomOffset || 20,
        icon: config.icon,
        autoHide: config.autoHide !== undefined ? config.autoHide : true,
        onPress: config.onPress,
        backgroundColor: config.backgroundColor,
        customContent: config.customContent,
      });

      // Reset position for animation based on toast position
      translateYAnim.setValue(config.position === "bottom" ? 100 : -100);

      // Animate in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      Animated.timing(translateYAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Auto hide after duration if autoHide is true
      if (config.autoHide !== false) {
        const timer = setTimeout(() => {
          hideToast();
        }, config.duration || DEFAULT_TOAST_DURATION);

        return () => clearTimeout(timer);
      }
    };

    const hideListener = () => {
      hideToast();
    };

    const showUnsubscribe = ToastService.addEventListener(showListener);
    
    // Add listener for manual hide
    ToastService.emitter.addListener("hide", hideListener);
    
    return () => {
      showUnsubscribe();
      ToastService.emitter.removeListener("hide", hideListener);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const hideToast = () => {
    // Determine the correct direction to animate out based on position
    const dismissValue = toast.position === "bottom" ? 100 : -100;
    
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateYAnim, {
        toValue: dismissValue,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setToast((prev) => ({ ...prev, visible: false }));
    });
  };

  const handleToastPress = () => {
    // If there's a custom press handler, call it
    if (toast.onPress) {
      toast.onPress();
      hideToast();
    }
    
    // If no custom handler or if it doesn't prevent default behavior, hide the toast
    if (!toast.onPress) {
      hideToast();
    }
  };

  const getToastIcon = () => {
    // If a custom icon is provided, use it
    if (toast.icon) {
      return toast.icon;
    }
    
    // Otherwise use default icons based on type
    switch (toast.type) {
      case "success":
        return "checkmark-circle";
      case "error":
        return "alert-circle";
      case "warning":
        return "warning";
      case "info":
      default:
        return "star"; // Default for info type
    }
  };

  const getBackgroundColor = () => {
    // If custom background color is provided, use it
    if (toast.backgroundColor) {
      return toast.backgroundColor;
    }
    
    // For the recording notification style, use purple
    if (toast.type === "info") {
      return "#5E5CE6"; // Purple color from the image
    }
    
    // For other types, use standard colors
    switch (toast.type) {
      case "success":
        return "#4CAF50"; // Green
      case "error":
        return "#F44336"; // Red
      case "warning":
        return "#FF9800"; // Orange
      default:
        return "#5E5CE6"; // Default purple
    }
  };
  
  return (
    <View style={{ flex: 1 }}>
      {children}

      {toast.visible && (
        <TouchableWithoutFeedback onPress={handleToastPress}>
          <Animated.View
            style={[
              styles.toastContainer,
              toast.position === "top"
                ? [styles.topToast, { marginTop: STATUSBAR_HEIGHT }]
                : [styles.bottomToast, { bottom: toast.bottomOffset }],
              {
                opacity: fadeAnim,
                transform: [{ translateY: translateYAnim }],
              },
            ]}
            {...panResponder.panHandlers}
          >
            <Animated.View 
              style={[
                styles.toast,
                { backgroundColor: toast.backgroundColor || getBackgroundColor() }
              ]}
            >
              <View style={styles.iconContainer}>
                {toast.customContent ? (
                  toast.customContent
                ) : (
                  <Ionicons name={getToastIcon()} size={24} color="#FFFFFF" />
                )}
              </View>
              <View style={styles.toastContent}>
                <Text style={styles.toastText}>
                  {toast.message}
                </Text>
              </View>
              <Pressable
                style={styles.dismissButton}
                onPress={() => hideToast()}
              >
                <Ionicons name="close" size={20} color="#FFFFFF" />
              </Pressable>
            </Animated.View>
          </Animated.View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    alignItems: "center",
    paddingHorizontal: 16,
    zIndex: 9999,
  },
  topToast: {
    top: 0,
  },
  bottomToast: {
    // Will be dynamically set in the style array
  },
  toast: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    width: "100%",
    maxWidth: 400,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    marginRight: 12,
  },
  toastContent: {
    flex: 1,
  },
  toastText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  dismissButton: {
    padding: 4,
    marginLeft: 8,
  },
});

export default ToastProvider;