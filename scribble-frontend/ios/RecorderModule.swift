//
//  RecorderModule.swift
//  Scribble
//
//  Created by <PERSON><PERSON> on 7/18/25.
//


import React
import Foundation
import AVFoundation
import UIKit

@objc(RecorderModule)
class RecorderModule: RCTEventEmitter {
  var audioRecorder: AVAudioRecorder?
  var backgroundTask: UIBackgroundTaskIdentifier = .invalid
  var recordingURL: URL?

  @objc(startRecording:withRejecter:)
  func startRecording(resolve: @escaping RCTPromiseResolveBlock,
                      reject: @escaping RCTPromiseRejectBlock) {
    let session = AVAudioSession.sharedInstance()
    do {
      try session.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
      try session.setActive(true)

      NotificationCenter.default.addObserver(
        self,
        selector: #selector(handleInterruption),
        name: AVAudioSession.interruptionNotification,
        object: session
      )

      backgroundTask = UIApplication.shared.beginBackgroundTask {
        UIApplication.shared.endBackgroundTask(self.backgroundTask)
        self.backgroundTask = .invalid
      }

      let settings: [String: Any] = [
        AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
        AVSampleRateKey: 44100,
        AVNumberOfChannelsKey: 1,
        AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
      ]

      let filename = "\(UUID().uuidString).m4a"
      let path = FileManager.default.temporaryDirectory.appendingPathComponent(filename)
      self.recordingURL = path

      audioRecorder = try AVAudioRecorder(url: path, settings: settings)
      audioRecorder?.prepareToRecord()
      audioRecorder?.record()

      resolve(path.absoluteString)

    } catch {
      reject("RECORD_ERR", "Failed to start recording", error)
    }
  }

  @objc(stopRecording:withRejecter:)
  func stopRecording(resolve: RCTPromiseResolveBlock,
                     reject: RCTPromiseRejectBlock) {
    guard let recorder = audioRecorder else {
      reject("STOP_ERR", "No active recorder", nil)
      return
    }

    recorder.stop()
    audioRecorder = nil

    NotificationCenter.default.removeObserver(
      self,
      name: AVAudioSession.interruptionNotification,
      object: AVAudioSession.sharedInstance()
    )

    UIApplication.shared.endBackgroundTask(self.backgroundTask)
    self.backgroundTask = .invalid

    resolve(recorder.url.absoluteString)
  }

  @objc(pauseRecording:withRejecter:)
  func pauseRecording(resolve: RCTPromiseResolveBlock,
                      reject: RCTPromiseRejectBlock) {
    if let recorder = audioRecorder, recorder.isRecording {
      recorder.pause()
      resolve(true)
    } else {
      reject("PAUSE_ERR", "No active recorder to pause", nil)
    }
  }

  @objc(resumeRecording:withRejecter:)
  func resumeRecording(resolve: RCTPromiseResolveBlock,
                       reject: RCTPromiseRejectBlock) {
    print("recorder.isRecording: \(audioRecorder!.isRecording)")
    if let recorder = audioRecorder, !recorder.isRecording {
      recorder.record()
      resolve(true)
    } else {
      reject("RESUME_ERR", "No paused recorder to resume", nil)
    }
  }
  
  @objc
  func deleteFile(_ filePath: String,
                  resolve: @escaping RCTPromiseResolveBlock,
                  reject: @escaping RCTPromiseRejectBlock) {
    // Convert string → URL (supports both "file://" and raw paths)
    let url: URL
    if let asURL = URL(string: filePath), asURL.scheme == "file" {
      url = asURL
    } else {
      url = URL(fileURLWithPath: filePath)
    }

    // If you want to be extra safe, ensure it's inside your sandbox:
    // guard url.path.hasPrefix(NSHomeDirectory()) else {
    //   reject("DELETE_ERR", "Refusing to delete outside app sandbox", nil)
    //   return
    // }

    // If we’re currently recording to this file, stop and clear refs
    if let current = self.recordingURL, current == url {
      audioRecorder?.stop()
      audioRecorder = nil
      self.recordingURL = nil
    }

    let fm = FileManager.default
    do {
      if fm.fileExists(atPath: url.path) {
        try fm.removeItem(at: url)
        resolve(["deleted": true, "existed": true])
      } else {
        // Not found—treat as success but report it
        resolve(["deleted": false, "existed": false])
      }
    } catch {
      reject("DELETE_ERR", "Failed to delete file at path: \(url.path)", error)
    }
  }
  
  @objc private func handleInterruption(notification: Notification) {
    guard let userInfo = notification.userInfo,
          let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
          let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
      return
    }

    switch type {
    case .began:
      print("📞 Interruption began — pausing recorder")
      audioRecorder?.pause()
      sendEvent(withName: "RecordingInterruption", body: ["status": "paused"])

    case .ended:
      print("✅ Interruption ended — attempting to resume")
      if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
        let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
        if options.contains(.shouldResume) {
          try? AVAudioSession.sharedInstance().setActive(true)
          //audioRecorder?.record()
          
          print("🎙️ Resumed recording after interruption")
          sendEvent(withName: "RecordingInterruption", body: ["status": "resumed"])

        }
      }

    default:
      break
    }
  }
  
//  @objc private func handleInterruption(notification: Notification) {
//    print("🔔 Interruption notification received")
//    
//    guard let userInfo = notification.userInfo,
//          let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
//          let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
//      print("❌ Failed to parse interruption notification")
//      return
//    }
//
//    switch type {
//    case .began:
//      print("📞 Interruption began — pausing recorder")
//      audioRecorder?.pause()
//      sendEvent(withName: "RecordingInterruption", body: ["status": "paused"])
//
//    case .ended:
//      print("✅ Interruption ended — keeping session active but not resuming")
//      
//      if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
//        let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
//        if options.contains(.shouldResume) {
//          do {
//            // Reactivate the audio session
//            try AVAudioSession.sharedInstance().setActive(true)
//            
//            // THIS IS KEY: Start recording briefly to keep session active, then immediately pause
//            audioRecorder?.pause()
//            sendEvent(withName: "RecordingInterruption", body: ["status": "resumed"])
//
//            
//            // Small delay to ensure the session stays active
//         
//          } catch {
//            print("❌ Failed to reactivate audio session: \(error)")
//            sendEvent(withName: "RecordingInterruption", body: ["status": "error"])
//          }
//        }
//      }
//
//    default:
//      break
//    }
//  }

  @objc
  override static func requiresMainQueueSetup() -> Bool {
    return false
  }
  override func supportedEvents() -> [String]! {
    return ["RecordingInterruption"]
  }
 
 
}


