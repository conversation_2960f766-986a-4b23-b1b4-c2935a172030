module.exports = function(api) {
    api.cache(true);
    return {
      presets: ['babel-preset-expo'],
      plugins: [
        ["module:react-native-dotenv", {
          "moduleName": "@env",
          "path": `.env.${process.env.EXPO_ENV || 'development'}`,
          "blacklist": null,
          "whitelist": null,
          "safe": false,
          "allowUndefined": true
        }]
      ]
    };
  };