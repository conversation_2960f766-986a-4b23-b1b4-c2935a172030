import React, { useEffect, useState } from 'react';
import { createNavigationContainerRef, NavigationContainer, NavigationContainerRef, useNavigation } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { PaperProvider } from 'react-native-paper';
import { useFonts } from 'expo-font';
import {
  Poppins_400Regular,
  Poppins_500Medium,
  Poppins_600SemiBold,
  Poppins_700Bold,
} from '@expo-google-fonts/poppins';
import {
  PlusJakartaSans_400Regular,
  PlusJakartaSans_500Medium,
  PlusJakartaSans_600SemiBold,
} from '@expo-google-fonts/plus-jakarta-sans';
import AudioRecorder from 'src/features/floating-recorder/AudioRecorder';
import { Provider, useSelector, useDispatch } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { persistor, RootState, store } from 'src/redux/store';
import { setRoute } from 'src/redux/slices/routeSlice';
// Import the action from audioRecorderSlice
import { setRecordingState } from 'src/redux/slices/audioRecorderSlice';
import StackNavigator from 'src/navigation/StackNavigator';
import { ToastProvider } from './src/components/providers/ToastProvider';
import { LoginProvider } from './src/context/LoginContext';
import { Platform, View, ActivityIndicator } from 'react-native';
import { RootStackParamList } from './src/navigation/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { login } from './src/redux/slices/authSlice';
import { useIdleTimer } from './src/hooks/useIdleTimer';
import { getSecureItem } from './src/utils/cryptoHelper';
import * as Sentry from '@sentry/react-native';
import { SENTRY_DSN } from '@env';

Sentry.init({
  dsn: SENTRY_DSN,

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

// Create a properly typed navigation ref
//export const navigationRef = React.createRef<NavigationContainerRef<RootStackParamList>>();
export const navigationRef = createNavigationContainerRef();

export function navigate(name: string, params?: object) {
  // console.log(navigationRef)
  if (navigationRef.current ) {
    navigationRef.navigate(name as never, params as never);
  }
}
// AuthInitializer component to handle initial auth state
const AuthInitializer = ({ children }) => {
  const [isAuthChecking, setIsAuthChecking] = useState(true);
  const dispatch = useDispatch();
  // useIdleTimer();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if token exists
        // const token = await AsyncStorage.getItem('authToken');
        // const email = await AsyncStorage.getItem('email');
        // const firstName = await AsyncStorage.getItem('firstName');
        // const lastName = await AsyncStorage.getItem('lastName');
        // const staffId = await AsyncStorage.getItem('staffId');
        // const discipline = await AsyncStorage.getItem('discipline');
        // const phone = await AsyncStorage.getItem('phone');
        // const dateOfBirth = await AsyncStorage.getItem('dateOfBirth');

        const token = await getSecureItem("authToken");
        const email = await getSecureItem("email");
        const firstName = await getSecureItem("firstName");
        const lastName = await getSecureItem("lastName");
        const staffId = await getSecureItem("staffId");
        const discipline = await getSecureItem("discipline");
        const phone = await getSecureItem("phone");
        const dateOfBirth = await getSecureItem("dateOfBirth");
        console.log("token ",token )
        console.log("email ",token )
        // If token exists, restore the session
        if (token && email) {
          // Get stored role or default to 'user'
          // const roleData = await AsyncStorage.getItem('userRole');

          const roleData = await getSecureItem('userRole');
          const role = roleData || 'user';
          
          // Restore the session
          dispatch(login({
            user: {
              accessToken: token,
              email,
              firstName: firstName || '',
              lastName: lastName || '',
              staffId: staffId || '',
              disciplineId: discipline || '',
              phone: phone || '',
              dateOfBirth: dateOfBirth || '',
              isFirstLogin: false,
              // Add other needed properties
            },
            role
          }));
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Optional: handle the error (could show a toast, etc.)
      } finally {
        setIsAuthChecking(false);
      }
    };

    initializeAuth();
  }, [dispatch]);

  if (isAuthChecking) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#4C51BF" />
      </View>
    );
  }

  return children;
};

export default Sentry.wrap(function App() {
  const [loaded, error] = useFonts({
    SpaceMono: require('./assets/fonts/SpaceMono-Regular.ttf'),
    SansSerif: require('./assets/fonts/SansSerif.ttf'),
    PlusJakartaSansBold: require('./assets/fonts/PlusJakartaSans-Bold.ttf'),
    Poppins_400Regular,
    Poppins_500Medium,
    Poppins_600SemiBold,
    Poppins_700Bold,
    PlusJakartaSans_400Regular,
    PlusJakartaSans_500Medium,
    PlusJakartaSans_600SemiBold,
  });
  
  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  if (!loaded) {
    return null;
  }

  const paperTheme = {
    fonts: {
      bodySmall: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 14,
      },
      titleLarge: {
        fontFamily: 'Poppins_600SemiBold',
        fontSize: 24,
      },
      labelMedium: {
        fontFamily: 'Poppins_500Medium',
        fontSize: 16,
      },
      labelLarge: {
        fontFamily: 'Poppins_500Medium',
        fontSize: 18,
      },
      bodyLarge: {
        fontFamily: 'Poppins_400Regular',
        fontSize: 16,
      },
    },
  };

  const linking = {
    prefixes: ['https://d1qto9aqdoefup.cloudfront.net/', 'd1qto9aqdoefup.cloudfront.net://', 'http://localhost:8081/', 'localhost:8081://'],
    config: {
      screens: {
        ResetPassword: 'recover-password/:token',
        Dashboard: 'home',
        AllVisits: 'allVisits',
        VisitScreen: 'visitScreen',
        Assesment: 'assesment',
        Recording: 'recording',
        VisitDetails: 'visitDetails',
        MyAccount: 'myAccount',
      },
    },
  };

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <PaperProvider theme={paperTheme}>
          <ToastProvider>
            <SafeAreaProvider>
              <LoginProvider>
                <AuthInitializer>
                  <NavigationContainer 
                    ref={navigationRef}
                    linking={linking}
                    onStateChange={state => {
                      if (!state) return;
                      const routeName = state.routes[state.index]?.name;
                      store.dispatch(setRoute(routeName));
                    }}
                  >
                    <StackNavigator />
                    <EnhancedAudioRecorder /> 
                  </NavigationContainer>
                </AuthInitializer>
              </LoginProvider>
            </SafeAreaProvider>
          </ToastProvider>
        </PaperProvider>
      </PersistGate>
    </Provider>
  );
});

const EnhancedAudioRecorder = () => {
  const recorderState = useSelector((state: RootState) => state.audioRecorder);
  const currentRoute = useSelector((state: RootState) => state.route.currentRoute);
  const navigation = useNavigation();
  
  // Check if we're on the Dashboard screen
  const isDashboard = currentRoute === 'Dashboard';
  
  // Only show recorder if it's visible in redux state or recording
  if (!recorderState.isVisible && !recorderState.isRecording) {
    return null;
  }
  
  // Return the AudioRecorder with improved positioning
  return (
    <View 
      style={{
        position: 'absolute',
        bottom: isDashboard ? (Platform.OS === 'ios' ? 80 : 60) : 3,
        left: 3,
        right: 3,
        zIndex: 998, // High z-index to show above other content
      }}
    >
      <AudioRecorder
        navigation={navigation}
        isRecording={recorderState.isRecording}
        recordingURI={recorderState.recordingURI}
        showPauseResume = {recorderState.showPauseResume}
        setRecordingURI={(uri) => 
          store.dispatch(setRecordingState({ recordingURI: uri }))
        }
        setIsRecording={(recording) => 
          store.dispatch(setRecordingState({ isRecording: recording }))
        }
        setShowPauseResume={(pauseResume) => 
          store.dispatch(setRecordingState({ showPauseResume: pauseResume }))
        }
        assesId={recorderState.assesId}
        initialExpanded={recorderState.isExpanded}
      />
    </View>
  );
};