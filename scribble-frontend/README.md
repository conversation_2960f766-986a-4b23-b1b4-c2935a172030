# Scribble Frontend

A modern, responsive healthcare application built with React Native and Expo, providing cross-platform functionality for web, iOS, and Android.

## Overview

Scribble Frontend is the client-side application for the Scribble healthcare platform. It provides a user-friendly interface for healthcare professionals to manage patient data, schedule visits, and complete clinical documentation.

## Features

- Cross-platform compatibility (Web, iOS, Android)
- Responsive design for different device sizes
- Offline capability with data synchronization
- Interactive forms for clinical documentation
- Dashboard for managing patient visits
- Multi-language support via i18next

## Tech Stack

- **Framework**: React Native with Expo
- **Navigation**: React Navigation v7
- **State Management**: Redux Toolkit with Redux Persist
- **UI Components**: React Native Paper
- **Networking**: Axios
- **Date Handling**: Day.js
- **Internationalization**: i18next
- **Icons**: Expo Vector Icons, Lucide React Native

## Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Expo CLI

## Installation

1. Clone the repository:
```bash
git clone https://github.com/Acutedge/scribble-frontend.git
cd scribble-frontend
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Set up environment variables:
```bash
# Copy the example environment file
cp .env.example .env.local

# Edit .env.local with your actual values
# Make sure to set:
# - SENTRY_DSN: Your Sentry project DSN
# - SENTRY_AUTH_TOKEN: Your Sentry auth token (for build uploads)
# - BASE_URL: API endpoint URL
```

**Important**: Never commit `.env.local` to version control as it contains sensitive information.

## Running the App

```bash
# Start the development server
npm start
# or
yarn start

# Run on iOS simulator
npm run ios
# or
yarn ios

# Run on Android emulator
npm run android
# or
yarn android

# Run in web browser
npm run web
# or
yarn web
```

## Building for Production

### Web
```bash
npm run build:web
# or
yarn build:web
```

This will create a production-ready build in the `web-build` directory and generate a service worker using Workbox.

## Project Structure

```
scribble-frontend/
├── assets/                  # Static assets like images and fonts
│   ├── fonts/
│   └── images/
├── public/                  # Public assets for web build
├── src/
│   ├── api/                 # API integration
│   ├── components/          # Reusable UI components
│   ├── context/             # React context providers
│   ├── data/                # Static data and constants
│   ├── enums/               # Enumeration types
│   ├── features/            # Feature-specific components
│   ├── hooks/               # Custom React hooks
│   ├── navigation/          # Navigation configuration
│   ├── redux/               # Redux store, slices, and actions
│   ├── screens/             # Application screens
│   ├── styles/              # Global styles
│   ├── theme/               # Theme configuration
│   └── utils/               # Utility functions
├── App.tsx                  # Main app component
└── index.ts                 # Entry point
```

## Related Projects

- [Scribble Backend](https://github.com/yourusername/scribble-backend) - Backend API service
- [Scribble AI Core](https://github.com/yourusername/Scribble/apps/ai_core) - AI services for the platform


# Starting
You must login via valid tenant url since the app passes the subdomain to the backend for tenant id.
Modify your host file
```bash
# prevents the overriding of dev environment url dev1.goscribble.ai
127.0.0.1 dev1.local-goscribble.ai

```

```bash
npm run web
```

go to http://dev1.local-goscribble.ai:8081/