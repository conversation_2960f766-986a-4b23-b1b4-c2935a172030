const XLSX = require("xlsx");
const fs = require("fs");

// Function to convert Excel date number to MM/DD/YYYY format
function excelDateToMMDDYYYY(excelDate) {
  if (!excelDate || typeof excelDate !== "number") return excelDate;

  // Excel dates are number of days since 1900-01-01
  const date = new Date((excelDate - 25569) * 86400 * 1000);
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const year = date.getFullYear();

  return `${month}/${day}/${year}`;
}

// Function to convert decimal time to HH:MM AM/PM format
function decimalTimeToHHMMAMPM(decimalTime) {
  if (!decimalTime || typeof decimalTime !== "number") return decimalTime;

  // Convert decimal time to hours and minutes
  const totalMinutes = Math.round(decimalTime * 24 * 60);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  // Convert to 12-hour format
  const period = hours >= 12 ? "PM" : "AM";
  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;

  const formattedHours = String(displayHours).padStart(2, "0");
  const formattedMinutes = String(minutes).padStart(2, "0");

  return `${formattedHours}:${formattedMinutes} ${period}`;
}

// Function to handle null/undefined values
function handleNullValue(value) {
  if (value === null || value === undefined) {
    return "";
  }
  return value;
}

// Read the Excel file
const workbook = XLSX.readFile("Haggai Healthcare Test Visits.xlsx");

// Get the first sheet
const sheetName = workbook.SheetNames[0];
const worksheet = workbook.Sheets[sheetName];

// Convert to JSON with proper handling of missing values
const jsonData = XLSX.utils.sheet_to_json(worksheet, {
  defval: "", // Set default value for missing cells
});

// Process the data to convert dates and times
const processedData = jsonData.map((record) => {
  const processedRecord = { ...record };

  // Convert date fields
  const dateFields = [
    "Client DOB",
    "Client Signed Date",
    "Clinician Confirmed Date",
    "Clinician Date of Birth",
    "Episode Start Date",
    "Episode End Date",
    "SOC Date",
    "Visit Date",
  ];

  dateFields.forEach((field) => {
    if (processedRecord[field] !== undefined && processedRecord[field] !== "") {
      processedRecord[field] = excelDateToMMDDYYYY(processedRecord[field]);
    }
  });

  // Convert visit start time
  if (
    processedRecord["Visit Start Time"] !== undefined &&
    processedRecord["Visit Start Time"] !== ""
  ) {
    processedRecord["Visit Start Time"] = decimalTimeToHHMMAMPM(
      processedRecord["Visit Start Time"],
    );
  }

  // Handle null/undefined values for all fields
  Object.keys(processedRecord).forEach((key) => {
    processedRecord[key] = handleNullValue(processedRecord[key]);
  });

  return processedRecord;
});

// Write to JSON file
fs.writeFileSync("testData.json", JSON.stringify(processedData, null, 2));

logger.info(
  `Converted ${processedData.length} records to testData.json with formatted dates and times`,
);
