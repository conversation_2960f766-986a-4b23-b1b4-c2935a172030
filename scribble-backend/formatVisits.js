const fs = require("fs");
const path = require("path");

// Load the input JSON
const rawData = JSON.parse(
  fs.readFileSync(path.join(__dirname, "testData.json")),
);
const records = rawData.slice(0, 118); // Use only the first 118

// Clinician distribution logic
const clinicianDistribution = {
  1001: 30,
  1002: 20,
  1003: 15,
  2001: 15,
  100002: 20,
  4136: 10,
  1015: 7,
};

// Build clinician assignment list
let clinicianAssignments = [];
for (const [id, count] of Object.entries(clinicianDistribution)) {
  clinicianAssignments.push(...Array(count).fill(Number(id)));
}
if (clinicianAssignments.length < 118) {
  const ids = Object.keys(clinicianDistribution).map(Number);
  while (clinicianAssignments.length < 118) {
    clinicianAssignments.push(ids[Math.floor(Math.random() * ids.length)]);
  }
} else {
  clinicianAssignments = clinicianAssignments.slice(0, 118);
}

// Format JS Date to MM/DD/YYYY
function formatMMDDYYYY(date) {
  const mm = String(date.getMonth() + 1).padStart(2, "0");
  const dd = String(date.getDate()).padStart(2, "0");
  const yyyy = date.getFullYear();
  return `${mm}/${dd}/${yyyy}`;
}

// Generate 10 dates from tomorrow
const today = new Date();
const visitDates = Array.from({ length: 10 }, (_, i) => {
  const d = new Date(today);
  d.setDate(d.getDate() + i + 1);
  return formatMMDDYYYY(d);
});
const visitDateAssignments = Array.from(
  { length: 118 },
  (_, i) => visitDates[i % 10],
);

// Safe date parser
function safeDate(str, fallback = "01/01/1970") {
  const d = new Date(str);
  return isNaN(d.getTime()) ? fallback : formatMMDDYYYY(d);
}

// Gender generator
const randomGender = () => (Math.random() < 0.5 ? "Male" : "Female");

// Transform records
const transformed = records.map((rec, i) => ({
  episode: {
    episodeNo: rec["Episode Number"],
    startDate: safeDate(rec["Episode Start Date"]),
    endDate: safeDate(rec["Episode End Date"]),
  },
  client: {
    clientNo: rec["Client ID"],
    firstName: rec["Client First Name"],
    lastName: rec["Client Last Name"],
    dateOfBirth: safeDate(rec["Client DOB"]),
    gender: randomGender(),
    email: rec["Client Email"],
    address: {
      addressLine1: rec["Client Address 1"],
      addressLine2: rec["Client Address 2"],
      state: rec["Client State"],
      city: rec["Client City"],
      zipcode: rec["Client Zip"],
      county: rec["Client County"],
    },
    phone: rec["Client Phone"],
    emergencyContact: "Thompson, Charles",
    emergencyContactPhone: "(*************",
  },
  clinician: {
    clinicianNo: clinicianAssignments[i],
  },
  visit: {
    visitId: rec["Visit ID"],
    visitDate: visitDateAssignments[i],
    visitStartTime: rec["Visit Start Time"],
    visitType: "SOC",
  },
}));

// Write to file
fs.writeFileSync(
  path.join(__dirname, "formatted_visits.json"),
  JSON.stringify(transformed, null, 2),
);

console.log(
  "✅ formatted_visits.json created with MM/DD/YYYY dates and proper clinician distribution.",
);
