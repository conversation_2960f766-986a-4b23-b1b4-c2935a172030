const fs = require("fs");

// Read the JSON file
const data = JSON.parse(fs.readFileSync("testData.json", "utf8"));

// Track used Visit IDs
const usedVisitIds = new Set();
const duplicateVisitId = 456953;
let duplicateCount = 0;

// Process each record
const processedData = data.map((record, index) => {
  let visitId = record["Visit ID"];

  // If this Visit ID is already used, generate a new unique one
  if (usedVisitIds.has(visitId)) {
    duplicateCount++;
    // Generate a new unique Visit ID by adding the duplicate count
    visitId = duplicateVisitId + duplicateCount;
    record["Visit ID"] = visitId;
  }

  usedVisitIds.add(visitId);
  return record;
});

// Write back to JSON file
fs.writeFileSync("testData.json", JSON.stringify(processedData, null, 2));

logger.info(`Fixed ${duplicateCount} duplicate Visit IDs`);
logger.info(`Total records: ${processedData.length}`);
logger.info(`Unique Visit IDs: ${usedVisitIds.size}`);

// Verify uniqueness
const finalVisitIds = processedData.map((record) => record["Visit ID"]);
const uniqueVisitIds = new Set(finalVisitIds);
logger.info(
  `Verification - Unique Visit IDs after fix: ${uniqueVisitIds.size}`,
);
