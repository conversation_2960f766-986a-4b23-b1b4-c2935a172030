module.exports = {
  async up(db) {
    console.log("Starting visit field rename migration...");

    // Get total visit count for progress tracking
    const totalVisits = await db.collection("visits").countDocuments();
    console.log(`Total visits to process: ${totalVisits}`);

    if (totalVisits === 0) {
      console.log("No visits found, skipping migration");
      return;
    }

    // Drop old indexes first to avoid conflicts
    console.log("Dropping old indexes...");
    try {
      await db.collection("visits").dropIndex("visitId_1");
      console.log(
        "✅ Successfully dropped old unique index on 'visitId' field",
      );
    } catch (error) {
      console.log(
        "ℹ️  Index 'visitId_1' does not exist, skipping:",
        error.message,
      );
    }

    // Convert visitDate to UTC Date object
    console.log("Converting visitDate to UTC Date object...");
    await db.collection("visits").updateMany({ visitDate: { $exists: true } }, [
      {
        $set: {
          visitDate: { $toDate: "$visitDate" },
        },
      },
    ]);

    // Remove week field
    console.log("Removing week field...");
    await db.collection("visits").updateMany({ week: { $exists: true } }, [
      {
        $unset: ["week"],
      },
    ]);

    // Rename fields using aggregation pipeline
    const result = await db.collection("visits").updateMany(
      {
        $or: [
          { visitId: { $exists: true } },
          { visitStartTime: { $exists: true } },
          { visitType: { $exists: true } },
          { visitStatus: { $exists: true } },
        ],
      },
      [
        {
          $set: {
            no: "$visitId",
            startTime: "$visitStartTime",
            type: "$visitType",
            status: "$visitStatus",
          },
        },
        {
          $unset: ["visitId", "visitStartTime", "visitType", "visitStatus"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    // Remove service and serviceCode fields
    console.log("Removing service and serviceCode fields...");
    const removeServiceResult = await db.collection("visits").updateMany(
      {
        $or: [
          { service: { $exists: true } },
          { serviceCode: { $exists: true } },
        ],
      },
      [
        {
          $unset: ["service", "serviceCode"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully removed service fields from ${removeServiceResult.modifiedCount} visits`,
    );

    console.log(
      `Successfully renamed fields in ${result.modifiedCount} visits`,
    );

    // Create new indexes
    console.log("Creating new indexes...");

    try {
      await db
        .collection("visits")
        .createIndex({ no: 1 }, { unique: true, name: "no_1" });
      console.log("✅ Successfully created unique index on 'no' field");
    } catch (error) {
      console.log(
        "❌ Failed to create unique index on 'no' field:",
        error.message,
      );
    }

    console.log("Visit field rename migration completed successfully");
  },

  async down(db) {
    console.log("Rolling back visit field rename migration...");

    // Get total visit count for progress tracking
    const totalVisits = await db.collection("visits").countDocuments();
    console.log(`Total visits to process: ${totalVisits}`);

    if (totalVisits === 0) {
      console.log("No visits found, skipping rollback");
      return;
    }

    // Drop new indexes first to avoid conflicts
    console.log("Dropping new indexes...");
    try {
      await db.collection("visits").dropIndex("no_1");
      console.log("✅ Successfully dropped new unique index on 'no' field");
    } catch (error) {
      console.log("ℹ️  Index 'no_1' does not exist, skipping:", error.message);
    }

    // Restore original field names using aggregation pipeline
    const result = await db.collection("visits").updateMany(
      {
        $or: [
          { no: { $exists: true } },
          { startTime: { $exists: true } },
          { type: { $exists: true } },
          { status: { $exists: true } },
        ],
      },
      [
        {
          $set: {
            visitId: "$no",
            visitStartTime: "$startTime",
            visitType: "$type",
            visitStatus: "$status",
          },
        },
        {
          $unset: ["no", "startTime", "type", "status"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    // Convert visitDate from UTC to MM/DD/YYYY string format
    console.log("Converting visitDate from UTC to MM/DD/YYYY string format...");
    await db.collection("visits").updateMany({ visitDate: { $exists: true } }, [
      {
        $set: {
          visitDate: {
            $cond: {
              if: { $eq: [{ $type: "$visitDate" }, "date"] },
              then: {
                $dateToString: {
                  date: "$visitDate",
                  format: "%m/%d/%Y",
                },
              },
              else: "$visitDate",
            },
          },
        },
      },
    ]);
    console.log(
      `Successfully restored original field names in ${result.modifiedCount} visits`,
    );

    // Restore old indexes
    console.log("Restoring old indexes...");

    try {
      await db
        .collection("visits")
        .createIndex({ visitId: 1 }, { unique: true, name: "visitId_1" });
      console.log("✅ Successfully restored unique index on 'visitId' field");
    } catch (error) {
      console.log(
        "❌ Failed to restore unique index on 'visitId' field:",
        error.message,
      );
    }

    console.log("Visit field rename rollback completed successfully");
  },
};
