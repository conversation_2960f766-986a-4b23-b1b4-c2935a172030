module.exports = {
  async up(db) {
    // Rename collection from client_infos to clients
    try {
      await db.renameCollection("client_infos", "clients");
    } catch (error) {
      // If collection doesn't exist or is already renamed, continue
      console.log("Collection rename skipped:", error.message);
    }

    // Move individual address fields into nested address object
    await db
      .collection("clients")
      .updateMany({ addressLine1: { $exists: true } }, [
        {
          $set: {
            address: {
              addressLine1: "$addressLine1",
              addressLine2: "$addressLine2",
              city: "$city",
              state: "$state",
              zipcode: "$zipCode",
              county: "$county",
            },
          },
        },
        {
          $unset: [
            "addressLine1",
            "addressLine2",
            "city",
            "state",
            "zipCode",
            "county",
          ],
        },
      ]);

    // Remove age field if it exists
    await db
      .collection("clients")
      .updateMany({ age: { $exists: true } }, { $unset: { age: "" } });

    // Convert dob field to Date object if it exists
    await db.collection("clients").updateMany({ dob: { $exists: true } }, [
      {
        $set: {
          dateOfBirth: { $toDate: "$dob" },
        },
      },
      {
        $unset: "dob",
      },
    ]);

    // Remove clientGroupId field if it exists
    await db
      .collection("clients")
      .updateMany(
        { clientGroupId: { $exists: true } },
        { $unset: { clientGroupId: "" } },
      );

    // Rename clientId field to no if it exists
    try {
      // Drop the unique index on clientId if it exists
      await db.collection("clients").dropIndex("clientId_1");
    } catch (error) {
      // Index might not exist, continue
      console.log("Index drop skipped:", error.message);
    }

    await db.collection("clients").updateMany({ clientId: { $exists: true } }, [
      {
        $set: {
          no: "$clientId",
        },
      },
      {
        $unset: "clientId",
      },
    ]);

    // Create unique index on the new no field
    try {
      await db
        .collection("clients")
        .createIndex({ no: 1 }, { unique: true, sparse: true });
    } catch (error) {
      console.log("Index creation skipped:", error.message);
    }

    // Update updatedAt timestamp for all modified documents
    await db
      .collection("clients")
      .updateMany(
        { address: { $exists: true } },
        { $set: { updatedAt: new Date() } },
      );
  },

  async down(db) {
    // Restore individual address fields from nested address object
    await db.collection("clients").updateMany({ address: { $exists: true } }, [
      {
        $set: {
          addressLine1: "$address.addressLine1",
          addressLine2: "$address.addressLine2",
          city: "$address.city",
          state: "$address.state",
          zipCode: "$address.zipcode",
          county: "$address.county",
        },
      },
      {
        $unset: "address",
      },
    ]);

    // Convert dob field back to MM/DD/YYYY string if it exists
    await db
      .collection("clients")
      .updateMany({ dateOfBirth: { $exists: true } }, [
        {
          $set: {
            dob: {
              $dateToString: {
                date: "$dateOfBirth",
                format: "%m/%d/%Y",
              },
            },
          },
        },
        {
          $unset: "dateOfBirth",
        },
      ]);

    // Calculate and add age field as string from dob
    await db.collection("clients").updateMany({ dob: { $exists: true } }, [
      {
        $set: {
          age: {
            $toString: {
              $floor: {
                $divide: [
                  { $subtract: [new Date(), { $toDate: "$dob" }] },
                  365.25 * 24 * 60 * 60 * 1000,
                ],
              },
            },
          },
        },
      },
    ]);

    // Rename no field back to clientId if it exists
    try {
      // Drop the unique index on no if it exists
      await db.collection("clients").dropIndex("no_1");
    } catch (error) {
      // Index might not exist, continue
      console.log("Index drop skipped:", error.message);
    }

    await db.collection("clients").updateMany({ no: { $exists: true } }, [
      {
        $set: {
          clientId: "$no",
        },
      },
      {
        $unset: "no",
      },
    ]);

    // Create unique index on the original clientId field
    try {
      await db
        .collection("clients")
        .createIndex({ clientId: 1 }, { unique: true, sparse: true });
    } catch (error) {
      console.log("Index creation skipped:", error.message);
    }

    // Update updatedAt timestamp for all modified documents
    await db
      .collection("clients")
      .updateMany(
        { addressLine1: { $exists: true } },
        { $set: { updatedAt: new Date() } },
      );

    // Rename collection back from clients to client_infos
    try {
      await db.renameCollection("clients", "client_infos");
    } catch (error) {
      // If collection doesn't exist or is already renamed, continue
      console.log("Collection rename skipped:", error.message);
    }
  },
};
