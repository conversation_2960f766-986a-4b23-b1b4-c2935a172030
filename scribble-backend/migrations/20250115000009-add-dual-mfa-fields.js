module.exports = {
  async up(db) {
    console.log("Adding dual MFA fields to user profiles...");

    // Add new MFA fields to user_profiles collection
    await db.collection("user_profiles").updateMany(
      {
        mfaMethod: { $exists: false },
      },
      {
        $set: {
          mfaMethod: "",
          mfaEmailOtpSecret: "",
          mfaEmailOtpExpiry: null,
          mfaEmailOtpAttempts: 0,
          mfaEmailFallbackUsed: false,
        },
      },
    );

    // Update existing MFA-enabled users to have proper method
    await db.collection("user_profiles").updateMany(
      {
        mfaEnabled: true,
        mfaSecret: { $exists: true, $ne: "" },
      },
      {
        $set: {
          mfaMethod: "",
        },
      },
    );

    console.log("Dual MFA fields added successfully");
  },

  async down(db) {
    console.log("Rolling back dual MFA fields...");

    // Remove the new MFA fields
    await db.collection("user_profiles").updateMany(
      {},
      {
        $unset: {
          mfaMethod: "",
          mfaEmailOtpSecret: "",
          mfaEmailOtpExpiry: "",
          mfaEmailOtpAttempts: "",
          mfaEmailFallbackUsed: "",
        },
      },
    );

    console.log("Dual MFA fields removed successfully");
  },
};
