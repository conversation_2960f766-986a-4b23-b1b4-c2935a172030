module.exports = {
  async up(db) {
    console.log("Starting user tenantId field removal migration...");

    // Get total user count for progress tracking
    const totalUsers = await db.collection("users").countDocuments();
    console.log(`Total users to process: ${totalUsers}`);

    if (totalUsers === 0) {
      console.log("No users found, skipping migration");
      return;
    }

    // Remove tenantId field using aggregation pipeline
    const result = await db.collection("users").updateMany(
      {
        tenantId: { $exists: true },
      },
      [
        {
          $unset: ["tenantId"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully removed tenantId field from ${result.modifiedCount} users`,
    );

    // Drop any indexes on tenantId if they exist
    console.log("Checking for tenantId indexes...");

    try {
      await db.collection("users").dropIndex("tenantId_1");
      console.log("Dropped index on tenantId");
    } catch (error) {
      console.log("Index tenantId_1 might not exist:", error.message);
    }

    try {
      await db.collection("users").dropIndex("tenantId_1_email_1");
      console.log("Dropped compound index on tenantId and email");
    } catch (error) {
      console.log("Index tenantId_1_email_1 might not exist:", error.message);
    }

    console.log("User tenantId field removal migration completed successfully");
  },

  async down(db) {
    console.log("Rolling back user tenantId field removal migration...");

    // Get total user count for progress tracking
    const totalUsers = await db.collection("users").countDocuments();
    console.log(`Total users to process: ${totalUsers}`);

    if (totalUsers === 0) {
      console.log("No users found, skipping rollback");
      return;
    }

    // Note: We cannot restore the original tenantId values since we don't have them
    // This rollback will only add the field back with a default value
    console.log(
      "WARNING: Cannot restore original tenantId values - adding field with default value",
    );

    // Add tenantId field back with a default value
    const result = await db.collection("users").updateMany({}, [
      {
        $set: {
          tenantId: "default", // Default value since we can't restore original
        },
      },
      {
        $set: { updatedAt: new Date() },
      },
    ]);

    console.log(
      `Successfully added tenantId field back to ${result.modifiedCount} users with default value`,
    );

    // Recreate indexes
    console.log("Recreating indexes...");

    try {
      await db
        .collection("users")
        .createIndex({ tenantId: 1 }, { name: "tenantId_1" });
      console.log("Recreated index on tenantId");
    } catch (error) {
      console.log("Error creating index on tenantId:", error.message);
    }

    console.log(
      "User tenantId field removal rollback completed (with default values)",
    );
  },
};
