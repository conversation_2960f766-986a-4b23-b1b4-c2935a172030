module.exports = {
  async up(db) {
    console.log("Starting startTime field conversion migration...");

    // Get total visit count for progress tracking
    const totalVisits = await db.collection("visits").countDocuments();
    console.log(`Total visits to process: ${totalVisits}`);

    if (totalVisits === 0) {
      console.log("No visits found, skipping migration");
      return;
    }

    // Function to convert 12-hour time string to 24-hour time string
    const convertTo24Hour = (timeStr) => {
      if (!timeStr || typeof timeStr !== "string") return null;

      // Match patterns like "05:33 PM", "12:00 AM", "1:30 PM", etc.
      const match = timeStr.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
      if (!match) return null;

      // eslint-disable-next-line
      let [, hour, minute, period] = match;
      hour = parseInt(hour, 10);
      minute = parseInt(minute, 10);

      // Convert to 24-hour format
      if (period.toUpperCase() === "PM" && hour !== 12) {
        hour += 12;
      } else if (period.toUpperCase() === "AM" && hour === 12) {
        hour = 0;
      }

      // Format as HH:MM
      return `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
    };

    // Process visits in batches
    const batchSize = 1000;
    let processed = 0;
    let updated = 0;

    console.log(
      "Converting startTime field from 12-hour format to 24-hour time format...",
    );

    while (processed < totalVisits) {
      const visits = await db
        .collection("visits")
        .find({ startTime: { $exists: true, $ne: null } })
        .skip(processed)
        .limit(batchSize)
        .toArray();

      if (visits.length === 0) break;

      const bulkOps = [];

      for (const visit of visits) {
        const convertedTime = convertTo24Hour(visit.startTime);

        if (convertedTime && convertedTime !== visit.startTime) {
          bulkOps.push({
            updateOne: {
              filter: { _id: visit._id },
              update: {
                $set: {
                  startTime: convertedTime,
                  updatedAt: new Date(),
                },
              },
            },
          });
          updated++;
        }
      }

      if (bulkOps.length > 0) {
        await db.collection("visits").bulkWrite(bulkOps);
        console.log(
          `Processed batch: ${processed + visits.length}/${totalVisits}, Updated: ${updated}`,
        );
      }

      processed += visits.length;
    }

    console.log(`✅ Migration completed successfully!`);
    console.log(`📊 Statistics:`);
    console.log(`   - Total visits processed: ${processed}`);
    console.log(`   - Visits updated: ${updated}`);
    console.log(`   - Visits unchanged: ${processed - updated}`);

    // Create index on startTime for better sorting performance
    console.log("Creating index on startTime field...");
    try {
      await db.collection("visits").createIndex({ startTime: 1 });
      console.log("✅ Index created on startTime field");
    } catch (error) {
      console.log("ℹ️  Index on startTime may already exist:", error.message);
    }

    // Create compound index for date + time sorting
    console.log("Creating compound index on visitDate + startTime...");
    try {
      await db.collection("visits").createIndex({ visitDate: 1, startTime: 1 });
      console.log("✅ Compound index created on visitDate + startTime");
    } catch (error) {
      console.log("ℹ️  Compound index may already exist:", error.message);
    }
  },

  async down(db) {
    console.log("Rolling back startTime field conversion...");
    console.log(
      "Converting 24-hour format back to 12-hour format (HH:MM AM/PM)...",
    );

    // Get total visit count for progress tracking
    const totalVisits = await db.collection("visits").countDocuments();
    console.log(`Total visits to process: ${totalVisits}`);

    if (totalVisits === 0) {
      console.log("No visits found, skipping rollback");
      return;
    }

    // Function to convert 24-hour time string to 12-hour time string
    const convertTo12Hour = (timeStr) => {
      if (!timeStr || typeof timeStr !== "string") return null;

      // Check if it's already in 12-hour format (contains AM/PM)
      if (timeStr.includes("AM") || timeStr.includes("PM")) {
        return timeStr;
      }

      // Parse 24-hour format (HH:MM)
      const match = timeStr.match(/^(\d{1,2}):(\d{2})$/);
      if (!match) return null;

      let [, hour, minute] = match;
      hour = parseInt(hour, 10);
      minute = parseInt(minute, 10);

      let period = "AM";
      let displayHour = hour;

      if (hour === 0) {
        displayHour = 12;
      } else if (hour === 12) {
        period = "PM";
      } else if (hour > 12) {
        displayHour = hour - 12;
        period = "PM";
      }

      return `${displayHour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")} ${period}`;
    };

    // Process visits in batches
    const batchSize = 1000;
    let processed = 0;
    let updated = 0;

    while (processed < totalVisits) {
      const visits = await db
        .collection("visits")
        .find({ startTime: { $exists: true, $ne: null } })
        .skip(processed)
        .limit(batchSize)
        .toArray();

      if (visits.length === 0) break;

      const bulkOps = [];

      for (const visit of visits) {
        const convertedTime = convertTo12Hour(visit.startTime);

        if (convertedTime && convertedTime !== visit.startTime) {
          bulkOps.push({
            updateOne: {
              filter: { _id: visit._id },
              update: {
                $set: {
                  startTime: convertedTime,
                  updatedAt: new Date(),
                },
              },
            },
          });
          updated++;
        }
      }

      if (bulkOps.length > 0) {
        await db.collection("visits").bulkWrite(bulkOps);
        console.log(
          `Processed batch: ${processed + visits.length}/${totalVisits}, Updated: ${updated}`,
        );
      }

      processed += visits.length;
    }

    console.log(`✅ Rollback completed successfully!`);
    console.log(`📊 Statistics:`);
    console.log(`   - Total visits processed: ${processed}`);
    console.log(`   - Visits updated: ${updated}`);
    console.log(`   - Visits unchanged: ${processed - updated}`);
    console.log(`   - Time format: 12-hour (HH:MM AM/PM)`);
  },
};
