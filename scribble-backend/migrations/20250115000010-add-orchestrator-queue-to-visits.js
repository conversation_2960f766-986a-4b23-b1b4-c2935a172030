module.exports = {
  async up(db) {
    console.log("Adding orchestratorQueue field to visits collection...");

    // Get total visit count for progress tracking
    const totalVisits = await db.collection("visits").countDocuments();
    console.log(`Total visits to process: ${totalVisits}`);

    if (totalVisits === 0) {
      console.log("No visits found, skipping migration");
      return;
    }

    // Add orchestratorQueue field to all existing visits
    const result = await db.collection("visits").updateMany(
      {
        orchestratorQueue: { $exists: false },
      },
      {
        $set: {
          orchestratorQueue: "haggaihealth_kantime_performer",
          updatedAt: new Date(),
        },
      },
    );

    console.log(
      `Successfully added orchestratorQueue field to ${result.modifiedCount} visits`,
    );
    console.log("OrchestratorQueue field added successfully");
  },

  async down(db) {
    console.log("Rolling back orchestratorQueue field from visits...");

    // Get total visit count for progress tracking
    const totalVisits = await db.collection("visits").countDocuments();
    console.log(`Total visits to process: ${totalVisits}`);

    if (totalVisits === 0) {
      console.log("No visits found, skipping rollback");
      return;
    }

    // Remove the orchestratorQueue field
    const result = await db.collection("visits").updateMany(
      { orchestratorQueue: { $exists: true } },
      {
        $unset: {
          orchestratorQueue: "",
        },
        $set: {
          updatedAt: new Date(),
        },
      },
    );

    console.log(
      `Successfully removed orchestratorQueue field from ${result.modifiedCount} visits`,
    );
    console.log("OrchestratorQueue field removed successfully");
  },
};
