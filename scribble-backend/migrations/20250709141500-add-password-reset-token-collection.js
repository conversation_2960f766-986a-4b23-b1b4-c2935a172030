// MongoDB creates collections on first insert, so this migration is for documentation only.
module.exports = {
  async up(db) {
    // Create index on userId for fast lookups
    await db.collection("password_reset_tokens").createIndex({ userId: 1 });

    // Create index on tokenHash for fast lookups during password reset
    await db.collection("password_reset_tokens").createIndex({ tokenHash: 1 });

    // Create TTL index on expiresAt to automatically remove expired tokens
    // await db.collection("password_reset_tokens").createIndex(
    //     { expiresAt: 1 },
    //     { expireAfterSeconds: 0 }
    // );
  },
  async down(db) {
    await db.collection("password_reset_tokens").drop();
  },
};
