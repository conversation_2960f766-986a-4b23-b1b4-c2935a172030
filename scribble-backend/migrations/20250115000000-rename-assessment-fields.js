module.exports = {
  async up(db) {
    console.log("Starting assessment field rename migration...");

    // Get total assessment count for progress tracking
    const totalAssessments = await db
      .collection("assessments")
      .countDocuments();
    console.log(`Total assessments to process: ${totalAssessments}`);

    if (totalAssessments === 0) {
      console.log("No assessments found, skipping migration");
      return;
    }

    // Rename fields using aggregation pipeline
    const result = await db.collection("assessments").updateMany(
      {
        $or: [
          { assessmentName: { $exists: true } },
          { assessmentQuestion: { $exists: true } },
          { assessmentAnswer: { $exists: true } },
        ],
      },
      [
        {
          $set: {
            name: "$assessmentName",
            question: "$assessmentQuestion",
            answer: "$assessmentAnswer",
          },
        },
        {
          $unset: ["assessmentName", "assessmentQuestion", "assessmentAnswer"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully renamed fields in ${result.modifiedCount} assessments`,
    );
  },

  async down(db) {
    console.log("Rolling back assessment field rename migration...");

    // Get total assessment count for progress tracking
    const totalAssessments = await db
      .collection("assessments")
      .countDocuments();
    console.log(`Total assessments to process: ${totalAssessments}`);

    if (totalAssessments === 0) {
      console.log("No assessments found, skipping rollback");
      return;
    }

    // Restore original field names using aggregation pipeline
    const result = await db.collection("assessments").updateMany(
      {
        $or: [
          { name: { $exists: true } },
          { question: { $exists: true } },
          { answer: { $exists: true } },
        ],
      },
      [
        {
          $set: {
            assessmentName: "$name",
            assessmentQuestion: "$question",
            assessmentAnswer: "$answer",
          },
        },
        {
          $unset: ["name", "question", "answer"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully restored original field names in ${result.modifiedCount} assessments`,
    );
  },
};
