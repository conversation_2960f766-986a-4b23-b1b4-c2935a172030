module.exports = {
  async up(db) {
    console.log("Starting episode field rename and data type migration...");

    // Drop the unique index on episodeNo if it exists
    try {
      await db.collection("episodes").dropIndex("episodeNo_1");
      console.log("✅ Dropped unique index on 'episodeNo' field");
    } catch (error) {
      console.log("ℹ️  Index 'episodeNo_1' might not exist:", error.message);
    }

    // Rename episodeNo to no and convert to number
    await db
      .collection("episodes")
      .updateMany({ episodeNo: { $exists: true } }, [
        {
          $set: {
            no: { $toInt: "$episodeNo" },
          },
        },
        {
          $unset: "episodeNo",
        },
      ]);

    // Rename episodeDuration to duration and convert to number
    await db
      .collection("episodes")
      .updateMany({ episodeDuration: { $exists: true } }, [
        {
          $set: {
            duration: { $toInt: "$episodeDuration" },
          },
        },
        {
          $unset: "episodeDuration",
        },
      ]);

    // Convert startDate to UTC Date object
    await db
      .collection("episodes")
      .updateMany({ startDate: { $exists: true } }, [
        {
          $set: {
            startDate: { $toDate: "$startDate" },
          },
        },
      ]);

    // Convert endDate to UTC Date object
    await db.collection("episodes").updateMany({ endDate: { $exists: true } }, [
      {
        $set: {
          endDate: { $toDate: "$endDate" },
        },
      },
    ]);

    // Update updatedAt timestamp for all modified documents
    await db
      .collection("episodes")
      .updateMany({}, { $set: { updatedAt: new Date() } });

    // Create unique index on the new no field
    try {
      await db
        .collection("episodes")
        .createIndex({ no: 1 }, { unique: true, name: "no_1" });
      console.log("✅ Successfully created unique index on 'no' field");
    } catch (error) {
      console.log(
        "❌ Failed to create unique index on 'no' field:",
        error.message,
      );
    }

    console.log(
      "Episode field rename and data type migration completed successfully",
    );
  },

  async down(db) {
    console.log("Starting episode field rename and data type rollback...");

    // Drop the unique index on no if it exists
    try {
      await db.collection("episodes").dropIndex("no_1");
      console.log("✅ Dropped unique index on 'no' field");
    } catch (error) {
      console.log("ℹ️  Index 'no_1' might not exist:", error.message);
    }

    // Rename no back to episodeNo and convert to string
    await db.collection("episodes").updateMany({ no: { $exists: true } }, [
      {
        $set: {
          episodeNo: { $toString: "$no" },
        },
      },
      {
        $unset: "no",
      },
    ]);

    // Rename duration back to episodeDuration and convert to string
    await db
      .collection("episodes")
      .updateMany({ duration: { $exists: true } }, [
        {
          $set: {
            episodeDuration: { $toString: "$duration" },
          },
        },
        {
          $unset: "duration",
        },
      ]);

    // Convert startDate from UTC to MM/DD/YYYY string format
    await db
      .collection("episodes")
      .updateMany({ startDate: { $exists: true } }, [
        {
          $set: {
            startDate: {
              $dateToString: {
                date: "$startDate",
                format: "%m/%d/%Y",
              },
            },
          },
        },
      ]);

    // Convert endDate from UTC to MM/DD/YYYY string format
    await db.collection("episodes").updateMany({ endDate: { $exists: true } }, [
      {
        $set: {
          endDate: {
            $dateToString: {
              date: "$endDate",
              format: "%m/%d/%Y",
            },
          },
        },
      },
    ]);

    // Update updatedAt timestamp for all modified documents
    await db
      .collection("episodes")
      .updateMany({}, { $set: { updatedAt: new Date() } });

    // Create unique index on the original episodeNo field
    try {
      await db
        .collection("episodes")
        .createIndex({ episodeNo: 1 }, { unique: true, name: "episodeNo_1" });
      console.log("✅ Successfully created unique index on 'episodeNo' field");
    } catch (error) {
      console.log(
        "❌ Failed to create unique index on 'episodeNo' field:",
        error.message,
      );
    }

    console.log(
      "Episode field rename and data type rollback completed successfully",
    );
  },
};
