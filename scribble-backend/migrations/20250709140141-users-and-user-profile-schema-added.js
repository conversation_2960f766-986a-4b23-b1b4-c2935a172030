module.exports = {
  async up(db) {
    // Convert string booleans to real booleans for isDeleted and isVerified
    await db
      .collection("users")
      .updateMany({ isDeleted: "true" }, { $set: { isDeleted: true } });
    await db
      .collection("users")
      .updateMany({ isDeleted: "false" }, { $set: { isDeleted: false } });
    await db
      .collection("users")
      .updateMany({ isVerified: "true" }, { $set: { isVerified: true } });
    await db
      .collection("users")
      .updateMany({ isVerified: "false" }, { $set: { isVerified: false } });

    // Add 'status' and 'statusUpdatedAt' if missing
    await db
      .collection("users")
      .updateMany(
        { status: { $exists: false } },
        { $set: { status: "active" } },
      );
    await db
      .collection("users")
      .updateMany(
        { statusUpdatedAt: { $exists: false } },
        { $set: { statusUpdatedAt: new Date() } },
      );

    // If isDeleted is true, set status to 'inactive'
    await db
      .collection("users")
      .updateMany({ isDeleted: true }, { $set: { status: "inactive" } });

    // Remove 'isVerified' and 'isDeleted' fields if they exist
    await db
      .collection("users")
      .updateMany({}, { $unset: { isVerified: "", isDeleted: "" } });

    // Rename 'lastLoginTime' to 'lastLoginAt' if it exists
    await db
      .collection("users")
      .updateMany({ lastLoginTime: { $exists: true } }, [
        { $set: { lastLoginAt: "$lastLoginTime" } },
        { $unset: "lastLoginTime" },
      ]);
  },

  async down(db) {
    // Remove 'status' and 'statusUpdatedAt' from users documents
    await db
      .collection("users")
      .updateMany({}, { $unset: { status: "", statusUpdatedAt: "" } });
    // Restore 'isVerified' as false for all
    await db
      .collection("users")
      .updateMany({}, { $set: { isVerified: false } });
    // Restore 'isDeleted': true if status is 'inactive', else false
    await db
      .collection("users")
      .updateMany({ status: "inactive" }, { $set: { isDeleted: true } });
    await db
      .collection("users")
      .updateMany(
        { status: { $ne: "inactive" } },
        { $set: { isDeleted: false } },
      );
    // Rename 'lastLoginAt' back to 'lastLoginTime' if it exists
    await db
      .collection("users")
      .updateMany({ lastLoginAt: { $exists: true } }, [
        { $set: { lastLoginTime: "$lastLoginAt" } },
        { $unset: "lastLoginAt" },
      ]);
  },
};
