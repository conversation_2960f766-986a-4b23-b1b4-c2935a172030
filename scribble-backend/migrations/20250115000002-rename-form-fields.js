module.exports = {
  async up(db) {
    console.log("Starting form field rename migration...");

    // Get total form count for progress tracking
    const totalForms = await db.collection("forms").countDocuments();
    console.log(`Total forms to process: ${totalForms}`);

    if (totalForms === 0) {
      console.log("No forms found, skipping migration");
      return;
    }

    // Drop old indexes first to avoid conflicts
    console.log("Dropping old indexes...");
    try {
      await db.collection("forms").dropIndex("formName_1");
      console.log(
        "✅ Successfully dropped old unique index on 'formName' field",
      );
    } catch (error) {
      console.log(
        "ℹ️  Index 'formName_1' does not exist, skipping:",
        error.message,
      );
    }

    // Rename fields using aggregation pipeline
    const result = await db.collection("forms").updateMany(
      {
        $or: [
          { formName: { $exists: true } },
          { assessmentForm: { $exists: true } },
        ],
      },
      [
        {
          $set: {
            name: "$formName",
            question: "$assessmentForm",
          },
        },
        {
          $unset: ["formName", "assessmentForm"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(`Successfully renamed fields in ${result.modifiedCount} forms`);

    // Create new indexes
    console.log("Creating new indexes...");

    try {
      await db
        .collection("forms")
        .createIndex({ name: 1 }, { unique: true, name: "name_1" });
      console.log("✅ Successfully created unique index on 'name' field");
    } catch (error) {
      console.log(
        "❌ Failed to create unique index on 'name' field:",
        error.message,
      );
    }

    console.log("Form field rename migration completed successfully");
  },

  async down(db) {
    console.log("Rolling back form field rename migration...");

    // Get total form count for progress tracking
    const totalForms = await db.collection("forms").countDocuments();
    console.log(`Total forms to process: ${totalForms}`);

    if (totalForms === 0) {
      console.log("No forms found, skipping rollback");
      return;
    }

    // Drop new indexes first to avoid conflicts
    console.log("Dropping new indexes...");
    try {
      await db.collection("forms").dropIndex("name_1");
      console.log("✅ Successfully dropped new unique index on 'name' field");
    } catch (error) {
      console.log(
        "ℹ️  Index 'name_1' does not exist, skipping:",
        error.message,
      );
    }

    // Restore original field names using aggregation pipeline
    const result = await db.collection("forms").updateMany(
      {
        $or: [{ name: { $exists: true } }, { question: { $exists: true } }],
      },
      [
        {
          $set: {
            formName: "$name",
            assessmentForm: "$question",
          },
        },
        {
          $unset: ["name", "question"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully restored original field names in ${result.modifiedCount} forms`,
    );

    // Restore old indexes
    console.log("Restoring old indexes...");

    try {
      await db
        .collection("forms")
        .createIndex({ formName: 1 }, { unique: true, name: "formName_1" });
      console.log("✅ Successfully restored unique index on 'formName' field");
    } catch (error) {
      console.log(
        "❌ Failed to restore unique index on 'formName' field:",
        error.message,
      );
    }

    console.log("Form field rename rollback completed successfully");
  },
};
