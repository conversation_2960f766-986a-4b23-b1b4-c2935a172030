module.exports = {
  async up(db) {
    console.log("Starting grid field rename migration...");

    // Get total grid count for progress tracking
    const totalGrids = await db.collection("grids").countDocuments();
    console.log(`Total grids to process: ${totalGrids}`);

    if (totalGrids === 0) {
      console.log("No grids found, skipping migration");
      return;
    }

    // Drop old indexes first to avoid conflicts
    console.log("Dropping old indexes...");
    try {
      await db.collection("grids").dropIndex("gridName_1");
      console.log(
        "✅ Successfully dropped old unique index on 'gridName' field",
      );
    } catch (error) {
      console.log(
        "ℹ️  Index 'gridName_1' does not exist, skipping:",
        error.message,
      );
    }

    // Rename fields using aggregation pipeline
    const result = await db.collection("grids").updateMany(
      {
        gridName: { $exists: true },
      },
      [
        {
          $set: {
            name: "$gridName",
          },
        },
        {
          $unset: ["gridName"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(`Successfully renamed fields in ${result.modifiedCount} grids`);

    // Create new indexes
    console.log("Creating new indexes...");

    try {
      await db
        .collection("grids")
        .createIndex({ name: 1 }, { unique: true, name: "name_1" });
      console.log("✅ Successfully created unique index on 'name' field");
    } catch (error) {
      console.log(
        "❌ Failed to create unique index on 'name' field:",
        error.message,
      );
    }

    console.log("Grid field rename migration completed successfully");
  },

  async down(db) {
    console.log("Rolling back grid field rename migration...");

    // Get total grid count for progress tracking
    const totalGrids = await db.collection("grids").countDocuments();
    console.log(`Total grids to process: ${totalGrids}`);

    if (totalGrids === 0) {
      console.log("No grids found, skipping rollback");
      return;
    }

    // Drop new indexes first to avoid conflicts
    console.log("Dropping new indexes...");
    try {
      await db.collection("grids").dropIndex("name_1");
      console.log("✅ Successfully dropped new unique index on 'name' field");
    } catch (error) {
      console.log(
        "ℹ️  Index 'name_1' does not exist, skipping:",
        error.message,
      );
    }

    // Restore original field names using aggregation pipeline
    const result = await db.collection("grids").updateMany(
      {
        name: { $exists: true },
      },
      [
        {
          $set: {
            gridName: "$name",
          },
        },
        {
          $unset: ["name"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully restored original field names in ${result.modifiedCount} grids`,
    );

    // Restore old indexes
    console.log("Restoring old indexes...");

    try {
      await db
        .collection("grids")
        .createIndex({ gridName: 1 }, { unique: true, name: "gridName_1" });
      console.log("✅ Successfully restored unique index on 'gridName' field");
    } catch (error) {
      console.log(
        "❌ Failed to restore unique index on 'gridName' field:",
        error.message,
      );
    }

    console.log("Grid field rename rollback completed successfully");
  },
};
