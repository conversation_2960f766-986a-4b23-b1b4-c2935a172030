const BATCH_SIZE = 1000;

async function migrateCollection(db, source, mapFn) {
  let lastId = null;
  let hasMore = true;
  while (hasMore) {
    const query = lastId ? { _id: { $gt: lastId } } : {};
    const batch = await db
      .collection(source)
      .find(query)
      .sort({ _id: 1 })
      .limit(BATCH_SIZE)
      .toArray();
    if (batch.length === 0) break;

    const profiles = batch.map(mapFn).filter(Boolean);
    if (profiles.length) {
      try {
        await db
          .collection("user_profiles")
          .insertMany(profiles, { ordered: false });
      } catch {
        // Ignore duplicate key errors and continue
      }
    }

    lastId = batch[batch.length - 1]._id;
    hasMore = batch.length === BATCH_SIZE;
  }
}

module.exports = {
  async up(db) {
    // 2. CREATE user_profile COLLECTION IF NOT EXISTS
    const collections = await db.listCollections().toArray();
    const hasUserProfiles = collections.some(
      (col) => col.name === "user_profiles",
    );
    if (!hasUserProfiles) {
      await db.createCollection("user_profiles");
      // Create unique index on no
      await db
        .collection("user_profiles")
        .createIndex({ no: 1 }, { unique: true });
    }
    // 1. Migrate clinicianinfos
    await migrateCollection(db, "clinician_infos", (c) => ({
      userId: c.userId,
      disciplineId: c.disciplineId,
      no: c.clinicianNo,
      firstName: c.firstName,
      lastName: c.lastName,
      phone: c.primaryPhone,
      dateOfBirth: c.dob ? new Date(c.dob) : undefined,
      gender: c.gender,
      jobTitle: c.jobTitle,
      address: {
        addressLine1: c.address1,
        addressLine2: c.address2,
        state: c.state,
        zipcode: c.zip,
        county: c.county,
      },
      createdAt: c.createdAt,
      updatedAt: c.updatedAt,
      migratedFrom: "clinicianinfos",
    }));

    // 2. Migrate admininfos
    await migrateCollection(db, "admin_infos", (a) => ({
      userId: a.userId,
      no: a.adminNo,
      firstName: a.firstName,
      lastName: a.lastName,
      phone: a.primaryPhone,
      dateOfBirth: a.dob ? new Date(a.dob) : undefined,
      gender: a.gender,
      jobTitle: a.jobTitle,
      address: {
        addressLine1: a.address1,
        addressLine2: a.address2,
        state: a.state,
        zipcode: a.zip,
        county: a.county,
      },
      createdAt: a.createdAt,
      updatedAt: a.updatedAt,
      migratedFrom: "admininfos",
    }));
  },

  async down(db) {
    // Remove only the migrated profiles
    await db
      .collection("user_profiles")
      .deleteMany({ migratedFrom: { $in: ["clinicianinfos", "admininfos"] } });
    // Drop user_profile collection if it exists
    const collections = await db.listCollections().toArray();
    const hasUserProfiles = collections.some(
      (col) => col.name === "user_profiles",
    );
    if (hasUserProfiles) {
      await db.collection("user_profiles").drop();
    }
  },
};
