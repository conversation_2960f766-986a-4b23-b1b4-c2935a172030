module.exports = {
  async up(db) {
    console.log("Starting form template field rename migration...");

    // Get total form template count for progress tracking
    const totalFormTemplates = await db
      .collection("form_templates")
      .countDocuments();
    console.log(`Total form templates to process: ${totalFormTemplates}`);

    if (totalFormTemplates === 0) {
      console.log("No form templates found, skipping migration");
      return;
    }

    // Drop old indexes first to avoid conflicts
    console.log("Dropping old indexes...");
    try {
      await db.collection("form_templates").dropIndex("formName_1");
      console.log(
        "✅ Successfully dropped old unique index on 'formName' field",
      );
    } catch (error) {
      console.log(
        "ℹ️  Index 'formName_1' does not exist, skipping:",
        error.message,
      );
    }

    // Rename fields using aggregation pipeline
    const result = await db.collection("form_templates").updateMany(
      {
        $or: [
          { formName: { $exists: true } },
          { assessmentForm: { $exists: true } },
        ],
      },
      [
        {
          $set: {
            name: "$formName",
            question: "$assessmentForm",
          },
        },
        {
          $unset: ["formName", "assessmentForm"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully renamed fields in ${result.modifiedCount} form templates`,
    );

    // Create new indexes
    console.log("Creating new indexes...");

    try {
      await db
        .collection("form_templates")
        .createIndex({ name: 1 }, { unique: true, name: "name_1" });
      console.log("✅ Successfully created unique index on 'name' field");
    } catch (error) {
      console.log(
        "❌ Failed to create unique index on 'name' field:",
        error.message,
      );
    }

    console.log("Form template field rename migration completed successfully");
  },

  async down(db) {
    console.log("Rolling back form template field rename migration...");

    // Get total form template count for progress tracking
    const totalFormTemplates = await db
      .collection("form_templates")
      .countDocuments();
    console.log(`Total form templates to process: ${totalFormTemplates}`);

    if (totalFormTemplates === 0) {
      console.log("No form templates found, skipping rollback");
      return;
    }

    // Drop new indexes first to avoid conflicts
    console.log("Dropping new indexes...");
    try {
      await db.collection("form_templates").dropIndex("name_1");
      console.log("✅ Successfully dropped new unique index on 'name' field");
    } catch (error) {
      console.log(
        "ℹ️  Index 'name_1' does not exist, skipping:",
        error.message,
      );
    }

    // Restore original field names using aggregation pipeline
    const result = await db.collection("form_templates").updateMany(
      {
        $or: [{ name: { $exists: true } }, { question: { $exists: true } }],
      },
      [
        {
          $set: {
            formName: "$name",
            assessmentForm: "$question",
          },
        },
        {
          $unset: ["name", "question"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully restored original field names in ${result.modifiedCount} form templates`,
    );

    // Restore old indexes
    console.log("Restoring old indexes...");

    try {
      await db
        .collection("form_templates")
        .createIndex({ formName: 1 }, { unique: true, name: "formName_1" });
      console.log("✅ Successfully restored unique index on 'formName' field");
    } catch (error) {
      console.log(
        "❌ Failed to restore unique index on 'formName' field:",
        error.message,
      );
    }

    console.log("Form template field rename rollback completed successfully");
  },
};
