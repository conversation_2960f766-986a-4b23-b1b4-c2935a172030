module.exports = {
  async up(db) {
    console.log("Starting role field rename migration...");

    // Get total role count for progress tracking
    const totalRoles = await db.collection("roles").countDocuments();
    console.log(`Total roles to process: ${totalRoles}`);

    if (totalRoles === 0) {
      console.log("No roles found, skipping migration");
      return;
    }

    // Drop old indexes first to avoid conflicts
    console.log("Dropping old indexes...");
    try {
      await db.collection("roles").dropIndex("roleName_1");
      console.log(
        "✅ Successfully dropped old unique index on 'roleName' field",
      );
    } catch (error) {
      console.log(
        "ℹ️  Index 'roleName_1' does not exist, skipping:",
        error.message,
      );
    }

    try {
      await db.collection("roles").dropIndex("scope_1");
      console.log("✅ Successfully dropped old unique index on 'scope' field");
    } catch (error) {
      console.log(
        "ℹ️  Index 'scope_1' does not exist, skipping:",
        error.message,
      );
    }

    // Rename fields using aggregation pipeline
    const result = await db.collection("roles").updateMany(
      {
        $or: [{ roleName: { $exists: true } }, { scope: { $exists: true } }],
      },
      [
        {
          $set: {
            name: "$roleName",
            permission: "$scope",
          },
        },
        {
          $unset: ["roleName", "scope"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(`Successfully renamed fields in ${result.modifiedCount} roles`);

    // Create new indexes
    console.log("Creating new indexes...");

    try {
      await db
        .collection("roles")
        .createIndex({ name: 1 }, { unique: true, name: "name_1" });
      console.log("✅ Successfully created unique index on 'name' field");
    } catch (error) {
      console.log(
        "❌ Failed to create unique index on 'name' field:",
        error.message,
      );
    }

    try {
      await db
        .collection("roles")
        .createIndex({ permission: 1 }, { name: "permission_1" });
      console.log("✅ Successfully created index on 'permission' field");
    } catch (error) {
      console.log(
        "❌ Failed to create index on 'permission' field:",
        error.message,
      );
    }

    console.log("Role field rename migration completed successfully");
  },

  async down(db) {
    console.log("Rolling back role field rename migration...");

    // Get total role count for progress tracking
    const totalRoles = await db.collection("roles").countDocuments();
    console.log(`Total roles to process: ${totalRoles}`);

    if (totalRoles === 0) {
      console.log("No roles found, skipping rollback");
      return;
    }

    // Drop new indexes first to avoid conflicts
    console.log("Dropping new indexes...");
    try {
      await db.collection("roles").dropIndex("name_1");
      console.log("✅ Successfully dropped new unique index on 'name' field");
    } catch (error) {
      console.log(
        "ℹ️  Index 'name_1' does not exist, skipping:",
        error.message,
      );
    }

    try {
      await db.collection("roles").dropIndex("permission_1");
      console.log(
        "✅ Successfully dropped new unique index on 'permission' field",
      );
    } catch (error) {
      console.log(
        "ℹ️  Index 'permission_1' does not exist, skipping:",
        error.message,
      );
    }

    // Restore original field names using aggregation pipeline
    const result = await db.collection("roles").updateMany(
      {
        $or: [{ name: { $exists: true } }, { permission: { $exists: true } }],
      },
      [
        {
          $set: {
            roleName: "$name",
            scope: "$permission",
          },
        },
        {
          $unset: ["name", "permission"],
        },
        {
          $set: { updatedAt: new Date() },
        },
      ],
    );

    console.log(
      `Successfully restored original field names in ${result.modifiedCount} roles`,
    );

    // Restore old indexes
    console.log("Restoring old indexes...");

    try {
      await db
        .collection("roles")
        .createIndex({ roleName: 1 }, { unique: true, name: "roleName_1" });
      console.log("✅ Successfully restored unique index on 'roleName' field");
    } catch (error) {
      console.log(
        "❌ Failed to restore unique index on 'roleName' field:",
        error.message,
      );
    }

    try {
      await db
        .collection("roles")
        .createIndex({ scope: 1 }, { name: "scope_1" });
      console.log("✅ Successfully restored index on 'scope' field");
    } catch (error) {
      console.log(
        "❌ Failed to restore index on 'scope' field:",
        error.message,
      );
    }

    console.log("Role field rename rollback completed successfully");
  },
};
