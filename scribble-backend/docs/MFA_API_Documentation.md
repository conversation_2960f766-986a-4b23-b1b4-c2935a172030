# Enhanced Dual MFA API Documentation

## Overview
The Enhanced Dual MFA system provides users with multiple authentication options:
- **Authenticator App (TOTP)**: Google Authenticator, <PERSON><PERSON>, etc.
- **Email OTP**: 6-digit verification codes sent via email
- **Email Fallback**: When authenticator app is lost, users can use email to login and reset their MFA method

## API Endpoints

### 1. Setup MFA
**POST** `/api/v1/mfa/setup-mfa`

Sets up MFA with the user's preferred authentication method.

#### Request Body
```json
{
  "mfaMethod": "authenticator" | "email",
  "newSetup": false // Optional: true to force new setup
}
```

#### Response
**Success (200)**
```json
{
  "success": true,
  "data": {
    "mfaMethod": "authenticator",
    "qrCodeUrl": "data:image/png;base64,...", // Only for authenticator
    "secret": "JBSWY3DPEHPK3PXP", // Only for authenticator
    "message": "MFA setup completed. Scan QR code with your authenticator app."
  }
}
```

**For Email Method:**
```json
{
  "success": true,
  "data": {
    "mfaMethod": "email",
    "message": "MFA setup completed. Check your email for verification code."
  }
}
```

### 2. Verify MFA
**POST** `/api/v1/mfa/verify-mfa`

Verifies the MFA token (authenticator app or email OTP).

#### Request Body
```json
{
  "token": "123456", // 6-digit code from authenticator app or email
  "mfaMethod": "authenticator" | "email"
}
```

#### Response
**Success (200)**
```json
{
  "success": true,
  "data": {
    "success": true,
    "message": "MFA verification successful"
  }
}
```

**Error (400)**
```json
{
  "success": false,
  "error": "Invalid verification code"
}
```

### 3. Request Email Fallback
**POST** `/api/v1/mfa/request-email-fallback`

Requests an email OTP when the user has lost access to their authenticator app.

#### Request Body
```json
{} // No body required
```

#### Response
**Success (200)**
```json
{
  "success": true,
  "data": {
    "message": "Fallback verification code sent to your email. After successful verification, you'll need to reset your MFA method."
  }
}
```

### 4. Reset MFA Method
**POST** `/api/v1/mfa/reset-mfa-method`

Resets the MFA method after successful fallback authentication, allowing the user to choose a new method.

#### Request Body
```json
{} // No body required
```

#### Response
**Success (200)**
```json
{
  "success": true,
  "data": {
    "message": "MFA method has been reset. You can now choose a new authentication method."
  }
}
```

### 5. Get MFA Status
**GET** `/api/v1/mfa/status`

Retrieves the current MFA status and configuration for the user.

#### Response
**Success (200)**
```json
{
  "success": true,
  "data": {
    "mfaEnabled": true,
    "mfaMethod": "authenticator",
    "hasAuthenticator": true,
    "hasEmail": false,
    "emailFallbackUsed": false
  }
}
```

### 6. Disable MFA
**DELETE** `/api/v1/mfa/disable`

Completely disables MFA for the user.

#### Response
**Success (200)**
```json
{
  "success": true,
  "data": {
    "message": "MFA has been disabled successfully"
  }
}
```

## User Flow Examples

### Flow 1: Setup Authenticator App
1. **Setup**: `POST /api/v1/mfa/setup-mfa` with `mfaMethod: "authenticator"`
2. **Response**: Returns QR code URL and secret
3. **User Action**: Scan QR code with authenticator app
4. **Verify**: `POST /api/v1/mfa/verify-mfa` with `mfaMethod: "authenticator"` and 6-digit code

### Flow 2: Setup Email OTP
1. **Setup**: `POST /api/v1/mfa/setup-mfa` with `mfaMethod: "email"`
2. **Response**: Returns success message
3. **User Action**: Check email for 6-digit code
4. **Verify**: `POST /api/v1/mfa/verify-mfa` with `mfaMethod: "email"` and 6-digit code

### Flow 3: Fallback to Email (Lost Authenticator)
1. **User**: Cannot access authenticator app
2. **Request Fallback**: `POST /api/v1/mfa/request-email-fallback`
3. **User Action**: Check email for 6-digit code
4. **Verify**: `POST /api/v1/mfa/verify-mfa` with `mfaMethod: "email"` and code
5. **Reset Method**: `POST /api/v1/mfa/reset-mfa-method` to choose new method

## Security Features

### Email OTP Security
- **Expiry**: 10 minutes
- **Attempts Limit**: Maximum 3 failed attempts
- **Auto-reset**: OTP is cleared after successful verification
- **Fallback Flag**: Tracks when email is used as fallback

### Authenticator App Security
- **TOTP Algorithm**: Industry-standard time-based one-time password
- **Clock Skew Tolerance**: 30-second window for time differences
- **Base32 Encoding**: Secure secret storage

### General Security
- **Protected Routes**: All endpoints require authentication
- **Tenant Isolation**: Multi-tenant database separation
- **Audit Logging**: Comprehensive logging for security monitoring
- **Method Isolation**: Users can only use one method at a time

## Error Handling

### Common Error Codes
- **400**: Invalid request (missing fields, invalid MFA method)
- **404**: User profile not found
- **500**: Internal server error

### Error Response Format
```json
{
  "success": false,
  "error": "Error message description",
  "apiId": "unique-api-identifier",
  "errorCode": "ERROR_CODE_IF_APPLICABLE"
}
```

## Frontend Implementation Guidelines

### 1. MFA Setup Flow
```javascript
// Step 1: Let user choose MFA method
const mfaMethod = userChoice; // "authenticator" or "email"

// Step 2: Setup MFA
const setupResponse = await api.post('/api/v1/mfa/setup-mfa', { mfaMethod });

// Step 3: Handle response based on method
if (mfaMethod === 'authenticator') {
  // Display QR code
  displayQRCode(setupResponse.data.qrCodeUrl);
} else if (mfaMethod === 'email') {
  // Show message about email OTP
  showMessage("Check your email for verification code");
}
```

### 2. MFA Verification Flow
```javascript
// Step 1: Get verification code from user
const token = userInput; // 6-digit code
const mfaMethod = userSelectedMethod; // "authenticator" or "email"

// Step 2: Verify MFA
const verifyResponse = await api.post('/api/v1/mfa/verify-mfa', { token, mfaMethod });

// Step 3: Handle success/failure
if (verifyResponse.success) {
  // MFA verified, proceed with login/action
  proceedWithAction();
} else {
  // Show error message
  showError(verifyResponse.error);
}
```

### 3. Fallback to Email (Lost Authenticator)
```javascript
// If user can't access authenticator app
const requestFallback = async () => {
  try {
    await api.post('/api/v1/mfa/request-email-fallback');
    showMessage("Fallback verification code sent to your email");
  } catch (error) {
    showError("Failed to send fallback code");
  }
};

// After successful fallback verification, reset MFA method
const resetMFAMethod = async () => {
  try {
    await api.post('/api/v1/mfa/reset-mfa-method');
    showMessage("MFA method reset. Choose a new method.");
    // Redirect to MFA setup
    redirectToMFASetup();
  } catch (error) {
    showError("Failed to reset MFA method");
  }
};
```

### 4. MFA Status Check
```javascript
// Check current MFA status
const checkMFAStatus = async () => {
  const statusResponse = await api.get('/api/v1/mfa/status');
  const { mfaEnabled, mfaMethod, hasAuthenticator, hasEmail, emailFallbackUsed } = statusResponse.data;
  
  // Update UI based on status
  updateMFAUI(mfaEnabled, mfaMethod, hasAuthenticator, hasEmail, emailFallbackUsed);
};
```

## Testing Scenarios

### 1. New User Setup
- Test authenticator method setup
- Test email method setup
- Verify QR code generation
- Verify email OTP delivery

### 2. Existing User
- Test method switching
- Test fallback scenarios
- Verify backward compatibility

### 3. Fallback Scenarios
- Lost authenticator app
- Email fallback request
- MFA method reset after fallback
- New method selection after reset

### 4. Error Scenarios
- Invalid MFA methods
- Expired OTP codes
- Too many failed attempts
- Network failures

### 5. Security Testing
- Verify tenant isolation
- Test authentication requirements
- Validate OTP expiry
- Check attempt limiting
- Verify fallback flag tracking

## Migration Notes

### Database Changes
- New fields added to `user_profiles` collection
- Existing MFA users automatically mapped to "authenticator" method
- Migration script handles data transformation
- Added `mfaEmailFallbackUsed` flag for fallback tracking

### Backward Compatibility
- Existing authenticator app users continue to work
- No breaking changes to current MFA functionality
- Gradual migration to new system

## Support and Troubleshooting

### Common Issues
1. **QR Code Not Displaying**: Check if `mfaMethod` is "authenticator"
2. **Email Not Received**: Verify email service configuration
3. **OTP Expired**: Use "request-email-fallback" endpoint for new code
4. **Too Many Attempts**: Wait for system reset or contact support
5. **Fallback Not Working**: Ensure user has MFA enabled

### Debug Information
- All API calls are logged with user context
- MFA setup and verification events are tracked
- Email delivery status is monitored
- Failed attempts are recorded for security analysis
- Fallback usage is tracked for security monitoring
