const axios = require("axios");

// Replace this array with the full JSON data I generated above
const visitsData = [
  {
    episode: {
      episodeNo: 12,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "701239",
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      dateOfBirth: "06/05/1947",
      gender: "Male",
      email: "<EMAIL>",
      address: {
        addressLine1: "1347 Oakridge Ln",
        addressLine2: "Apt 232",
        state: "TX",
        city: "Austin",
        zipcode: "78745",
        county: "Travis",
      },
      phone: "(*************",
      emergencyContact: "<PERSON>, <PERSON>",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "843920",
      visitDate: "08/04/2025",
      visitStartTime: "09:12 AM",
      visitType: "S<PERSON>",
    },
  },
  {
    episode: {
      episodeNo: 19,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "604293",
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      dateOfBirth: "01/12/1955",
      gender: "Female",
      email: "<EMAIL>",
      address: {
        addressLine1: "2381 Birchwood Ave",
        addressLine2: "Apt 556",
        state: "AZ",
        city: "Mesa",
        zipcode: "85204",
        county: "Maricopa",
      },
      phone: "(*************",
      emergencyContact: "Rogers, Charles",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "539182",
      visitDate: "08/05/2025",
      visitStartTime: "02:41 PM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 33,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "817364",
      firstName: "Joshua",
      lastName: "Gonzalez",
      dateOfBirth: "09/14/1946",
      gender: "Male",
      email: "<EMAIL>",
      address: {
        addressLine1: "5821 Meadow View Rd",
        addressLine2: "Apt 187",
        state: "FL",
        city: "Tampa",
        zipcode: "33604",
        county: "Hillsborough",
      },
      phone: "(*************",
      emergencyContact: "Price, John",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "482930",
      visitDate: "08/06/2025",
      visitStartTime: "11:05 AM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 42,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "937182",
      firstName: "Daniel",
      lastName: "Mitchell",
      dateOfBirth: "05/20/1953",
      gender: "Female",
      email: "<EMAIL>",
      address: {
        addressLine1: "7543 Riverbend Dr",
        addressLine2: "Apt 311",
        state: "OH",
        city: "Cleveland",
        zipcode: "44109",
        county: "Cuyahoga",
      },
      phone: "(*************",
      emergencyContact: "Bailey, Robert",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "128450",
      visitDate: "08/07/2025",
      visitStartTime: "04:18 PM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 55,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "753920",
      firstName: "Joseph",
      lastName: "Edwards",
      dateOfBirth: "03/22/1948",
      gender: "Male",
      email: "<EMAIL>",
      address: {
        addressLine1: "4210 Lakeview Blvd",
        addressLine2: "Apt 403",
        state: "WA",
        city: "Tacoma",
        zipcode: "98408",
        county: "Pierce",
      },
      phone: "(*************",
      emergencyContact: "Barnes, James",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "394817",
      visitDate: "08/08/2025",
      visitStartTime: "01:45 PM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 67,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "128347",
      firstName: "Andrew",
      lastName: "Walker",
      dateOfBirth: "07/19/1949",
      gender: "Female",
      email: "<EMAIL>",
      address: {
        addressLine1: "8702 Spruce Ct",
        addressLine2: "Apt 100",
        state: "CO",
        city: "Boulder",
        zipcode: "80301",
        county: "Boulder",
      },
      phone: "(*************",
      emergencyContact: "Wright, Patricia",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "902813",
      visitDate: "08/09/2025",
      visitStartTime: "10:32 AM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 74,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "452817",
      firstName: "Ryan",
      lastName: "King",
      dateOfBirth: "08/17/1954",
      gender: "Male",
      email: "<EMAIL>",
      address: {
        addressLine1: "3207 Cedar Springs Rd",
        addressLine2: "Apt 302",
        state: "GA",
        city: "Atlanta",
        zipcode: "30305",
        county: "Fulton",
      },
      phone: "(*************",
      emergencyContact: "Carter, Brian",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "761238",
      visitDate: "08/10/2025",
      visitStartTime: "03:27 PM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 85,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "912834",
      firstName: "Kevin",
      lastName: "Harris",
      dateOfBirth: "04/01/1957",
      gender: "Female",
      email: "<EMAIL>",
      address: {
        addressLine1: "4569 Maple Grove Dr",
        addressLine2: "Apt 214",
        state: "WI",
        city: "Madison",
        zipcode: "53704",
        county: "Dane",
      },
      phone: "(*************",
      emergencyContact: "Morgan, Jason",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "581039",
      visitDate: "08/11/2025",
      visitStartTime: "01:18 PM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 93,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "375291",
      firstName: "Eric",
      lastName: "Young",
      dateOfBirth: "12/29/1956",
      gender: "Male",
      email: "<EMAIL>",
      address: {
        addressLine1: "2398 Willow Bend Rd",
        addressLine2: "Apt 506",
        state: "NC",
        city: "Charlotte",
        zipcode: "28209",
        county: "Mecklenburg",
      },
      phone: "(*************",
      emergencyContact: "Simmons, Paul",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "930284",
      visitDate: "08/12/2025",
      visitStartTime: "09:44 AM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 18,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "642819",
      firstName: "Jason",
      lastName: "Allen",
      dateOfBirth: "02/11/1952",
      gender: "Female",
      email: "<EMAIL>",
      address: {
        addressLine1: "1785 Birchwood Ct",
        addressLine2: "Apt 228",
        state: "OR",
        city: "Portland",
        zipcode: "97217",
        county: "Multnomah",
      },
      phone: "(*************",
      emergencyContact: "Bennett, Thomas",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "281930",
      visitDate: "08/13/2025",
      visitStartTime: "11:57 AM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 21,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "481920",
      firstName: "Paul",
      lastName: "Wright",
      dateOfBirth: "09/09/1953",
      gender: "Male",
      email: "<EMAIL>",
      address: {
        addressLine1: "6120 Summit Dr",
        addressLine2: "Apt 345",
        state: "IL",
        city: "Chicago",
        zipcode: "60641",
        county: "Cook",
      },
      phone: "(*************",
      emergencyContact: "Butler, Kenneth",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "812309",
      visitDate: "08/14/2025",
      visitStartTime: "03:09 PM",
      visitType: "SOC",
    },
  },
  {
    episode: {
      episodeNo: 29,
      startDate: "02/13/2025",
      endDate: "04/13/2025",
    },
    client: {
      clientNo: "910284",
      firstName: "Mark",
      lastName: "Lopez",
      dateOfBirth: "05/17/1947",
      gender: "Female",
      email: "<EMAIL>",
      address: {
        addressLine1: "3058 Highland Ave",
        addressLine2: "Apt 520",
        state: "CA",
        city: "Los Angeles",
        zipcode: "90027",
        county: "Los Angeles",
      },
      phone: "(*************",
      emergencyContact: "Reed, Edward",
      emergencyContactPhone: "(*************",
    },
    clinician: {
      clinicianNo: 4474,
    },
    visit: {
      visitId: "591820",
      visitDate: "08/15/2025",
      visitStartTime: "08:36 AM",
      visitType: "SOC",
    },
  },
];
const config = {
  method: "post",
  maxBodyLength: Infinity,
  url: "https://api-dev.goscribble.ai/api/v1/visit",
  headers: {
    accept: "application/json",
    "x-tenant-id": "applereview",
    "Content-Type": "application/json",
    Authorization:
      "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ2IjoyLCJ0eXBlIjoiYWNjZXNzIiwiaWQiOiI2ODhlMzhjNWZjMDRiZGY5NDliMTdlZmEiLCJyb2xlcyI6WyJhZG1pbiJdLCJzY29wZXMiOlsiYWRtaW4uY3JlYXRlIiwiYWRtaW4ucmVhZCIsImFkbWluLnVwZGF0ZSIsImFkbWluLmRlbGV0ZSIsInVzZXIuY3JlYXRlIiwidXNlci5yZWFkIiwidXNlci51cGRhdGUiLCJ1c2VyLmRlbGV0ZSIsInJvbGUuY3JlYXRlIiwicm9sZS5yZWFkIiwicm9sZS51cGRhdGUiLCJyb2xlLmRlbGV0ZSIsImZvcm0uY3JlYXRlIiwiZm9ybS5yZWFkIiwiZm9ybS51cGRhdGUiLCJmb3JtLmRlbGV0ZSIsInZpc2l0LmNyZWF0ZSIsInZpc2l0LnJlYWQiLCJ2aXNpdC51cGRhdGUiLCJ2aXNpdC5kZWxldGUiLCJzZWxmLnJlYWQiLCJzZWxmLnVwZGF0ZSIsImdyaWQucmVhZCIsImdyaWQudXBkYXRlIl0sImlzc3VlciI6IlNjcmliYmxlIiwiaWF0IjoxNzU0MTUyNzMzLCJleHAiOjE3NTQxNTk5MzN9.UAeQE_CNvLyanfdaAi8OSIv4gobtatW5yPpglUKCipo",
  },
};

// Function to send all visits one by one
async function sendVisits() {
  for (const visit of visitsData) {
    try {
      const response = await axios.post(config.url, visit, {
        headers: config.headers,
      });
      console.log("Visit sent:", response.data);
    } catch (error) {
      console.error(
        "Error sending visit:",
        error.response ? error.response.data : error.message,
      );
    }
  }
}

sendVisits();
