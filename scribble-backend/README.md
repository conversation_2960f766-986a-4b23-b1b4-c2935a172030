# Scribble Backend

This is the backend codebase for the Scribble application. It provides RESTful APIs, authentication, user management, and other core features for the Scribble platform.

## Features
- Express.js REST API
- MongoDB integration (via Mongoose)
- Authentication and authorization
- User, clinician, and visit management
- Serverless deployment support (AWS Lambda)
- Data migration with migrate-mongo
- Linting and formatting with ESLint and Prettier
- Redis caching (via ioredis)

## Prerequisites
- [Node.js](https://nodejs.org/) (v14 or higher recommended)
- [npm](https://www.npmjs.com/) (comes with Node.js)
- [MongoDB](https://www.mongodb.com/) instance (local or remote)
- [Redis](https://redis.io/) instance (local or remote)

## Installation

1. **Clone the repository:**
   ```zsh
   git clone https://github.com/your-org/scribble-backend.git
   cd scribble-backend
   ```

2. **Install dependencies:**
   ```zsh
   npm install
   ```

3. **Configure environment variables:**
   - Copy `.env.example` to `.env` and fill in the required values:
     ```zsh
     cp .env.example .env
     # Edit .env with your editor
     ```

## Running the Application

### Local Development

Start the server with hot-reloading:
```zsh
npm start
```

The server will run on the port specified in your `.env` file (default: 3000).

### Running in Serverless Mode (Offline)

To emulate AWS Lambda locally:
```zsh
npx serverless offline
```

### Running Migrations

To run database migrations:
```zsh
npx migrate-mongo up
```

## Testing

Run tests with:
```zsh
npm test
```

## Linting and Formatting

Lint code:
```zsh
npm run lint
```

Format code:
```zsh
npm run prettier
```

## Project Structure

- `src/` - Main source code (controllers, models, routes, middlewares, etc.)
- `migrations/` - Database migration scripts
- `templates/` - Form templates
- `testDataCreator.js` - Script for populating test data

## Installing Redis Locally

### macOS (Homebrew)
If you don't already have Redis installed, you can install and start it locally with Homebrew:

```zsh
brew install redis
brew services start redis
```

### Linux (apt)
For Ubuntu/Debian-based systems:

```sh
sudo apt update
sudo apt install redis-server
sudo systemctl enable redis-server.service
sudo systemctl start redis-server.service
```

### Windows
- Download and install Redis from the official Microsoft archive: https://github.com/microsoftarchive/redis/releases
- Start Redis using the installed executable.

By default, Redis will listen on port 6379.

For other platforms or installation methods, see the [Redis documentation](https://redis.io/docs/getting-started/).

## Installing MongoDB Locally

### macOS (Homebrew)
If you don't already have MongoDB installed, you can install and start it locally with Homebrew:

```zsh
brew tap mongodb/brew
brew install mongodb-community@6.0
brew services start mongodb-community@6.0
```

### Linux (apt)
For Ubuntu/Debian-based systems:

```sh
sudo apt update
sudo apt install -y gnupg curl
curl -fsSL https://www.mongodb.org/static/pgp/server-8.0.asc |
  sudo gpg --dearmor -o /usr/share/keyrings/mongodb-server-8.0.gpg

echo "deb [arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-8.0.gpg] https://repo.mongodb.org/apt/ubuntu noble/mongodb-org/8.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-8.0.list

sudo apt update
sudo apt install -y mongodb-org

sudo systemctl start mongod
sudo systemctl enable mongod
sudo systemctl status mongod


```

### Windows
- Download the MongoDB installer from https://www.mongodb.com/try/download/community
- Follow the installation wizard and start the MongoDB service from the Services app or with `net start MongoDB` in Command Prompt.

By default, MongoDB will listen on port 27017. You can connect to it using the connection string:

```
mongodb://localhost:27017
```

For other platforms or installation methods, see the [MongoDB installation documentation](https://www.mongodb.com/docs/manual/installation/).

## Restoring a MongoDB Backup

If you have a `mongodump.zip` backup file and want to restore it into your local MongoDB instance:

1. Unzip the backup:
   ```zsh
   unzip mongodump.zip
   ```
2. Restore the dump into your local MongoDB (default port 27017):
   ```zsh
   mongorestore --host localhost --port 27017 --drop ./mongodump
   ```
   - The `--drop` flag will drop each collection before restoring it.
   - Adjust the path if your dump directory is elsewhere.

For more options, see the [mongorestore documentation](https://www.mongodb.com/docs/database-tools/mongorestore/).


