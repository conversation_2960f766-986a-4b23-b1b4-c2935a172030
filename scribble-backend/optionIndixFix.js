const { MongoClient } = require("mongodb");

// Query to check the duplicated option value

// form and form_template
// [
//   { $unwind: "$assessmentForm" },
//   { $unwind: "$assessmentForm.items" },
//   {
//     $match: {
//       "assessmentForm.items.items": { $exists: true } // Only questions with options
//     }
//   },
//   { $unwind: "$assessmentForm.items.items" },
//   {
//     $group: {
//       _id: {
//         formId: "$_id",
//         questionId: "$assessmentForm.items.id",
//         optionValue: "$assessmentForm.items.items.value"
//       },
//       count: { $sum: 1 },
//       optionIds: { $push: "$assessmentForm.items.items.id" },
//       labels: { $push: "$assessmentForm.items.items.label" }
//     }
//   },
//   { $match: { count: { $gt: 1 } } }
// ]

// Assessment Form

// [
//   { $unwind: "$question" },
//   { $unwind: "$question.items" },
//   {
//     $match: {
//       "question.items.items": { $exists: true } // Only questions with options
//     }
//   },
//   { $unwind: "$question.items.items" },
//   {
//     $group: {
//       _id: {
//         formId: "$_id",
//         questionId: "$question.items.id",
//         optionValue: "$question.items.items.value"
//       },
//       count: { $sum: 1 },
//       optionIds: { $push: "$question.items.items.id" },
//       labels: { $push: "$question.items.items.label" }
//     }
//   },
//   { $match: { count: { $gt: 1 } } }
// ]

async function reindexAllFormsOptions() {
  const uri = "mongodb://"; // Replace with your Mongo URI
  const dbName = "dev1"; // Replace with your DB name
  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db(dbName);

    // Collections to update
    const collections = ["forms", "form_templates", "assessments"];

    for (const collectionName of collections) {
      const collection = db.collection(collectionName);

      // Query for assessmentForm structure
      const assessmentFormCursor = collection.find({
        "assessmentForm.items.items": { $exists: true },
      });

      while (await assessmentFormCursor.hasNext()) {
        const form = await assessmentFormCursor.next();
        let changed = false;

        form.assessmentForm.forEach((section) => {
          if (!Array.isArray(section.items)) return;

          section.items.forEach((question) => {
            if (!Array.isArray(question.items)) return;

            question.items.forEach((opt, idx) => {
              const newValue = String(idx);
              if (opt.value !== newValue) {
                opt.value = newValue;
                changed = true;
              }
            });
          });
        });

        if (changed) {
          await collection.updateOne(
            { _id: form._id },
            { $set: { assessmentForm: form.assessmentForm } },
          );
          console.log(
            `Updated assessmentForm in ${collectionName}: ${form.formName || form._id} (${form._id})`,
          );
        }
      }

      // Query for assessmentQuestion structure
      const assessmentQuestionCursor = collection.find({
        "assessmentQuestion.items.items": { $exists: true },
      });

      while (await assessmentQuestionCursor.hasNext()) {
        const form = await assessmentQuestionCursor.next();
        let changed = false;

        form.assessmentQuestion.forEach((section) => {
          if (!Array.isArray(section.items)) return;

          section.items.forEach((question) => {
            if (!Array.isArray(question.items)) return;

            question.items.forEach((opt, idx) => {
              const newValue = String(idx);
              if (opt.value !== newValue) {
                opt.value = newValue;
                changed = true;
              }
            });
          });
        });

        if (changed) {
          await collection.updateOne(
            { _id: form._id },
            { $set: { assessmentQuestion: form.assessmentQuestion } },
          );
          console.log(
            `Updated assessmentQuestion in ${collectionName}: ${form.formName || form._id} (${form._id})`,
          );
        }
      }
    }

    console.log("All forms and templates processed successfully.");
  } catch (err) {
    console.error("Error:", err);
  } finally {
    await client.close();
  }
}

reindexAllFormsOptions();
