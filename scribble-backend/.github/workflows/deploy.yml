name: Deploy Scribble API to EC2

on:
  push:
    branches:
      - develop
      - main
    tags:
      - 'stg-v*.*.*'

jobs:
  deploy-dev:
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install Dependencies
        run: npm install

      - name: Run Code Standards Check
        run: |
          npm run lint
          # npm run prettier

      - name: Setup SSH Key
        run: |
          echo "${{ secrets.EC2_SSH_KEY }}" > deploy_key
          chmod 600 deploy_key

      - name: Deploy to EC2 via SSH
        run: |
          ssh -T -o StrictHostKeyChecking=no -i deploy_key ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << 'EOF'
            cd ${{ secrets.EC2_PATH }}
            git pull origin develop
            timestamp=$(date +%Y%m%d%H%M%S)
            docker logs scribble-api > /var/log/scribble-api/docker_$timestamp.log 
            docker-compose down
            docker-compose up -d --build
            docker ps  # Check if containers are running

          EOF

      - name: Trigger scribble-testing workflow.
        run: |
          curl -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.TEST_REPO_TOKEN }}" \
            https://api.github.com/repos/Acutedge-Inc/scribble-testing/dispatches \
            -d '{"event_type":"run-build","client_payload":{"source_repo":"${{ github.repository }}","ref":"${{ github.ref }}"}}'          


  deploy-prod:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install Dependencies
        run: npm install

      - name: Run Code Standards Check
        run: |
          npm run lint
          npm run prettier

      - name: Setup SSH Key
        run: |
          echo "${{ secrets.PROD_EC2_SSH_KEY }}" > deploy_key
          chmod 600 deploy_key

      - name: Deploy to EC2 via SSH
        run: |
          ssh -o StrictHostKeyChecking=no -i deploy_key ubuntu@************* << 'EOF'
            cd /home/<USER>/scribble-backend
            git pull origin main
            timestamp=$(date +%Y%m%d%H%M%S)
            docker logs scribble-api > /var/log/scribble-api/docker_$timestamp.log 
            docker-compose down
            sudo docker-compose up -d --build
            docker ps  # Check if containers are running
          EOF

  deploy-staging:
    if: startsWith(github.ref, 'refs/tags/stg-v')
    runs-on: ubuntu-latest
    environment: Staging
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22.17"

      - name: Install Dependencies
        run: npm i

      - name: Run Code Standards Check
        run: |
          npm run lint
          # npm run prettier

      - name: Setup SSH Key
        run: |
          echo "${{ secrets.EC2_SSH_KEY }}" > deploy_key
          chmod 600 deploy_key

      - name: Deploy to EC2 via SSH
        run: |
          ssh -T -o StrictHostKeyChecking=no -i deploy_key ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << 'EOF'
            cd ${{ secrets.EC2_PATH }}
            git fetch --all --tags
            git checkout ${{ github.ref }}
            timestamp=$(date +%Y%m%d%H%M%S)
            docker logs scribble-api > /var/log/scribble-api/docker_$timestamp.log 
            docker-compose down
            docker-compose up -d --build
            docker ps  # Check if containers are running
          EOF
