const axios = require("axios");
let data = require("./formatted_visits.json");

(() => {
  let promise = Promise.resolve();

  data.forEach((item) => {
    promise = promise.then(() => {
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `https://api-stg.goscribble.ai/api/v1/visit`,
        headers: {
          accept: "application/json",
          "x-tenant-id": "stg",
          Authorization:
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ2IjoyLCJ0eXBlIjoiYWNjZXNzIiwiaWQiOiI2ODM1ZTUyOGVkN2ZhNzI0Y2I3NDMxYzMiLCJyb2xlcyI6WyJhZG1pbiJdLCJwZXJtaXNzaW9uIjpbImFkbWluLmNyZWF0ZSIsImFkbWluLnJlYWQiLCJhZG1pbi51cGRhdGUiLCJhZG1pbi5kZWxldGUiLCJ1c2VyLmNyZWF0ZSIsInVzZXIucmVhZCIsInVzZXIudXBkYXRlIiwidXNlci5kZWxldGUiLCJyb2xlLmNyZWF0ZSIsInJvbGUucmVhZCIsInJvbGUudXBkYXRlIiwicm9sZS5kZWxldGUiLCJmb3JtLmNyZWF0ZSIsImZvcm0ucmVhZCIsImZvcm0udXBkYXRlIiwiZm9ybS5kZWxldGUiLCJ2aXNpdC5jcmVhdGUiLCJ2aXNpdC5yZWFkIiwidmlzaXQudXBkYXRlIiwidmlzaXQuZGVsZXRlIiwic2VsZi5yZWFkIiwic2VsZi51cGRhdGUiLCJncmlkLnJlYWQiLCJncmlkLnVwZGF0ZSJdLCJpc3N1ZXIiOiJTY3JpYmJsZSIsImlhdCI6MTc1NTExOTc2NSwiZXhwIjoxNzU1MTI2OTY1fQ.6P--ij5ZfE0AAt48yv7HLb_BLxLwgADlusgzM7MN-jQ",
          "Content-Type": "application/json",
        },
        data: JSON.stringify(item),
      };

      return axios
        .request(config)
        .then((response) => {
          console.log(JSON.stringify(response.data.data));
        })
        .catch((error) => {
          console.log(error);
        });
    });
  });
})();
