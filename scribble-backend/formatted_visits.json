[{"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 676, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "08/19/1957", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "1957 Mcdonald Shore", "addressLine2": "", "state": "MT", "city": "Laceytown", "zipcode": "66740", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456854, "visitDate": "09/27/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 678, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "03/18/1973", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "49700 Johnson Island", "addressLine2": "Apt 406", "state": "MT", "city": "Wilkinson<PERSON>", "zipcode": "56138", "county": "Solis"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456855, "visitDate": "09/28/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 680, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/26/1987", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "8200 Richard Viaduct", "addressLine2": "", "state": "NJ", "city": "North Heather", "zipcode": "27676", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456856, "visitDate": "09/29/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 682, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/06/1973", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "271 <PERSON>", "addressLine2": "", "state": "OK", "city": "North Donnaborough", "zipcode": "83436", "county": "<PERSON><PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456857, "visitDate": "09/30/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 684, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "05/22/1987", "gender": "Female", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@ellis.com", "address": {"addressLine1": "11664 <PERSON>", "addressLine2": "", "state": "OR", "city": "Bernardhaven", "zipcode": "29777", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456858, "visitDate": "09/31/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 686, "firstName": "<PERSON>", "lastName": "Moss", "dateOfBirth": "12/04/1984", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "56284 <PERSON>", "addressLine2": "", "state": "MO", "city": "<PERSON><PERSON>", "zipcode": "67039", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456859, "visitDate": "09/01/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 688, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "11/07/1996", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "7067 <PERSON>", "addressLine2": "#333", "state": "MO", "city": "Leslieport", "zipcode": "26180", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456860, "visitDate": "09/02/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 690, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "12/16/1990", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "6373 <PERSON>", "addressLine2": "", "state": "CO", "city": "<PERSON>", "zipcode": "10247", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456861, "visitDate": "09/03/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 692, "firstName": "Cheyenne", "lastName": "<PERSON>", "dateOfBirth": "08/19/1957", "gender": "Female", "email": "j<PERSON><PERSON><PERSON>@hotmail.com", "address": {"addressLine1": "788 <PERSON>", "addressLine2": "", "state": "MN", "city": "Port Kristin", "zipcode": "56439", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456862, "visitDate": "09/04/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 694, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/04/1977", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "707 Adkins Divide", "addressLine2": "", "state": "WV", "city": "<PERSON><PERSON>", "zipcode": "82730", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456863, "visitDate": "09/05/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 696, "firstName": "<PERSON>", "lastName": "Middleton", "dateOfBirth": "05/15/1949", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "1381 Craig Extension", "addressLine2": "", "state": "IA", "city": "<PERSON><PERSON>", "zipcode": "63222", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456864, "visitDate": "09/27/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 698, "firstName": "<PERSON>", "lastName": "French", "dateOfBirth": "10/20/1975", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "9656 <PERSON>", "addressLine2": "", "state": "NM", "city": "<PERSON><PERSON>", "zipcode": "54148", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456865, "visitDate": "09/28/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 700, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "03/22/1972", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "384 <PERSON>", "addressLine2": "", "state": "MS", "city": "Gravesfurt", "zipcode": "40311", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456866, "visitDate": "09/29/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 702, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/25/1988", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "671 Billy <PERSON>", "addressLine2": "", "state": "AZ", "city": "East Amy", "zipcode": "07868", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456867, "visitDate": "09/30/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 704, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/08/1970", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "9755 Smith Course", "addressLine2": "", "state": "WI", "city": "Wilsonchester", "zipcode": "53738", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456868, "visitDate": "09/31/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 706, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "09/04/1954", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "767 Haley Valley", "addressLine2": "", "state": "MI", "city": "New Robert", "zipcode": "36572", "county": "Bell"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456869, "visitDate": "09/01/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 708, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "09/11/1971", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "83901 Castillo Parks", "addressLine2": "", "state": "AZ", "city": "Port Bryan", "zipcode": "74254", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456870, "visitDate": "09/02/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 710, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "07/03/1946", "gender": "Male", "email": "joseta<PERSON><EMAIL>", "address": {"addressLine1": "67728 <PERSON>", "addressLine2": "Apt. #1", "state": "UT", "city": "New Annaton", "zipcode": "01915", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456871, "visitDate": "09/03/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 712, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/11/1986", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "64059 Fowler Hills", "addressLine2": "Apt. #2", "state": "NC", "city": "Port Karen", "zipcode": "12018", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456872, "visitDate": "09/04/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 714, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/04/1994", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "666 Laura Station", "addressLine2": "Apt. #3", "state": "NM", "city": "East Monica", "zipcode": "64014", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456873, "visitDate": "09/05/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 716, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "12/11/1950", "gender": "Male", "email": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@heath-stevens.com", "address": {"addressLine1": "722 <PERSON><PERSON><PERSON>", "addressLine2": "Apt. #4", "state": "NE", "city": "West Jenniferfort", "zipcode": "01526", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456874, "visitDate": "09/27/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 718, "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "dateOfBirth": "05/06/1969", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "762 <PERSON> Overpass", "addressLine2": "Apt. #5", "state": "MA", "city": "North Garyville", "zipcode": "69564", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456875, "visitDate": "09/28/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 720, "firstName": "<PERSON>", "lastName": "Sosa", "dateOfBirth": "05/20/1952", "gender": "Female", "email": "jose<PERSON><PERSON><PERSON><PERSON>@hotmail.com", "address": {"addressLine1": "131 Thompson Mountain", "addressLine2": "Apt. #6", "state": "DC", "city": "Waltonview", "zipcode": "43612", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456876, "visitDate": "09/29/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 722, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "01/09/1977", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "781 Haas Lodge", "addressLine2": "", "state": "NM", "city": "<PERSON><PERSON>", "zipcode": "53857", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456877, "visitDate": "09/30/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 724, "firstName": "Regina", "lastName": "Middleton", "dateOfBirth": "06/16/1958", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "696 Julia <PERSON>", "addressLine2": "", "state": "WI", "city": "Fieldsshire", "zipcode": "22759", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456878, "visitDate": "09/31/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 726, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/04/1973", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "4269 <PERSON>", "addressLine2": "", "state": "GA", "city": "South Michelle", "zipcode": "01549", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456879, "visitDate": "09/01/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 728, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "06/01/1995", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "659 Becker Islands", "addressLine2": "", "state": "WI", "city": "Jamesfurt", "zipcode": "49437", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456880, "visitDate": "09/02/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 730, "firstName": "<PERSON>", "lastName": "Valencia", "dateOfBirth": "10/23/1976", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "051 <PERSON>", "addressLine2": "", "state": "IN", "city": "Lake Josephstad", "zipcode": "83228", "county": "Roach"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456881, "visitDate": "09/03/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 732, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "06/24/1952", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "787 Johnson Plains", "addressLine2": "", "state": "WY", "city": "North Keith", "zipcode": "08771", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456882, "visitDate": "09/04/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 734, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "09/20/1984", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "50087 Hensley Road", "addressLine2": "", "state": "MI", "city": "West Brandonburgh", "zipcode": "47942", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456883, "visitDate": "09/05/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 736, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "08/17/1987", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "9904 <PERSON>", "addressLine2": "", "state": "CO", "city": "West Nicole", "zipcode": "43784", "county": "<PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456884, "visitDate": "09/27/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 738, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "03/20/1973", "gender": "Male", "email": "smithje<PERSON><PERSON>@gmail.com", "address": {"addressLine1": "1786 Victoria Bypass", "addressLine2": "", "state": "WY", "city": "Bushhaven", "zipcode": "25093", "county": "Roman"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456885, "visitDate": "09/28/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 740, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/01/1984", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "67272 <PERSON>", "addressLine2": "", "state": "CO", "city": "Hilltown", "zipcode": "86421", "county": "Bass"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456886, "visitDate": "09/29/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 742, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "09/20/1968", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "890 Powell Trace", "addressLine2": "", "state": "WI", "city": "<PERSON><PERSON>", "zipcode": "06544", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456887, "visitDate": "09/30/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 744, "firstName": "<PERSON>", "lastName": "Potts", "dateOfBirth": "03/24/1974", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "42712 <PERSON>", "addressLine2": "", "state": "WA", "city": "<PERSON>", "zipcode": "16383", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456888, "visitDate": "09/31/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 746, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/21/1990", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "4249 Vicki Course", "addressLine2": "", "state": "UT", "city": "East Paulhaven", "zipcode": "06544", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456889, "visitDate": "09/01/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 748, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/10/1949", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "584 Turner Run", "addressLine2": "", "state": "SC", "city": "<PERSON><PERSON>", "zipcode": "63749", "county": "<PERSON><PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456890, "visitDate": "09/02/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 750, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/08/1976", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "2579 Bush Lakes", "addressLine2": "", "state": "OH", "city": "Garciaville", "zipcode": "13358", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456891, "visitDate": "09/03/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 752, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "dateOfBirth": "12/08/1981", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "94193 James Island", "addressLine2": "", "state": "RI", "city": "Port Douglas", "zipcode": "88699", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456892, "visitDate": "09/04/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 754, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/17/1947", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "250 Jamie Streets", "addressLine2": "", "state": "IA", "city": "West Johnmouth", "zipcode": "50716", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456893, "visitDate": "09/05/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 756, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/06/1968", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "96289 White Park", "addressLine2": "", "state": "WV", "city": "South Adam", "zipcode": "97191", "county": "Bass"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456894, "visitDate": "09/27/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 758, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/11/1979", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "27121 Beck Street", "addressLine2": "", "state": "SD", "city": "West Sherry", "zipcode": "01369", "county": "<PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456895, "visitDate": "09/28/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 760, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "08/07/1981", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "6649 Birch Rd", "addressLine2": "", "state": "WY", "city": "New Madisonmouth", "zipcode": "12091", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456896, "visitDate": "09/29/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 762, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "12/21/1988", "gender": "Male", "email": "tiffany<PERSON><PERSON><PERSON>@chapman.biz", "address": {"addressLine1": "9421 Oak Ln", "addressLine2": "", "state": "MT", "city": "East Dakotaview", "zipcode": "09758", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456897, "visitDate": "09/30/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 764, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/24/1987", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "2345 Maple Dr", "addressLine2": "", "state": "UT", "city": "<PERSON><PERSON>", "zipcode": "45669", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456898, "visitDate": "09/31/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 766, "firstName": "<PERSON>", "lastName": "Ju<PERSON>z", "dateOfBirth": "09/09/1967", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "3640 Maple St", "addressLine2": "", "state": "MT", "city": "Port Jenniferfort", "zipcode": "98386", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456899, "visitDate": "09/01/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 768, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "03/03/1962", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "7071 Ash Blvd", "addressLine2": "", "state": "TX", "city": "Blevinsfort", "zipcode": "26085", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456900, "visitDate": "09/02/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 770, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "05/14/1962", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "9486 Spruce Way", "addressLine2": "", "state": "MI", "city": "<PERSON> Johnny", "zipcode": "64382", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456901, "visitDate": "09/03/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 772, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "11/03/1964", "gender": "Male", "email": "lisamu<PERSON><EMAIL>", "address": {"addressLine1": "9897 Cherry St", "addressLine2": "", "state": "OR", "city": "Josephmouth", "zipcode": "72435", "county": "Li"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456902, "visitDate": "09/04/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 774, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "03/03/1994", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "3989 Birch <PERSON>", "addressLine2": "", "state": "IA", "city": "East Courtney", "zipcode": "99262", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456903, "visitDate": "09/05/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 776, "firstName": "Cheyenne", "lastName": "<PERSON>", "dateOfBirth": "02/16/1990", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "7924 Cedar Blvd", "addressLine2": "", "state": "WY", "city": "South Richard", "zipcode": "98984", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456904, "visitDate": "09/27/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 778, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "11/02/1956", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "4203 Cedar Ln", "addressLine2": "", "state": "AR", "city": "New Christopher", "zipcode": "11255", "county": "<PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456905, "visitDate": "09/28/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 780, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/07/1949", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "8090 Pine Blvd", "addressLine2": "", "state": "VA", "city": "<PERSON>", "zipcode": "86986", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456906, "visitDate": "09/29/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 782, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "09/05/1949", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "5896 Maple Way", "addressLine2": "", "state": "LA", "city": "Kevin<PERSON>", "zipcode": "50295", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456907, "visitDate": "09/30/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 784, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "06/19/1958", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "3531 Ash Rd", "addressLine2": "", "state": "VT", "city": "Port Brittneymouth", "zipcode": "12980", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456908, "visitDate": "09/31/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 786, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "05/27/1958", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "8578 Spruce Pl", "addressLine2": "", "state": "MT", "city": "East Tanya", "zipcode": "29930", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2002}, "visit": {"visitId": 456909, "visitDate": "09/01/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 788, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "05/21/1966", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "2810 Cherry Blvd", "addressLine2": "", "state": "KY", "city": "<PERSON><PERSON>", "zipcode": "15894", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456910, "visitDate": "09/02/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 790, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "06/17/1966", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "5810 Oak Ln", "addressLine2": "", "state": "KY", "city": "North Rachaelberg", "zipcode": "41399", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456911, "visitDate": "09/03/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 792, "firstName": "<PERSON>", "lastName": "Winters", "dateOfBirth": "02/05/1943", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "1865 Spruce Ct", "addressLine2": "", "state": "HI", "city": "<PERSON><PERSON>", "zipcode": "80418", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456912, "visitDate": "09/04/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 794, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "09/09/1995", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "9614 Ash Rd", "addressLine2": "", "state": "VT", "city": "<PERSON><PERSON>", "zipcode": "92595", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456913, "visitDate": "09/05/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 796, "firstName": "<PERSON>", "lastName": "Austin", "dateOfBirth": "12/26/1994", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "1193 Pine Terrace", "addressLine2": "", "state": "NV", "city": "Jonesside", "zipcode": "28328", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456914, "visitDate": "09/27/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 798, "firstName": "<PERSON>", "lastName": "Fox", "dateOfBirth": "08/18/1973", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "9055 Maple Blvd", "addressLine2": "", "state": "ND", "city": "<PERSON><PERSON>", "zipcode": "26215", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456915, "visitDate": "09/28/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 800, "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/11/1960", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "530 Oak Pl", "addressLine2": "", "state": "HI", "city": "<PERSON><PERSON>", "zipcode": "91689", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456916, "visitDate": "09/29/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 802, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/20/1941", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "4090 Cedar Terrace", "addressLine2": "", "state": "MN", "city": "Kaylamouth", "zipcode": "92200", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456917, "visitDate": "09/30/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 804, "firstName": "<PERSON>", "lastName": "Hall", "dateOfBirth": "04/23/1965", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "6110 Spruce Ct", "addressLine2": "", "state": "ME", "city": "<PERSON><PERSON>", "zipcode": "72650", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456918, "visitDate": "09/31/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 806, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "05/01/1959", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "1778 Birch Blvd", "addressLine2": "", "state": "SD", "city": "<PERSON><PERSON>", "zipcode": "71625", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456919, "visitDate": "09/01/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 808, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/10/1989", "gender": "Male", "email": "david<PERSON><PERSON><PERSON>@porter.com", "address": {"addressLine1": "9712 Birch Ct", "addressLine2": "", "state": "WY", "city": "Lake Kiaratown", "zipcode": "83896", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456920, "visitDate": "09/02/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 810, "firstName": "<PERSON>", "lastName": "White", "dateOfBirth": "03/03/1950", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "3384 Willow Rd", "addressLine2": "", "state": "NH", "city": "Lake Peter", "zipcode": "75444", "county": "White"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456921, "visitDate": "09/03/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 812, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "05/18/1982", "gender": "Male", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@gonzalez.biz", "address": {"addressLine1": "9803 Elm Blvd", "addressLine2": "", "state": "HI", "city": "Goodwinview", "zipcode": "48119", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456922, "visitDate": "09/04/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 814, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "11/13/1957", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "2456 Willow Terrace", "addressLine2": "", "state": "NC", "city": "<PERSON><PERSON>", "zipcode": "11445", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456923, "visitDate": "09/05/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 816, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "07/16/1993", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "3273 Ash Terrace", "addressLine2": "", "state": "OK", "city": "Port Kimberlyshire", "zipcode": "16900", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456924, "visitDate": "09/27/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 818, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/04/1995", "gender": "Male", "email": "mckinney<PERSON><PERSON>@gmail.com", "address": {"addressLine1": "2074 Birch Dr", "addressLine2": "", "state": "NH", "city": "<PERSON><PERSON>", "zipcode": "87383", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456925, "visitDate": "09/28/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 820, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "12/17/1981", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "772 Oak Terrace", "addressLine2": "", "state": "MI", "city": "Andrewport", "zipcode": "15735", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456926, "visitDate": "09/29/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 822, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "dateOfBirth": "06/28/1972", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "5648 Spruce Ct", "addressLine2": "", "state": "IL", "city": "Myersshire", "zipcode": "02075", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456927, "visitDate": "09/30/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 824, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/28/1976", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "5137 Ash St", "addressLine2": "", "state": "WA", "city": "Michael<PERSON>", "zipcode": "23455", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456928, "visitDate": "09/31/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 826, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/17/1951", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "9874 Elm Ave", "addressLine2": "", "state": "AK", "city": "Martinport", "zipcode": "80341", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456929, "visitDate": "09/01/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 828, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/27/1986", "gender": "Male", "email": "thomas<PERSON><PERSON><PERSON>@good.com", "address": {"addressLine1": "2304 Willow St", "addressLine2": "", "state": "NV", "city": "<PERSON><PERSON>", "zipcode": "13687", "county": "<PERSON><PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456930, "visitDate": "09/02/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 830, "firstName": "<PERSON>", "lastName": "Green", "dateOfBirth": "01/26/1984", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "1798 Pine Terrace", "addressLine2": "", "state": "RI", "city": "East Julia", "zipcode": "81379", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456931, "visitDate": "09/03/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 832, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "08/30/1939", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "1468 Cedar Dr", "addressLine2": "", "state": "FL", "city": "East Kimberlymouth", "zipcode": "80893", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456932, "visitDate": "09/04/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 834, "firstName": "<PERSON>", "lastName": "Garza", "dateOfBirth": "12/27/1972", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "7136 Elm Ave", "addressLine2": "", "state": "AL", "city": "South Robertfurt", "zipcode": "52491", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456933, "visitDate": "09/05/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 836, "firstName": "<PERSON>", "lastName": "Herring", "dateOfBirth": "01/01/1996", "gender": "Female", "email": "<PERSON><PERSON><PERSON>@hotmail.com", "address": {"addressLine1": "4620 Pine Ln", "addressLine2": "", "state": "CO", "city": "West Maryhaven", "zipcode": "47477", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456934, "visitDate": "09/27/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 838, "firstName": "Dawn", "lastName": "<PERSON>", "dateOfBirth": "11/08/1964", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "531 Elm Rd", "addressLine2": "", "state": "HI", "city": "East Steven", "zipcode": "81514", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456935, "visitDate": "09/28/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 840, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/07/1969", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "519 Spruce Dr", "addressLine2": "", "state": "PA", "city": "Patrickhaven", "zipcode": "05100", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456936, "visitDate": "09/29/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 842, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "02/11/1954", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "3310 Pine St", "addressLine2": "", "state": "MO", "city": "Daltonport", "zipcode": "28756", "county": "Medina"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456937, "visitDate": "09/30/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 844, "firstName": "<PERSON>", "lastName": "Santiago", "dateOfBirth": "10/01/1972", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "1309 Willow Rd", "addressLine2": "", "state": "KY", "city": "South Georgeberg", "zipcode": "57416", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456938, "visitDate": "09/31/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 846, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/19/1985", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "153 Willow Blvd", "addressLine2": "", "state": "IA", "city": "South Davidshire", "zipcode": "63925", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456939, "visitDate": "09/01/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 848, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/29/1988", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "6687 Cherry St", "addressLine2": "", "state": "VT", "city": "North Andrea", "zipcode": "61509", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456940, "visitDate": "09/02/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 850, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "09/13/1940", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "8806 Oak Rd", "addressLine2": "", "state": "SD", "city": "Kellermouth", "zipcode": "30452", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456941, "visitDate": "09/03/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 852, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "12/20/1939", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "2190 Maple Terrace", "addressLine2": "", "state": "IL", "city": "<PERSON> Roy", "zipcode": "77876", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456942, "visitDate": "09/04/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 854, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/13/1959", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "569 Pine St", "addressLine2": "", "state": "SD", "city": "<PERSON><PERSON>", "zipcode": "23193", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456943, "visitDate": "09/05/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 856, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "03/25/1996", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "260 Maple Ln", "addressLine2": "", "state": "KY", "city": "New Shawnmouth", "zipcode": "69558", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456944, "visitDate": "09/27/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 858, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "06/26/1960", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "4141 Maple Ln", "addressLine2": "", "state": "AK", "city": "William<PERSON>", "zipcode": "23983", "county": "<PERSON><PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456945, "visitDate": "09/28/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 860, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/03/1968", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "4743 Oak Ave", "addressLine2": "", "state": "FL", "city": "Lake Jacobfurt", "zipcode": "91064", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456946, "visitDate": "09/29/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 862, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON> Jr.", "dateOfBirth": "04/08/1974", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "5328 Spruce Rd", "addressLine2": "", "state": "AL", "city": "<PERSON><PERSON>", "zipcode": "94765", "county": "Parks"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456947, "visitDate": "09/30/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 864, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "12/10/1943", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "9857 Cherry Ave", "addressLine2": "", "state": "ND", "city": "Hayeshaven", "zipcode": "54005", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456948, "visitDate": "09/31/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 866, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "08/09/1963", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "3368 Ash Dr", "addressLine2": "", "state": "CT", "city": "Sullivanbury", "zipcode": "06968", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456949, "visitDate": "09/01/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 868, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/26/1976", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "5950 Ash Terrace", "addressLine2": "", "state": "AK", "city": "New Christophertown", "zipcode": "38944", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456950, "visitDate": "09/02/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 870, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "03/14/1958", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "7010 Maple Ave", "addressLine2": "", "state": "MA", "city": "Davidsonfurt", "zipcode": "78398", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456951, "visitDate": "09/03/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 872, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "04/19/1985", "gender": "Female", "email": "christine<PERSON><PERSON>@yahoo.com", "address": {"addressLine1": "2625 Oak Terrace", "addressLine2": "", "state": "DE", "city": "<PERSON><PERSON>", "zipcode": "66042", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456952, "visitDate": "09/04/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 874, "firstName": "<PERSON>", "lastName": "Hall", "dateOfBirth": "11/11/1971", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "2660 Cedar Way", "addressLine2": "", "state": "MN", "city": "Fostermouth", "zipcode": "16523", "county": "White"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456953, "visitDate": "09/05/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 876, "firstName": "<PERSON>", "lastName": "Jordan", "dateOfBirth": "12/11/1970", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "25048 Railroad Rd", "addressLine2": "", "state": "AZ", "city": "<PERSON>", "zipcode": "55111", "county": "Clearwater"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456954, "visitDate": "09/27/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 878, "firstName": "Ana", "lastName": "<PERSON><PERSON><PERSON>", "dateOfBirth": "11/02/1978", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "1234 Pelican Ln", "addressLine2": "", "state": "AZ", "city": "Detroit Lakes", "zipcode": "56501", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456955, "visitDate": "09/28/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 880, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "10/21/1948", "gender": "Male", "email": "<PERSON>@gmail.com", "address": {"addressLine1": "25136 Railroad Rd", "addressLine2": "", "state": "AZ", "city": "<PERSON>", "zipcode": "56652", "county": "Clearwather"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456956, "visitDate": "09/29/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 882, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "07/01/1967", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "908 SW 5th Ave", "addressLine2": "", "state": "AZ", "city": "Grand Rapids", "zipcode": "55744", "county": "Itasca"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456957, "visitDate": "09/30/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 884, "firstName": "Esperanza", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "06/13/1969", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "2811 South Hwy 168", "addressLine2": "", "state": "AZ", "city": "Grand Rapids", "zipcode": "55744", "county": "Itasca"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456958, "visitDate": "09/31/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 886, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "dateOfBirth": "11/16/1947", "gender": "Male", "email": "<PERSON>@gmail.coom", "address": {"addressLine1": "354 River Rd", "addressLine2": "", "state": "AZ", "city": "Grand Rapids", "zipcode": "55744", "county": "Itasca"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456959, "visitDate": "09/01/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 888, "firstName": "MILDRED", "lastName": "MYERS", "dateOfBirth": "02/12/1937", "gender": "Male", "email": "<PERSON><PERSON>@gmail.com", "address": {"addressLine1": "1743 <PERSON>", "addressLine2": "", "state": "AZ", "city": "Saint Paul", "zipcode": "55116", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456960, "visitDate": "09/02/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 890, "firstName": "<PERSON>", "lastName": "Ford", "dateOfBirth": "08/14/1935", "gender": "Female", "email": "<EMAIL>", "address": {"addressLine1": "63856 Pfeifer Road", "addressLine2": "", "state": "AZ", "city": "Saint Paul", "zipcode": "56630", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456961, "visitDate": "09/03/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 892, "firstName": "<PERSON>", "lastName": "Green", "dateOfBirth": "12/07/1943", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "36485 Pincherry Rd", "addressLine2": "", "state": "AZ", "city": "Cohasset", "zipcode": "55104", "county": "Itasca"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456962, "visitDate": "09/04/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 894, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "dateOfBirth": "06/12/1956", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "2632 Alvarado Ln N", "addressLine2": "", "state": "AZ", "city": "Plymouth", "zipcode": "55447", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456963, "visitDate": "09/05/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 896, "firstName": "<PERSON>ra", "lastName": "Suchite <PERSON>az<PERSON>", "dateOfBirth": "03/03/1952", "gender": "Male", "email": "<EMAIL>", "address": {"addressLine1": "1678 Brueberry Lane", "addressLine2": "", "state": "AZ", "city": "Arden Hill", "zipcode": "55112", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456964, "visitDate": "09/27/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 898, "firstName": "<PERSON>", "lastName": "Faccone", "dateOfBirth": "10/28/1947", "gender": "Male", "email": "<PERSON>@gmail.com", "address": {"addressLine1": "1625 Golf Course Rd", "addressLine2": "", "state": "AZ", "city": "Grand Rapids", "zipcode": "55744", "county": "Itasca"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456965, "visitDate": "09/28/2025", "visitStartTime": "10:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 900, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "dateOfBirth": "07/07/1936", "gender": "Female", "email": "<PERSON><PERSON>@gmail.com", "address": {"addressLine1": "2478 SW 8th Street", "addressLine2": "", "state": "AZ", "city": "Grand Rapids", "zipcode": "55744", "county": "Itasca"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456966, "visitDate": "09/29/2025", "visitStartTime": "11:00 AM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 902, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "01/17/1939", "gender": "Female", "email": "<PERSON>@gmail.com", "address": {"addressLine1": "15352 Great Valley Rd NW", "addressLine2": "", "state": "AZ", "city": "<PERSON><PERSON>", "zipcode": "56676", "county": "<PERSON><PERSON><PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456967, "visitDate": "09/30/2025", "visitStartTime": "12:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 904, "firstName": "<PERSON><PERSON>", "lastName": "Munoz", "dateOfBirth": "05/31/1944", "gender": "Male", "email": "<PERSON><PERSON>@gmail.com", "address": {"addressLine1": "619 S Pokegama Ave", "addressLine2": "", "state": "AZ", "city": "Grand Rapids", "zipcode": "55744", "county": "Itasca"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456968, "visitDate": "09/31/2025", "visitStartTime": "01:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 906, "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "12/27/1953", "gender": "Female", "email": "<PERSON>@gmail.com", "address": {"addressLine1": "524 Main Ave N", "addressLine2": "", "state": "AZ", "city": "<PERSON><PERSON>", "zipcode": "56621", "county": "Clearwater"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456969, "visitDate": "09/01/2025", "visitStartTime": "02:00 PM", "visitType": "SOC"}}, {"episode": {"episodeNo": 1, "startDate": "06/01/2025", "endDate": "08/01/2025"}, "client": {"clientNo": 908, "firstName": "<PERSON><PERSON>", "lastName": "Marks", "dateOfBirth": "07/13/1961", "gender": "Male", "email": "<PERSON><PERSON>@gmail.com", "address": {"addressLine1": "2748 Hamline Ave N", "addressLine2": "", "state": "AZ", "city": "Roseville", "zipcode": "55113", "county": "<PERSON>"}, "phone": "(*************", "emergencyContact": "<PERSON>, <PERSON>", "emergencyContactPhone": "(*************"}, "clinician": {"clinicianNo": 2004}, "visit": {"visitId": 456970, "visitDate": "09/02/2025", "visitStartTime": "09:00 AM", "visitType": "SOC"}}]