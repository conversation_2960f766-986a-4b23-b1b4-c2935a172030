import globals from "globals";
import pluginJs from "@eslint/js";

/** @type {import('eslint').Linter.Config[]} */
export default [
  {
    files: ["**/*.js"],
    languageOptions: {
      sourceType: "commonjs",
      globals: {
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
      },
    },
    rules: {
      "prettier/prettier": "off",
      "import/no-dynamic-require": "off",
      "no-async-promise-executor": "error",
      "no-await-in-loop": "off",
      "no-class-assign": "error",
      "no-compare-neg-zero": "error",
      "no-cond-assign": "error",
      "no-const-assign": "error",
      "no-constant-condition": "off",
      "no-constructor-return": "error",
      "no-debugger": "error",
      "no-dupe-args": "error",
      "no-dupe-class-members": "error",
      "no-dupe-else-if": "error",
      "no-dupe-keys": "error",
      "no-duplicate-case": "error",
      "no-duplicate-imports": "error",
      "no-empty-character-class": "error",
      "no-empty-pattern": "error",
      "no-ex-assign": "error",
      "no-fallthrough": "error",
      "no-func-assign": "error",
      "no-import-assign": "error",
      "no-inner-declarations": "error",
      "no-irregular-whitespace": "error",
      "no-obj-calls": "error",
      "no-promise-executor-return": "error",
      "no-prototype-builtins": "error",
      "no-self-assign": "error",
      "no-self-compare": "error",
      "no-setter-return": "error",
      "no-sparse-arrays": "error",
      "no-template-curly-in-string": "error",
      "no-this-before-super": "error",
      "no-undef": "error",
      "no-unexpected-multiline": "error",
      "no-unmodified-loop-condition": "error",
      "no-unreachable": "error",
      "no-unreachable-loop": "error",
      "no-unsafe-finally": "error",
      "no-unsafe-negation": "error",
      "no-unsafe-optional-chaining": "error",
      "no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
      "no-use-before-define": "error",
      "use-isnan": "error",
      "valid-typeof": "error",

      "block-scoped-var": "error",
      "default-case": "error",
      "default-param-last": ["error"],
      "dot-notation": [
        "error",
        {
          allowPattern: "^[a-z]+(-[a-z]+)+$",
        },
      ],
      eqeqeq: ["error", "always"],
      "max-depth": ["error", 5],
      "max-lines-per-function": [
        "error",
        {
          max: 500,
          skipBlankLines: true,
        },
      ],
      "max-nested-callbacks": ["error", 4],
      "max-classes-per-file": "off",
      "max-params": ["error", 7],
      "max-params": ["error", 7],
      "no-alert": "error",
      "no-bitwise": "error",
      "no-case-declarations": "error",
      "no-logger": "off",
      "no-continue": "error",
      "no-delete-var": "error",
      "no-else-return": "error",
      "no-empty": "error",
      "no-extra-semi": "error",
      "no-empty-function": "error",
      "no-empty-static-block": "off",
      "no-eq-null": "error",
      "no-extra-label": "error",
      "no-global-assign": "error",
      "no-lone-blocks": "error",
      "no-lonely-if": "error",
      "no-loop-func": "error",
      "no-redeclare": "error",
      "no-shadow-restricted-names": "error",
      "no-unneeded-ternary": "error",
      "no-useless-concat": "error",
      "no-useless-rename": "error",
      "no-useless-return": "error",
      "no-var": "error",
      "prefer-const": "error",
      "arrow-body-style": "off",
      "prefer-promise-reject-errors": "off",
      "no-param-reassign": "off",
      camelcase: "off",
      "global-require": "off",
    },
  },
  { languageOptions: { globals: globals.browser } },
  pluginJs.configs.recommended,
];
