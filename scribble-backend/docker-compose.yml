version: "3.8"

services:
  mongo1:
    image: mongo:latest
    container_name: mongo1
    restart: always
    ports:
      - "27017:27017"
    command: [ "mongod", "--replSet", "rs0", "--bind_ip_all", "--keyFile", "/data/mongo-keyfile" ]
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: adminpassword
    volumes:
      - mongo1_data:/data/db
      - ./mongo-keyfile:/data/mongo-keyfile
    networks:
      - backend

  mongo2:
    image: mongo:latest
    container_name: mongo2
    restart: always
    ports:
      - "27018:27017"
    command: [ "mongod", "--replSet", "rs0", "--bind_ip_all", "--keyFile", "/data/mongo-keyfile" ]
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: adminpassword
    volumes:
      - mongo2_data:/data/db
      - ./mongo-keyfile:/data/mongo-keyfile
    networks:
      - backend

  mongo3:
    image: mongo:latest
    container_name: mongo3
    restart: always
    ports:
      - "27019:27017"
    command: [ "mongod", "--replSet", "rs0", "--bind_ip_all", "--keyFile", "/data/mongo-keyfile" ]
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: adminpassword
    volumes:
      - mongo3_data:/data/db
      - ./mongo-keyfile:/data/mongo-keyfile
    networks:
      - backend

  mongo-init:
    image: mongo:latest
    container_name: mongo-init
    depends_on:
      - mongo1
      - mongo2
      - mongo3
    entrypoint: [ "bash", "-c", 'sleep 10 && mongosh --host mongo1:27017 -u admin -p adminpassword --authenticationDatabase admin --eval ''rs.initiate({_id: "rs0", members: [{ _id: 0, host: "mongo1:27017" }, { _id: 1, host: "mongo2:27017" }, { _id: 2, host: "mongo3:27017" }]})''' ]
    networks:
      - backend

  redis:
    image: redis:latest
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
    command: [ "redis-server", "--bind", "0.0.0.0", "--protected-mode", "no" ]
    volumes:
      - redis_data:/data
    networks:
      - backend

  scribble-api:
    build: .
    container_name: scribble-api
    mem_limit: 1.5g
    memswap_limit: 2g
    restart: always
    ports:
      - "3000:3000"
    depends_on:
      - mongo-init
      - redis
    environment:
      MONGO_URI: "*************************************************************************************************************"
      REDIS_HOST: "redis"
      REDIS_PORT: "6379"
    networks:
      - backend

volumes:
  mongo1_data:
  mongo2_data:
  mongo3_data:
  redis_data:

networks:
  backend:
    external: true
