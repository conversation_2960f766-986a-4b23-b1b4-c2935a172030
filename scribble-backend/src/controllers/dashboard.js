const { getTenantDB } = require("../lib/dbManager.js");

const userProfiles = require("../model/tenant/userProfiles.js");
const Client_Info = require("../model/tenant/clientInfo.js");
const Episode = require("../model/tenant/episode.js");
const Visit = require("../model/tenant/visit.js");
const Form = require("../model/tenant/form.js");

require("dotenv").config();
const {
  responses: { SuccessResponse },
} = require("../lib/index.js");
const { ErrorResponse } = require("../lib/responses.js");

const adminKpis = async (req, res) => {
  try {
    const connection = await getTenantDB(req.tenantDb);
    const Client_InfoModel = Client_Info(connection);
    const userProfileInfoModel = userProfiles(connection);
    const FormModel = Form(connection);
    const VisitModel = Visit(connection);
    const EpisodeModel = Episode(connection);

    const result = {
      client: await Client_InfoModel.countDocuments(),
      activeClinician: await userProfileInfoModel.countDocuments({
        status: "Active",
      }),
      inActiveClinician: await userProfileInfoModel.countDocuments({
        status: "Inactive",
      }),
      clinician: await userProfileInfoModel.countDocuments(),
      form: await FormModel.countDocuments(),
      visit: await VisitModel.countDocuments(),
      episode: await EpisodeModel.countDocuments(),
    };

    return res.status(201).json(new SuccessResponse(result));
  } catch (error) {
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

module.exports = {
  adminKpis,
};
