const UserProfile = require("../model/tenant/userProfiles.js");
const User = require("../model/tenant/user.js");
const { getTenantDB } = require("../lib/dbManager.js");
const speakeasy = require("speakeasy");
const qrcode = require("qrcode");
const crypto = require("crypto");
const {
  responses: { SuccessResponse },
  logger,
} = require("../lib/index.js");
const { ErrorResponse } = require("../lib/responses.js");
const { sendMFAEmailOTP } = require("../lib/emails.js");

/**
 * Setup MFA with user's preferred method (authenticator OR email)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const setupMFA = async (req, res) => {
  try {
    logger.debug(`Setting up MFA for user: ${req.user.id}`);
    const { "x-tenant-id": subDomain } = req.headers;
    const { mfaMethod, newSetup } = req.body;

    if (!mfaMethod || !["authenticator", "email"].includes(mfaMethod)) {
      return res
        .status(400)
        .json(
          new ErrorResponse(
            "Invalid MFA method. Must be 'authenticator' or 'email'",
          ),
        );
    }

    const connection = await getTenantDB(subDomain);
    const UserProfileModel = UserProfile(connection);
    const UserModel = User(connection);

    const user = await UserProfileModel.findOne({ userId: req.user.id });
    const userAccount = await UserModel.findById(req.user.id);

    if (!user || !userAccount) {
      logger.error(`User or user profile not found for user: ${req.user.id}`);
      return res.status(404).json(new ErrorResponse("User profile not found"));
    }

    // Restore previous logic: if MFA is already enabled and not forcing new setup, show setup page
    if (user.mfaEnabled && !newSetup) {
      logger.debug(
        `MFA already enabled for user: ${user.firstName} ${user.lastName}, showing setup page`,
      );
      return res.status(200).json(
        new SuccessResponse({
          mfaEnabled: true,
          mfaMethod: user.mfaMethod,
          message:
            "MFA is already enabled. Use 'newSetup: true' to reconfigure.",
        }),
      );
    }

    const updateData = {
      mfaMethod,
      mfaEnabled: false, // Will be set to true after verification
      mfaEmailFallbackUsed: false, // Reset fallback flag
    };

    // Generate authenticator secret if needed
    if (mfaMethod === "authenticator") {
      const secret = speakeasy.generateSecret({
        name: `Scribble (${user.firstName})`,
      });
      updateData.mfaSecret = secret.base32;
      updateData.otpauthUrl = secret.otpauth_url;
      // Clear email OTP data
      updateData.mfaEmailOtpSecret = "";
      updateData.mfaEmailOtpExpiry = null;
      updateData.mfaEmailOtpAttempts = 0;
    }

    // Generate email OTP secret if needed
    if (mfaMethod === "email") {
      const emailOtp = crypto.randomInt(100000, 999999).toString();
      updateData.mfaEmailOtpSecret = emailOtp;
      updateData.mfaEmailOtpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
      updateData.mfaEmailOtpAttempts = 0;
      // Clear authenticator data
      updateData.mfaSecret = "";
    }

    // Save MFA setup data
    await UserProfileModel.findOneAndUpdate(
      { userId: req.user.id },
      updateData,
      { upsert: true },
    );

    // Send email OTP if email method is selected
    if (mfaMethod === "email") {
      try {
        await sendMFAEmailOTP(
          userAccount.email,
          updateData.mfaEmailOtpSecret,
          user.firstName,
        );
        logger.debug(`MFA email OTP sent to user: ${req.user.id}`);
      } catch (emailError) {
        logger.error(`Failed to send MFA email OTP: ${emailError.message}`);
        return res
          .status(500)
          .json(new ErrorResponse("Failed to send verification email"));
      }
    }

    // Generate QR code for authenticator if needed
    if (mfaMethod === "authenticator") {
      qrcode.toDataURL(updateData.otpauthUrl, (err, data_url) => {
        if (err) {
          logger.error(`Error generating QR code: ${err.message}`);
          return res
            .status(500)
            .json(new ErrorResponse("Error generating QR code"));
        }

        logger.debug(
          `MFA setup completed successfully for user: ${req.user.id}`,
        );
        return res.status(200).json(
          new SuccessResponse({
            mfaMethod,
            qrCodeUrl: data_url,
            secret: updateData.mfaSecret,
            message:
              "MFA setup completed. Scan QR code with your authenticator app.",
          }),
        );
      });
    } else {
      // Email-only method
      logger.debug(`MFA setup completed successfully for user: ${req.user.id}`);
      return res.status(200).json(
        new SuccessResponse({
          mfaMethod,
          message:
            "MFA setup completed. Check your email for verification code.",
        }),
      );
    }
  } catch (err) {
    logger.error(`Error in setup MFA: ${err.message}`);
    return res.status(500).json(new ErrorResponse("Internal Server Error"));
  }
};

/**
 * Verify MFA token (authenticator app or email OTP)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const verifyMFA = async (req, res) => {
  try {
    logger.debug(`Verifying MFA for user: ${req.user.id}`);
    const { token, mfaMethod } = req.body;
    const { "x-tenant-id": subDomain } = req.headers;

    if (!token || !mfaMethod) {
      return res
        .status(400)
        .json(new ErrorResponse("Token and MFA method are required"));
    }

    if (!["authenticator", "email"].includes(mfaMethod)) {
      return res.status(400).json(new ErrorResponse("Invalid MFA method"));
    }

    const connection = await getTenantDB(subDomain);
    const UserProfileModel = UserProfile(connection);

    const user = await UserProfileModel.findOne({ userId: req.user.id });

    if (!user) {
      logger.error(`User profile not found for user: ${req.user.id}`);
      return res.status(404).json(new ErrorResponse("User profile not found"));
    }

    if (!user.mfaEnabled && user.mfaMethod !== mfaMethod) {
      logger.error(`MFA method mismatch for user: ${req.user.id}`);
      return res.status(400).json(new ErrorResponse("MFA method mismatch"));
    }

    let verified = false;

    // Verify based on method
    if (mfaMethod === "authenticator") {
      if (!user.mfaSecret) {
        return res
          .status(400)
          .json(
            new ErrorResponse("Authenticator MFA not set up for this user"),
          );
      }

      verified = speakeasy.totp.verify({
        secret: user.mfaSecret,
        encoding: "base32",
        token,
        window: 1, // Handle 30s clock skew
      });
    } else if (mfaMethod === "email") {
      if (!user.mfaEmailOtpSecret) {
        return res
          .status(400)
          .json(new ErrorResponse("Email MFA not set up for this user"));
      }

      // Check if OTP is expired
      if (new Date() > user.mfaEmailOtpExpiry) {
        return res
          .status(400)
          .json(new ErrorResponse("Verification code has expired"));
      }

      // Check attempts limit
      if (user.mfaEmailOtpAttempts >= 3) {
        return res
          .status(400)
          .json(
            new ErrorResponse(
              "Too many failed attempts. Please request a new code.",
            ),
          );
      }

      // Verify OTP
      verified = token === user.mfaEmailOtpSecret;

      if (verified) {
        // Reset OTP after successful verification
        await UserProfileModel.findOneAndUpdate(
          { userId: req.user.id },
          {
            mfaEmailOtpSecret: "",
            mfaEmailOtpExpiry: null,
            mfaEmailOtpAttempts: 0,
          },
        );
      } else {
        // Increment failed attempts
        await UserProfileModel.findOneAndUpdate(
          { userId: req.user.id },
          { $inc: { mfaEmailOtpAttempts: 1 } },
        );
      }
    }

    if (verified) {
      // Enable MFA after successful verification
      await UserProfileModel.findOneAndUpdate(
        { userId: req.user.id },
        { mfaEnabled: true },
      );

      logger.debug(`MFA verification successful for user: ${req.user.id}`);
      return res.status(200).json(
        new SuccessResponse({
          success: true,
          message: "MFA verification successful",
        }),
      );
    }

    logger.warn(`Invalid MFA token for user: ${req.user.id}`);
    return res.status(400).json(new ErrorResponse("Invalid verification code"));
  } catch (err) {
    logger.error(`Error in verify MFA: ${err.message}`);
    return res.status(500).json(new ErrorResponse("Internal Server Error"));
  }
};

/**
 * Request email OTP for fallback authentication (when authenticator app is lost)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const requestEmailFallback = async (req, res) => {
  try {
    logger.debug(`Requesting email fallback for user: ${req.user.id}`);
    const { "x-tenant-id": subDomain } = req.headers;

    const connection = await getTenantDB(subDomain);
    const UserProfileModel = UserProfile(connection);
    const UserModel = User(connection);

    const user = await UserProfileModel.findOne({ userId: req.user.id });
    const userAccount = await UserModel.findById(req.user.id);

    if (!user || !userAccount) {
      logger.error(`User or user profile not found for user: ${req.user.id}`);
      return res.status(404).json(new ErrorResponse("User profile not found"));
    }

    if (!user.mfaEnabled) {
      return res
        .status(400)
        .json(new ErrorResponse("MFA is not enabled for this user"));
    }

    // Generate new email OTP for fallback
    const emailOtp = crypto.randomInt(100000, 999999).toString();
    const expiryTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Update user profile with fallback OTP and mark fallback as used
    await UserProfileModel.findOneAndUpdate(
      { userId: req.user.id },
      {
        mfaEmailOtpSecret: emailOtp,
        mfaEmailOtpExpiry: expiryTime,
        mfaEmailOtpAttempts: 0,
        mfaEmailFallbackUsed: true,
      },
    );

    // Send email OTP
    try {
      await sendMFAEmailOTP(userAccount.email, emailOtp, user.firstName);
      logger.debug(`Email fallback OTP sent to user: ${req.user.id}`);

      return res.status(200).json(
        new SuccessResponse({
          message:
            "Fallback verification code sent to your email. After successful verification, you'll need to reset your MFA method.",
        }),
      );
    } catch (emailError) {
      logger.error(`Failed to send fallback email OTP: ${emailError.message}`);
      return res
        .status(500)
        .json(new ErrorResponse("Failed to send verification email"));
    }
  } catch (err) {
    logger.error(`Error in request email fallback: ${err.message}`);
    return res.status(500).json(new ErrorResponse("Internal Server Error"));
  }
};

/**
 * Reset MFA method after fallback authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const resetMFAMethod = async (req, res) => {
  try {
    logger.debug(`Resetting MFA method for user: ${req.user.id}`);
    const { "x-tenant-id": subDomain } = req.headers;

    const connection = await getTenantDB(subDomain);
    const UserProfileModel = UserProfile(connection);

    const user = await UserProfileModel.findOne({ userId: req.user.id });

    if (!user) {
      logger.error(`User profile not found for user: ${req.user.id}`);
      return res.status(404).json(new ErrorResponse("User profile not found"));
    }

    if (!user.mfaEmailFallbackUsed) {
      return res
        .status(400)
        .json(
          new ErrorResponse(
            "Email fallback was not used. Cannot reset MFA method.",
          ),
        );
    }

    // Reset all MFA fields and allow user to choose new method
    await UserProfileModel.findOneAndUpdate(
      { userId: req.user.id },
      {
        mfaEnabled: false,
        mfaSecret: "",
        mfaMethod: "",
        mfaEmailOtpSecret: "",
        mfaEmailOtpExpiry: null,
        mfaEmailOtpAttempts: 0,
        mfaEmailFallbackUsed: false,
      },
    );

    logger.debug(`MFA method reset successfully for user: ${req.user.id}`);
    return res.status(200).json(
      new SuccessResponse({
        message:
          "MFA method has been reset. You can now choose a new authentication method.",
      }),
    );
  } catch (err) {
    logger.error(`Error in reset MFA method: ${err.message}`);
    return res.status(500).json(new ErrorResponse("Internal Server Error"));
  }
};

/**
 * Get MFA status and configuration for user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMFAStatus = async (req, res) => {
  try {
    logger.debug(`Getting MFA status for user: ${req.user.id}`);
    const { "x-tenant-id": subDomain } = req.headers;

    const connection = await getTenantDB(subDomain);
    const UserProfileModel = UserProfile(connection);

    const user = await UserProfileModel.findOne({ userId: req.user.id });

    if (!user) {
      logger.error(`User profile not found for user: ${req.user.id}`);
      return res.status(404).json(new ErrorResponse("User profile not found"));
    }

    const mfaStatus = {
      mfaEnabled: user.mfaEnabled || false,
      mfaMethod: user.mfaMethod || "",
      hasAuthenticator: !!user.mfaSecret,
      hasEmail: user.mfaMethod === "email",
      emailFallbackUsed: user.mfaEmailFallbackUsed || false,
    };

    logger.debug(`MFA status retrieved for user: ${req.user.id}`);
    return res.status(200).json(new SuccessResponse(mfaStatus));
  } catch (err) {
    logger.error(`Error in get MFA status: ${err.message}`);
    return res.status(500).json(new ErrorResponse("Internal Server Error"));
  }
};

/**
 * Toggle MFA status for user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const toggleMFA = async (req, res) => {
  try {
    const { status } = req.body;
    const { "x-tenant-id": subDomain } = req.headers;

    if (!status || !["enable", "disable"].includes(status)) {
      return res
        .status(400)
        .json(new ErrorResponse("Status must be either 'enable' or 'disable'"));
    }

    logger.debug(`Toggling MFA status to '${status}' for user: ${req.user.id}`);

    const connection = await getTenantDB(subDomain);
    const UserProfileModel = UserProfile(connection);

    const user = await UserProfileModel.findOne({ userId: req.user.id });

    if (!user) {
      logger.error(`User profile not found for user: ${req.user.id}`);
      return res.status(404).json(new ErrorResponse("User profile not found"));
    }

    if (status === "enable" && user.mfaEnabled) {
      return res
        .status(400)
        .json(new ErrorResponse("MFA is already enabled for this user"));
    }

    if (status === "disable" && !user.mfaEnabled) {
      return res
        .status(400)
        .json(new ErrorResponse("MFA is not enabled for this user"));
    }

    // Only update the mfaEnabled field, preserve other MFA values
    await UserProfileModel.findOneAndUpdate(
      { userId: req.user.id },
      { mfaEnabled: status === "enable" },
    );

    const action = status === "enable" ? "enabled" : "disabled";
    logger.debug(`MFA ${action} successfully for user: ${req.user.id}`);

    return res.status(200).json(
      new SuccessResponse({
        message: `MFA has been ${action} successfully`,
      }),
    );
  } catch (err) {
    logger.error(`Error in toggle MFA: ${err.message}`);
    return res.status(500).json(new ErrorResponse("Internal Server Error"));
  }
};

module.exports = {
  setupMFA,
  verifyMFA,
  requestEmailFallback,
  resetMFAMethod,
  getMFAStatus,
  toggleMFA,
};
