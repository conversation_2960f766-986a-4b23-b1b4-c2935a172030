const { getTenantDB } = require("../lib/dbManager.js");
const { transformAssessmentForAI } = require("../lib/utils.js");

const userProfiles = require("../model/tenant/userProfiles.js");
const Client_Info = require("../model/tenant/clientInfo.js");
const User = require("../model/tenant/user.js");
const Form = require("../model/tenant/form.js");
const Assessment = require("../model/tenant/assessment.js");
const Visit = require("../model/tenant/visit.js");
require("dotenv").config();
const {
  responses: { SuccessResponse },
  logger,
} = require("../lib/index.js");
const { ErrorResponse } = require("../lib/responses.js");
const { uploadFile, pushToQueue } = require("../lib/aws.js");
// const nconf = require("nconf");

const processAudio = async (req, res) => {
  try {
    logger.debug(`Starting audio processing for tenant: ${req.tenantDb}`);
    const AWS = require("aws-sdk");
    const { assessmentId } = req.body;

    logger.debug(
      `Processing audio for assessment ID: ${assessmentId} for tenant: ${req.tenantDb}`,
    );

    const audioFile = req.file;
    if (!audioFile) {
      logger.error(
        `No audio file found in request for tenant: ${req.tenantDb}`,
      );
      return res
        .status(400)
        .json(
          new ErrorResponse(
            `No audio file uploaded for assessment: ${assessmentId}`,
          ),
        );
    }

    logger.debug(
      `Received audio file: ${audioFile.originalname} (${audioFile.size} bytes) for tenant: ${req.tenantDb}`,
    );

    if (audioFile.size > 100 * 1024 * 1024) {
      // 100 MB limit
      logger.error(
        `File size ${audioFile.size} exceeds 100MB limit for tenant: ${req.tenantDb}`,
      );
      return res
        .status(400)
        .json(
          new ErrorResponse(
            `Audio file exceeds 50 MB limit. Got ${audioFile.size} bytes`,
          ),
        );
    }

    if (!assessmentId) {
      logger.error(
        `Missing required assessment ID for tenant: ${req.tenantDb}`,
      );
      return res
        .status(400)
        .json(new ErrorResponse("Assessment ID is required"));
    }

    logger.debug(`Connecting to tenant database: ${req.tenantDb}`);
    const connection = await getTenantDB(req.tenantDb);
    const AssessmentModel = Assessment(connection);
    // eslint-disable-next-line
    const userProfileInfoModel = userProfiles(connection);
    // eslint-disable-next-line
    const Client_InfoModel = Client_Info(connection);
    const VisitModel = Visit(connection);
    // eslint-disable-next-line
    const UserModel = User(connection);
    const FormModel = Form(connection);

    logger.debug(
      `Fetching assessment details for ID: ${assessmentId} for tenant: ${req.tenantDb}`,
    );
    const assessment = await AssessmentModel.findById(assessmentId).populate({
      path: "formId",
      model: FormModel,
    });

    if (!assessment) {
      logger.error(
        `Assessment not found with ID: ${assessmentId} for tenant: ${req.tenantDb}`,
      );
      return res
        .status(404)
        .json(
          new ErrorResponse(
            `Assessment not found for id: ${assessmentId} on tenant ${req.tenantDb}`,
          ),
        );
    }

    logger.debug(`Preparing form data for upload for tenant: ${req.tenantDb}`);
    const form = assessment.formId.question;
    const aiFormatForm = transformAssessmentForAI(form);
    const formBuffer = Buffer.from(JSON.stringify(aiFormatForm));
    if (!(formBuffer instanceof Buffer)) {
      logger.error(`Failed to convert form to buffer`);
      return res
        .status(500)
        .json(new ErrorResponse("Error converting form to buffer"));
    }

    const visit = assessment.visitId;
    if (!visit) {
      logger.error(
        `Visit not found for assessment: ${assessmentId} on tenant ${req.tenantDb}`,
      );
      return res
        .status(404)
        .json(
          new ErrorResponse(
            `Visit not found for assessment: ${assessmentId} on tenant ${req.tenantDb}`,
          ),
        );
    }

    logger.debug(`Preparing to upload form to S3 for visit: ${visit._id}`);
    const params = {
      Bucket: process.env.S3_BUCKET,
      Key: `${req.tenantDb}/${visit._id}/${assessmentId}/input/questionForm.json`,
      Body: formBuffer,
      ContentType: "application/json",
    };

    const s3 = new AWS.S3();
    logger.debug(`Uploading form to S3`);
    const formUploadData = await s3.upload(params).promise();
    logger.debug(`Form uploaded successfully to: ${formUploadData.Location}`);

    logger.debug(`Uploading audio file to S3`);
    await uploadFile(
      audioFile,
      `${req.tenantDb}/${visit._id}/${assessmentId}/input/${audioFile.originalname}`,
    );

    logger.debug(`Updating visit status for visit: ${visit._id}`);
    await VisitModel.findByIdAndUpdate(visit._id, {
      status: "In Progress",
    });

    logger.debug(`Updating assessment status for assessment: ${assessmentId}`);
    await AssessmentModel.findByIdAndUpdate(assessmentId, {
      status: "Submitted to AI",
    });

    const visitDetails = await VisitModel.findById(visit._id);

    logger.debug(`Preparing message for AI processing queue`);
    const message = {
      audioFilePath: `${req.tenantDb}/${visit._id}/${assessmentId}/input/${audioFile.originalname}`,
      questionFormPath: `${req.tenantDb}/${visit._id}/${assessmentId}/input/questionForm.json`,
      user_id: req.user.email,
      client_id: visitDetails.clientId,
      visit_id: visit._id,
      id: assessmentId,
      assessment_id: assessmentId,
      company_id: req.tenantDb,
      transcribe_type: "deepgram",
      audio_files: [audioFile.originalname],
      question_files: ["questionForm.json"],
    };

    const queueUrl = process.env.AI_INPUT_QUEUE_URL;
    logger.debug(`Sending message to AI queue: ${queueUrl}`);
    logger.debug(`Message: ${JSON.stringify(message)}`);
    await pushToQueue(queueUrl, message);
    logger.debug(`Message successfully sent to AI queue`);

    logger.debug(`Audio processing completed successfully`);
    return res
      .status(200)
      .json(new SuccessResponse("Audio file uploaded and message sent to SQS"));
  } catch (error) {
    logger.error(`Error processing audio: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

module.exports = {
  processAudio,
};
