const AdminUser = require("../model/scribble-admin/adminUser.js");
const Tenant = require("../model/scribble-admin/tenants.js");
const adminDetails = require("../model/default/admin.js");
const validator = require("validator");
const { isEmpty } = require("lodash");

const { getTenantDB } = require("../lib/dbManager.js");
const {
  getFilterQuery,
  generateHashedPassword,
  generateRandomPassword,
  isPasswordCompromised,
  formatDateToMMDDYYYY,
} = require("../lib/utils.js");

const {
  sendAccountVerificationEmail,
  sendPasswordResetEmail,
} = require("../lib/emails.js");
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

const tenantModels = require("../model/tenant/index.js");
const User = require("../model/tenant/user.js");
const Role = require("../model/tenant/role.js");
const Discipline = require("../model/tenant/discipline.js");
// const { generateAccessToken } = require("../lib/jwt.js");

const { createFolder } = require("../lib/aws.js");
require("dotenv").config();
const {
  responses: { SuccessResponse, HTTPError, ERROR_CODES },
  logger,
  tokens,
  session,
} = require("../lib/index.js");
const { ErrorResponse } = require("../lib/responses.js");
const { V2SuccessResponse } = require("../lib/v2-responses.js");
const userProfiles = require("../model/tenant/userProfiles.js");
const PasswordResetToken = require("../model/tenant/passwordResetToken.js");
const crypto = require("crypto");
const AuditAccount = require("../model/tenant/auditAccount.js");

// Function to create admin user if it doesn't exist
async function createAdminUser(adminDbUrl) {
  logger.debug(`Creating admin user with URL: ${adminDbUrl}`, {
    apiId: "system",
  });
  try {
    await mongoose.createConnection(adminDbUrl, {});
    logger.debug(`Connected to admin database`, { apiId: "system" });

    const existingAdmin = await AdminUser.findOne({
      email: "<EMAIL>",
    });
    logger.debug(
      `Checked for existing admin: ${existingAdmin ? "found" : "not found"}`,
      { apiId: "system" },
    );

    if (!existingAdmin) {
      const salt = bcrypt.genSaltSync(10);
      adminDetails.password = bcrypt.hashSync(adminDetails.password, salt);
      logger.debug(`Generated hashed password for new admin`, {
        apiId: "system",
      });

      const newAdmin = new AdminUser(adminDetails);

      await newAdmin.save();
      logger.info(`Admin user created successfully`, { apiId: "system" });
    } else {
      logger.info(`Admin user already exists`, { apiId: "system" });
    }
  } catch (error) {
    logger.error(`Error creating admin user: ${error}`, { apiId: "system" });
  }
}

async function adminLogin(req, res) {
  logger.debug(`Admin login attempt for email: ${req.body.email}`, {
    apiId: req.apiId,
  });
  try {
    let { email } = req.body;
    const { password } = req.body;
    email = email.toLowerCase();
    const adminUser = await AdminUser.findOne({ email });
    console.log("🚀 ~ adminLogin ~ adminUser:", adminUser);
    logger.debug(
      `Admin user lookup result: ${adminUser ? "found" : "not found"}`,
      { apiId: req.apiId },
    );

    if (!adminUser) {
      logger.warn(`Login failed - admin not found for email: ${email}`, {
        apiId: req.apiId,
      });
      return res
        .status(404)
        .json(new ErrorResponse(`Admin not found for email: ${email}`));
    }

    const isPasswordValid = bcrypt.compareSync(password, adminUser.password);
    logger.debug(`Password validation result: ${isPasswordValid}`, {
      apiId: req.apiId,
    });

    if (!isPasswordValid) {
      logger.warn(`Login failed - invalid credentials for admin: ${email}`, {
        apiId: req.apiId,
      });
      return res
        .status(401)
        .json(new ErrorResponse(`Invalid credentials for admin: ${email}`));
    }

    await AdminUser.updateOne(
      { _id: adminUser._id },
      { $set: { lastLoginAt: new Date() } },
    );
    logger.debug(`Updated last login time for admin: ${adminUser._id}`, {
      apiId: req.apiId,
    });

    const accessTokenTtl = "600";
    const refreshTokenttl = "604800";

    const roles = "scribble_admin";
    const permission = adminUser.permission;
    const accessToken = await tokens.createTokenV2(
      {
        user_id: adminUser._id,
        roles,
        permission,
      },
      accessTokenTtl,
    );
    logger.debug(`Generated access token for admin`, { apiId: req.apiId });

    const refreshToken = await tokens.createRefreshToken(
      adminUser._id,
      refreshTokenttl,
    );
    logger.debug(`Generated refresh token for admin`, { apiId: req.apiId });

    await session.storeAccessToken(adminUser._id, accessToken, accessTokenTtl);
    await session.storeRefreshToken(
      adminUser._id,
      refreshToken,
      refreshTokenttl,
    );
    logger.debug(`Stored tokens in session`, { apiId: req.apiId });

    const responseInst = {
      email: adminUser.email,
      userId: adminUser._id,
      firstName: adminUser.firstName,
      lastName: adminUser.lastName,
      isFirstLogin: adminUser?.isFirstLogin,
      lastLoginAt: new Date(),
      roles,
      permission,
      accessToken,
      refreshToken,
      created: adminUser.createdAt,
      updated: adminUser.updatedAt,
    };
    logger.info(`Admin login successful for email: ${email}`, {
      apiId: req.apiId,
    });
    return res.json(new SuccessResponse(responseInst));
  } catch (err) {
    logger.error(`Error on Admin Login ${err}`, { apiId: req.apiId });
    return res.json(new ErrorResponse(err?.message || err));
  }
}

async function userLogin(req, res) {
  const { "x-tenant-id": subDomain } = req.headers;
  let { email = "" } = req.body;
  const { password = "" } = req.body;
  email = email.toLowerCase();
  logger.debug(`User login attempt for email=${email}, tenant=${subDomain}`, {
    apiId: req.apiId,
  });

  try {
    const tenant = await Tenant.findOne({ uniqueName: subDomain });
    logger.debug(`Tenant lookup result: ${tenant ? "found" : "not found"}`, {
      apiId: req.apiId,
    });

    // If tenant not found, return an error
    if (!tenant) {
      logger.warn(
        `Login failed - tenant not found: ${subDomain} for email: ${email}`,
        { apiId: req.apiId },
      );
      return res.status(400).json(
        new ErrorResponse({
          message: `Login failed - tenant not found: ${subDomain}. email: ${email}`,
        }),
      );
    }

    const connection = await getTenantDB(tenant.databaseName);
    logger.debug(`Connected to tenant database: ${tenant.databaseName}`, {
      apiId: req.apiId,
    });

    const UserModel = User(connection);
    // eslint-disable-next-line
    const RoleModel = Role(connection);

    const user = await UserModel.findOne({ email }).populate({
      path: "roleId",
      select: "name permission",
    });
    logger.debug(`User lookup result: ${user ? "found" : "not found"}`, {
      apiId: req.apiId,
    });

    if (!user) {
      logger.warn(
        `Login failed - user not found: ${email} for tenant: ${subDomain}`,
      );
      return res
        .status(404)
        .json(
          new ErrorResponse(
            `User not found for email: ${email}`,
            req.apiId,
            ERROR_CODES.WRONG_CREDENTIALS,
          ),
        );
    }

    // Check if account is locked or deleted
    if (user.status === "locked") {
      logger.warn(
        `Login failed - account locked for user: ${email} for tenant: ${subDomain}`,
      );
      return res
        .status(401)
        .json(
          new ErrorResponse(`Account is locked. Please contact administrator.`),
        );
    }

    if (user.status === "inactive") {
      logger.warn(
        `Login failed - account inactive for user: ${email} for tenant: ${subDomain}`,
      );
      return res
        .status(401)
        .json(
          new ErrorResponse(
            `Account has been deactivated. Please contact administrator.`,
          ),
        );
    }

    const { name, permission } = user.roleId;

    const isPasswordValid = bcrypt.compareSync(password, user.password);
    logger.debug(`Password validation result: ${isPasswordValid}`);

    if (!isPasswordValid) {
      await UserModel.updateOne(
        { _id: user._id },
        { $inc: { loginAttempts: 1 } },
      );
      logger.warn(
        `Failed login attempt for user: ${email} for tenant: ${subDomain}`,
      );

      if (user.loginAttempts >= 5) {
        // Lock the account when user exceeds max login attempts
        await UserModel.updateOne(
          { _id: user._id },
          {
            $set: {
              status: "locked",
              statusUpdatedAt: new Date(),
            },
          },
        );
        logger.warn(
          `Account locked for user: ${email} - exceeded max login attempts for tenant: ${subDomain}`,
        );
        return res
          .status(401)
          .json(
            new ErrorResponse(
              `Account locked due to multiple failed login attempts. Please contact administrator.`,
            ),
          );
      }

      return res
        .status(401)
        .json(
          new ErrorResponse(
            `Invalid credentials for user: ${email}`,
            req.apiId,
            ERROR_CODES.WRONG_CREDENTIALS,
          ),
        );
    }

    await UserModel.updateOne(
      { _id: user._id },
      { $set: { lastLoginAt: new Date(), loginAttempts: 0 } },
    );
    logger.debug(`Updated last login time for user: ${user._id}`);

    const userProfileModel = userProfiles(connection);
    const userDetails = await userProfileModel.findOne({ userId: user.id });

    //2 hours access token ttl (7200 seconds)
    const accessTokenTtl = "600";
    //1 day refresh token ttl (600 seconds)
    const refreshTokenttl = "604800";

    const roles = [name];
    const accessToken = await tokens.createTokenV2(
      {
        user_id: user._id,
        roles,
        permission,
      },
      accessTokenTtl,
    );
    logger.debug(`Generated access token for user`);

    const refreshToken = await tokens.createRefreshToken(
      user._id,
      refreshTokenttl,
      subDomain,
    );
    logger.debug(`Generated refresh token for user`);

    await session.storeAccessToken(user._id, accessToken, accessTokenTtl);
    await session.storeRefreshToken(user._id, refreshToken, refreshTokenttl);
    logger.debug(`Stored tokens in session`);

    const responseInst = {
      email: user.email,
      userId: user._id,
      firstName: userDetails?.firstName,
      lastName: userDetails?.lastName,
      staffId: userDetails?.no || userDetails?.staffId,
      dateOfBirth: formatDateToMMDDYYYY(userDetails?.dateOfBirth),
      isFirstLogin: user?.isFirstLogin,
      lastLoginAt: user?.lastLoginAt || new Date(),
      address: userDetails?.address,
      gender: userDetails?.gender,
      jobTitle: userDetails?.jobTitle,
      phone: userDetails?.phone,
      disciplineId: userDetails?.disciplineId,
      roles,
      permission,
      accessToken,
      refreshToken,
      created: user.createdAt,
      updated: user.updatedAt,
      mfaEnabled: userDetails.mfaEnabled || false,
      mfaMethod: userDetails.mfaMethod || "",
    };
    logger.info(
      `User login successful for email: ${email} for tenant: ${subDomain}`,
    );
    return res.json(new SuccessResponse(responseInst));
  } catch (err) {
    logger.error(`User login error: ${err}`);
    return res.json(new ErrorResponse(err?.message || err));
  }
}
// V2 Clinician Login Function
async function clinicianLogin(req, res) {
  const startTime = Date.now();
  const { "x-tenant-id": subDomain } = req.headers;
  let { email = "" } = req.body;
  const { password = "" } = req.body;
  email = email.toLowerCase();

  logger.info(
    `🔐 V2 Clinician login attempt for email: ${email} for tenant: ${subDomain}`,
    {
      email: email,
      tenant: subDomain,
      deviceInfo: {
        deviceId: req.headers["x-device-id"],
        platform: req.headers["x-platform"],
        appVersion: req.headers["x-app-version"],
        ipAddress: req.headers["x-ip-address"],
      },
    },
  );

  try {
    const tenant = await Tenant.findOne({ uniqueName: subDomain });
    logger.debug(`Tenant lookup result: ${tenant ? "found" : "not found"}`);

    // If tenant not found, return an error
    if (!tenant) {
      logger.warn(
        `❌ V2 Clinician login failed - tenant not found: ${subDomain}`,
      );

      return res
        .status(401)
        .json(
          new ErrorResponse(
            `Invalid tenant or credentials`,
            req.apiId,
            ERROR_CODES.WRONG_CREDENTIALS,
          ),
        );
    }

    const connection = await getTenantDB(tenant.databaseName);
    logger.debug(`Connected to tenant database: ${tenant.databaseName}`);

    const UserModel = User(connection);
    // eslint-disable-next-line
    const RoleModel = Role(connection);

    const user = await UserModel.findOne({ email }).populate({
      path: "roleId",
      select: "name permission",
    });
    logger.debug(`User lookup result: ${user ? "found" : "not found"}`);

    if (!user) {
      logger.warn(`❌ V2 Clinician login failed - user not found: ${email}`);
      return res
        .status(401)
        .json(
          new ErrorResponse(
            `Please check your email and password`,
            req.apiId,
            ERROR_CODES.WRONG_CREDENTIALS,
          ),
        );
    }

    // Check if account is locked or deleted
    if (user.status === "locked") {
      logger.warn(`❌ V2 Clinician login failed - account locked: ${email}`);
      return res
        .status(401)
        .json(
          new ErrorResponse(
            `Account is locked. Please contact administrator.`,
            req.apiId,
            ERROR_CODES.WRONG_CREDENTIALS,
          ),
        );
    }

    if (user.status === "inactive") {
      logger.warn(`❌ V2 Clinician login failed - account inactive: ${email}`);
      return res
        .status(401)
        .json(
          new ErrorResponse(
            `Account has been deactivated. Please contact administrator.`,
            req.apiId,
            ERROR_CODES.WRONG_CREDENTIALS,
          ),
        );
    }

    const { name, permission } = user.roleId;

    const isPasswordValid = bcrypt.compareSync(password, user.password);
    logger.debug(`Password validation result: ${isPasswordValid}`);

    if (!isPasswordValid) {
      await UserModel.updateOne(
        { _id: user._id },
        { $inc: { loginAttempts: 1 } },
      );
      logger.warn(`❌ V2 Clinician failed login attempt: ${email}`);

      if (user.loginAttempts >= 5) {
        // Lock the account when user exceeds max login attempts
        await UserModel.updateOne(
          { _id: user._id },
          {
            $set: {
              status: "locked",
              statusUpdatedAt: new Date(),
            },
          },
        );
        logger.warn(
          `🔒 V2 Clinician account locked: ${email} - exceeded max login attempts`,
        );
        return res
          .status(401)
          .json(
            new ErrorResponse(
              `Account locked due to multiple failed login attempts. Please contact administrator.`,
              req.apiId,
              ERROR_CODES.WRONG_CREDENTIALS,
            ),
          );
      }

      return res
        .status(401)
        .json(
          new ErrorResponse(
            `Please check your email and password`,
            req.apiId,
            ERROR_CODES.WRONG_CREDENTIALS,
          ),
        );
    }

    await UserModel.updateOne(
      { _id: user._id },
      { $set: { lastLoginAt: new Date(), loginAttempts: 0 } },
    );
    logger.debug(`Updated last login time for user: ${user._id}`);

    // Note: userDetails not used in v2 response - only returning access token
    const userProfileModel = userProfiles(connection);
    await userProfileModel.findOne({ userId: user.id });

    //2 hours access token ttl (7200 seconds)
    const accessTokenTtl = "600";
    //1 day refresh token ttl (600 seconds)
    const refreshTokenttl = "604800";

    const roles = [name];
    const accessToken = await tokens.createTokenV2(
      {
        user_id: user._id,
        roles,
        permission,
      },
      accessTokenTtl,
    );
    logger.debug(`Generated access token for user`);

    const refreshToken = await tokens.createRefreshToken(
      user._id,
      refreshTokenttl,
      subDomain,
    );
    logger.debug(`Generated refresh token for user`);

    await session.storeAccessToken(user._id, accessToken, accessTokenTtl);
    await session.storeRefreshToken(user._id, refreshToken, refreshTokenttl);
    logger.debug(`Stored tokens in session`);

    const responseTime = Date.now() - startTime;
    logger.info(`✅ V2 Clinician login successful`, {
      email: user.email,
      tenant: subDomain,
      responseTime: `${responseTime}ms`,
      role: name,
    });

    // Return access token with success message in v2 standard format
    return res.json(
      new V2SuccessResponse({
        accessToken,
        refreshToken,
        message: "Login successful",
      }),
    );
  } catch (err) {
    const responseTime = Date.now() - startTime;
    logger.error(`❌ V2 Clinician login error`, {
      error: err.message,
      stack: err.stack,
      responseTime: `${responseTime}ms`,
    });

    return res
      .status(503)
      .json(
        new ErrorResponse(
          `Service temporarily unavailable. Please try again later.`,
          req.apiId,
          ERROR_CODES.SERVICE_UNAVAILABLE,
        ),
      );
  }
}
/**
 *
 * @param {object} req
 * @param {object} res
 * @param {Function} next
 */
const performLogin = async (req, res) => {
  logger.debug(
    `Login request received for tenant: ${req.headers["x-tenant-id"]} and email: ${req.body.email}`,
    {
      tenantId: req.headers["x-tenant-id"],
      email: req.body.email,
    },
  );

  try {
    const { "x-tenant-id": tenantId } = req.headers;

    if (!tenantId) {
      logger.debug(
        `No tenant ID - proceeding with admin login for email: ${req.body.email}`,
      );
      await adminLogin(req, res);
    } else {
      logger.debug(
        `Tenant ID present - proceeding with user login for email: ${req.body.email}`,
      );
      await userLogin(req, res);
    }
  } catch (err) {
    logger.error(`Login error: ${err}`);
    res.status(500).json(new ErrorResponse(err.message));
  }
};

const createTenant = async (req, res) => {
  const { tenantName } = req.body;
  const uniqueName = tenantName.toLowerCase().replace(/ /g, "");
  logger.debug(
    `Creating new tenant: ${tenantName} with unique name: ${uniqueName}`,
  );

  try {
    const existingTenant = await Tenant.findOne({ uniqueName });
    logger.debug(
      `Checked for existing tenant: ${existingTenant ? "found" : "not found"}`,
    );

    if (existingTenant) {
      logger.warn(`Tenant creation failed - already exists: ${uniqueName}`);
      return res.status(400).json(new ErrorResponse("Tenant already exists"));
    }

    await tenantModels.init(uniqueName);
    logger.debug(`Initialized tenant models for tenant: ${tenantName}`);

    // Save tenant details in adminDB
    const tenant = new Tenant({
      tenantName: tenantName,
      uniqueName: uniqueName,
      databaseName: uniqueName,
      createdBy: req.user,
    });
    await tenant.save();
    logger.debug(`Saved tenant details to database for tenant: ${tenantName}`);

    createFolder(uniqueName);
    logger.debug(`Created tenant folder for tenant: ${tenantName}`);

    logger.info(`Successfully created new tenant: ${uniqueName}`);
    return res.status(201).json(new SuccessResponse(tenant));
  } catch (error) {
    logger.error(`Tenant creation error: ${error}`);
    res.status(500).json(new ErrorResponse(error.message));
  }
};

const getTenant = async (req, res) => {
  logger.debug(`Getting tenants with query: ${req.query}`);
  try {
    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);
    logger.debug(
      `Parsed filter query: ${JSON.stringify({ query, parsedLimit, parsedOffset })}`,
    );

    const existingTenants = await Tenant.find(query)
      .limit(parsedLimit)
      .skip(parsedOffset);
    logger.debug(`Found tenants count: ${existingTenants.length}`);

    const totalCount = await Tenant.countDocuments(query);
    logger.debug(`Total tenants count: ${totalCount}`);

    return res
      .status(201)
      .json(new SuccessResponse(existingTenants, totalCount));
  } catch (error) {
    logger.error(`Error on getting tenants: ${error}`);
    res.status(500).json(new ErrorResponse(error.message));
  }
};

const getRoles = async (req, res) => {
  logger.debug(`Getting roles for tenant: ${req.tenantId}`);
  try {
    const { tenantId } = req;
    // const tenant = await Tenant.findOne({ uniqueName: tenantId });
    const tenant = await Tenant.findById(tenantId);

    logger.debug(`Tenant lookup result: ${tenant ? "found" : "not found"}`);

    // If tenant not found, return an error
    if (!tenant) {
      logger.warn(`Get roles failed - tenant not found: ${tenantId}`);
      return res.status(400).json(
        new ErrorResponse({
          message: `Get roles failed - tenant not found: ${tenantId}`,
        }),
      );
    }

    const connection = await getTenantDB(tenant.databaseName);
    logger.debug(`Connected to tenant database: ${tenant.databaseName}`);

    const RoleModel = Role(connection);

    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);
    logger.debug(
      `Parsed filter query: ${JSON.stringify({ query, parsedLimit, parsedOffset })}`,
    );

    const role = await RoleModel.find(query)
      .limit(parsedLimit)
      .skip(parsedOffset);
    logger.debug(`Found roles count: ${role.length}`);

    if (!role) {
      logger.warn(`No roles found for query: ${JSON.stringify(query)}`);
      return res
        .status(400)
        .json(new ErrorResponse({ message: "Role not found" }));
    }
    const totalCount = await RoleModel.countDocuments(query);
    logger.debug(`Total roles count: ${totalCount}`);

    return res.status(201).json(new SuccessResponse(role, totalCount));
  } catch (error) {
    logger.error(`Error on getting roles: ${error}`);
    res.status(500).json(new ErrorResponse(error));
  }
};

/**
 * Creates a record in the appropriate UserInfo table.
 */
async function createUserInfo({ userId, roleName, req, connection, session }) {
  logger.debug(
    `Creating user profile for userId: ${userId}, role: ${roleName}`,
  );
  const {
    employeeId,
    firstName,
    lastName,
    disciplineId,
    jobTitle,
    dob,
    gender,
    address1,
    address2,
    city,
    state,
    zip,
    country,
    primaryPhone,
  } = req.body;
  try {
    const userProfileModel = userProfiles(connection);
    const profileData = {
      userId,
      no: employeeId,
      firstName,
      lastName,
      jobTitle,
      dateOfBirth: dob,
      gender,
      address: {
        addressLine1: address1,
        addressLine2: address2,
        city,
        state,
        zipcode: zip,
        county: country,
      },
      phone: primaryPhone,
    };
    if (roleName !== "admin" && roleName !== "scribble_admin") {
      profileData.disciplineId = disciplineId;
    }
    await userProfileModel.create([profileData], { session });
    logger.debug(`Created user profile for userId: ${userId}`);
  } catch (err) {
    logger.error(`Error creating user info: ${err}`);
    throw err;
  }
}

const register = async (req, res) => {
  let session;

  try {
    logger.debug(`Request body to Register User: ${JSON.stringify(req.body)}`);
    const { "x-tenant-id": tenantIdBody } = req.body;
    const { "x-tenant-id": tenantIdHeader } = req.headers;
    const subDomain = tenantIdBody || tenantIdHeader;
    logger.debug(
      `User registration request received: ${req.body.email} on tenant: ${subDomain} with id ${req.tenantId}`,
    );
    let { email } = req.body;
    const { roleId } = req.body;
    email = email.toLowerCase();
    const tenantId = req.tenantId;
    logger.warn(`Tenant Details: ${tenantId}`);

    if (!tenantId) {
      logger.warn(`Registration failed - missing tenant ID`);
      return res
        .status(400)
        .json(new ErrorResponse({ message: "Tenant ID is required" }));
    }
    const tenant = await Tenant.findById(tenantId);

    // const tenant = await Tenant.findById(tenantId);
    if (!tenant) throw new Error(`Tenant not found ${tenantId}`);
    logger.debug(`Found tenant: ${tenant.tenantName}`);

    const connection = await getTenantDB(tenant.databaseName);
    session = await connection.startSession();
    session.startTransaction();
    logger.debug(
      `Started database transaction for tenant: ${tenant.tenantName}`,
    );

    const RoleModel = Role(connection);
    const role = await RoleModel.findById(roleId).session(session);
    if (!role) throw new Error("Role not found");
    logger.debug(`Found role: ${role.name}`);

    const UserModel = User(connection);
    const existingUser = await UserModel.findOne({ email }).session(session);
    if (existingUser) throw new Error("User already exists");

    // const password = "Admin@123";
    const password = generateRandomPassword(10);
    const hashedPassword = await generateHashedPassword(password);
    logger.debug(`Generated hashed password for new user: ${email}`);

    const newUser = await UserModel.create(
      [
        {
          email,
          password: hashedPassword,
          roleId,
          isFirstLogin: true,
          createdBy: req.user.id,
          updatedBy: req.user.id,
        },
      ],
      { session },
    );
    logger.debug(`Created new user: ${email}`);

    await createUserInfo({
      userId: newUser[0]._id,
      roleName: role.name,
      req,
      connection,
      session,
    });
    logger.debug(`Created user info for user: ${email}`);
    const scribbleUrl = `https://${subDomain}.goscribble.ai`;
    await sendAccountVerificationEmail(
      email,
      password,
      req.body?.firstName,
      scribbleUrl,
    );
    logger.debug(`Sent verification email to user: ${email}`);

    await session.commitTransaction();
    logger.info(`Successfully registered new user: ${email}`);

    res.status(201).json(new SuccessResponse(newUser[0]));
  } catch (err) {
    logger.error(`Registration error: ${err}`);
    if (session && session.inTransaction()) {
      await session.abortTransaction();
      logger.debug(`Aborted database transaction`);
    }
    res
      .status(500)
      .json(
        new ErrorResponse(
          (err?.message || err) + " " + JSON.stringify(req.body),
        ),
      );
  } finally {
    if (session) {
      await session.endSession();
      logger.debug(`Ended database session`);
    }
  }
};

const health = async (req, res) => {
  logger.debug(
    `Health check request received for tenant: ${req.headers["x-tenant-id"]}`,
  );
  try {
    return res.json({ message: "health" });
  } catch (err) {
    logger.error(`Health check error: ${err}`);
    return res.send({ err });
  }
};

/**
 * GET new access token based on refresh token
 * @param {String} req.body.refreshToken
 * @param {*} res
 */
const getAccessToken = async (req, res) => {
  logger.debug(
    `Get access token request received for tenant: ${req.headers["x-tenant-id"]}`,
  );
  try {
    const { refreshToken } = req.body;
    const tokenData = await tokens.verifyRefreshToken(refreshToken);
    logger.debug(`Verified refresh token for user: ${tokenData.userId}`);

    const accessTokenTtl = "600";

    const response = await session.checkIfRefreshTokenExists(tokenData.userId);
    logger.debug(
      `Checked refresh token exists: ${response ? "yes" : "no"} for user: ${tokenData.userId}`,
    );

    if (!response || response !== refreshToken) {
      logger.warn(`Session expired for user: ${tokenData.userId}`);
      throw new HTTPError(
        419,
        "Session expired! Login again",
        ERROR_CODES.EXPIRED_TOKEN,
      );
    }

    let user = {};
    let roles = [];
    let permissions = [];

    if (tokenData.tenant === "scribble_admin") {
      user = await AdminUser.findById(tokenData.userId);
      roles = "scribble_admin";
      permissions = user.permission;
    } else {
      const connection = await getTenantDB(tokenData.tenant);
      logger.debug(`Connected to tenant database: ${tokenData.tenant}`);

      const UserModel = User(connection);
      // eslint-disable-next-line
      const RoleModel = Role(connection);
      user = await UserModel.findById(tokenData.userId).populate({
        path: "roleId",
        select: "name permission",
      });
      const { name, permission } = user.roleId;
      roles = [name];
      permissions = permission;
    }

    const accessToken = await tokens.createTokenV2(
      {
        user_id: user._id,
        roles,
        permissions,
      },
      accessTokenTtl,
    );
    logger.debug(`Generated new access token for user: ${user.id}`);

    res.cookie("token", accessToken, {
      httpOnly: true,
      secure: true,
      sameStrict: "strict",
    });
    // Update redis with new accesstoken for particular refreshtoken
    await session.storeAccessToken(user.id, accessToken, accessTokenTtl);
    logger.debug(`Stored new access token for user: ${user.id}`);

    res.json(new SuccessResponse({ accessToken }));
  } catch (err) {
    logger.error(`Get access token error: ${err}`);
    res
      .status(err.statusCode || 500)
      .json(new ErrorResponse(err, err.errorCode, req?.apiId, err.data));
  }
};

/**
 * Validate password. Throws if invalid.
 * @param {String} password
 * @returns {Boolean}
 */
const validatePassword = async (password) => {
  logger.debug(`Validating password: ${password}`);

  const lengthValid = validator.isLength(password, { min: 12, max: 64 });
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSpecial = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);

  if (!lengthValid || !hasUppercase || !hasNumber || !hasSpecial) {
    logger.warn(`Password validation failed - does not meet policy`);
    throw new HTTPError(
      400,
      "Invalid password. Please ensure it is 12-64 characters, contains at least one uppercase letter, one number, and one special character.",
      ERROR_CODES.INVALID_DATA,
    );
  }

  const isCompromised = await isPasswordCompromised(password);
  if (isCompromised) {
    logger.warn(`Password validation failed - found in HIBP database`);
    throw new HTTPError(
      400,
      "This password has appeared in a data breach. Please choose a different password for your security.",
      ERROR_CODES.INVALID_DATA,
    );
  }

  logger.debug(`Password validation successful for password: ${password}`);
  return true;
};
/**
 * PUT /change-password
 * Update the password for the authenticated account [AG-904]
 * @param {Object} req
 * @param {Object} req.body
 * @param {String} req.user.id (from token)
 * @param {String} req.body.newPassword
 * @param {String} req.body.oldPassword
 * @returns Response stating the password update status
 */
const changePassword = async (req, res) => {
  logger.debug(
    `Change password request received for user: ${req.user.id} for tenant: ${req.tenantDb}`,
  );
  try {
    const { newPassword, oldPassword } = req.body;

    // Current password entered by user should be correct to be allowed to change it
    if (!bcrypt.compareSync(oldPassword, req.user.password)) {
      logger.warn(
        `Change password failed - incorrect current password: ${oldPassword}`,
      );
      throw new HTTPError(
        400,
        "Current password entered is incorrect",
        "INCORRECT_CURRENT_PASSWORD",
      );
    }

    // New password can not be the same as the current password
    if (bcrypt.compareSync(newPassword, req.user.password)) {
      logger.warn(
        `Change password failed - new password same as current: ${newPassword}`,
      );
      throw new HTTPError(
        403,
        "Your new password cannot be the same as your current password.",
        "SAME_AS_CURRENT_PASSWORD",
      );
    }

    // Enforce password policy and HIBP check
    await validatePassword(newPassword);

    // Update password
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(newPassword, salt);
    req.user.password = hash;
    logger.debug(
      `Generated new hashed password: ${hash} for user: ${req.user.id} for tenant: ${req.tenantDb}`,
    );

    const connection = await getTenantDB(req.tenantDb);

    const UserModel = User(connection);

    await UserModel.updateOne(
      { email: req.user?.email }, // Filter: Find user by email
      { $set: { password: hash, isFirstLogin: false } }, // Update password field
    );
    logger.debug(
      `Updated password in database: ${req.user.email} for user: ${req.user.id} for tenant: ${req.tenantDb}`,
    );

    logger.info(
      `Successfully changed password for user: ${req.user.email} for tenant: ${req.tenantDb}`,
    );
    res.json(
      new SuccessResponse({
        message:
          "Your password has been reset. You may now log in with your new password.",
      }),
    );
  } catch (err) {
    logger.error(`Change password error: ${err}`);
    res
      .status(err.statusCode || 500)
      .json(new ErrorResponse(err, err.errorCode, req?.apiId, err.data));
  }
};

/**
 * PUT /change-password
 * Update the password for the authenticated account [AG-904]
 * @param {Object} req
 * @param {Object} req.body
 * @param {String} req.user.id (from token)
 * @param {String} req.body.newPassword
 * @param {String} req.body.oldPassword
 * @returns Response stating the password update status
 */
const updateProfile = async (req, res) => {
  logger.info(
    `Profile update requested for userId: ${req.user.id}, email: ${req.user.email} for tenant: ${req.tenantDb}`,
  );
  try {
    const connection = await getTenantDB(req.tenantDb);
    const userProfileModel = userProfiles(connection);
    const UserModel = User(connection);

    // Separate profile data from user data
    const { email, ...profileData } = req.body;

    // Update user profile (schema will handle read-only field protection)
    if (Object.keys(profileData).length > 0) {
      await userProfileModel.updateOne(
        { userId: req.user.id },
        { $set: profileData },
      );
      logger.debug(
        `Updated profile fields: ${Object.keys(profileData).join(", ")}`,
      );
    }

    // Update user email if provided
    if (email) {
      await UserModel.updateOne({ _id: req.user.id }, { $set: { email } });
      logger.debug(
        `Updated user email: ${email} for user: ${req.user.id} for tenant: ${req.tenantDb}`,
      );
    }

    logger.debug(
      `Updated profile in database: ${req.user.email} for user: ${req.user.id} for tenant: ${req.tenantDb}`,
    );
    logger.info(
      `Successfully updated profile for user: ${req.user.email} for tenant: ${req.tenantDb}`,
    );

    res.json(new SuccessResponse({ message: "Profile updated successfully." }));
  } catch (err) {
    logger.error(`Update profile error: ${err}`);
    res
      .status(err.statusCode || 500)
      .json(new ErrorResponse(err, err.errorCode, req?.apiId, err.data));
  }
};

const updateUser = async (req, res) => {
  logger.info(
    `Updating userId: ${req.params.id} by adminId: ${req.user.id} for tenant: ${req.tenantDb}`,
  );
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }

    const {
      firstName,
      lastName,
      dateOfBirth,
      employeeId,
      gender,
      phone,
      isDeleted,
      disciplineId,
      address,
      roleName,
      isFirstLogin,
      lastLoginAt,
      status,
      jobTitle,
      email,
    } = req.body;
    const connection = await getTenantDB(req.tenantDb);
    const UserModel = User(connection);
    const userProfileModel = userProfiles(connection);
    // eslint-disable-next-line
    const RoleModel = Role(connection);
    req.body.updatedBy = req.user.id;

    const userUpdateBody = {};

    // Handle deletion logic
    if (isDeleted !== undefined) {
      if (isDeleted === true) {
        // Mark user as deleted with status and timestamp
        userUpdateBody.status = "deleted";
        userUpdateBody.statusUpdatedAt = new Date();
        logger.info(
          `Marking user as deleted: userId: ${req.params.id} for tenant: ${req.tenantDb}`,
        );
      } else {
        // Reactivate user
        userUpdateBody.status = "active";
        userUpdateBody.statusUpdatedAt = new Date();
        logger.info(
          `Reactivating user: userId: ${req.params.id} for tenant: ${req.tenantDb}`,
        );
      }
    }

    email !== undefined && (userUpdateBody.email = email);

    const user = await UserModel.findByIdAndUpdate(
      req.params.id,
      userUpdateBody,
      {
        new: true,
      },
    ).populate({
      path: "roleId",
      select: "name permission",
    });
    const updateBody = {
      firstName,
      lastName,
      dateOfBirth,
      employeeId,
      gender,
      phone,
      disciplineId,
      address,
      name: roleName,
      isFirstLogin,
      lastLoginAt,
      status,
      jobTitle,
    };
    userProfileModel.updateOne({ userId: req.params.id }, { $set: updateBody });

    return res.status(200).json(new SuccessResponse(user));
  } catch (error) {
    logger.error(`Error on updating user: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

/**
 * POST /auth/v1/recover-password-email
 * @description Send password recovery email containing a link to reset the password
 * @param {String} req.body.email The email of the user who wants to reset their password
 * @returns {Object} Returns JSON response saying email has been sent / not sent
 */
const sendRecoverPasswordEmail = async (req, res) => {
  function genericSuccess() {
    return res.json(
      new SuccessResponse({
        message:
          "A password recovery link has been sent to your email address. Please check your inbox and follow the instructions to reset your password.",
      }),
    );
  }
  logger.debug(
    `Password recovery email request received for tenant: ${req.headers["x-tenant-id"]}`,
  );
  const { email } = req.body;
  let session;
  try {
    if (isEmpty(email)) {
      logger.info(
        `Password reset requested with missing email. IP: ${req.ip} for tenant: ${req.headers["x-tenant-id"]}`,
      );
      return genericSuccess();
    }
    const { "x-tenant-id": subDomain } = req.headers;
    const tenant = await Tenant.findOne({ uniqueName: subDomain });
    if (!tenant) {
      logger.info(
        `Password reset requested for non-existent tenant. Tenant: ${subDomain}, Email: ${email}, IP: ${req.ip} for tenant: ${req.headers["x-tenant-id"]}`,
      );
      return genericSuccess();
    }
    const connection = await getTenantDB(tenant.databaseName);
    session = await connection.startSession();
    session.startTransaction();
    logger.debug(
      "Started database transaction for password reset token creation",
    );

    const UserModel = User(connection);
    const PasswordResetTokenModel = PasswordResetToken(connection);

    const user = await UserModel.findOne({ email }).session(session);
    if (!user) {
      logger.info(
        `Password reset requested for non-existent user. Tenant: ${subDomain}, Email: ${email}, IP: ${req.ip}`,
      );
      return genericSuccess();
    }
    // Generate a secure random token
    const rawToken = crypto.randomBytes(32).toString("hex");
    const tokenHash = crypto
      .createHash("sha256")
      .update(rawToken)
      .digest("hex");
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    // Store in password reset token collection
    await PasswordResetTokenModel.create(
      [
        {
          userId: user._id,
          tokenHash,
          expiresAt,
          used: false,
        },
      ],
      { session },
    );
    const userProfileModel = userProfiles(connection);
    const userProfile = await userProfileModel
      .findOne({ userId: user._id })
      .session(session);
    const firstName = userProfile?.firstName || "";
    // Send link with only token - frontend will handle the rest
    const passwordRecoveryLink = `https://${subDomain}.goscribble.ai/recover-password/${tokenHash}`;
    await sendPasswordResetEmail(email, passwordRecoveryLink, firstName);

    await session.commitTransaction();
    logger.info(
      `Password reset email sent successfully. Email: ${email}, Tenant: ${subDomain}, IP: ${req.ip}`,
    );
    return genericSuccess();
  } catch (err) {
    logger.error(`Password recovery email error: ${err}`);
    if (session && session.inTransaction()) {
      await session.abortTransaction();
      logger.debug(
        "Aborted database transaction for password reset token creation",
      );
    }
    return res
      .status(err.statusCode || 500)
      .json(new ErrorResponse(err, err.errorCode, req?.apiId, err.data));
  } finally {
    if (session) {
      await session.endSession();
      logger.debug(
        `Ended database session for password reset token creation for tenant: ${req.headers["x-tenant-id"]}`,
      );
    }
  }
};

/**
 * POST /auth/v1/recover-password
 * @description Update the old password with new
 * @param {String} req.body.token Password reset token
 * @param {String} req.body.newPassword The new password that the user wants to update
 * @returns {Object} Returns JSON response saying password has been updated / not updated
 */
const recoverPassword = async (req, res) => {
  function genericSuccess() {
    return res.status(200).json(
      new SuccessResponse({
        message:
          "Your password has been reset. You may now log in with your new password.",
      }),
    );
  }
  logger.debug(
    `Password recovery request received for tenant: ${req.headers["x-tenant-id"]}`,
  );
  const { token, newPassword } = req.body;
  let session;
  try {
    // Get tenant connection first to access PasswordResetToken model
    const { "x-tenant-id": subDomain } = req.headers;
    const tenant = await Tenant.findOne({ uniqueName: subDomain });
    if (!tenant) {
      logger.info(
        `Password reset attempt for non-existent tenant. Tenant: ${subDomain}, IP: ${req.ip} for tenant: ${req.headers["x-tenant-id"]}`,
      );
      // return genericSuccess(); // Still generic to avoid tenant enumeration
      throw new HTTPError(
        404,
        "Please provide tenant details",
        ERROR_CODES.GENERAL_ERROR,
      );
    }
    const connection = await getTenantDB(tenant.databaseName);
    session = await connection.startSession();
    session.startTransaction();
    logger.debug(
      `Started database transaction for password reset for tenant: ${subDomain}`,
    );

    const PasswordResetTokenModel = PasswordResetToken(connection);
    // Find the token and get user info

    const resetTokenDoc = await PasswordResetTokenModel.findOne({
      tokenHash: token,
      expiresAt: { $gt: new Date() },
      used: false,
    }).session(session);

    if (!resetTokenDoc) {
      return res
        .status(400)
        .json(
          new ErrorResponse(
            "This password reset link is invalid or has expired. Please request a new password reset.",
          ),
        );
    }
    const UserModel = User(connection);
    const user = await UserModel.findById(resetTokenDoc.userId).session(
      session,
    );
    if (!user) {
      logger.info(
        `Password reset attempt for non-existent user. UserID: ${resetTokenDoc.userId}, Tenant: ${subDomain}, IP: ${req.ip}`,
      );
      return genericSuccess(); // Still generic to avoid user enumeration
    }

    // Validate the new password
    try {
      await validatePassword(newPassword);
    } catch (validationErr) {
      return res.status(400).json(new ErrorResponse(validationErr.message));
    }

    // Check if new password is same as current password
    if (bcrypt.compareSync(newPassword, user.password)) {
      logger.info(
        `Password reset attempt with same password. UserID: ${user._id}, Tenant: ${subDomain}, IP: ${req.ip}`,
      );
      return res
        .status(400)
        .json(
          new ErrorResponse(
            "Your new password cannot be the same as your current password.",
          ),
        );
    }

    // All validations passed, now update the password
    const salt = bcrypt.genSaltSync(10);
    const hashedPassword = bcrypt.hashSync(newPassword, salt);

    // Update user password
    await UserModel.updateOne(
      { _id: user._id },
      {
        $set: {
          password: hashedPassword,
          status: "active",
          loginAttempts: 0,
          statusUpdatedAt: new Date(),
        },
      },
      { session },
    );

    // Mark token as used only after password is successfully updated
    await PasswordResetTokenModel.updateOne(
      { _id: resetTokenDoc._id },
      { $set: { used: true } },
      { session },
    );

    await session.commitTransaction();
    logger.info(
      `Password reset successful. UserID: ${user._id}, Email: ${user.email}, Tenant: ${subDomain}, IP: ${req.ip}`,
    );
    return genericSuccess();
  } catch (err) {
    logger.error(`Password recovery error: ${err}`);
    if (session && session.inTransaction()) {
      await session.abortTransaction();
      logger.debug(
        `Aborted database transaction for password reset for tenant: ${req.headers["x-tenant-id"]}`,
      );
    }
    return res
      .status(err.statusCode || 500)
      .json(new ErrorResponse(err, err.errorCode, req?.apiId, err.data));
  } finally {
    if (session) {
      await session.endSession();
      logger.debug(
        `Ended database session for password reset for tenant: ${req.headers["x-tenant-id"]}`,
      );
    }
  }
};

const logout = async (req, res) => {
  logger.info(`Logging out user: ${req.user.id} for tenant: ${req.tenantDb}`);
  try {
    const { id: identity } = req.user;
    const { refreshToken } = req.body;

    const response = await session.checkIfRefreshTokenExists(identity);
    // Validate if it exists in session store
    if (!response || response !== refreshToken) {
      throw new HTTPError(
        419,
        "Session expired! Login again",
        ERROR_CODES.EXPIRED_TOKEN,
      );
    }

    await session.removeAccessToken(identity);
    await session.removeRefreshToken(identity);
    res.json(new SuccessResponse());
  } catch (err) {
    res
      .status(err.statusCode || 500)
      .json(new ErrorResponse(err, err.errorCode, req?.apiId, err.data));
  }
};

const usersList = async (req, res) => {
  logger.debug(`Fetching users list for tenantDb: ${req.tenantDb}`);
  try {
    const connection = await getTenantDB(req.tenantDb);
    const UserModel = User(connection);
    // eslint-disable-next-line
    const DisciplineModel = Discipline(connection);
    const userProfileModel = userProfiles(connection);
    // const Admin_InfoModel = Admin_Info(connection);
    // const Clinician_InfoModel = Clinician_Info(connection);
    // eslint-disable-next-line
    const RoleModel = Role(connection);
    // Extract query parameters for search and pagination
    const { name, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;
    const searchCriteria = {};

    if (name) {
      const adminMatches = await userProfileModel
        .find({
          $or: [
            { firstName: { $regex: name, $options: "i" } },
            { lastName: { $regex: name, $options: "i" } },
          ],
        })
        .select("userId");

      const userIds = adminMatches.map((doc) => doc.userId.toString());

      const userIdsFromSearch = [...new Set([...userIds])]; // Unique userIds

      // Step 2: Build searchCriteria for UserModel
      if (userIdsFromSearch && userIdsFromSearch.length > 0) {
        searchCriteria._id = { $in: userIdsFromSearch };
      } else if (name) {
        // name was provided but no matches found in related collections
        return res.status(200).json(new SuccessResponse([], 0));
      }
    }

    // Fetch users with pagination and search
    const users = await UserModel.find(searchCriteria)
      .populate({
        path: "roleId",
        select: "name permission",
      })
      .skip(skip)
      .limit(parseInt(limit));

    const totalUsers = await UserModel.countDocuments(searchCriteria);

    const usersList = await Promise.all(
      users.map(async (user) => {
        const userProfile = await userProfileModel
          .findOne({
            userId: user._id,
          })
          .populate({
            path: "disciplineId",
            select: "name",
          });
        return {
          _id: user._id,
          email: user.email,
          roleId: user.roleId?.id,
          roleName: user.roleId?.name,
          isFirstLogin: user.isFirstLogin,
          lastLoginAt: user.lastLoginAt,
          staffId: userProfile?.no,
          firstName: userProfile?.firstName,
          lastName: userProfile?.lastName,
          jobTitle: userProfile?.jobTitle,
          dateOfBirth: formatDateToMMDDYYYY(userProfile?.dateOfBirth),
          gender: userProfile?.gender,
          address: userProfile?.address,
          phone: userProfile?.phone,
          disciplineId: userProfile?.disciplineId?.id,
          disciplineName: userProfile?.disciplineId?.name,
          created: user.createdAt,
          updated: user.updatedAt,
        };
      }),
    );
    return res.status(200).json(new SuccessResponse(usersList, totalUsers));
  } catch (err) {
    res
      .status(err.statusCode || 500)
      .json(new ErrorResponse(err, err.errorCode, req?.apiId, err.data));
  }
};

// V2 API - Get user profile
const getProfile = async (req, res) => {
  try {
    logger.debug(
      `Profile requested for userId: ${req.user.id}, email: ${req.user.email} for tenant: ${req.tenantDb}`,
    );

    const connection = await getTenantDB(req.tenantDb);
    const userProfileModel = userProfiles(connection);
    const RoleModel = Role(connection);
    // Register Discipline model with connection for populate to work
    Discipline(connection);

    // Get user profile with populated discipline
    const userProfile = await userProfileModel
      .findOne({ userId: req.user.id })
      .populate({
        path: "disciplineId",
        select: "name",
      });

    if (!userProfile) {
      logger.warn(`Profile not found for user: ${req.user.email}`);
      return res
        .status(404)
        .json(
          new ErrorResponse(
            "Your profile information could not be found. Please contact your administrator.",
            req.apiId,
            ERROR_CODES.GENERAL_ERROR,
          ),
        );
    }

    // Get role information
    const role = await RoleModel.findById(req.user.roleId).select("name");

    const profileData = {
      email: req.user.email,
      firstName: userProfile.firstName,
      lastName: userProfile.lastName,
      staffId: userProfile.no,
      discipline: userProfile.disciplineId?.name || null,
      phone: userProfile.phone,
      dateOfBirth: formatDateToMMDDYYYY(userProfile.dateOfBirth),
      role: role?.name || null,
    };

    logger.debug(
      `Successfully retrieved profile for user: ${req.user.email} for tenant: ${req.tenantDb}`,
    );

    res.json(new V2SuccessResponse(profileData));
  } catch (err) {
    logger.error(`Get profile error: ${err}`);

    // Handle specific error types with user-friendly messages
    if (err.name === "MissingSchemaError") {
      return res
        .status(500)
        .json(
          new ErrorResponse(
            "There was an issue loading your profile data. Please try again later.",
            req.apiId,
            ERROR_CODES.GENERAL_ERROR,
          ),
        );
    }

    if (err.name === "CastError") {
      return res
        .status(400)
        .json(
          new ErrorResponse(
            "Invalid profile data format. Please contact support.",
            req.apiId,
            ERROR_CODES.GENERAL_ERROR,
          ),
        );
    }

    res
      .status(500)
      .json(
        new ErrorResponse(
          "We're having trouble loading your profile right now. Please try again in a few moments.",
          req.apiId,
          ERROR_CODES.GENERAL_ERROR,
        ),
      );
  }
};

const deleteAccount = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json(new ErrorResponse("Email is required"));
    }

    const connection = await getTenantDB(req.tenantDb);

    const UserModel = User(connection);
    const user = await UserModel.findOne({ email: email.toLowerCase() });

    if (!user) {
      return res.status(404).json(new ErrorResponse("User not found"));
    }

    const AuditAccountModel = AuditAccount(connection);
    await AuditAccountModel.create({
      email: email.toLowerCase(),
      createdBy: req.user ? req.user._id : null,
    });

    logger.info(`Account deletion audit record created for email: ${email}`, {
      apiId: req.apiId,
    });

    return res.status(200).json({ status: "ok" });
  } catch (error) {
    logger.error(`Error creating audit record for account deletion: ${error}`, {
      apiId: req.apiId,
    });
    return res.status(500).json(new ErrorResponse("Internal server error"));
  }
};

module.exports = {
  performLogin,
  health,
  register,
  getAccessToken,
  changePassword,
  sendRecoverPasswordEmail,
  recoverPassword,
  createTenant,
  getTenant,
  createAdminUser,
  getRoles,
  logout,
  updateProfile,
  updateUser,
  usersList,
  clinicianLogin,
  getProfile,
  deleteAccount,
};
