const Tenant = require("../model/scribble-admin/tenants.js");

const { getTenantDB } = require("../lib/dbManager.js");

// Helper function to convert 24-hour format to 12-hour format
const convertTo12Hour = (time24) => {
  if (!time24 || typeof time24 !== "string") return time24;

  // Check if it's already in 12-hour format (contains AM/PM)
  if (time24.includes("AM") || time24.includes("PM")) {
    return time24;
  }

  // Parse 24-hour format (HH:MM)
  const match = time24.match(/^(\d{1,2}):(\d{2})$/);
  if (!match) return time24;

  let [, hour, minute] = match;
  hour = parseInt(hour, 10);
  minute = parseInt(minute, 10);

  let period = "AM";
  let displayHour = hour;

  if (hour === 0) {
    displayHour = 12;
  } else if (hour === 12) {
    period = "PM";
  } else if (hour > 12) {
    displayHour = hour - 12;
    period = "PM";
  }

  return `${displayHour}:${minute.toString().padStart(2, "0")} ${period}`;
};

// Helper function to convert 12-hour format to 24-hour format
const convertTo24Hour = (time12) => {
  if (!time12 || typeof time12 !== "string") return time12;

  // Check if it's already in 24-hour format (no AM/PM)
  if (!time12.includes("AM") && !time12.includes("PM")) {
    return time12;
  }

  // Parse 12-hour format (H:MM AM/PM or HH:MM AM/PM)
  const match = time12.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
  if (!match) return time12;

  // eslint-disable-next-line
  let [, hour, minute, period] = match;
  hour = parseInt(hour, 10);
  minute = parseInt(minute, 10);

  // Convert to 24-hour format
  if (period.toUpperCase() === "PM" && hour !== 12) {
    hour += 12;
  } else if (period.toUpperCase() === "AM" && hour === 12) {
    hour = 0;
  }

  // Format as HH:MM
  return `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
};
const {
  getFilterQuery,
  sendMessageToUIPath,
  transformAssessmentForRPA,
  updateQuestions,
  formatDateToMMDDYYYY,
} = require("../lib/utils.js");

const { sendAlertEmail } = require("../lib/emails.js");
const mongoose = require("mongoose");

const userProfiles = require("../model/tenant/userProfiles.js");
const Client_Info = require("../model/tenant/clientInfo.js");
const Episode = require("../model/tenant/episode.js");
const Visit = require("../model/tenant/visit.js");
const Form = require("../model/tenant/form.js");
const Form_Type = require("../model/tenant/formType.js");
const Discipline = require("../model/tenant/discipline.js");
const Assessment = require("../model/tenant/assessment.js");
const Notification = require("../model/tenant/notification.js");
const Notification_Type = require("../model/tenant/notificationType.js");
const Form_Template = require("../model/tenant/formTemplate.js");
// const nconf = require("nconf");
const {
  pushToQueue,
  deleteMessageFromQueue,
  downloadFile,
} = require("../lib/aws.js");
require("dotenv").config();
const {
  responses: { SuccessResponse },
  logger,
} = require("../lib/index.js");
const { ErrorResponse } = require("../lib/responses.js");

/** Start a database session and return connection + session */
const startDatabaseSession = async (tenantDb) => {
  logger.debug(`Starting database session for tenant: ${tenantDb}`);
  const connection = await getTenantDB(tenantDb);
  const session = await connection.startSession();
  session.startTransaction();
  logger.debug(`Database session started successfully for tenant: ${tenantDb}`);
  return { connection, session };
};

//UserAdmin create a form
const createForm = async (req, res) => {
  logger.debug(`Creating form with formTypeId: ${req.body.formTypeId}`);
  try {
    const { formName, assessmentForm, disciplineId, formTypeId } = req.body;
    const connection = await getTenantDB(req.tenantDb);
    const FormModel = Form(connection);

    let form = await FormModel.find({
      name: formName,
      isDeleted: false,
    });
    logger.debug(`Found existing assessment forms: ${form.length}`);
    if (form.length) {
      return res
        .status(400)
        .json(new ErrorResponse("Assessment Form already available"));
    }
    form = await FormModel.create({
      name: formName,
      question: assessmentForm,
      disciplineId,
      formTypeId,
      createdBy: req.user.id,
      updatedBy: req.user.id,
    });
    logger.debug(`Created new assessment form with id: ${form._id}`);
    return res.status(200).json(new SuccessResponse(form));
  } catch (error) {
    logger.error(`Error listing episodes: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const listEpisode = async (req, res) => {
  try {
    logger.debug(`Listing episodes`);
    const connection = await getTenantDB(req.tenantDb);
    const EpisodeModel = Episode(connection);
    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);
    logger.debug(
      `Query params: ${JSON.stringify(query)}, limit: ${parsedLimit}, offset: ${parsedOffset}`,
    );

    const episode = await EpisodeModel.find(query)
      .limit(parsedLimit)
      .skip(parsedOffset);
    const totalCount = await EpisodeModel.countDocuments(query);
    logger.debug(`Found ${totalCount} episodes`);

    return res.status(201).json(new SuccessResponse(episode, totalCount));
  } catch (error) {
    logger.error(`Error listing episodes: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const listDiscipline = async (req, res) => {
  try {
    logger.debug(`Listing Discipline`);
    const connection = await getTenantDB(req.tenantDb);
    const DisciplineModel = Discipline(connection);
    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);
    logger.debug(
      `Query params: ${JSON.stringify(query)}, limit: ${parsedLimit}, offset: ${parsedOffset}`,
    );

    const discipline = await DisciplineModel.find(query)
      .limit(parsedLimit)
      .skip(parsedOffset);
    const totalCount = await DisciplineModel.countDocuments(query);
    logger.debug(`Found ${totalCount} discipline`);

    return res.status(201).json(new SuccessResponse(discipline, totalCount));
  } catch (error) {
    logger.error(`Error listing Discipline: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const formTypes = async (req, res) => {
  try {
    logger.debug(`Listing Form Types`);
    const connection = await getTenantDB(req.tenantDb);
    const Form_TypeModel = Form_Type(connection);
    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);
    logger.debug(
      `Query params: ${JSON.stringify(query)}, limit: ${parsedLimit}, offset: ${parsedOffset}`,
    );

    const formTypes = await Form_TypeModel.find(query)
      .limit(parsedLimit)
      .skip(parsedOffset);
    const totalCount = await Form_TypeModel.countDocuments(query);
    logger.debug(`Found ${totalCount} Form Types`);

    return res.status(201).json(new SuccessResponse(formTypes, totalCount));
  } catch (error) {
    logger.error(`Error listing Form Types: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const listVisit = async (req, res) => {
  try {
    logger.debug(`Listing visits`);
    const connection = await getTenantDB(req.tenantDb);
    const VisitModel = Visit(connection);
    const clinicianId = req.user.id;

    let sort = { $sort: { updatedAt: -1 } };
    if (
      req.query.status === "To be reviewed" ||
      req.query.status === "In Progress"
    ) {
      req.query.status = "In Progress";
    }
    if (req.query.status === "Completed") {
      req.query.status = {
        $in: ["Completed", "Submitted for Processing"],
      };
    }
    if (req.query.status === "Past Due") {
      sort = { $sort: { visitDate: -1, visitStartTime: -1 } };
    }

    let clientId = "";
    if (req.query.clientId) {
      clientId = req.query.clientId;
      delete req.query.clientId;
    }
    let episodeId = "";
    if (req.query.episodeId) {
      episodeId = req.query.episodeId;
      delete req.query.episodeId;
    }

    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);

    // Default sorting: first by visitDate, then by startTime
    // Note: We'll handle startTime sorting in JavaScript after aggregation
    // since the data might still be in 12-hour format before migration
    if (req.query.status === "New" && req.query.screen === "home") {
      const oneDayBefore = new Date();
      oneDayBefore.setDate(oneDayBefore.getDate() - 1);
      query.visitDate = { $gte: oneDayBefore };
      sort = { $sort: { visitDate: 1 } }; // Only sort by date, handle time in JS
    } else if (req.query.status === "New") {
      sort = { $sort: { visitDate: 1 } }; // Only sort by date, handle time in JS
    } else {
      // Default sorting for all other cases: visitDate ascending
      sort = { $sort: { visitDate: 1 } }; // Only sort by date, handle time in JS
    }

    if (clientId) {
      query.clientId = new mongoose.Types.ObjectId(clientId);
    } else {
      query.clinicianId = new mongoose.Types.ObjectId(clinicianId);
    }

    if (episodeId) {
      query.episodeId = new mongoose.Types.ObjectId(episodeId);
    }

    logger.debug(
      `Query params: ${JSON.stringify(query)}, limit: ${parsedLimit}, offset: ${parsedOffset}`,
    );

    let visit = await VisitModel.aggregate([
      { $match: query },

      // 🔹 Join with episodes table
      {
        $lookup: {
          from: "episodes",
          localField: "episodeId",
          foreignField: "_id",
          as: "episode",
        },
      },
      { $unwind: { path: "$episode", preserveNullAndEmptyArrays: true } },

      // 🔹 Join with clients table
      {
        $lookup: {
          from: "clients",
          localField: "clientId",
          foreignField: "_id",
          as: "client",
        },
      },
      { $unwind: { path: "$client", preserveNullAndEmptyArrays: true } },

      // 🔹 Join with users table to get clinician details
      {
        $lookup: {
          from: "users",
          localField: "clinicianId",
          foreignField: "_id",
          as: "clinician",
        },
      },
      { $unwind: { path: "$clinician", preserveNullAndEmptyArrays: true } },

      // 🔹 Join with user_profile table to get clinician details
      {
        $lookup: {
          from: "user_profiles",
          localField: "clinician._id",
          foreignField: "userId",
          as: "clinicianInfo",
        },
      },
      { $unwind: { path: "$clinicianInfo", preserveNullAndEmptyArrays: true } },

      // 🔹 Project only necessary fields
      {
        $project: {
          _id: 1,
          visitId: "$no",
          visitDate: {
            $dateToString: {
              format: "%Y-%m-%dT%H:%M:%S.%LZ",
              date: { $toDate: "$visitDate" },
            },
          },
          visitStartTime: "$startTime",
          visitType: "$type",
          visitStatus: "$status",
          createdAt: 1,
          updatedAt: 1,
          episodeId: "$episode._id",
          episodeNo: "$episode.no",
          episodeDuration: "$episode.duration",
          episodeStartDate: {
            $dateToString: {
              format: "%Y-%m-%dT%H:%M:%S.%LZ",
              date: { $toDate: "$episode.startDate" },
            },
          },
          episodeEndDate: {
            $dateToString: {
              format: "%Y-%m-%dT%H:%M:%S.%LZ",
              date: { $toDate: "$episode.endDate" },
            },
          },
          clientId: "$client._id",
          clientFirstName: "$client.firstName",
          clientLastName: "$client.lastName",
          clientStaffNo: "$client.no",
          clientGender: "$client.gender",
          clientDob: {
            $dateToString: {
              format: "%m/%d/%Y",
              date: { $toDate: "$client.dateOfBirth" },
            },
          },
          clientAge: {
            $dateDiff: {
              startDate: { $toDate: "$client.dateOfBirth" },
              endDate: "$$NOW",
              unit: "year",
            },
          },
          clientEmail: "$client.email",
          clientPrimaryPhone: "$client.phone",
          clientAddress1: "$client.address.addressLine1",
          clientAddress2: "$client.address.addressLine2",
          clientCity: "$client.address.city",
          clientState: "$client.address.state",
          clientZip: "$client.address.zipcode",
          clientCounty: "$client.address.county",
          clinicianId: "$clinician._id",
          clinicianEmail: "$clinician.email",
          clinicianFirstName: "$clinicianInfo.firstName",
          clinicianLastName: "$clinicianInfo.lastName",
          clinicianGender: "$clinicianInfo.gender",
          clinicianAddress1: "$clinicianInfo.address.addressLine1",
          clinicianAddress2: "$clinicianInfo.address.addressLine2",
          clinicianState: "$clinicianInfo.address.state",
          clinicianZip: "$clinicianInfo.address.zipcode",
          clinicianCounty: "$clinicianInfo.address.county",
        },
      },
      sort,
      { $skip: parsedOffset },
      { $limit: parsedLimit },
    ]);

    // Sort by startTime within each date group (handles both 12-hour and 24-hour formats)
    const convertTimeToMinutes = (timeStr) => {
      if (!timeStr) return 0;

      // Check if it's already in 24-hour format (HH:MM)
      const time24Match = timeStr.match(/^(\d{1,2}):(\d{2})$/);
      if (time24Match) {
        const [, hour, minute] = time24Match;
        return parseInt(hour, 10) * 60 + parseInt(minute, 10);
      }

      // Handle 12-hour format (H:MM AM/PM)
      const time12Match = timeStr.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
      if (!time12Match) return 0;

      // eslint-disable-next-line
      let [, hour, minute, period] = time12Match;
      hour = parseInt(hour, 10);
      minute = parseInt(minute, 10);

      if (period.toUpperCase() === "PM" && hour !== 12) {
        hour += 12;
      } else if (period.toUpperCase() === "AM" && hour === 12) {
        hour = 0;
      }

      return hour * 60 + minute;
    };

    // Sort visits by date first, then by time within each date
    visit.sort((a, b) => {
      // First sort by date
      const dateA = new Date(a.visitDate);
      const dateB = new Date(b.visitDate);

      if (dateA.getTime() !== dateB.getTime()) {
        return dateA - dateB;
      }

      // If dates are the same, sort by time
      const timeA = convertTimeToMinutes(a.visitStartTime);
      const timeB = convertTimeToMinutes(b.visitStartTime);

      return timeA - timeB;
    });

    // Convert 24-hour format back to 12-hour format for frontend
    visit = visit.map((visitItem) => {
      if (visitItem.visitStartTime) {
        visitItem.visitStartTime = convertTo12Hour(visitItem.visitStartTime);
      }
      return visitItem;
    });

    const totalCount = await VisitModel.countDocuments(query);
    logger.debug(`Found ${totalCount} visits`);
    return res.status(201).json(new SuccessResponse(visit, totalCount));
  } catch (error) {
    logger.error(`Error listing visits: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const listAssessment = async (req, res) => {
  try {
    logger.debug(`Listing assessments`);
    const connection = await getTenantDB(req.tenantDb);
    const { visitId, ...restQuery } = req.query;
    const AssessmentModel = Assessment(connection);
    const { query, parsedLimit, parsedOffset } = getFilterQuery(restQuery);
    query.visitId = new mongoose.Types.ObjectId(visitId);
    logger.debug(
      `Query params: ${JSON.stringify(query)}, limit: ${parsedLimit}, offset: ${parsedOffset}`,
    );

    const assessment = await AssessmentModel.find(query)
      .limit(parsedLimit)
      .skip(parsedOffset);

    const totalCount = await AssessmentModel.countDocuments(query);
    logger.debug(`Found ${totalCount} assessments`);
    return res.status(201).json(new SuccessResponse(assessment, totalCount));
  } catch (error) {
    logger.error(`Error listing assessments: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const getAssessmentById = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }
    logger.debug(`Getting assessment by id: ${req.params.id}`);
    const { id } = req.params;
    const { connection } = await startDatabaseSession(req.tenantDb);
    const AssessmentModel = Assessment(connection);
    const assessment = await AssessmentModel.findById(id);
    logger.debug(`Found assessment: ${assessment ? "yes" : "no"}`);
    return res.status(200).json(new SuccessResponse(assessment));
  } catch (error) {
    logger.error(`Error getting assessment by id: ${error.message}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const createNotification = async (
  clinicianId,
  content,
  type,
  connection,
  session,
) => {
  logger.debug(
    `Creating notification for clinician ${clinicianId} of type ${type}`,
  );
  const NotificationTypeModel = Notification_Type(connection);
  const notificationType = await NotificationTypeModel.findOne({
    name: type,
  }).session(session);
  const NotificationModel = Notification(connection);
  const notification = await NotificationModel.create(
    [
      {
        userId: clinicianId,
        notificationTypeId: notificationType._id,
        notificationContent: content,
      },
    ],
    { session },
  );
  logger.debug(`Notification created successfully: ${notification[0]._id}`);
  return notification[0];
};

/** Validation schema for request data */
const validateRequestSchema = (data, schema, path = "") => {
  const errors = [];

  for (const [key, required] of Object.entries(schema)) {
    const currentPath = path ? `${path}.${key}` : key;

    if (
      required &&
      (!data[key] || (typeof data[key] === "string" && data[key].trim() === ""))
    ) {
      errors.push(`Missing required field: ${currentPath}`);
    } else if (
      data[key] &&
      typeof data[key] === "object" &&
      !Array.isArray(data[key])
    ) {
      // Recursively validate nested objects
      const nestedErrors = validateRequestSchema(
        data[key],
        required,
        currentPath,
      );
      errors.push(...nestedErrors);
    }
  }

  return errors;
};

/** Extract request data from req.body */
const extractRequestData = (body) => {
  logger.debug(`Extracting request data from body`);

  // Define validation schema for the body structure
  const schema = {
    episode: {
      episodeNo: true,
      startDate: true,
      endDate: true,
    },
    client: {
      clientNo: true,
      firstName: true,
      lastName: true,
      dateOfBirth: true,
      address: {
        addressLine1: true,
        state: true,
        city: true,
        zipcode: true,
        county: true,
      },
      phone: false, // Optional
      email: false, // Optional
      gender: false, // Optional
      emergencyContact: false, // Optional
      emergencyContactPhone: false, // Optional
    },
    clinician: {
      clinicianNo: true,
    },
    visit: {
      visitId: true,
      visitDate: true,
      visitType: true,
      visitStartTime: false, // Optional
    },
  };

  // Validate the request data
  const validationErrors = validateRequestSchema(body, schema);

  if (validationErrors.length > 0) {
    throw new Error(`Invalid request format: ${validationErrors.join(", ")}`);
  }

  const data = {
    clinician: body.clinician,
    episode: body.episode,
    client: body.client,
    visit: body.visit,
  };

  logger.debug(`Request data extracted successfully`);
  return data;
};

/** Get clinician record from unified userProfiles collection */
const getClinician = async (no, connection, session) => {
  logger.debug(`Getting clinician with staff number: ${no}`);
  const userProfileInfoModel = userProfiles(connection);
  const query = userProfileInfoModel
    .findOne({
      no,
    })
    .session(session);
  logger.info(`Executing Query: ${JSON.stringify(query.getFilter())}`); // Print the query

  const clinician = await query;
  logger.debug(`Found existing clinician: ${clinician ? "yes" : "no"}`);
  if (!clinician) return null;

  // Note: Clinician updates are now handled through the unified userProfiles model
  // with schema-level protection for read-only fields (no, jobTitle, disciplineId, gender, address)
  // Individual field updates should be done through the user management APIs

  return clinician;
};

/** Get or create an episode */
const getOrCreateEpisode = async (data, connection, session) => {
  logger.debug(`Getting/Creating episode with number: ${data.episodeNo}`);
  const EpisodeModel = Episode(connection);

  let episode = await EpisodeModel.findOne({
    no: data.episodeNo,
  }).session(session);
  logger.debug(`Found existing episode: ${episode ? "yes" : "no"}`);

  if (!episode) {
    let attempts = 0;
    const maxAttempts = 3;
    // Date format is MM/DD/YYYY
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    while (attempts < maxAttempts) {
      try {
        episode = await EpisodeModel.create(
          [
            {
              no: Number(data.episodeNo),
              duration:
                startDate <= endDate
                  ? Math.round((endDate - startDate) / (1000 * 60 * 60 * 24)) +
                    1
                  : 0,
              startDate,
              endDate,
            },
          ],
          { session },
        );
        break;
      } catch (error) {
        attempts++;
        logger.error(
          `Error creating episode (attempt ${attempts}): ${error.message}`,
        );
        if (attempts >= maxAttempts) {
          throw new Error(
            "Error creating episode: Please retry your operation or multi-document transaction.",
          );
        }
        // eslint-disable-next-line
        await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for 1 second before retrying
      }
    }
    episode = episode[0];
    logger.debug(`Created new episode: ${episode._id}`);
  }

  return episode;
};

/** Get or update the client record */
const getOrUpdateClient = async (data, connection, session) => {
  logger.debug(`Getting/Updating client with number: ${JSON.stringify(data)}`);
  const Client_InfoModel = Client_Info(connection);

  // Try to find and update the client in one step
  const updateFields = {
    firstName: data.firstName,
    lastName: data.lastName,
    dateOfBirth: new Date(data.dateOfBirth),
    gender: data.gender,
    phone: data.phone,
    email: data.email,
    emergencyContact: data.emergencyContact,
    emergencyContactPhone: data.emergencyContactPhone,
    address: {
      addressLine1: data.address.addressLine1,
      addressLine2: data.address.addressLine2,
      state: data.address.state,
      city: data.address.city,
      zipcode: data.address.zipcode,
      county: data.address.county,
    },
    createdBy: data.createdBy || "60c72b2f9b9bc04d8c456789",
    updatedBy: data.updatedBy || "60c72b2f9b9bc04d8c456789",
  };

  const client = await Client_InfoModel.findOneAndUpdate(
    { no: data.clientNo },
    { $set: updateFields },
    { new: true, upsert: true, session, setDefaultsOnInsert: true },
  );

  logger.debug(`Found or created client: ${client ? client._id : "none"}`);
  return client;
};

/** Create a new visit record */
const createVisitRecord = async (
  data,
  clinicianId,
  episodeId,
  clientId,
  connection,
  session,
  orchestratorQueueName = null,
) => {
  logger.debug(`Creating visit record with number: ${data.visitId}`);
  const VisitModel = Visit(connection);

  const existingVisit = await VisitModel.findOne({
    no: data.visitId,
  }).session(session);
  logger.debug(`Found existing visit: ${existingVisit ? "yes" : "no"}`);
  if (existingVisit) {
    throw new Error(`Visit with ID ${data.visitId} already exists`);
  }

  const visitData = {
    episodeId,
    clinicianId,
    clientId,
    no: data.visitId,
    visitDate: data.visitDate,
    startTime: convertTo24Hour(data.visitStartTime),
    type: data.visitType,
    status: "New",
  };

  // Add orchestratorQueue if provided, otherwise use default
  if (orchestratorQueueName) {
    visitData.orchestratorQueue = orchestratorQueueName;
  }

  const visit = await VisitModel.create([visitData], { session });
  logger.debug(`Created new visit: ${visit[0]._id}`);
  return visit[0];
};

/** Get or create a form */
const getOrCreateForm = async (disciplineId, connection, session) => {
  logger.debug(`Getting/Creating form`);
  const FormModel = Form(connection);

  const forms = await FormModel.find({
    disciplineId,
    isPublished: true,
    isDeleted: false,
  }).session(session);
  logger.debug(`Found form: ${forms ? forms.length : "not found"}`);
  if (!forms) throw new Error("Form not found");

  return forms;
};

/** Create an assessment record */
const createAssessment = async (forms, visitId, connection, session) => {
  const assessments = await Promise.all(
    forms.map(async (form) => {
      logger.debug(
        `Creating assessment for form ${form.id} and visit ${visitId}`,
      );
      const AssessmentModel = Assessment(connection);

      const existingAssessment = await AssessmentModel.findOne({
        formId: form.id,
        visitId,
      }).session(session);
      if (existingAssessment) return existingAssessment;

      const assessment = await AssessmentModel.create(
        [
          {
            formId: form.id,
            visitId,
            question: form.question,
            name: form.name,
          },
        ],
        {
          session,
        },
      );
      logger.debug(`Created assessment: ${assessment[0]._id}`);
      return assessment[0];
    }),
  );
  return assessments;
};

const createVisit = async (req, res) => {
  logger.debug(`Creating visit`);
  const { connection, session } = await startDatabaseSession(req.tenantDb);

  try {
    const requestData = extractRequestData(req.body);
    logger.debug(`Request data: ${JSON.stringify(requestData)}`);

    // Get orchestratorQueueName from header or body
    const orchestratorQueueName =
      req.headers["orchestrator-queue-name"] ||
      requestData?.orchestratorQueueName;
    logger.debug(`Orchestrator queue name: ${orchestratorQueueName}`);

    const clinician = await getClinician(
      requestData?.clinician?.clinicianNo,
      connection,
      session,
    );
    logger.debug(
      `Found/Updated clinician: ${clinician ? clinician._id : "not found"}`,
    );
    if (!clinician)
      return res
        .status(404)
        .json(
          new ErrorResponse(
            `Clinician not found for ${requestData?.clinician?.clinicianNo} on tenant ${req.tenantDb}`,
          ),
        );

    const episode = await getOrCreateEpisode(
      requestData?.episode,
      connection,
      session,
    );
    logger.debug(`Found/Created episode: ${episode._id}`);

    const client = await getOrUpdateClient(
      requestData?.client,
      connection,
      session,
    );
    logger.debug(`Found/Updated client: ${client._id}`);

    const visit = await createVisitRecord(
      requestData?.visit,
      clinician.userId,
      episode._id,
      client._id,
      connection,
      session,
      orchestratorQueueName,
    );
    logger.debug(`Created visit: ${visit._id}`);

    const forms = await getOrCreateForm(
      clinician.disciplineId,
      connection,
      session,
    );

    await createAssessment(forms, visit.id, connection, session);
    logger.debug(`Created assessment`);

    await createNotification(
      clinician.userId,
      `New Visit created for ${client.firstName} ${client.lastName}`,
      "New Visits",
      connection,
      session,
    );
    logger.debug(`Created notification`);

    await session.commitTransaction();
    logger.debug(`Transaction committed successfully`);
    return res.status(201).json(new SuccessResponse(visit));
  } catch (error) {
    sendAlertEmail(error.message);
    logger.error(`Error creating visit: ${error.message}`);
    if (session && session.inTransaction()) {
      await session.abortTransaction();
      logger.debug(`Aborted database transaction`);
    }
    res.status(500).json(new ErrorResponse(error?.message || error));
  } finally {
    if (session) {
      await session.endSession();
      logger.debug(`Ended database session`);
    }
  }
};

const createVisitFromRPA = async (err, data) => {
  // {
  //   "tenantDb":"haggiehealth",
  //   "body":{
  //     "Care Type": "Intermitent",
  //     "Classification": "Home Safety Check,Skin Check",
  //     "Client First Name": "Connie",
  //     "Client Last Name": "Wimpee",
  //     "Client Address 1": "6331 W Cholla St",
  //     "Client Address 2": "string",
  //     "Client Age": "72",
  //     "Client Cell": "string",
  //     "Client City": "Glendale",
  //     "Client County": "Maricopa",
  //     "Client DOB": "10/10/1952",
  //     "Client Email": "string",
  //     "Client Emergency Contact": "Wimpee, William",
  //     "Client Emergency Contact #": "(*************",
  //     "Client ID": 71251,
  //     "Client Group ID": 71251,
  //     "Client LOB": "Adult Skilled",
  //     "Client Notes": "string",
  //     "Client Phone": "(*************",
  //     "Client Signed": "No",
  //     "Client Signed Date": "string",
  //     "Client State": "AZ",
  //     "Clinician First Name": "Amber",
  //     "Clinician Last Name": "Miller",
  //     "Clinician Address 1": "409 E. Colter Street",
  //     "Clinician Cell": "(*************",
  //     "Clinician City": "Phoenix",
  //     "Clinician Confirmed": "No",
  //     "Clinician Date of Birth": "11/11/1976",
  //     "Clinician Discipline": "PT",
  //     "Clinician Email": "<EMAIL>",
  //     "Clinician Gender": "Female",
  //     "Clinician ID": "00045",
  //     "Clinician SSN": "***********",
  //     "Clinician State": "AZ",
  //     "Clinician Status": "Active",
  //     "Clinician Zip": 85012,
  //     "eChart Status": "Open",
  //     "Episode Duration": 60,
  //     "Episode Number": 1,
  //     "Episode Start Date": "02/13/2025",
  //     "Episode End Date": "04/13/2025",
  //     "EVV Visit": "No",
  //     "Intake Status": "Ready to Schedule",
  //     "Month": "2025 - February",
  //     "Non-Billable Hours": 0,
  //     "Referral Date": "02/11/2025",
  //     "Schedule Status": "Planned",
  //     "Service": "PT SOC OASIS",
  //     "Service Code": "PT SOC",
  //     "SOC Date": "02/13/2025",
  //     "Visit Date": "02/13/2025",
  //     "Visit ID": 4192054,
  //     "Visit Type": "Normal",
  //     "Week": "02/09/2025 - 02/15/2025",
  //     "Weekday": "Thursday"
  //   }
  //   }
  let msg;

  try {
    logger.debug(`Creating visit from RPA`);
    if (err) {
      logger.error(`message container error: ${err.toString()}`);
      return {
        msgStatus: false,
        reason: `message contains error: ${err.toString()}`,
      };
    }
    if (data) {
      msg = JSON.parse(data.Body);
      logger.debug(`Create Visit message body: ${data.Body}`);
      if (!msg) {
        logger.debug(
          `Something wrong happened during verification. Most probably object does not exist.`,
        );
        throw new Error(
          `Something wrong happened during verification. Most probably object does not exist.`,
        );
      }
    }
    const { connection, session } = await startDatabaseSession(msg.tenantDb);

    try {
      const requestData = extractRequestData(msg.body);
      logger.debug(`Request data: ${JSON.stringify(requestData)}`);

      // Get orchestratorQueueName from message
      const orchestratorQueueName = msg.orchestratorQueueName;
      logger.debug(`Orchestrator queue name: ${orchestratorQueueName}`);

      const clinician = await getClinician(
        requestData?.clinician?.clinicianNo,
        connection,
        session,
      );
      logger.debug(
        `Found/Updated clinician: ${clinician ? clinician._id : "not found"}`,
      );
      if (!clinician)
        throw new Error(
          `Clinician not found for ${requestData?.clinician?.clinicianNo} on tenant ${msg.tenantDb}`,
        );

      const episode = await getOrCreateEpisode(
        requestData?.episode,
        connection,
        session,
      );
      logger.debug(`Found/Created episode: ${episode._id}`);

      const client = await getOrUpdateClient(
        requestData?.client,
        connection,
        session,
      );
      logger.debug(`Found/Updated client: ${client._id}`);

      const visit = await createVisitRecord(
        requestData?.visit,
        clinician.userId,
        episode._id,
        client._id,
        connection,
        session,
        orchestratorQueueName,
      );
      logger.debug(`Created visit: ${visit ? visit._id : "failed"}`);
      if (!visit) throw new Error("Visit already exists");

      const forms = await getOrCreateForm(
        clinician.disciplineId,
        connection,
        session,
      );

      await createAssessment(forms, visit.id, connection, session);
      logger.debug(`Created assessment`);

      await createNotification(
        clinician.userId,
        `New visit created for ${client.firstName} ${client.lastName}`,
        "New Visits",
        connection,
        session,
      );
      logger.debug(`Created notification`);

      await session.commitTransaction();
      logger.debug(`Transaction committed successfully`);
      return { msgStatus: true, reason: "Visit created successfully" };
    } catch (error) {
      logger.error(`Error in transaction: ${error.message}`);
      if (session && session.inTransaction()) {
        await session.abortTransaction();
        logger.debug(`Aborted database transaction`);
      }
      throw error;
    } finally {
      if (session) {
        await session.endSession();
        logger.debug(`Ended database session`);
      }
    }
  } catch (error) {
    sendAlertEmail(error.message);
    logger.error(`message container error: ${error.toString()}`);
    await pushToQueue(process.env.RPA_DATA_QUEUE_DLQ, {
      msgStatus: false,
      reason: error.message,
      tenantDb: msg.tenantDb,
      body: msg.body,
    });
    await deleteMessageFromQueue(process.env.RPA_DATA_QUEUE, data);
    return { msgStatus: false, reason: error.message };
  } finally {
    await deleteMessageFromQueue(process.env.RPA_DATA_QUEUE, data);
  }
};

const updateVisit = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }
    logger.debug(`Updating visit with id: ${req.params.id}`);
    const { connection } = await startDatabaseSession(req.tenantDb);
    const VisitModel = Visit(connection);
    const visit = await VisitModel.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    });
    logger.debug(`Updated visit: ${visit.id}`);
    return res.status(200).json(new SuccessResponse(visit));
  } catch (err) {
    res.status(404).json(new ErrorResponse(err));
  }
};

const updateAssessment = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }
    logger.debug(`Updating assessment with id: ${req.params.id}`);
    const { connection } = await startDatabaseSession(req.tenantDb);
    const AssessmentModel = Assessment(connection);
    req.body.updatedBy = req.user.id;
    const assessment = await AssessmentModel.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
      },
    );
    logger.debug(`Updated assessment: ${assessment._id}`);

    if (req.body.status === "Submitted to EMR") {
      const rpaInput = transformAssessmentForRPA(assessment.question);
      const visitId = assessment.visitId;
      const VisitModel = Visit(connection);
      const visitRecord = await VisitModel.aggregate([
        {
          $match: { _id: visitId },
        },
        {
          $lookup: {
            from: "clients",
            localField: "clientId",
            foreignField: "_id",
            as: "clientInfo",
          },
        },
        {
          $unwind: "$clientInfo",
        },
        {
          $project: {
            _id: 1,
            clientId: 1,
            status: 1,
            type: 1,
            "clientInfo._id": 1,
            "clientInfo.no": 1,
            "clientInfo.firstName": 1,
            "clientInfo.lastName": 1,
            "clientInfo.dateOfBirth": 1,
          },
        },
      ]).exec();

      // Get form type name from assessment
      const FormModel = Form(connection);
      const Form_TypeModel = Form_Type(connection);
      const form = await FormModel.findById(assessment.formId);
      const formType = await Form_TypeModel.findById(form.formTypeId);

      if (!visitRecord || visitRecord.length === 0) {
        logger.error(`Visit record not found for visitId: ${visitId}`);
        throw new Error(`Visit record not found for visitId: ${visitId}`);
      }

      const clientInfo = visitRecord[0].clientInfo; // Access the joined clientInfo

      if (!clientInfo) {
        logger.error(
          `Client info not found for clientId: ${visitRecord[0].clientId}`,
        );
        throw new Error(
          `Client info not found for clientId: ${visitRecord[0].clientId}`,
        );
      }
      logger.debug(`Found client info: ${JSON.stringify(clientInfo)}`);

      // Get orchestratorQueueName from visit record
      const visitWithQueue = await VisitModel.findById(
        assessment.visitId,
      ).select("orchestratorQueue");
      const orchestratorQueueName =
        visitWithQueue?.orchestratorQueue || process.env.UIPATH_QUEUE_NAME;

      const message = {
        clientNo: clientInfo.no || clientInfo.clientId,
        clientFirstName: clientInfo.firstName,
        clientLastName: clientInfo.lastName,
        clientDob: formatDateToMMDDYYYY(clientInfo.dateOfBirth),
        tenantDb: req.tenantDb,
        visitType: visitRecord[0].type || visitRecord[0].visitType,
        formType: formType.name,
        assessmentId: assessment.id,
        assessmentAnswer: rpaInput,
      };
      logger.info(`Publishing message to ${orchestratorQueueName}`);
      await sendMessageToUIPath(message, orchestratorQueueName);

      const allAssessments = await AssessmentModel.find({
        visitId: assessment.visitId,
      });
      const allSubmittedToEMR = allAssessments.every(
        (a) => a.status === "Submitted to EMR",
      );
      const allCompleted = allAssessments.every(
        (a) => a.status === "Completed",
      );

      logger.debug(`allSubmittedToEMR: ${allSubmittedToEMR}`);
      logger.debug(`allCompleted: ${allCompleted}`);

      if (allCompleted) {
        await VisitModel.findByIdAndUpdate(assessment.visitId, {
          status: "Completed",
        });
      } else if (allSubmittedToEMR) {
        await VisitModel.findByIdAndUpdate(assessment.visitId, {
          status: "Submitted for Processing",
        });
      }
    }
    return res.status(200).json(new SuccessResponse(assessment));
  } catch (err) {
    res.status(404).json(new ErrorResponse(err));
  }
};

const processAIOutput = async (err, data) => {
  // Sample message
  // let b = {
  //   "id": null,
  //   "user_id": "<EMAIL>",
  //   "client_id": "67ef3410abe5fd7a0a8bc750",
  //   "assessment_id": "67ef34baabe5fd7a0a8bc8e8",
  //   "company_id": "haggaihealth",
  //   "visit_id": "67ef34baabe5fd7a0a8bc8da",
  //   "transcribe_type": "deepgram",
  //   "status": "completed",
  //   "started": **********,
  //   "completed": **********,
  //   "answer_files": [
  //     "haggaihealth/67ef34bbabe5fd7a0a8bc8f8/67ef34bbabe5fd7a0a8bc906/output/answers.json"
  //   ],
  //   "transcription_files": [
  //     "haggiehealth/67d551267e49f4aed9a2d8e6/67d551267e49f4aed9a2d8eb/output/transcription.json"
  //   ],
  //   "conversation_files": [
  //     "haggiehealth/67d551267e49f4aed9a2d8e6/67d551267e49f4aed9a2d8eb/output/conversation.json"
  //   ]
  // };
  try {
    let msg;

    logger.debug(`Processing AI output`);
    if (err) {
      logger.error(`message container error: ${err.toString()}`);
      return {
        msgStatus: false,
        reason: `message contains error: ${err.toString()}`,
      };
    }

    if (data) {
      msg = JSON.parse(data.Body);
    }

    const { connection, session } = await startDatabaseSession(msg.company_id);
    try {
      const AssessmentModel = Assessment(connection);
      const VisitModel = Visit(connection);
      const assessment = await AssessmentModel.findById(
        msg.assessment_id,
      ).populate({
        path: "visitId",
        model: VisitModel,
        select: "clinicianId visitId clientId",
      });

      const client = await Client_Info(connection).findById(
        assessment.visitId.clientId,
      );

      if (msg.status === "error") {
        await AssessmentModel.updateOne(
          { _id: msg.assessment_id },
          { $set: { status: "AI Processing Failed", reason: msg.exception } },
        );
        await createNotification(
          assessment.visitId.clinicianId,
          `AI processing failed on ${assessment.name} for ${client.firstName || ""} ${client.lastName || ""}. Reason: ${msg.exception}`,
          "AI Processing Failed",
          connection,
          session,
        );
        await session.commitTransaction();
        return { msgStatus: false, reason: msg.exception };
      }

      if (!assessment) {
        logger.error(
          `Assessment not found: ${msg.assessment_id} for tenant: ${msg.company_id}`,
        );
        throw new Error("Assessment not found");
      }

      let jsonContent;
      try {
        logger.debug(
          `Downloading file from S3: ${process.env.S3_BUCKET}/${msg.answer_files[0]}`,
        );
        const s3Response = await downloadFile(
          process.env.S3_BUCKET,
          msg.answer_files[0],
        );
        // Convert S3 response buffer to string and store JSON content
        const responseBuffer = s3Response.Body;
        if (!(responseBuffer instanceof Buffer)) {
          throw new Error("S3 response body is not a buffer");
        }
        jsonContent = responseBuffer.toString("utf-8");
        msg.aiOutput = JSON.parse(jsonContent);
        logger.debug(`Successfully parsed AI output`);
      } catch (error) {
        logger.error(
          `Error downloading from S3: ${error.toString()} for tenant: ${msg.company_id}`,
        );
        throw error;
      }

      const answers = msg.aiOutput.Responses;
      const answersMap = new Map();
      answers.forEach((answer) => {
        answersMap.set(answer.question_code, {
          answer_context: answer.answer_context,
          answer_text: answer.answer_text,
          answer_code: answer.answer_code,
        });
      });

      const processedAnswer = updateQuestions(assessment.question, answersMap);

      // logger.debug(`Processed answer: ${JSON.stringify(processedAnswer)}`);

      await AssessmentModel.updateOne(
        { _id: msg.assessment_id },
        {
          $set: {
            question: processedAnswer,
            answer: answers,
            status: "Ready for Review",
            reason: "",
          },
        },
      );

      const notification = await createNotification(
        assessment.visitId.clinicianId,
        `Your ${assessment.name} for ${client.firstName || ""} ${client.lastName || ""} is ready for review`,
        "AI Process Success",
        connection,
        session,
      );
      logger.debug(`Created notification: ${notification._id}`);
      await session.commitTransaction();
      return true;
    } catch (error) {
      logger.error(`Message container error: ${error.toString()}`);
      if (session && session.inTransaction()) {
        await session.abortTransaction();
        logger.debug(
          `Aborted database transaction for tenant: ${msg.company_id}`,
        );
      }
      await pushToQueue(process.env.AI_OUTPUT_DLQ_QUEUE_URL, {
        msgStatus: false,
        reason: error.message,
        body: data.Body,
      });
      sendAlertEmail(error);
      return { msgStatus: false, reason: error.message };
    } finally {
      if (session) {
        await session.endSession();
        logger.debug(`Ended database session for tenant: ${msg.company_id}`);
      }
      await deleteMessageFromQueue(process.env.AI_OUTPUT_QUEUE_URL, data);
      logger.debug(`Deleted message from queue for tenant: ${msg.company_id}`);
    }
  } catch (error) {
    logger.error(`Error parsing AI output: ${error.toString()}`);
    await pushToQueue(process.env.AI_OUTPUT_DLQ_QUEUE_URL, {
      msgStatus: false,
      reason: error.message,
      body: data.Body,
    });
    await deleteMessageFromQueue(process.env.AI_OUTPUT_QUEUE_URL, data);
    sendAlertEmail(error);
    return { msgStatus: false, reason: error.message };
  }
};

const updateAssessmentFromRPA = async (err, data) => {
  let msg;
  // {
  //   "tenantDb":"localtesting",
  //   "assessmentId":"67eefb3c4128e538122af83c",
  //   "visitId":"67eefb3c4128e538122af837",
  //   "status":"Completed/EMR Processing Failed",
  //   "reason":"Success"
  // }
  logger.debug(`Updating assessment from RPA`);
  if (err) {
    logger.error(`message container error: ${err.toString()}`);
    return {
      msgStatus: false,
      reason: `message contains error: ${err.toString()}`,
    };
  }
  if (data) {
    msg = JSON.parse(data.Body);
    logger.debug(`Update Assessment message body: ${data.Body}  `);
  }

  const { connection, session } = await startDatabaseSession(msg.tenantDb);
  const AssessmentModel = Assessment(connection);
  const VisitModel = Visit(connection);
  try {
    const assessment = await AssessmentModel.findByIdAndUpdate(
      msg.assessmentId,
      { status: msg.status },
      {
        new: true,
      },
    ).populate({
      path: "visitId",
      model: VisitModel,
      select: "clinicianId clientId visitId",
    });
    logger.debug(`Updated assessment status to Completed: ${assessment._id}`);

    const assessments = await AssessmentModel.find({
      visitId: assessment.visitId._id,
    }).populate({
      path: "visitId",
      model: VisitModel,
      select: "clinicianId visitId",
    });

    const Client_InfoModel = Client_Info(connection);
    const client = await Client_InfoModel.findById(
      assessment.visitId.clientId,
    ).session(session);
    if (!client) {
      logger.error(`Client not found for ID: ${assessment.visitId.clientId}`);
      throw new Error(
        `Client not found for ID: ${assessment.visitId.clientId}`,
      );
    }
    logger.debug(`Found client: ${client.firstName} ${client.lastName}`);

    const allSubmittedToEMR = assessments.every(
      (a) => a.status === "Submitted to EMR" || a.status === "Completed",
    );
    const allCompleted = assessments.every((a) => a.status === "Completed");

    logger.debug(`allSubmittedToEMR: ${allSubmittedToEMR}`);
    logger.debug(`allCompleted: ${allCompleted}`);

    // if ("EMR Processing Failed") {
    //   await VisitModel.findByIdAndUpdate(msg.visitId, {
    //     status: "Needs Attention",
    //   });
    // } else
    if (allCompleted) {
      await VisitModel.findByIdAndUpdate(msg.visitId, {
        status: "Completed",
      });
    } else if (allSubmittedToEMR) {
      await VisitModel.findByIdAndUpdate(msg.visitId, {
        status: "Submitted for Processing",
      });
    } else {
      await VisitModel.findByIdAndUpdate(msg.visitId, {
        status: "Needs Attention",
      });
    }
    await createNotification(
      assessment.visitId.clinicianId,
      `Hooray! We completed the visit for ${client.firstName} ${client.lastName}!`,
      "Visit Completed",
      connection,
      session,
    );
    await session.commitTransaction();

    return true;
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    if (session && session.inTransaction()) {
      await session.abortTransaction();
      logger.debug(`Aborted database transaction`);
    }
    await pushToQueue(process.env.RPA_UPDATE_DLQ_QUEUE_URL, {
      msgStatus: false,
      reason: error.message,
      body: data.Body,
    });
    sendAlertEmail(error);
    return { msgStatus: false, reason: error.message };
  } finally {
    if (session) {
      await session.endSession();
      logger.debug(`Ended database session`);
    }
    await deleteMessageFromQueue(process.env.RPA_UPDATE_QUEUE_URL, data);
  }
};

const getForm = async (req, res) => {
  try {
    const { connection } = await startDatabaseSession(req.tenantDb);
    const FormModel = Form(connection);
    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);
    query.isDeleted = false;
    logger.debug(
      `Query params: ${JSON.stringify(query)}, limit: ${parsedLimit}, offset: ${parsedOffset}`,
    );

    const form = await FormModel.find(query)
      .limit(parsedLimit)
      .skip(parsedOffset);
    if (!form) return res.status(404).json(new ErrorResponse("Form not found"));

    const totalCount = await FormModel.countDocuments(query);

    return res.status(200).json(new SuccessResponse(form, totalCount));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const getFormTemplate = async (req, res) => {
  try {
    const { connection } = await startDatabaseSession(req.tenantDb);
    const Form_TemplateModel = Form_Template(connection);
    // eslint-disable-next-line
    const DisciplineModel = Discipline(connection);
    // eslint-disable-next-line
    const Form_TypeModel = Form_Type(connection);
    const { query, parsedLimit, parsedOffset } = getFilterQuery(req.query);
    logger.debug(
      `Query params: ${JSON.stringify(query)}, limit: ${parsedLimit}, offset: ${parsedOffset}`,
    );
    const formTemplate = await Form_TemplateModel.find(query)
      .populate({
        path: "disciplineId",
        model: "Discipline",
        select: "name",
      })
      .populate({
        path: "formTypeId",
        model: "Form_Type",
        select: "name",
      })
      .limit(parsedLimit)
      .skip(parsedOffset);
    if (!formTemplate)
      return res
        .status(404)
        .json(new ErrorResponse("Form Templates not found"));
    const totalCount = await Form_TemplateModel.countDocuments(query);

    return res.status(200).json(new SuccessResponse(formTemplate, totalCount));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const getFormbyId = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }
    const { connection } = await startDatabaseSession(req.tenantDb);
    const FormModel = Form(connection);
    const form = await FormModel.findById(req.params.id);
    if (!form)
      return res
        .status(404)
        .json(new ErrorResponse(`Form not found for id: ${req.params.id}`));

    return res.status(200).json(new SuccessResponse(form));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const getFormTemplatebyId = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }
    const { connection } = await startDatabaseSession(req.tenantDb);
    const Form_TemplateModel = Form_Template(connection);
    const formTemplate = await Form_TemplateModel.findById(req.params.id);
    if (!formTemplate)
      return res
        .status(404)
        .json(
          new ErrorResponse(`Form Template not found for id: ${req.params.id}`),
        );

    return res.status(200).json(new SuccessResponse(formTemplate));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const updateForm = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(
          new ErrorResponse(
            `Please specify id in parameter for form template: ${req.params.id}`,
          ),
        );
    }
    const { connection } = await startDatabaseSession(req.tenantDb);
    const FormModel = Form(connection);
    req.body.updatedBy = req.user.id;
    const form = await FormModel.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    });
    return res.status(200).json(new SuccessResponse(form));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const updateFormTemplate = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }
    const { connection } = await startDatabaseSession(req.tenantDb);
    const Form_TemplateModel = Form_Template(connection);
    req.body.updatedBy = req.user.id;
    const formTemplate = await Form_TemplateModel.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
      },
    );
    return res.status(200).json(new SuccessResponse(formTemplate));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const deleteVisit = async (req, res) => {
  try {
    const { no } = req.body;

    const { connection } = await startDatabaseSession(req.tenantDb);
    const VisitModel = Visit(connection);

    const visit = await VisitModel.find({ no });
    if (visit.length === 0) {
      return res.status(404).json(new ErrorResponse("Visit not found"));
    }

    const AssessmentModel = Assessment(connection);
    await AssessmentModel.deleteMany({
      visitId: visit[0]._id,
    });

    await VisitModel.findByIdAndDelete(visit[0]._id);
    return res
      .status(200)
      .json(new SuccessResponse({ message: "Visit Deleted Successfully" }));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const deleteForm = async (req, res) => {
  try {
    if (!req.params.id) {
      return res
        .status(500)
        .json(new ErrorResponse("Please specify id in parameter"));
    }
    const { connection } = await startDatabaseSession(req.tenantDb);

    const AssessmentModel = Assessment(connection);
    const assessments = await AssessmentModel.find({
      formId: req.params.id,
    });
    if (assessments.length > 0) {
      return res
        .status(400)
        .json(
          new ErrorResponse(
            `Form is being used by ${assessments.length} assessments`,
          ),
        );
    }

    const FormModel = Form(connection);
    await FormModel.findByIdAndUpdate(
      req.params.id,
      { isDeleted: true, updatedBy: req.user.id },
      {
        new: true,
      },
    );
    return res
      .status(200)
      .json(new SuccessResponse({ message: "Form Deleted Successfully" }));
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return res.status(500).json(new ErrorResponse(error.message));
  }
};

const markVisitPastDue = async () => {
  try {
    const tenants = await Tenant.find();
    logger.debug(`Found ${tenants.length} tenants`);

    tenants.forEach(async (tenant) => {
      logger.debug(
        `Marking visits past due for tenant: ${tenant.databaseName} | Time: ${new Date()}`,
      );
      const connection = await getTenantDB(tenant.databaseName);
      const VisitModel = Visit(connection);
      const notInclue = [
        "New",
        "Completed",
        "Submitted for Processing",
        "Past Due",
        "Missed",
      ];
      const visits = await VisitModel.find({
        status: {
          $exists: true,
          $ne: null,
          $nin: notInclue,
        },
        visitDate: { $lt: new Date() },
      });
      logger.debug(
        `Found ${visits.length} visits to mark as past due in tenant ${tenant.databaseName}`,
      );
      visits.forEach(async (visit) => {
        const lastUpdatedTime = visit.updatedAt;
        const now = new Date();
        const timeDifference = now - lastUpdatedTime;
        const hoursDifference = timeDifference / (1000 * 60 * 60);
        logger.debug(
          `The visit ${visit._id} with status ${visit.status} | last updated ${visit.updatedAt} | ${hoursDifference} hours old`,
        );
        if (hoursDifference >= 48 && !notInclue.includes(visit.status)) {
          logger.debug(`Marking visit ${visit._id} as past due`);
          await VisitModel.findByIdAndUpdate(visit._id, {
            status: "Past Due",
          });

          const Client_InfoModel = Client_Info(connection);
          const client = await Client_InfoModel.findById(visit.clientId);
          if (!client) {
            logger.error(`Client not found for ID: ${visit.clientId}`);
            throw new Error(`Client not found for ID: ${visit.clientId}`);
          }
          logger.debug(`Found client: ${client.firstName} ${client.lastName}`);

          await createNotification(
            visit.clinicianId,
            `Visit for ${client.firstName} ${client.lastName} is past due`,
            "Visit Past Due",
            connection,
          );
        } else {
          logger.debug(`The visit ${visit._id} is not Past Due`);
        }
      });
    });
  } catch (error) {
    logger.error(`message container error: ${error.toString()}`);
    return new ErrorResponse(error.message);
  }
};

module.exports = {
  createForm,
  createVisit,
  formTypes,
  listVisit,
  listEpisode,
  listAssessment,
  createVisitFromRPA,
  updateVisit,
  updateAssessment,
  getAssessmentById,
  processAIOutput,
  updateAssessmentFromRPA,
  getForm,
  getFormTemplate,
  updateForm,
  markVisitPastDue,
  getFormbyId,
  deleteForm,
  getFormTemplatebyId,
  listDiscipline,
  updateFormTemplate,
  deleteVisit,
};
