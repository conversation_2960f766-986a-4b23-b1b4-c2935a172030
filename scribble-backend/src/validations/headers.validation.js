const { z } = require("zod");

// Generic header validation for v2 API - only required headers, no regex validation
const v2HeadersSchema = z.object({
  "x-tenant-id": z
    .string({ required_error: "Tenant ID is required" })
    .min(1, "Tenant ID is required"),

  "x-device-id": z
    .string({ required_error: "Device ID is required" })
    .min(1, "Device ID is required"),

  "x-device-model": z
    .string({ required_error: "Device model is required" })
    .min(1, "Device model is required"),

  "x-platform": z
    .string({ required_error: "Platform is required" })
    .min(1, "Platform is required"),

  "x-os-version": z
    .string({ required_error: "OS version is required" })
    .min(1, "OS version is required"),

  "x-app-version": z
    .string({ required_error: "App version is required" })
    .min(1, "App version is required"),

  "x-ip-address": z
    .string({ required_error: "IP address is required" })
    .min(1, "IP address is required"),

  "x-timezone": z
    .string({ required_error: "Timezone is required" })
    .min(1, "Timezone is required"),
});

// Optional headers schema (for endpoints that don't require all headers)
const optionalV2HeadersSchema = v2HeadersSchema.partial();

// Export validation schemas
module.exports = {
  v2HeadersSchema,
  optionalV2HeadersSchema,
};
