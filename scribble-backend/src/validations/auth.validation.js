const { z } = require("zod");

// Base schemas for reusability - User-friendly but secure error messages
const emailSchema = z
  .string({ required_error: "Please enter your email address" })
  .min(1, "Please enter your email address")
  .max(254, "Email address is too long")
  .email("Please enter a valid email address")
  .toLowerCase()
  .trim();

const passwordSchema = z
  .string({ required_error: "Please enter your password" })
  .min(1, "Please enter your password");
// .min(12, 'Password must be at least 12 characters long')
// .max(64, 'Password is too long')
// .regex(/[A-Z]/, 'Password must include at least one uppercase letter')
// .regex(/[a-z]/, 'Password must include at least one lowercase letter')
// .regex(/[0-9]/, 'Password must include at least one number')
// .regex(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/, 'Password must include at least one special character');

// Common field schemas based on your existing patterns - User-friendly error messages
const firstNameSchema = z
  .string({ required_error: "Please enter your first name" })
  .min(1, "Please enter your first name")
  .max(50, "First name is too long")
  .regex(/^[a-zA-Z\s]+$/, "First name can only contain letters and spaces");

const lastNameSchema = z
  .string({ required_error: "Please enter your last name" })
  .min(1, "Please enter your last name")
  .max(50, "Last name is too long")
  .regex(/^[a-zA-Z\s]+$/, "Last name can only contain letters and spaces");

const phoneSchema = z
  .string()
  .regex(/^\+?[\d\s\-()]+$/, "Please enter a valid phone number")
  .max(20, "Phone number is too long")
  .optional();

const employeeIdSchema = z
  .string({ required_error: "Please enter your employee ID" })
  .min(1, "Please enter your employee ID")
  .max(20, "Employee ID is too long");

const tenantNameSchema = z
  .string({ required_error: "Please enter a tenant name" })
  .min(1, "Please enter a tenant name")
  .max(100, "Tenant name is too long")
  .regex(
    /^[a-zA-Z0-9\s\-_]+$/,
    "Tenant name can only contain letters, numbers, spaces, hyphens, and underscores",
  );

// Login validation schema
const loginSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

// Query parameter schemas
const paginationSchema = z.object({
  page: z.string().regex(/^\d+$/, "Page must be a number").optional(),
  limit: z.string().regex(/^\d+$/, "Limit must be a number").optional(),
  sort: z.enum(["asc", "desc"]).optional(),
  search: z.string().max(100, "Search term is too long").optional(),
});

const filterSchema = z.object({
  status: z.enum(["active", "inactive", "all"]).optional(),
  role: z.string().min(1, "Role is required").optional(),
  dateFrom: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
    .optional(),
  dateTo: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
    .optional(),
});

// URL parameter schemas
const idParamSchema = z.object({
  id: z
    .string()
    .min(1, "ID is required")
    .regex(/^[a-zA-Z0-9]+$/, "Invalid ID format"),
});

const userIdParamSchema = z.object({
  userId: z
    .string()
    .min(1, "User ID is required")
    .regex(/^[a-zA-Z0-9]+$/, "Invalid user ID format"),
});

// Export validation schemas
module.exports = {
  loginSchema,
  // Export base schemas for reuse in other validation files
  emailSchema,
  passwordSchema,
  firstNameSchema,
  lastNameSchema,
  phoneSchema,
  employeeIdSchema,
  tenantNameSchema,
  // Export query and param schemas
  paginationSchema,
  filterSchema,
  idParamSchema,
  userIdParamSchema,
};
