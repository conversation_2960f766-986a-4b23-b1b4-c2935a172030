const express = require("express");
const {
  setupMFA,
  verifyMFA,
  requestEmailFallback,
  resetMFAMethod,
  getMFAStatus,
  toggleMFA,
} = require("../controllers/mfa.js");
const { auth } = require("../lib/index.js");

const mfaRoutes = express.Router();

// Setup MFA with preferred method (authenticator OR email)
mfaRoutes.post("/setup-mfa", auth.protect(["self.read"]), setupMFA);

// Verify MFA token (authenticator app or email OTP)
mfaRoutes.post("/verify-mfa", auth.protect(["self.read"]), verifyMFA);

// Request email OTP for fallback (when authenticator app is lost)
mfaRoutes.post(
  "/request-email-fallback",
  auth.protect(["self.read"]),
  requestEmailFallback,
);

// Reset MFA method after fallback authentication
mfaRoutes.post(
  "/reset-mfa-method",
  auth.protect(["self.read"]),
  resetMFAMethod,
);

// Get current MFA status and configuration
mfaRoutes.get("/status", auth.protect(["self.read"]), getMFAStatus);

// Toggle MFA status (enable/disable)
mfaRoutes.patch("/toggle", auth.protect(["self.read"]), toggleMFA);

module.exports = mfaRoutes;
