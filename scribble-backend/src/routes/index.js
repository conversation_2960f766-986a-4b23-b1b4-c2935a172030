const nconf = require("nconf");
const aws = require("../lib/aws.js");
const authRoutes = require("./auth.js");
const visitRoutes = require("./visit.js");
const settingRoutes = require("./settings.js");
const userRoutes = require("./user.js");
const dashboardRoutes = require("./dashboard.js");
const clinicianRoutes = require("./clinician.js");
const mfaRoutes = require("./mfa.js");
const v2Routes = require("./v2/index.js");
const logger = require("../lib/logger.js");
const {
  createVisitFromRPA,
  processAIOutput,
  updateAssessmentFromRPA,
  markVisitPastDue,
} = require("../controllers/visit.js");
module.exports = (app) => {
  const baseVersion = nconf.get("BASE_VERSION");
  app.use(`/health`, (req, res) => {
    res.status(200).json({ message: "OK" });
  });

  aws.subscribeToQueue(process.env.RPA_DATA_QUEUE, createVisitFromRPA);
  aws.subscribeToQueue(process.env.AI_OUTPUT_QUEUE_URL, processAIOutput);
  aws.subscribeToQueue(
    process.env.RPA_UPDATE_QUEUE_URL,
    updateAssessmentFromRPA,
  );

  const schedule = require("node-schedule");

  // Schedule a job to run every day at midnight
  schedule.scheduleJob("0 */3 * * *", async () => {
    // schedule.scheduleJob("*/1 * * * *", async () => {
    try {
      logger.info(`Daily task executed at ${new Date()}`);
      markVisitPastDue();
    } catch (error) {
      logger.error(`Error executing daily task: ${error}`);
    }
  });

  app.use(`/api/${baseVersion}/auth`, authRoutes);
  app.use(`/api/${baseVersion}/visit`, visitRoutes);
  app.use(`/api/${baseVersion}/setting`, settingRoutes);
  app.use(`/api/${baseVersion}/user`, userRoutes);
  app.use(`/api/${baseVersion}/dashboard`, dashboardRoutes);
  app.use(`/api/${baseVersion}/clinician`, clinicianRoutes);
  app.use(`/api/${baseVersion}/mfa`, mfaRoutes);

  // V2 API routes with Zod validation
  app.use(`/api/v2`, v2Routes);
};
