const express = require("express");
const { clinicianLogin, getProfile } = require("../../controllers/auth.js");
const { v2ValidateMulti, v2LoginLimiter } = require("../../middlewares");
const { loginSchema } = require("../../validations/auth.validation.js");
const { auth } = require("../../lib/index.js");

const authRoutes = express.Router();

// V2 API Login endpoint with Zod validation, rate limiting, and secure error handling
// Headers are validated globally for all v2 routes
authRoutes.post(
  "/login",
  v2LoginLimiter,
  v2ValidateMulti({ body: loginSchema }, "auth"),
  clinicianLogin,
);

// V2 API Get Profile endpoint - Returns user profile information
authRoutes.get("/profile", auth.protect(["self.read"]), getProfile);

// // Example of multi-source validation - User search with query parameters
// authRoutes.get("/users",
//     v2ValidateMulti({
//         query: paginationSchema
//     }, 'validation'),
//     (req, res) => {
//         // Handle user search with pagination
//         res.json({ message: 'User search endpoint', query: req.query });
//     }
// );

// // Example of multi-source validation - User details with params and query
// authRoutes.get("/users/:id",
//     v2ValidateMulti({
//         params: idParamSchema,
//         query: filterSchema
//     }, 'validation'),
//     (req, res) => {
//         // Handle user details with filters
//         res.json({ message: 'User details endpoint', params: req.params, query: req.query });
//     }
// );

// // Example of multi-source validation - Update user with body, params, and query
// authRoutes.put("/users/:id",
//     v2ValidateMulti({
//         body: loginSchema.pick({ email: true }).extend({
//             firstName: firstNameSchema,
//             lastName: lastNameSchema,
//             phone: phoneSchema
//         }),
//         params: idParamSchema,
//         query: z.object({
//             notify: z.boolean().optional()
//         })
//     }, 'validation'),
//     (req, res) => {
//         // Handle user update
//         res.json({ message: 'User update endpoint', body: req.body, params: req.params, query: req.query });
//     }
// );

module.exports = authRoutes;
