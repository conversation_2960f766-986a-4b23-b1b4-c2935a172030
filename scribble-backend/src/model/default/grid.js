module.exports = [
  {
    name: "clinicianTable",
    viewJson: {
      clinicianId: {
        isRequired: true,
        isVisible: true,
        label: "Clinician id",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clinicianNo: {
        isRequired: true,
        isVisible: true,
        label: "Clinician no",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      firstName: {
        isRequired: true,
        isVisible: true,
        label: "First name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      lastName: {
        isRequired: true,
        isVisible: true,
        label: "Last name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      status: {
        isRequired: true,
        isVisible: true,
        label: "Status",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      discipline: {
        isRequired: true,
        isVisible: true,
        label: "Discipline",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      jobTitle: {
        isRequired: true,
        isVisible: true,
        label: "Job title",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      email: {
        isRequired: true,
        isVisible: true,
        label: "Email",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      age: {
        isRequired: true,
        isVisible: true,
        label: "Age",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      dob: {
        isRequired: true,
        isVisible: true,
        label: "Dob",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      gender: {
        isRequired: true,
        isVisible: true,
        label: "Gender",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      address1: {
        isRequired: true,
        isVisible: true,
        label: "Address1",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      address2: {
        isRequired: true,
        isVisible: true,
        label: "Address2",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      city: {
        isRequired: true,
        isVisible: true,
        label: "City",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      state: {
        isRequired: true,
        isVisible: true,
        label: "State",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      zip: {
        isRequired: true,
        isVisible: true,
        label: "Zip",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      country: {
        isRequired: true,
        isVisible: true,
        label: "Country",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      primaryPhone: {
        isRequired: true,
        isVisible: true,
        label: "Primary phone",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
    },
  },
  {
    name: "clientTable",
    viewJson: {
      clientId: {
        isRequired: true,
        isVisible: true,
        label: "Client ID",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clientGroupId: {
        isRequired: true,
        isVisible: true,
        label: "Client group id",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      firstName: {
        isRequired: true,
        isVisible: true,
        label: "First name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      lastName: {
        isRequired: true,
        isVisible: true,
        label: "Last name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      dateOfBirth: {
        isRequired: true,
        isVisible: true,
        label: "Date of Birth",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      email: {
        isRequired: true,
        isVisible: true,
        label: "Email",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      gender: {
        isRequired: true,
        isVisible: true,
        label: "Gender",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      "address.addressLine1": {
        isRequired: true,
        isVisible: true,
        label: "Address Line 1",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      "address.addressLine2": {
        isRequired: true,
        isVisible: true,
        label: "Address Line 2",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      "address.city": {
        isRequired: true,
        isVisible: true,
        label: "City",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      "address.state": {
        isRequired: true,
        isVisible: true,
        label: "State",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      "address.zipcode": {
        isRequired: true,
        isVisible: true,
        label: "Zipcode",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      "address.county": {
        isRequired: true,
        isVisible: true,
        label: "County",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      phone: {
        isRequired: true,
        isVisible: true,
        label: "Phone",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      emergencyContact: {
        isRequired: true,
        isVisible: true,
        label: "Emergency contact",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      emergencyContactPhone: {
        isRequired: true,
        isVisible: true,
        label: "Emergency contact phone",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
    },
  },
  {
    name: "visitTable",
    viewJson: {
      _id: {
        isRequired: true,
        isVisible: true,
        label: "ID",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      no: {
        isRequired: true,
        isVisible: true,
        label: "Visit Id",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      visitDate: {
        isRequired: true,
        isVisible: true,
        label: "Visit date",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      week: {
        isRequired: true,
        isVisible: true,
        label: "Week",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      type: {
        isRequired: true,
        isVisible: true,
        label: "Visit type",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      service: {
        isRequired: true,
        isVisible: true,
        label: "Service",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      serviceCode: {
        isRequired: true,
        isVisible: true,
        label: "Service code",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      createdAt: {
        isRequired: true,
        isVisible: true,
        label: "Created at",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      updatedAt: {
        isRequired: true,
        isVisible: true,
        label: "Updated at",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      episodeId: {
        isRequired: true,
        isVisible: true,
        label: "Episode id",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      episodeNo: {
        isRequired: true,
        isVisible: true,
        label: "Episode no",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clientId: {
        isRequired: true,
        isVisible: true,
        label: "Client id",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clientFirstName: {
        isRequired: true,
        isVisible: true,
        label: "Client first name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clientLastName: {
        isRequired: true,
        isVisible: true,
        label: "Client last name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clinicianId: {
        isRequired: true,
        isVisible: true,
        label: "Clinician id",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clinicianEmail: {
        isRequired: true,
        isVisible: true,
        label: "Clinician email",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clinicianFirstName: {
        isRequired: true,
        isVisible: true,
        label: "Clinician first name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      clinicianLastName: {
        isRequired: true,
        isVisible: true,
        label: "Clinician last name",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      assessmentFormId: {
        isRequired: true,
        isVisible: true,
        label: "Assessment form id",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      answer: {
        isRequired: true,
        isVisible: true,
        isUnique: true,
        label: "Assessment answer",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
      s3Path: {
        isRequired: true,
        isVisible: true,
        label: "S3 path",
        questionType: "TEXT_INPUT",
        options: {
          name: "",
          value: "",
        },
      },
    },
  },
];
