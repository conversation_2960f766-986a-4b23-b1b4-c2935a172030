module.exports = [
  {
    name: "RN - SOC - OASIS",
    discipline: "RN",
    formType: "SOC",
    question: require("../../../templates/RN-SOC-OASIS.json"),
  },
  // {
  //   formName: "RN - Fall Risk",
  //   discipline: "RN",
  //   formType: "Fall Risk",
  //   assessmentForm: require("../../../templates/RN-FallRisk.json"),
  // },
  // {
  //   formName: "PT - SOC",
  //   discipline: "PT",
  //   formType: "SOC",
  //   assessmentForm: require("../../../templates/PT-SOC.json"),
  // },
  // {
  //   formName: "PT - Wound Care",
  //   discipline: "PT",
  //   formType: "Wound Care",
  //   assessmentForm: require("../../../templates/PT-WoundCare.json"),
  // },
  // {
  //   formName: "PT - Fall Risk",
  //   discipline: "PT",
  //   formType: "Fall Risk",
  //   assessmentForm: require("../../../templates/PT-FallRisk.json"),
  // },
];
