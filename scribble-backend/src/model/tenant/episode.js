const mongoose = require("mongoose");

const episodeSchema = new mongoose.Schema(
  {
    no: {
      type: Number,
      required: true,
      unique: true,
    },
    duration: {
      type: Number,
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: "createdAt", updatedAt: "updatedAt" },
  },
);

episodeSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

module.exports = (connection) => connection.model("Episode", episodeSchema);
