const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

const userSchema = new mongoose.Schema(
  {
    roleId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Role",
      required: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    password: {
      type: String,
      required: true,
    },
    isFirstLogin: {
      type: Boolean,
      required: true,
      default: true,
    },
    loginAttempts: {
      type: mongoose.Schema.Types.Int32,
      required: true,
      default: 0,
    },
    lastLoginAt: {
      type: Date,
      default: null,
    },
    status: {
      type: String,
      required: true,
      default: "active",
      enum: ["active", "pending", "suspended", "inactive", "deleted", "locked"],
    },
    statusUpdatedAt: {
      type: Date,
      default: new Date(),
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  { timestamps: { createdAt: "createdAt", updatedAt: "updatedAt" } },
);

// Method to compare passwords
userSchema.methods.comparePassword = function (password) {
  return bcrypt.compare(password, this.password);
};

// Pre-save hook to hash the password
userSchema.pre("save", async function (next) {
  this.updatedAt = new Date();
  next();
});

module.exports = (connection) => connection.model("User", userSchema);
