const mongoose = require("mongoose");

const FormTemplateSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    question: {
      type: Array,
      required: true,
    },
    disciplineId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Discipline",
    },
    formTypeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Form_Type",
    },
  },
  {
    timestamps: { createdAt: "createdAt", updatedAt: "updatedAt" },
  },
);

FormTemplateSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

module.exports = (connection) =>
  connection.model("Form_Template", FormTemplateSchema);
