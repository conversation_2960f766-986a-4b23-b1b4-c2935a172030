const mongoose = require("mongoose");

const userProfilesSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    disciplineId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Discipline",
      immutable: true,
    },
    no: {
      type: String,
      required: true,
      unique: true,
      immutable: true,
    },
    firstName: {
      type: String,
      required: true,
    },
    lastName: {
      type: String,
    },
    phone: {
      type: String,
    },
    dateOfBirth: {
      type: Date,
    },
    gender: {
      type: String,
      immutable: true,
    },
    jobTitle: {
      type: String,
      immutable: true,
    },
    address: {
      addressLine1: { type: String },
      addressLine2: { type: String },
      state: { type: String },
      zipcode: { type: String },
      county: { type: String },
    },
    mfaEnabled: {
      type: Boolean,
      default: false,
    },
    mfaSecret: {
      type: String,
      default: "",
    },
    mfaMethod: {
      type: String,
      enum: ["authenticator", "email"],
      default: "email",
    },
    mfaEmailOtpSecret: {
      type: String,
      default: "",
    },
    mfaEmailOtpExpiry: {
      type: Date,
      default: null,
    },
    mfaEmailOtpAttempts: {
      type: Number,
      default: 0,
    },
    mfaEmailFallbackUsed: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: { createdAt: "createdAt", updatedAt: "updatedAt" } },
);

userProfilesSchema.pre("save", async function (next) {
  this.updatedAt = new Date();
  next();
});

userProfilesSchema.pre(
  ["updateOne", "findOneAndUpdate", "updateMany"],
  function (next) {
    const readOnlyFields = [
      "no",
      "jobTitle",
      "disciplineId",
      "gender",
      "address",
    ];

    const updateData = this.getUpdate();
    if (updateData.$set) {
      const attemptedUpdates = Object.keys(updateData.$set);
      const readOnlyAttempts = attemptedUpdates.filter((field) =>
        readOnlyFields.includes(field),
      );

      if (readOnlyAttempts.length > 0) {
        return next(
          new Error(
            `Cannot update read-only fields: ${readOnlyAttempts.join(", ")}`,
          ),
        );
      }
    }

    next();
  },
);

module.exports = (connection) =>
  connection.model("user_profile", userProfilesSchema);
