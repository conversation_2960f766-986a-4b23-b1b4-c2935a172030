const mongoose = require("mongoose");

const visitSchema = new mongoose.Schema(
  {
    episodeId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Episode",
      required: true,
    },
    clinicianId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    clientId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Client_Info",
      required: true,
    },
    no: {
      type: String,
      required: true,
      unique: true,
    },
    visitDate: {
      type: Date,
    },
    startTime: {
      type: String,
      // Now stores time in 24-hour format (HH:MM) for proper sorting
      // Examples: "17:33", "09:15", "00:30"
    },
    type: {
      type: String,
    },
    status: {
      type: String,
      enum: [
        "New",
        "In Progress",
        "Visit Not Conducted",
        "Past Due",
        "Submitted for Processing",
        "Completed",
      ],
      default: "New",
    },
    orchestratorQueue: {
      type: String,
      default: "haggaihealth_kantime_performer",
    },
  },
  { timestamps: { createdAt: "createdAt", updatedAt: "updatedAt" } },
);

visitSchema.pre("save", async function (next) {
  this.updatedAt = new Date();
  next();
});

module.exports = (connection) => connection.model("Visit", visitSchema);
