// V2 API Response Classes - Clean format without generic wrapper
class V2SuccessResponse {
  constructor(data = {}, meta = null) {
    // Direct data response without wrapper
    Object.assign(this, data);

    // Add metadata if provided
    if (meta) {
      this.meta = meta;
    }
  }
}

class V2ErrorResponse {
  constructor(errors = [], message = null) {
    this.errors = errors;
    if (message) {
      this.message = message;
    }
  }
}

// Helper function to create validation error response with user-friendly messages
const createValidationError = (zodError) => {
  const errors = (zodError.errors || zodError).map((err) => {
    const fieldName = err.path.join(".");
    let sanitizedField = "input";

    // Only expose safe field names
    if (
      [
        "email",
        "firstName",
        "lastName",
        "phone",
        "employeeId",
        "tenantName",
      ].includes(fieldName)
    ) {
      sanitizedField = fieldName;
    }

    return {
      field: sanitizedField,
      message: err.message,
      code: err.code,
    };
  });

  return new V2ErrorResponse(errors, "Please check your input and try again");
};

// Helper function to create general error response
const createError = (message, field = null, code = "VALIDATION_ERROR") => {
  const errors = [
    {
      field: field || "general",
      message: message,
      code: code,
    },
  ];

  return new V2ErrorResponse(errors, message);
};

module.exports = {
  V2SuccessResponse,
  V2ErrorResponse,
  createValidationError,
  createError,
};
