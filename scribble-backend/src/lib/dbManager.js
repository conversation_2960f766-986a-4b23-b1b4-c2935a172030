const mongoose = require("mongoose");
const logger = require("../lib/logger.js");
const connections = {}; // Store tenant connections

const getTenantDB = async (dbName) => {
  if (!connections[dbName]) {
    let dbURI = process.env.MONGO_URI.replace("ADMIN_DB", dbName);
    dbURI = dbURI.replace(/<PUBLIC_IP>/g, process.env.IP);

    connections[dbName] = mongoose.createConnection(dbURI, {
      // useNewUrlParser: true,
      // useUnifiedTopology: true,
    });
    logger.info(`Connected to TenantDB: ${dbName}`);
  }
  return connections[dbName];
};

module.exports = { getTenantDB };
