const bcrypt = require("bcryptjs");
const nconf = require("nconf");
const validator = require("validator");
const axios = require("axios");
const { HTTPError } = require("../lib/responses.js");
const crypto = require("crypto");
const { DateTime } = require("luxon");
const logger = require("../lib/logger.js");
/**
 * Checks if a password is compromised using the Have I Been Pwned API (k-Anonymity model).
 * @param {string} password
 * @returns {Promise<boolean>} true if compromised, false otherwise
 */
async function isPasswordCompromised(password) {
  const sha1 = crypto
    .createHash("sha1")
    .update(password)
    .digest("hex")
    .toUpperCase();
  const prefix = sha1.slice(0, 5);
  const suffix = sha1.slice(5);
  try {
    const response = await axios.get(
      `https://api.pwnedpasswords.com/range/${prefix}`,
      {
        timeout: 1000 * 3, // Wait for 3 seconds
      },
    );
    const lines = response.data.split("\n");
    for (const line of lines) {
      const hashSuffix = line.split(":")[0];
      if (hashSuffix === suffix) {
        return true; // Compromised
      }
    }
    return false; // Not found
  } catch {
    // If the API fails, err on the side of caution: allow password (or optionally, block)
    return false;
  }
}

function formatDateToMMDDYYYY(date) {
  if (!date) return undefined;
  const jsDate = date instanceof Date ? date : new Date(date);
  return DateTime.fromJSDate(jsDate, { zone: "utc" }).toFormat("MM/dd/yyyy");
}

module.exports = {
  generateRandomPassword: (length = 12) => {
    try {
      const charset =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@!#";
      let password = "";

      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password += charset[randomIndex];
      }

      return password;
    } catch (e) {
      logger.error(`Error in generateRandomPassword: ${e}`);
      throw e;
    }
  },

  generateHashedPassword: (password) => {
    try {
      const salt = bcrypt.genSaltSync(10);
      const hash = bcrypt.hashSync(password, salt);
      return hash;
    } catch (e) {
      logger.error(`Error in generating hash: ${e}`);
      throw e;
    }
  },

  getFilterQuery: (reqQuery) => {
    const { limit = 10, page = 0, ...filters } = reqQuery;
    delete filters["x-tenant-id"];

    const parsedLimit = Math.max(parseInt(limit, 10) || 10, 1); // Default 10, minimum 1
    const parsedPage = Math.max(parseInt(page, 10) - 1 || 0, 0); // Default 0, minimum 0
    const parsedOffset = parsedPage * parsedLimit; // Offset calculation

    const orConditions = [];

    for (const key in filters) {
      if (filters[key]) {
        // Use regex for string fields (case-insensitive), otherwise direct match
        let condition;
        const isDateField = key.toLowerCase().includes("date");
        const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;

        if (
          typeof filters[key] === "string" &&
          isDateField &&
          dateRegex.test(filters[key])
        ) {
          const [month, day, year] = filters[key].split("/").map(Number);
          const startDate = new Date(Date.UTC(year, month - 1, day));
          const endDate = new Date(Date.UTC(year, month - 1, day + 1));

          condition = {
            [key]: {
              $gte: startDate,
              $lt: endDate,
            },
          };
        } else if (typeof filters[key] === "string") {
          condition = { [key]: { $regex: filters[key], $options: "i" } };
        } else {
          condition = { [key]: filters[key] };
        }

        orConditions.push(condition);
      }
    }

    // If no filters provided, return an empty query (matches all)
    const query = orConditions.length > 0 ? { $or: orConditions } : {};

    return { query, parsedLimit, parsedOffset };
  },

  /**
   * Restricts operations if there is an external authority for the domain name
   * of the specified email, by throwing an error with the relevant authority
   * as data property.
   *
   * @param {string} email
   * @throws {HTTPError}
   */
  restrictIfExternalAuthority: (email) => {
    let authority = null;
    Object.keys(nconf.get("ACCOUNT_DOMAIN_AUTHORITIES")).forEach((domain) => {
      if (validator.contains(email, `@${domain}`)) {
        authority = nconf.get("ACCOUNT_DOMAIN_AUTHORITIES")[domain];
      }
    });

    if (authority !== null) {
      const error = new HTTPError(
        400,
        `Unsupported operation for federated account with authority ${authority.TYPE}`,
        "FEDERATED_ACCOUNT",
      );

      error.data = authority;

      throw error;
    }
  },
  transformDataToNestedObject: (inputArray) => {
    return inputArray.reduce((acc, item) => {
      item.value = item.answer_code;
      const keys = item.question_code.split("."); // Split into multiple levels
      let currentLevel = acc;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!currentLevel[keys[i]]) {
          currentLevel[keys[i]] = {};
        }
        currentLevel = currentLevel[keys[i]];
      }

      currentLevel[keys[keys.length - 1]] = { ...item };
      return acc;
    }, {});
  },
  transformData: (inputArray) => {
    return inputArray.reduce((acc, item) => {
      item.value = item.answer_code;
      item.questionCode = item.question_code;
      item.questionType = item.question_type;

      acc[item.question_code] = { ...item };
      return acc;
    }, {});
  },
  sendMessageToUIPath: async (message, customQueueName = null) => {
    try {
      // Get orchestrator config from environment
      const orchestratorUrl = process.env.UIPATH_ORCHESTRATOR_URL;
      const queueName = customQueueName || process.env.UIPATH_QUEUE_NAME;
      const tenantName = process.env.UIPATH_TENANT_NAME;
      const personalAccessToken = process.env.UIPATH_PAT;
      const folderId = process.env.UIPATH_FOLDER_ID;

      await axios.get(
        `${orchestratorUrl}/odata/Folders?$select=Id,DisplayName`,
        {
          headers: {
            "Content-Type": "application/json",
            "X-UIPATH-TenantName": tenantName,
            Authorization: `Bearer ${personalAccessToken}`,
          },
          timeout: 1000 * 30, // Wait for 30 seconds
        },
      );
      const flattenedMessage = {};
      for (const key in message) {
        if (typeof message[key] === "object") {
          flattenedMessage[key] = JSON.stringify(message[key]);
        } else {
          flattenedMessage[key] = message[key];
        }
      }

      let queueResponse;
      try {
        queueResponse = await axios.post(
          `${orchestratorUrl}/odata/Queues/UiPathODataSvc.AddQueueItem`,
          {
            itemData: {
              Name: queueName,
              SpecificContent: flattenedMessage, // Use flattened object
            },
          },
          {
            headers: {
              Authorization: `Bearer ${personalAccessToken}`,
              "Content-Type": "application/json",
              "X-UIPATH-TenantName": tenantName,
              "X-UIPATH-OrganizationUnitId": folderId, // Folder ID
            },
          },
        );
      } catch (error) {
        logger.info(
          `RPA Queue message pushing failed, trying default queue ${error}`,
        );
        queueResponse = await axios.post(
          `${orchestratorUrl}/odata/Queues/UiPathODataSvc.AddQueueItem`,
          {
            itemData: {
              Name: process.env.UIPATH_QUEUE_NAME,
              SpecificContent: flattenedMessage, // Use flattened object
            },
          },
          {
            headers: {
              Authorization: `Bearer ${personalAccessToken}`,
              "Content-Type": "application/json",
              "X-UIPATH-TenantName": tenantName,
              "X-UIPATH-OrganizationUnitId": folderId, // Folder ID
            },
          },
        );
        logger.error(`Error sending message to UiPath queue: ${error}`);
        throw new Error("Failed to send message to UiPath queue");
      }

      logger.info(
        "Message sent to UiPath queue",
        queueResponse.data.SpecificContent,
      );
      return queueResponse.data;
    } catch (error) {
      logger.error(
        "Failed to send message to UiPath queue",
        error.response?.data || error.message,
      );
      throw new Error("Failed to send message to UiPath queue");
    }
  },

  // Function to transform the JSON from assessment form to AI processing format
  transformAssessmentForAI: (data) => {
    const transformed = [];

    data.forEach((section) => {
      if (
        section.container &&
        section.container.controlName === "step-container"
      ) {
        const sectionName = section.container.subHeading || "Unknown Section";

        section.items.forEach((item) => {
          if (item.controlName) {
            const transformedItem = {
              question_code: item.questionCode || "",
              question: item.description || "",
              question_type: item.controlName || "",
              labelName: item.labelName || "",
              section: sectionName,
            };

            if (
              item.controlName === "radio-group" ||
              item.controlName === "checklist" ||
              item.controlName === "select-drop-down"
            ) {
              transformedItem.options = (item.items || []).map(
                (option) => option.label || "",
              );
            }

            transformed.push(transformedItem);
          }
        });
      }
    });

    return transformed;
  },
  transformAssessmentForRPA: (data) => {
    const transformed = {};

    data.forEach((section) => {
      if (
        section.container &&
        section.container.controlName === "step-container"
      ) {
        const sectionName = section.container.subHeading || "Unknown Section";

        section.items.forEach((item) => {
          if (item.controlName) {
            const questionCode = item.questionCode || "";
            const transformedItem = {
              question_code: questionCode,
              question: item.description || "",
              question_type: item.controlName || "",
              labelName: item.labelName || "",
              section: sectionName,
            };

            if (item.answer_context) {
              transformedItem.answer_context = item.answer_context;
            }
            if (item.answer_text) {
              transformedItem.answer_text = item.answer_text;
            }
            if (item.answer_code) {
              transformedItem.answer_code = item.answer_code;
            }
            if (
              item.controlName === "radio-group" ||
              item.controlName === "checklist" ||
              item.controlName === "select-drop-down"
            ) {
              transformedItem.options = (item.items || []).map(
                (option) => option.label || "",
              );
            }

            if (questionCode) {
              transformed[questionCode] = transformedItem;
            }
          }
        });
      }
    });

    return transformed;
  },
  // Function to recursively update the JSON structure
  updateQuestions: function (items, answersMap) {
    const updateQuestions = (items, answersMap) => {
      items.forEach((item) => {
        if (item.questionCode && answersMap.has(item.questionCode)) {
          const answer = answersMap.get(item.questionCode);
          item.answer_context = answer.answer_context;
          item.answer_text = answer.answer_text;
          item.answer_code = answer.answer_code;
        }
        if (item.items && Array.isArray(item.items)) {
          updateQuestions(item.items, answersMap);
        }
      });
    };

    updateQuestions(items, answersMap);
    return items;
  },
  isPasswordCompromised,
  formatDateToMMDDYYYY,
};
