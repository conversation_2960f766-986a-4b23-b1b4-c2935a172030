const nconf = require("nconf");
const AWS = require("aws-sdk");
const nodemailer = require("nodemailer");
const logger = require("./logger.js");
const { WebClient } = require("@slack/web-api");

const EMAIL_PROVIDER = "SMTP";
const accountVerificationTemplate = require("../views/emailer-account-verification.js");
const mfaEmailOTPTemplate = require("../views/mfa-email-otp.js");

const awsConfig = null;

let provider = null;
let ses = null;

if (EMAIL_PROVIDER === "SES") {
  if (awsConfig) {
    AWS.config.update(awsConfig);
    // create SES provider
    ses = new AWS.SES(awsConfig);
  } else {
    ses = new AWS.SES();
  }
} else if (EMAIL_PROVIDER === "SMTP") {
  logger.info(`Creating SMTP transport using username: thillaimaharajang`);

  // create SMTP provider
  provider = nodemailer.createTransport({
    host: "smtp.gmail.com",
    port: 465,
    secure: true,
    requireTLS: true,
    // auth: {
    //   user: '<EMAIL>',
    //   pass: 'wwza lfjz ewxs akjc',
    // },
    auth: {
      user: process.env.MAIL_USER,
      pass: process.env.MAIL_PASS,
    },
  });

  // verify transport
  provider.verify((err) => {
    if (err) {
      logger.error(err);
    } else {
      logger.info(`SMTP transport ready`);
    }
  });
}

/**
 * Send an email using AWS SES
 * @param {string} destination recepient of the email
 * @param {string} subject subject of the email
 * @param {string} htmlBody html part of the body
 * @param {string} textBody text part of the body
 */
async function sendEmailSES(destination, subject, htmlBody) {
  const domainPrefix = `${nconf.get("AWS_ENV")}.${nconf.get("OEM")}.${
    nconf.get("AWS_DEFAULT_REGION").split("-")[0]
  }`;
  const params = {
    Source: `admin@${domainPrefix}.appstore.visteoncloud.com`,
    Destination: { ToAddresses: [destination] },
    Message: {
      Body: {
        Html: { Charset: "UTF-8", Data: htmlBody },
      },
      Subject: {
        Charset: "UTF-8",
        Data: subject,
      },
    },
  };

  return ses.sendEmail(params).promise();
}

async function sendEmailSMTP(destination, subject, htmlBody, textBody) {
  const params = {
    from: '"Scribble Support" <<EMAIL>>',
    to: destination,
    subject,
    html: htmlBody,
    text: textBody,
  };
  return provider.sendMail(params);
}

/**
 * Generate account verification email
 * @param {string} email recepient
 * @param {string} code url to include in the body
 * @param {string} [firstName] optional first name of the recipient
 */
async function sendAccountVerificationEmail(
  email,
  code,
  firstName,
  scribbleUrl,
) {
  logger.info(`Sending verification email to ${email}`);

  const subject = `${"Scribble - Account Sign up"}`;
  const htmlBody = accountVerificationTemplate(code, firstName, scribbleUrl);
  return sendEmailSMTP(email, subject, htmlBody);
}

/**
 * Generate account verification email
 * @param {string} email recepient
 * @param {string} url url to include in the body
 */
async function sendAlertEmail(error, type = "Handled Error") {
  error = error.message || error.toString();
  logger.info(`Sending alert email ${error}`);
  if (
    [
      "User not found",
      "Invalid credentials",
      "Token invalid",
      "Missing required permission",
      "Your new password cannot be the same as your current password.",
      "No authorization token was found",
      "Not Found",
      "Error: Not Found",
    ].includes(error)
  ) {
    logger.info(`No need to send alert mail for ${error}`);
    return;
  }

  const slackChannel = process.env.SLACK_CHANNEL || "#backend-errors";
  const slackToken = process.env.SLACK_TOKEN;
  const slackClient = new WebClient(slackToken);
  const messageBlocks = [
    // {
    //   type: "header",
    //   text: {
    //     type: "plain_text",
    //     text: `📡 Scribble Alert ${process.env.NODE_ENV} ${type}`,
    //     emoji: true,
    //   },
    // },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: `*Run Time:* ${new Date().toLocaleString("en-IN", {
          timeZone: "Asia/Kolkata",
        })}`,
      },
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: error.message || error.toString(),
      },
    },
    {
      type: "divider",
    },
  ];
  const envColorMap = {
    prod: "#FF0000", // 🔴 Red
    dev: "#FFA500", // 🟠 Orange
    default: "#36a64f", // 🟢 Green
  };
  try {
    await slackClient.chat.postMessage({
      channel: slackChannel,
      text: `*_Scribble Alert ${process.env.NODE_ENV} ${type}_*`,
      attachments: [
        {
          color: envColorMap[process.env.NODE_ENV] || envColorMap.default,
          blocks: messageBlocks,
        },
      ],
    });

    logger.info(`Sent report to Slack channel: ${slackChannel}`);
  } catch (error) {
    logger.error(`Failed to send message to Slack: ${error.message}`);
  }
}

/**
 * Generate password reseet email
 * @param {string} email recepient
 * @param {string} url url to include in the body
 * @param {string} [firstName]
 */
async function sendPasswordResetEmail(email, url, firstName) {
  logger.info(`Sending password reset email to ${email}`);
  const subject = "Password Reset for your Scribble Account";
  const htmlBodyCustomer = `<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Reset Your Password - Scribble</title>
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', sans-serif;
            background-color: #f9f9f9;
            color: #333;
        }

        .header, .footer {
            background-color: #333;
            color: #fff;
            text-align: center;
            padding: 15px 0;
        }

        .header a, .footer a {
            color: #fff;
            text-decoration: none;
            font-weight: bold;
        }

        .container {
            max-width: 600px;
            margin: 80px auto;
            background: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.05);
        }

        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #000;
        }

        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .btn {
            display: inline-block;
            background-color: #4F47E5;
            color: #fff !important;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            margin-top: 10px;
        }

        @media (max-width: 600px) {
            .container {
                margin: 60px 20px;
                padding: 25px;
            }
        }
    </style>
</head>

<body>

    <div class="container">
        <p>Dear ${firstName?.charAt(0).toUpperCase() + firstName?.slice(1)},</p>

        <p>We received a request to reset your password. To create a new password, click the link below:</p>

        <a class="btn" href="${url}">Reset Password</a>

        <p>This link is valid for 24 hours.</p>
        
        <p>
          If you didn't request this, contact us immediately at 
          <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>

        <p>For your security, never share your password.</p>

        <p><strong>Note</strong>: Upon successful password reset, you will be redirected to a login screen where you can log in with your new password.</p>

        <p>Thanks,<br><br>
        The Scribble Labs Team</p>
    </div>
</body>
</html>`;

  const textBody = `Paste the following link in the address bar of your browser to reset your password: ${url}`;

  return sendEmailSMTP(email, subject, htmlBodyCustomer, textBody);
}

/**
 * Send MFA verification email with OTP
 * @param {string} email recipient
 * @param {string} otp verification code
 * @param {string} firstName user's first name
 */
async function sendMFAEmailOTP(email, otp, firstName) {
  logger.info(`Sending MFA OTP email to ${email}`);

  const subject = "MFA Verification Code - Scribble";
  const htmlBody = mfaEmailOTPTemplate(otp, firstName);
  return sendEmailSMTP(email, subject, htmlBody);
}

module.exports = {
  sendAccountVerificationEmail,
  sendPasswordResetEmail,
  sendMFAEmailOTP,
  sendEmailSES,
  sendAlertEmail,
};
