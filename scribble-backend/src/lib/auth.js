const _ = require("lodash");
const jwt = require("jsonwebtoken");
const util = require("util");
const { getTenantDB } = require("../lib/dbManager.js");
const AdminUser = require("../model/scribble-admin/adminUser.js");
const Tenant = require("../model/scribble-admin/tenants.js");
const Role = require("../model/tenant/role.js");
const User = require("../model/tenant/user.js");

const session = require("./session-store.js");
const { ErrorResponse, HTTPError, ERROR_CODES } = require("./responses.js");
// const session = require("./redis");

function extractTokenFromRequest(req) {
  if (
    req.headers.authorization &&
    req.headers.authorization.split(" ")[0] === "Bearer"
  ) {
    return req.headers.authorization.split(" ")[1];
  }

  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }

  if (req.query && req.query.token) {
    return req.query.token;
  }
  return null;
}

const jwtVerify = util.promisify(jwt.verify);

/**
 * Perform token verification and decoding
 * @param {String} token
 * @returns {Promise<Object>}
 */
async function verifyToken(token) {
  try {
    const data = await jwtVerify(token, process.env.JWT_SECRET);
    return _.has(data, "value") ? JSON.parse(data.value) : data;
  } catch (err) {
    switch (err.name) {
      case "TokenExpiredError":
        throw new HTTPError(401, "Token expired", ERROR_CODES.EXPIRED_TOKEN);

      case "JsonWebTokenError":
        throw new HTTPError(419, "Token invalid", ERROR_CODES.INVALID_TOKEN);
      case "NotBeforeError":
        throw new HTTPError(403, "Token invalid", ERROR_CODES.EXPIRED_TOKEN);

      default:
        throw new HTTPError(500, err.message, ERROR_CODES.GENERAL);
    }
  }
}

const throwErrorIfMissingRequiredPermission = (
  requiredPermission,
  userPermission,
) => {
  const missingPermission = _.difference(requiredPermission, userPermission);
  if (missingPermission.length !== 0) {
    throw new HTTPError(
      403,
      `Missing required permission ${missingPermission.join(", ")}`,
      ERROR_CODES.MISSING_REQUIRED_PERMISSION,
    );
  }
};

// eslint-disable-next-line no-unused-vars
function protect(requiredPermission = [], ignoreExpiration = false) {
  return async (req, res, next) => {
    const rawToken = extractTokenFromRequest(req);
    if (!rawToken) {
      return res
        .status(401)
        .json(
          new ErrorResponse("No authorization token was found", req?.apiId),
        );
    }

    try {
      const tokenData = await verifyToken(rawToken);

      const identity = tokenData.v === 3 ? tokenData.sub : tokenData.id;

      if (tokenData.type !== "recover-password") {
        const response = await session.checkIfAccessTokenExists(identity);
        if (!response || response !== rawToken) {
          throw new HTTPError(403, "Token invalid", ERROR_CODES.INVALID_TOKEN);
        }
      }

      let subDomain;

      // Superadmin Case
      if (tokenData.roles === "scribble_admin") {
        const { "x-tenant-id": tenantIdBody } = req.body;
        const { "x-tenant-id": tenantIdQuery } = req.query;
        subDomain = tenantIdBody || tenantIdQuery;

        if (subDomain) {
          const tenant = await Tenant.findOne({ uniqueName: subDomain });
          if (!tenant) {
            return res
              .status(404)
              .json(
                new ErrorResponse(
                  `Tenant not found for super admin: ${subDomain}`,
                ),
              );
          }
          req.tenantId = tenant._id;

          req.tenantDb = tenant.databaseName;
        } else {
          const { "x-tenant-id": tenantIdHeader } = req.headers;

          if (tenantIdHeader) {
            const tenant = await Tenant.findOne({ uniqueName: subDomain });
            if (!tenant) {
              return res
                .status(404)
                .json(
                  new ErrorResponse(
                    "Tenant Id should not be in header for Super Admin",
                  ),
                );
            }
            req.tenantDb = tenant.databaseName;
          }
        }

        req.user = await AdminUser.findById(identity);
        if (!req.user) {
          throw new HTTPError(403, "Admin not found", ERROR_CODES.INVALID_USER);
        }

        throwErrorIfMissingRequiredPermission(
          requiredPermission,
          req.user.permission,
        );
      }

      // Tenant Admin Case
      else {
        const { "x-tenant-id": subDomain } = req.headers;

        if (!subDomain) {
          return res
            .status(400)
            .json(
              new ErrorResponse(
                `Tenant ID is required in headers for tenant: ${subDomain}`,
              ),
            );
        }

        const tenant = await Tenant.findOne({ uniqueName: subDomain });
        if (!tenant) {
          return res
            .status(404)
            .json(new ErrorResponse(`Tenant not found ${subDomain}`));
        }
        req.tenantId = tenant._id;

        // Attach Tenant Database Connection
        req.tenantDb = tenant.databaseName;
        const connection = await getTenantDB(tenant.databaseName);

        // Fetch User & Role
        const UserModel = User(connection);
        // eslint-disable-next-line
        const RoleModel = Role(connection);

        const user = await UserModel.findById(identity).populate({
          path: "roleId",
          select: "name permission",
        });

        if (!user && tokenData.roles !== "scribble_admin") {
          return res
            .status(403)
            .json(
              new ErrorResponse(
                `User ${identity} not registered to this tenant ${subDomain}`,
              ),
            );
        }

        throwErrorIfMissingRequiredPermission(
          requiredPermission,
          user?.roleId?.permission || req.user.permission,
        );
        req.user = user || req.user;
      }

      return next();
    } catch (err) {
      if (err.response) {
        const errorObj = {
          errorMessage: err?.response || "Invalid token",
          errorCode: "INVALID_TOKEN",
          ...err.response.data,
        };
        return res.status(err.response.status).json(errorObj);
      }
      if (err.message.startsWith("Missing required permission"))
        return res
          .status(403)
          .json(
            new ErrorResponse(
              "Missing required permission",
              req?.apiId,
              "INVALID_TOKEN",
            ),
          );
      return res
        .status(err.errorCode || 419)
        .json(
          new ErrorResponse(
            err?.message || "Invalid token",
            req?.apiId,
            "INVALID_TOKEN",
          ),
        );
    }
  };
}

module.exports = {
  protect,
};
