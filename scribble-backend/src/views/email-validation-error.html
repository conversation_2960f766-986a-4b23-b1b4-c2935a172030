<!-- not used -->
<html>

<head>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>
    <style>
        body {
            margin: 0;
            font-family: 'Roboto';
        }

        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 160px;
            /* Preferred icon size */
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            color: #f3594e;
            /* Support for all WebKit browsers. */
            -webkit-font-smoothing: antialiased;
            /* Support for Safari and Chrome. */
            text-rendering: optimizeLegibility;

            /* Support for Firefox. */
            -moz-osx-font-smoothing: grayscale;

            /* Support for IE. */
            font-feature-settings: 'liga';
        }
		
		.main {
			background-color: #eaeaea;
            height: 56px;
            width: 100%;
		}

        .footer {
			background-color: #eaeaea;
            height: 56px;
            width: 100%;
            position: absolute;
            bottom: 0;
        }
		
		.container {
			text-align: center;
			position: absolute;
			top: 50%;
			left: 50%;
			-moz-transform: translateX(-50%) translateY(-50%);
			-webkit-transform: translateX(-50%) translateY(-50%);
			transform: translateX(-50%) translateY(-50%);
		}

        .anchor {
            background-color: black;
            text-decoration: none;
            color: white;
            padding: 10px 20px;
            text-transform: uppercase;
            font-size: 14px;
            border-radius: 5px;
            font-weight: 400;
        }
		
		.header {
            font-size: 28px;
            color: #000000;
            line-height: 1.21;
        }

        .para {
            font-size: 16px;
            color: #707070;
            line-height: 1.5;
        }
		
    </style>
</head>

<body>
	<div class="main"></div>
    <div class="container">
        <svg xmlns="http://www.w3.org/2000/svg" width="649" height="280" viewBox="0 0 649 280">
            <text data-name="We are sorry, something has gone wrong.Please try again." transform="translate(0 234)" style="fill:#707070;font-size:16px;font-family:Roboto-Regular,Roboto"><tspan x="178.473" y="15">We are sorry, something has gone wrong.</tspan><tspan x="265.77" y="39">Please try again.</tspan></text>
            <text data-name="Verification Failed" transform="translate(324 202)" style="font-size:28px;font-family:Roboto-Bold,Roboto;font-weight:700"><tspan x="-114.071" y="0">Verification Failed</tspan></text>
            <g data-name="Ellipse 4" transform="translate(254 10)" style="stroke:#fff;stroke-width:10px;fill:#f3594e">
                <circle cx="70" cy="70" r="70" style="stroke:none"/>
                <circle cx="70" cy="70" r="75" style="fill:none"/>
            </g>
            <g data-name="Close Icon">
                <path data-name="Line 57" transform="translate(295.24 51.24)" style="fill:none;stroke:#fff;stroke-width:10px" d="m0 0 60.808 60.808"/>
                <path data-name="Line 58" transform="translate(295.24 51.24)" style="fill:none;stroke:#fff;stroke-width:10px" d="M60.808 0 0 60.808"/>
            </g>
        </svg>
    </div>
    <div class="footer"></div>
</body>

</html>