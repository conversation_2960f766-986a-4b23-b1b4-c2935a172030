<!-- not used -->
<html>

<head>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>
    <style>
        body {
            margin: 0;
            font-family: 'Roboto';
        }

        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 160px;
            /* Preferred icon size */
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            color: #62b866;
            /* Support for all WebKit browsers. */
            -webkit-font-smoothing: antialiased;
            /* Support for Safari and Chrome. */
            text-rendering: optimizeLegibility;

            /* Support for Firefox. */
            -moz-osx-font-smoothing: grayscale;

            /* Support for IE. */
            font-feature-settings: 'liga';
        }

        .main {
			background-color: #eaeaea;
            height: 56px;
            width: 100%;
		}

        .footer {
			background-color: #eaeaea;
            height: 56px;
            width: 100%;
            position: absolute;
            bottom: 0;
        }
		
		.container {
			text-align: center;
			position: absolute;
			top: 50%;
			left: 50%;
			-moz-transform: translateX(-50%) translateY(-50%);
			-webkit-transform: translateX(-50%) translateY(-50%);
			transform: translateX(-50%) translateY(-50%);
		}

        .anchor {
            background-color: black;
            text-decoration: none;
            color: white;
            padding: 10px 20px;
            text-transform: uppercase;
            font-size: 14px;
            border-radius: 5px;
            font-weight: 400;
        }
		
		.header {
            font-size: 28px;
            color: #000000;
            line-height: 1.21;
        }

        .para {
            font-size: 16px;
            color: #707070;
            line-height: 1.5;
        }
    </style>
</head>

<body>
    <div class="main"></div>
    <div class="container">
        <svg xmlns="http://www.w3.org/2000/svg" width="649" height="335" viewBox="0 0 649 335">
            <g transform="translate(-409 -217)">
                <rect width="87.281" height="30" rx="5" transform="translate(689.36 522)"/>
                <text data-name="LOG IN" transform="translate(733 542)" style="fill:#fff;font-size:14px;font-family:Roboto-Medium,Roboto;font-weight:500"><tspan x="-21.854" y="0"><a style="fill:#fff;font-weight:bold;font-size:14px;font-family:Roboto-Regular,Roboto;" xlink:href="https://dev.portal.visteoncloud.com/login">LOG IN</a></tspan></text>
            </g>
            <text data-name="Thanks for your support, we have successfully verified your account.You can now proceed to your home page." transform="translate(0 234)" style="fill:#707070;font-size:16px;font-family:Roboto-Regular,Roboto"><tspan x="82.543" y="15">Thanks for your support, we have successfully verified your account.</tspan><tspan x="177.922" y="39">You can now proceed to your home page.</tspan></text>
            <text data-name="Verification Success" transform="translate(324 202)" style="font-size:28px;font-family:Roboto-Bold,Roboto;font-weight:700"><tspan x="-129.254" y="0">Verification Success</tspan></text>
            <g data-name="Ellipse 3" transform="translate(254 10)" style="stroke:#fff;stroke-width:10px;fill:#62b866">
                <circle cx="70" cy="70" r="70" style="stroke:none"/>
                <circle cx="70" cy="70" r="75" style="fill:none"/>
            </g>
            <path data-name="Path 319" d="m1519.025 2217.643 23.3 23.295 40.794-40.793" transform="translate(-1227.525 -2140.645)" style="fill:none;stroke:#fff;stroke-width:10px"/>
        </svg>
    </div>
    <div class="footer"></div>
</body>

</html>