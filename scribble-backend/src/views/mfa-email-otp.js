module.exports = (otp, firstName) => {
  return `<html>
<head>
    <meta charset="UTF-8">
    <title>MFA Verification - Scribble</title>
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: '<PERSON>o', sans-serif;
            background-color: #f9f9f9;
            color: #333;
        }

        .header, .footer {
            background-color: #333;
            color: #fff;
            padding: 15px 0;
            text-align: center;
        }

        .main {
            max-width: 600px;
            margin: 80px auto 60px auto;
            background-color: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .main h1 {
            font-size: 26px;
            margin-bottom: 20px;
            color: #000;
        }

        .main p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .otp-code {
            font-size: 32px;
            font-weight: bold;
            background-color: #f0f0f0;
            padding: 20px;
            border-radius: 8px;
            display: inline-block;
            margin: 20px 0;
            letter-spacing: 8px;
            text-align: center;
            min-width: 200px;
        }

        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .footer {
            position: fixed;
            width: 100%;
            bottom: 0;
        }

        @media (max-width: 600px) {
            .main {
                margin: 100px 15px 80px 15px;
                padding: 25px;
            }
        }
    </style>
</head>

<body>
    <div class="main">
        <h1>MFA Verification Code</h1>
        
        <p>Hello ${firstName?.charAt(0).toUpperCase() + firstName?.slice(1)},</p>

        <p>You've requested a verification code for your Scribble account. Here's your one-time password:</p>

        <div class="otp-code">${otp}</div>

        <p><strong>This code will expire in 10 minutes.</strong></p>

        <div class="warning">
            <strong>Security Notice:</strong> Never share this code with anyone. Scribble staff will never ask for your verification code.
        </div>

        <p>If you didn't request this code, please contact our support team immediately at 
            <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>

        <p>Thanks,<br><strong>The Scribble Team</strong></p>
    </div>
</body>
</html>`;
};
