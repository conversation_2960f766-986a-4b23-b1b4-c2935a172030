module.exports = (password, firstName, scribbleUrl) => {
  return `<html>

<head>
    <meta charset="UTF-8">
    <title>Welcome to Scribble</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: 'Roboto', sans-serif;
            background-color: #f9f9f9;
            color: #333;
        }

        .header, .footer {
            background-color: #333;
            color: #fff;
            padding: 15px 0;
            text-align: center;
        }

        .header a, .footer a {
            color: #fff;
            font-weight: bold;
            text-decoration: none;
        }

        .main {
            max-width: 600px;
            margin: 80px auto 60px auto;
            background-color: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .main h1 {
            font-size: 26px;
            margin-bottom: 20px;
            color: #000;
        }

        .main p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .button-link {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background-color: #000;
            color: #fff !important;
            font-size: 16px;
            font-weight: bold;
            border-radius: 6px;
            text-decoration: none;
        }

        .temp-password {
            font-size: 18px;
            font-weight: bold;
            background-color: #f0f0f0;
            padding: 10px 15px;
            border-radius: 6px;
            display: inline-block;
            margin: 10px 0;
        }

        .footer {
            position: fixed;
            width: 100%;
            bottom: 0;
        }

        @media (max-width: 600px) {
            .main {
                margin: 100px 15px 80px 15px;
                padding: 25px;
            }
        }
    </style>
</head>

<body>

    <div class="main">
        <h1>Welcome to Scribble, ${firstName?.charAt(0).toUpperCase() + firstName?.slice(1)}!</h1>

        <p>We're excited to have you on board as we work together to transform healthcare.</p>

        <p>To help you get started, we've created a temporary password for your account:</p>

        <div class="temp-password">${password}</div>

        <p>For security reasons, you'll be prompted to change this password after your first login. Here's how:</p>
        <ul>
            <li>Go to <b><a href="${scribbleUrl}">Scribble</a></b>.</li>
            <li>Enter your email and temporary password.</li>
            <li>Follow the steps to create your own secure password.</li>
        </ul>

        <p>If you have any questions or run into issues, feel free to reach out to us at 
            <a href="mailto:<EMAIL>"><EMAIL></a>.
        </p>

        <p>Thank you for choosing Scribble. We're glad to have you with us!</p>

        <p>Best regards,<br><strong>The Scribble Team</strong></p>

        <a class="button-link" href="${scribbleUrl}">Log In to Scribble</a>
    </div>
</body>

</html>`;
};
