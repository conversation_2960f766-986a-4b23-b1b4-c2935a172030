{"openapi": "3.0.0", "info": {"title": "Scribble Healthcare API", "version": "1.0", "description": "Scribble is a comprehensive healthcare API that provides functionality for managing patient visits, assessments, clinician workflows, and administrative tasks in healthcare settings. This API supports both clinical and administrative operations.", "contact": {"name": "Scribble API Support", "email": "<EMAIL>"}}, "servers": [], "tags": [{"name": "<PERSON><PERSON>", "description": "Authentication and authorization endpoints for user management, including login, tenant management, and role-based access control"}, {"name": "Visit", "description": "Endpoints for managing patient visits, episodes of care, assessments, and clinical documentation"}, {"name": "Settings", "description": "Configuration endpoints for customizing application behavior, user preferences, and display settings"}, {"name": "User", "description": "User management endpoints for handling clinician and client profiles, notifications, and user-specific data"}, {"name": "Dashboard", "description": "Analytics and reporting endpoints providing insights into clinical operations and patient care metrics"}], "paths": {"/v1/auth/login": {"post": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "401": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Authenticates users through multiple methods including email/password and VIN-based federation. Supports both standard user authentication and Single Sign-On (SSO) workflows. Returns a JWT token for subsequent API calls.", "operationId": "post_auth_login", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}}}, "/v1/auth/tenant": {"post": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Creates a new tenant in the system. Tenants represent separate healthcare organizations or facilities with their own set of users, patients, and configurations. Requires administrative privileges.", "operationId": "post_auth_tenant_create", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "authorization", "schema": {"type": "string", "example": "Bearer token"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"oneOf": [{"type": "object", "properties": {"tenantName": {"type": "string"}}, "required": ["tenantName"]}]}}}, "required": true}, "security": [{"Bearer": []}]}, "get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantListResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Retrieves a list of all tenants accessible to the authenticated user. Supports pagination and filtering options for large-scale deployments.", "operationId": "get_auth_tenant_list", "parameters": [{"in": "header", "name": "authorization", "schema": {"type": "string", "example": "Bearer token"}, "required": true}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/auth/roles": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolesResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_auth_roles_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"name": "x-tenant-id", "in": "query", "required": false, "schema": {"type": "object", "properties": {"x-tenant-id": {"type": "string", "example": "67a89fad069cd3c52f8cff22"}}, "required": ["x-tenant-id"]}}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/auth/user": {"post": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseModel"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Perform user registration. If the client passes a redirect URL and the SSO requires verification, the response of the verification call will be redirected to that URL.(Currently used in developer registeration)", "operationId": "POST_v1-auth-user", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "employeeId": {"type": "string", "example": "2001"}, "primaryPhone": {"type": "string", "example": "************"}, "x-tenant-id": {"type": "string", "example": "67c03a5a14563c13b59a7846"}, "roleId": {"type": "string", "example": "67c03a5914563c13b59a7829"}, "firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON>"}, "status": {"type": "string", "enum": ["Active", "Inactive"], "example": "Active"}, "disciplineId": {"type": "string", "example": "67c03a5914563c13b59a7829"}, "jobTitle": {"type": "string", "example": "Therapist"}, "age": {"type": "string", "example": "50"}, "dob": {"type": "string", "format": "date", "example": "1974-07-08"}, "gender": {"type": "string", "enum": ["Male", "Female", "Other"], "example": "Male"}, "address1": {"type": "string", "example": "654 Pine St"}, "address2": {"type": "string", "example": ""}, "city": {"type": "string", "example": "San Diego"}, "state": {"type": "string", "example": "CA"}, "zip": {"type": "string", "example": "92101"}, "country": {"type": "string", "example": "USA"}}, "required": ["email", "employeeId", "x-tenant-id", "roleId", "firstName", "lastName", "status", "primaryPhone"]}}}}, "security": [{"Bearer": []}]}, "get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantListResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Retrieves a list of all users accessible to the authenticated user. Supports pagination and filtering options for large-scale deployments.", "operationId": "get_auth_user_list", "parameters": [{"in": "header", "name": "authorization", "schema": {"type": "string", "example": "Bearer token"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/auth/change-password": {"put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordResponse"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "description": "Change new password", "operationId": "put_auth_change_password", "requestBody": {"content": {"application/json": {"schema": {"properties": {"newPassword": {"type": "string"}, "oldPassword": {"type": "string"}}, "required": ["newPassword", "oldPassword"], "type": "object"}}}, "required": true}, "security": [{"Bearer": []}]}, "parameters": []}, "/v1/auth/user/{id}": {"put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordResponse"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}, {"name": "id", "in": "path", "schema": {"type": "string", "example": "haggaihealth"}}], "description": "Change new password", "operationId": "put_auth_user", "requestBody": {"content": {"application/json": {"schema": {"properties": {"isDeleted": {"type": "boolean"}}, "required": ["isDeleted"], "type": "object"}}}, "required": true}, "security": [{"Bearer": []}]}}, "/v1/auth/me": {"put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordResponse"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "description": "Change new password", "operationId": "put_auth_me", "requestBody": {"content": {"application/json": {"schema": {"properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "disciplineId": {"type": "string"}, "email": {"type": "string"}, "jobTitle": {"type": "string"}, "primaryPhone": {"type": "string"}, "dob": {"type": "string"}}, "required": [], "type": "object"}}}, "required": true}, "security": [{"Bearer": []}]}, "parameters": []}, "/v1/auth/recover-password-email": {"post": {"responses": {"200": {"description": "Password reset email sent successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponseRecoverPassword"}, "examples": {"success": {"summary": "<PERSON>ail sent successfully", "value": {"status": "ok", "data": {"message": "A password recovery link has been sent to your email address. Please check your inbox and follow the instructions to reset your password."}}}}}}}, "429": {"description": "Too many password reset email requests", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"rate_limited_email": {"summary": "Email rate limit exceeded", "value": {"status": "error", "errorMessage": "Please wait at least 5 minutes between password reset email requests.", "errorCode": "TOO_MANY_REQUESTS", "data": {}}}}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Sends a password reset email to the user. The email contains a secure link with a token that allows the user to reset their password. The token is valid for 24 hours and can only be used once. Rate limited to 3 email requests per 5 minutes per IP. For security reasons, this endpoint always returns a success response regardless of whether the email exists or not.", "operationId": "post_auth_recover_password_email", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": true, "description": "Tenant ID (required)"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"type": "string", "format": "email", "description": "User's email address"}}, "required": ["email"], "type": "object"}}}, "required": true}}}, "/v1/auth/recover-password": {"post": {"responses": {"200": {"description": "Password reset successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponseRecoverPassword"}, "examples": {"success": {"summary": "Password reset successful", "value": {"status": "ok", "data": {"message": "Your password has been reset. You may now log in with your new password."}}}}}}}, "400": {"description": "Invalid token, missing inputs, or password validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"invalid_token": {"summary": "Invalid or expired token", "value": {"status": "error", "errorMessage": "This password reset link is invalid or has expired. Please request a new password reset.", "errorCode": "INVALID_TOKEN", "data": {}}}, "password_validation_failed": {"summary": "Password does not meet requirements", "value": {"status": "error", "errorMessage": "Invalid password. Please ensure it is 12-64 characters, contains at least one uppercase letter, one number, and one special character.", "errorCode": "INVALID_DATA", "data": {}}}, "missing_inputs": {"summary": "Missing required inputs", "value": {"status": "error", "errorMessage": "Missing inputs - token, newPassword", "errorCode": "WRONG_INPUT", "data": {}}}}}}}, "429": {"description": "Too many password reset validation attempts", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"rate_limited_validation": {"summary": "Validation rate limit exceeded", "value": {"status": "error", "errorMessage": "Please wait at least 5 minutes between password reset validation attempts.", "errorCode": "TOO_MANY_REQUESTS", "data": {}}}}}}}}, "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": true, "description": "Tenant ID (required)"}], "tags": ["<PERSON><PERSON>"], "description": "Resets the user's password using a valid reset token. The token is obtained from the password reset email link. Tokens are one-time use and expire after 24 hours. The new password must meet security requirements: 12-64 characters, at least one uppercase letter, one number, and one special character. The password is also checked against known data breaches for security. Rate limited to 3 validation attempts per 5 minutes per IP.", "operationId": "post_auth_recover_password", "requestBody": {"content": {"application/json": {"schema": {"properties": {"token": {"type": "string", "description": "Password reset token from email link"}, "newPassword": {"type": "string", "description": "New password (must be 12-64 characters, contain at least one uppercase letter, one number, and one special character, and not found in data breaches)", "minLength": 12, "maxLength": 64}}, "required": ["token", "newPassword"], "type": "object"}}}, "required": true}}}, "/v1/auth/logout": {"post": {"security": [{"Bearer": []}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoutResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Clear the data from client and server side.", "operationId": "post_auth_logout", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"refreshToken": {"type": "string"}}, "required": ["refreshToken"], "type": "object"}}}, "required": true}}}, "/v1/auth/refresh": {"post": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoutResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Get new accesstoken by using refreshtoken", "operationId": "post_auth_refresh_token", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"refreshToken": {"type": "string"}}, "required": ["refreshToken"], "type": "object"}}}, "required": true}}}, "/v1/auth/deleteAccount": {"post": {"security": [{"Bearer": []}], "responses": {"200": {"description": "Account deletion audit record created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAccountResponse"}}}}, "400": {"description": "Bad request - Email is required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden - Insufficient permissions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "tags": ["<PERSON><PERSON>"], "description": "Create an audit record for account deletion. This endpoint stores the email and metadata for audit purposes when a user account is being deleted.", "operationId": "post_auth_delete_account", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"type": "string", "format": "email", "description": "Email address of the account to be deleted", "example": "<EMAIL>"}}, "required": ["email"], "type": "object"}}}, "required": true}}}, "/v1/user/client": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientResponse"}}}}}, "tags": ["User"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_user_client_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/user/clinician": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicianResponse"}}}}}, "tags": ["User"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_user_clinician_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/user/clinician/{id}": {"put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClinicianResponse"}}}}}, "tags": ["User"], "description": "Update a clinician", "operationId": "put_user_clinician_update", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "security": [{"Bearer": []}]}}, "/v1/user/clinician/visitDetails": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitDetailResponse"}}}}}, "tags": ["User"], "description": "Get the visit details of the clinician", "operationId": "get_user_clinician_visit_details", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "visitDate", "in": "query", "schema": {"type": "string", "example": "02/15/2025"}}], "security": [{"Bearer": []}]}}, "/v1/user/notification": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}}, "tags": ["User"], "description": "Get the notifications of the user", "operationId": "get_user_notifications", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}], "security": [{"Bearer": []}]}}, "/v1/visit/form": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssessmentResponse"}, "example": {"status": "ok", "data": [{"_id": "67cf182ecd6dbb2d123519e8", "formTypeId": 1, "createdAt": "2025-03-10T16:49:50.961Z", "updatedAt": "2025-03-14T12:16:04.920Z", "__v": 0, "assessmentForm": [{"question_code": "A1110.A", "question": "What is your preferred language?", "question_type": "TEXT_INPUT", "options": ["0. English", "1. Spanish", "9. Unable to determine"], "section": "Administrative"}], "formName": "Start of Care"}], "total_records_available": 1}}}}}, "tags": ["Visit"], "description": "Retrieves assessment forms and templates used for clinical documentation. Forms can include various types of clinical assessments, questionnaires, and documentation templates.", "operationId": "get_visit_form_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"in": "query", "name": "limit", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}, "post": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormPostResponse"}, "example": {"status": "ok", "data": {"formName": "ROC", "assessmentForm": [{"question_code": "M1610", "question": "Have you been experiencing any issues with urinary leakage or incontinence? Do you currently use a urinary catheter?", "options": ["0. No incontinence or catheter (includes anuria or ostomy for urinary drainage)", "1. <PERSON>ient is incontinent", "2. Patient requires a urinary catheter (specifically: external, indwelling, intermittent, or suprapubic)"], "question_type": "TEXT_INPUT", "section": "Bladder and Bowel"}, {"question_code": "M1028", "question": "Do you have any other active health conditions that we should be aware of?", "options": ["1. Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)", "2. <PERSON><PERSON><PERSON> (DM)", "3. None of the above"], "question_type": "TEXT_INPUT", "section": "Medications"}], "_id": "67db119962a34a167b810ade", "createdAt": "2025-03-19T18:48:57.085Z", "updatedAt": "2025-03-19T18:48:57.086Z", "__v": 0}}}}}}, "tags": ["Visit"], "description": "Creates new assessment forms or documentation templates. Supports multiple question types including text input, checklists, radio buttons, and dropdowns. Forms can be organized by sections for better usability.", "operationId": "post_visit_form_create", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormPostRequest"}, "example": {"formName": "ROC", "disciplineId": "67eb2eb2c9c8d85d9568ce14", "formTypeId": "67eb2eb2c9c8d85d9568ce21", "assessmentForm": [{"question_code": "M1610", "question": "Have you been experiencing any issues with urinary leakage or incontinence? Do you currently use a urinary catheter?", "options": ["0. No incontinence or catheter (includes anuria or ostomy for urinary drainage)", "1. <PERSON>ient is incontinent", "2. Patient requires a urinary catheter (specifically: external, indwelling, intermittent, or suprapubic)"], "question_type": "TEXT_INPUT", "section": "Bladder and Bowel"}, {"question_code": "M1028", "question": "Do you have any other active health conditions that we should be aware of?", "options": ["1. Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)", "2. <PERSON><PERSON><PERSON> (DM)", "3. None of the above"], "question_type": "TEXT_INPUT", "section": "Medications"}]}}}}, "security": [{"Bearer": []}]}}, "/v1/visit/form/{id}": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormPutResponse"}, "example": {"status": "ok", "data": {"formName": "ROC", "assessmentForm": [{"question_code": "M1610", "question": "Have you been experiencing any issues with urinary leakage or incontinence? Do you currently use a urinary catheter?", "options": ["0. No incontinence or catheter (includes anuria or ostomy for urinary drainage)", "1. <PERSON>ient is incontinent", "2. Patient requires a urinary catheter (specifically: external, indwelling, intermittent, or suprapubic)"], "question_type": "TEXT_INPUT", "section": "Bladder and Bowel"}, {"question_code": "M1028", "question": "Do you have any other active health conditions that we should be aware of?", "options": ["1. Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)", "2. <PERSON><PERSON><PERSON> (DM)", "3. None of the above"], "question_type": "TEXT_INPUT", "section": "Medications"}], "_id": "67db119962a34a167b810ade", "createdAt": "2025-03-19T18:48:57.085Z", "updatedAt": "2025-03-19T18:48:57.086Z", "__v": 0}}}}}}, "tags": ["Visit"], "description": "Get the form of the visit", "operationId": "get_visit_form_by_id", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "security": [{"Bearer": []}]}, "put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormPutResponse"}, "example": {"status": "ok", "data": {"formName": "ROC", "assessmentForm": [{"question_code": "M1610", "question": "Have you been experiencing any issues with urinary leakage or incontinence? Do you currently use a urinary catheter?", "options": ["0. No incontinence or catheter (includes anuria or ostomy for urinary drainage)", "1. <PERSON>ient is incontinent", "2. Patient requires a urinary catheter (specifically: external, indwelling, intermittent, or suprapubic)"], "question_type": "TEXT_INPUT", "section": "Bladder and Bowel"}, {"question_code": "M1028", "question": "Do you have any other active health conditions that we should be aware of?", "options": ["1. Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)", "2. <PERSON><PERSON><PERSON> (DM)", "3. None of the above"], "question_type": "TEXT_INPUT", "section": "Medications"}], "_id": "67db119962a34a167b810ade", "createdAt": "2025-03-19T18:48:57.085Z", "updatedAt": "2025-03-19T18:48:57.086Z", "__v": 0}}}}}}, "tags": ["Visit"], "description": "Update the form of the visit", "operationId": "put_visit_form_update", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Form"}, "example": {"assessmentForm": [{"question_code": "A1110.A", "question": "What is your preferred language?", "question_type": "TEXT_INPUT", "options": ["0. English", "1. Spanish", "9. Unable to determine"], "section": "Administrative"}]}}}}, "security": [{"Bearer": []}]}, "delete": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormPutResponse"}, "example": {"status": "ok", "data": {"formName": "ROC", "assessmentForm": [{"question_code": "M1610", "question": "Have you been experiencing any issues with urinary leakage or incontinence? Do you currently use a urinary catheter?", "options": ["0. No incontinence or catheter (includes anuria or ostomy for urinary drainage)", "1. <PERSON>ient is incontinent", "2. Patient requires a urinary catheter (specifically: external, indwelling, intermittent, or suprapubic)"], "question_type": "TEXT_INPUT", "section": "Bladder and Bowel"}, {"question_code": "M1028", "question": "Do you have any other active health conditions that we should be aware of?", "options": ["1. Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)", "2. <PERSON><PERSON><PERSON> (DM)", "3. None of the above"], "question_type": "TEXT_INPUT", "section": "Medications"}], "_id": "67db119962a34a167b810ade", "createdAt": "2025-03-19T18:48:57.085Z", "updatedAt": "2025-03-19T18:48:57.086Z", "__v": 0}}}}}}, "tags": ["Visit"], "description": "Delete the form of the visit", "operationId": "delete_visit_form_update", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "security": [{"Bearer": []}]}}, "/v1/visit/template": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssessmentResponse"}, "example": {"status": "ok", "data": [{"_id": "67cf182ecd6dbb2d123519e8", "disciplineId": {"_id": "67eb2eb2c9c8d85d9568ce11", "name": "RN"}, "formTypeId": {"_id": "67eb2eb2c9c8d85d9568ce23", "name": "Fall Risk"}, "createdAt": "2025-03-10T16:49:50.961Z", "updatedAt": "2025-03-14T12:16:04.920Z", "__v": 0, "assessmentForm": [{"question_code": "A1110.A", "question": "What is your preferred language?", "question_type": "TEXT_INPUT", "options": ["0. English", "1. Spanish", "9. Unable to determine"], "section": "Administrative"}], "formName": "Start of Care"}], "total_records_available": 1}}}}}, "tags": ["Visit"], "description": "Get the form of the visit", "operationId": "get_visit_template_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"in": "query", "name": "limit", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/visit/template/{id}": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormPutResponse"}, "example": {"status": "ok", "data": {"formName": "ROC", "assessmentForm": [{"question_code": "M1610", "question": "Have you been experiencing any issues with urinary leakage or incontinence? Do you currently use a urinary catheter?", "options": ["0. No incontinence or catheter (includes anuria or ostomy for urinary drainage)", "1. <PERSON>ient is incontinent", "2. Patient requires a urinary catheter (specifically: external, indwelling, intermittent, or suprapubic)"], "question_type": "TEXT_INPUT", "section": "Bladder and Bowel"}, {"question_code": "M1028", "question": "Do you have any other active health conditions that we should be aware of?", "options": ["1. Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)", "2. <PERSON><PERSON><PERSON> (DM)", "3. None of the above"], "question_type": "TEXT_INPUT", "section": "Medications"}], "_id": "67db119962a34a167b810ade", "createdAt": "2025-03-19T18:48:57.085Z", "updatedAt": "2025-03-19T18:48:57.086Z", "__v": 0}}}}}}, "tags": ["Visit"], "description": "Get the form template of the visit", "operationId": "get_visit_template_by_id", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "security": [{"Bearer": []}]}, "put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FormPutResponse"}, "example": {"status": "ok", "data": {"formName": "ROC", "assessmentForm": [{"question_code": "M1610", "question": "Have you been experiencing any issues with urinary leakage or incontinence? Do you currently use a urinary catheter?", "options": ["0. No incontinence or catheter (includes anuria or ostomy for urinary drainage)", "1. <PERSON>ient is incontinent", "2. Patient requires a urinary catheter (specifically: external, indwelling, intermittent, or suprapubic)"], "question_type": "TEXT_INPUT", "section": "Bladder and Bowel"}, {"question_code": "M1028", "question": "Do you have any other active health conditions that we should be aware of?", "options": ["1. Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)", "2. <PERSON><PERSON><PERSON> (DM)", "3. None of the above"], "question_type": "TEXT_INPUT", "section": "Medications"}], "_id": "67db119962a34a167b810ade", "createdAt": "2025-03-19T18:48:57.085Z", "updatedAt": "2025-03-19T18:48:57.086Z", "__v": 0}}}}}}, "tags": ["Visit"], "description": "Update the form template of the visit", "operationId": "put_visit_template_update", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Form"}, "example": {"assessmentForm": [{"question_code": "A1110.A", "question": "What is your preferred language?", "question_type": "TEXT_INPUT", "options": ["0. English", "1. Spanish", "9. Unable to determine"], "section": "Administrative"}]}}}}, "security": [{"Bearer": []}]}}, "/v1/visit": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitListResponse"}}}}}, "tags": ["Visit"], "description": "Retrieves visit records with comprehensive details including patient information, clinician assignments, visit types, and scheduling information. Results are automatically sorted by visit date (ascending) and then by start time (ascending) within each date. Supports pagination and advanced filtering.", "operationId": "get_visit_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}, "post": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitResponse"}}}}}, "tags": ["Visit"], "description": "Creates new visit records. Captures essential visit details including patient information, assigned clinician, visit type, service codes, and scheduling information.", "operationId": "post_visit_create", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID (optional)"}, {"in": "header", "name": "orchestrator-queue-name", "schema": {"type": "string", "example": "haggaihealth_kantime_performer"}, "required": false, "description": "Orchestrator queue name for EMR processing (optional)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Visit"}}}}, "security": [{"Bearer": []}]}, "put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Visit"}}}}}, "tags": ["Visit"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "put_visit_update", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitUpdateRequest"}}}}, "security": [{"Bearer": []}]}, "delete": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Visit"}}}}}, "tags": ["Visit"], "description": "Delete the visit record by no", "operationId": "delete_visit_by_no", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitDeleteRequest"}}}}, "security": [{"Bearer": []}]}}, "/v1/visit/{id}": {"put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Visit"}}}}}, "tags": ["Visit"], "description": "Update the visit record by id", "operationId": "put_visit_update_by_id", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitUpdateRequest"}}}}, "security": [{"Bearer": []}]}}, "/v1/visit/assessment": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssessmentResponse"}}}}}, "tags": ["Visit"], "description": "Retrieves completed or in-progress patient assessments. Supports filtering by visit ID and includes detailed clinical documentation and responses.", "operationId": "get_visit_assessment_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"type": "object", "properties": {"visitId": {"type": "string", "example": "67c669e52f53af0f16bf90f4"}}, "required": ["visitId"]}}], "security": [{"Bearer": []}]}}, "/v1/visit/assessment/{id}": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssessmentResponse"}, "example": {"status": "ok", "data": {"_id": "67cf182ecd6dbb2d123519e8", "formTypeId": 1, "createdAt": "2025-03-10T16:49:50.961Z", "updatedAt": "2025-03-19T18:34:21.310Z", "__v": 0, "assessmentForm": [{"question_code": "A1110.A", "question": "What is your preferred language?", "question_type": "TEXT_INPUT", "options": ["0. English", "1. Spanish", "9. Unable to determine"], "section": "Administrative"}], "formName": "Start of Care"}}}}}}, "tags": ["Visit"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for all other requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_visit_assessment_by_id", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "security": [{"Bearer": []}]}, "put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssessmentResponse"}}}}}, "tags": ["Visit"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for all other requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "put_visit_assessment_update", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "id", "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssessmentUpdateRequest"}}}}, "security": [{"Bearer": []}]}}, "/v1/visit/episode": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitListResponse"}}}}}, "tags": ["Visit"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_visit_episode_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/visit/formTypes": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitListResponse"}}}}}, "tags": ["Visit"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_visit_formtype_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/visit/discipline": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VisitListResponse"}}}}}, "tags": ["Visit"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_visit_discipline_list", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}, {"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "filters", "in": "query", "schema": {"$ref": "#/components/schemas/Filter"}}], "security": [{"Bearer": []}]}}, "/v1/setting/gridView": {"put": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "tags": ["Settings"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "put_setting_grid_view_update", "parameters": [{"in": "header", "name": "content-type", "schema": {"type": "string", "example": "application/json"}, "required": true}, {"in": "header", "name": "authorization", "schema": {"type": "string", "example": "Bearer token"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"gridName": {"type": "string"}, "viewJson": {"type": "object", "properties": {"clinicianId": {"$ref": "#/components/schemas/LogoutResponse"}, "clinicianNo": {"$ref": "#/components/schemas/LogoutResponse"}, "firstName": {"$ref": "#/components/schemas/LogoutResponse"}, "lastName": {"$ref": "#/components/schemas/LogoutResponse"}, "status": {"$ref": "#/components/schemas/FieldSchema"}, "discipline": {"$ref": "#/components/schemas/FieldSchema"}, "jobTitle": {"$ref": "#/components/schemas/FieldSchema"}, "email": {"$ref": "#/components/schemas/FieldSchema"}, "age": {"$ref": "#/components/schemas/FieldSchema"}, "dob": {"$ref": "#/components/schemas/FieldSchema"}, "gender": {"$ref": "#/components/schemas/FieldSchema"}, "address1": {"$ref": "#/components/schemas/FieldSchema"}, "address2": {"$ref": "#/components/schemas/FieldSchema"}, "city": {"$ref": "#/components/schemas/FieldSchema"}, "state": {"$ref": "#/components/schemas/FieldSchema"}, "zip": {"$ref": "#/components/schemas/FieldSchema"}, "country": {"$ref": "#/components/schemas/FieldSchema"}, "primaryPhone": {"$ref": "#/components/schemas/FieldSchema"}}}}, "required": ["gridName", "viewJson"]}}}, "required": true}, "security": [{"Bearer": []}]}, "get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "tags": ["Settings"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_setting_grid_view", "parameters": [{"in": "header", "name": "authorization", "schema": {"type": "string", "example": "Bearer token"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}, {"name": "gridName", "in": "query", "required": true, "schema": {"type": "string", "enum": ["clinicianTable", "clientTable", "visitTable"], "example": "clinicianTable"}}], "security": [{"Bearer": []}]}}, "/v1/dashboard/admin": {"get": {"responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "tags": ["Dashboard"], "description": "Perform federated user authentication. This returns an extended user model with added apiKey property to use for allother requests. Basically it is login api for web portal(dev). There are 2 ways to login. 1. Email Login 2. VIN login. For VIN login, <PERSON><PERSON> will get the jwt from routing server and attach it in the header using which we are going to login the user", "operationId": "get_dashboard_admin_metrics", "parameters": [{"in": "header", "name": "authorization", "schema": {"type": "string", "example": "Bearer token"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}], "security": [{"Bearer": []}]}}, "/v1/clinician/upload-audio": {"post": {"requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"audio": {"type": "string", "format": "binary", "description": "Audio file to upload"}, "assessmentId": {"type": "string", "example": "67a95e6208705766eb2a4619", "description": "ID of the assessment"}}, "required": ["audio", "assessmentId"]}}}}, "responses": {"200": {"description": "Audio file uploaded successfully and message sent to SQS", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadAudioResponse"}}}}}, "tags": ["Clinician"], "description": "Handles audio file uploads for clinical documentation. Supports voice recordings that can be processed for transcription and documentation purposes. Integrates with AWS S3 for storage and SQS for processing.", "operationId": "post_clinician_upload_audio", "parameters": [{"in": "header", "name": "authorization", "schema": {"type": "string", "example": "Bearer token"}, "required": true}, {"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "haggaihealth"}, "required": false, "description": "Tenant ID of the customer"}], "security": [{"Bearer": []}]}}, "/v2/auth/login": {"post": {"tags": ["Auth V2"], "summary": "V2 User Login", "description": "Authenticates users with enhanced security, validation, and rate limiting. This endpoint is specifically designed for mobile applications and includes comprehensive header validation and standardized v2 response formats. Supports all user roles within the tenant.", "operationId": "v2_clinician_login", "parameters": [{"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "dev1"}, "required": true, "description": "Tenant identifier for multi-tenant architecture"}, {"in": "header", "name": "x-device-id", "schema": {"type": "string", "example": "uuid-1234-5678-90ab"}, "required": true, "description": "Unique device identifier for rate limiting and tracking"}, {"in": "header", "name": "x-device-model", "schema": {"type": "string", "example": "iPhone14,2"}, "required": true, "description": "Device model information for analytics"}, {"in": "header", "name": "x-platform", "schema": {"type": "string", "enum": ["iOS", "Android", "Web"], "example": "iOS"}, "required": true, "description": "Platform type (iOS, Android, Web)"}, {"in": "header", "name": "x-os-version", "schema": {"type": "string", "example": "iOS 17.1"}, "required": true, "description": "Operating system version"}, {"in": "header", "name": "x-app-version", "schema": {"type": "string", "example": "5.2.0 50200"}, "required": true, "description": "Application version and build number"}, {"in": "header", "name": "x-ip-address", "schema": {"type": "string", "example": "************"}, "required": true, "description": "Client IP address for security and rate limiting"}, {"in": "header", "name": "x-timezone", "schema": {"type": "string", "example": "Asia/Kolkata"}, "required": true, "description": "Client timezone for localized responses"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2LoginRequest"}, "examples": {"valid_credentials": {"summary": "Valid user credentials", "value": {"email": "<EMAIL>", "password": "SecurePassword123!"}}, "invalid_credentials": {"summary": "Invalid credentials example", "value": {"email": "<EMAIL>", "password": "WrongPassword"}}}}}}, "responses": {"200": {"description": "Login successful - Returns access token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2LoginSuccessResponse"}, "examples": {"success": {"summary": "Successful login response", "value": {"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ2IjoyLCJ0eXBlIjoiYWNjZXNzIiwiaWQiOiI2ODJmM2ZhMGNiYTEzODMxNjQyMmFmZTIiLCJyb2xlcyI6WyJjbGluaWNpYW4iXSwic2NvcGVzIjpbImFzc2Vzc21lbnQuY3JlYXRlIiwiYXNzZXNzbWVudC5yZWFkIiwiYXNzZXNzbWVudC51cGRhdGUiLCJhc3Nlc3NtZW50LmRlbGV0ZSIsImFuc3dlci5jcmVhdGUiLCJhbnN3ZXIucmVhZCIsImFuc3dlci51cGRhdGUiLCJhbnN3ZXIuZGVsZXRlIiwiZm9ybS5jcmVhdGUiLCJmb3JtLnJlYWQiLCJmb3JtLnVwZGF0ZSIsImZvcm0uZGVsZXRlIiwidmlzaXQuY3JlYXRlIiwidmlzaXQucmVhZCIsInZpc2l0LnVwZGF0ZSIsInNlbGYucmVhZCIsInNlbGYudXBkYXRlIl0sImlzc3VlciI6IlNjcmliYmxlIiwiaWF0IjoxNzUzMjU2MjUwLCJleHAiOjE3NTMyNjM0NTB9.6veuBiuVl11G00qktcVUswnRMK0XGwh931a-hoCvhuI", "message": "Login successful"}}}}}}, "400": {"description": "Bad Request - Missing or invalid headers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"missing_headers": {"summary": "Missing required headers", "value": {"errors": [{"field": "headers", "message": "Missing or invalid required headers", "code": "HEADER_VALIDATION_ERROR"}], "message": "Missing or invalid required headers"}}, "validation_error": {"summary": "Input validation error", "value": {"errors": [{"field": "credentials", "message": "Please check your email and password", "code": "AUTH_ERROR"}], "message": "Please check your email and password"}}}}}}, "401": {"description": "Unauthorized - Invalid credentials or tenant", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"invalid_credentials": {"summary": "Invalid credentials", "value": {"errors": [{"field": "credentials", "message": "Please check your email and password", "code": "AUTH_ERROR"}], "message": "Please check your email and password"}}, "tenant_not_found": {"summary": "Tenant not found", "value": {"errors": [{"field": "auth", "message": "Invalid tenant or credentials", "code": "TENANT_NOT_FOUND"}], "message": "Invalid tenant or credentials"}}, "account_locked": {"summary": "Account locked", "value": {"errors": [{"field": "auth", "message": "Account is locked. Please contact administrator.", "code": "ACCOUNT_LOCKED"}], "message": "Account is locked. Please contact administrator."}}, "account_inactive": {"summary": "Account inactive", "value": {"errors": [{"field": "auth", "message": "Account has been deactivated. Please contact administrator.", "code": "ACCOUNT_INACTIVE"}], "message": "Account has been deactivated. Please contact administrator."}}}}}}, "429": {"description": "Too Many Requests - Rate limit exceeded", "headers": {"RateLimit-Limit": {"schema": {"type": "string", "example": "5"}, "description": "Maximum number of requests allowed per window"}, "RateLimit-Remaining": {"schema": {"type": "string", "example": "0"}, "description": "Number of requests remaining in current window"}, "RateLimit-Reset": {"schema": {"type": "string", "example": "**********"}, "description": "Timestamp when the rate limit window resets"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"rate_limit_exceeded": {"summary": "Rate limit exceeded", "value": {"errors": [{"field": "auth", "message": "Too many login attempts. Please wait 15 minutes before trying again.", "code": "RATE_LIMIT_EXCEEDED"}], "message": "Too many login attempts. Please wait 15 minutes before trying again."}}}}}}, "503": {"description": "Service Unavailable - System error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"service_unavailable": {"summary": "Service temporarily unavailable", "value": {"errors": [{"field": "service", "message": "Service temporarily unavailable. Please try again later.", "code": "SERVICE_UNAVAILABLE"}], "message": "Service temporarily unavailable. Please try again later."}}}}}}}}}, "/v2/auth/profile": {"get": {"tags": ["Auth V2"], "summary": "V2 Get User Profile", "description": "Retrieves the current user's profile information including personal details, contact information, and role details. This endpoint requires authentication and returns user-friendly error messages for better frontend integration.", "operationId": "v2_get_profile", "security": [{"Bearer": []}], "parameters": [{"in": "header", "name": "x-tenant-id", "schema": {"type": "string", "example": "dev1"}, "required": true, "description": "Tenant identifier for multi-tenant architecture"}, {"in": "header", "name": "x-device-id", "schema": {"type": "string", "example": "uuid-1234-5678-90ab"}, "required": true, "description": "Unique device identifier for rate limiting and tracking"}, {"in": "header", "name": "x-device-model", "schema": {"type": "string", "example": "iPhone14,2"}, "required": true, "description": "Device model information for analytics"}, {"in": "header", "name": "x-platform", "schema": {"type": "string", "enum": ["iOS", "Android", "Web"], "example": "iOS"}, "required": true, "description": "Platform type (iOS, Android, Web)"}, {"in": "header", "name": "x-os-version", "schema": {"type": "string", "example": "iOS 17.1"}, "required": true, "description": "Operating system version"}, {"in": "header", "name": "x-app-version", "schema": {"type": "string", "example": "5.2.0 50200"}, "required": true, "description": "Application version and build number"}, {"in": "header", "name": "x-ip-address", "schema": {"type": "string", "example": "************"}, "required": true, "description": "Client IP address for security and rate limiting"}, {"in": "header", "name": "x-timezone", "schema": {"type": "string", "example": "Asia/Kolkata"}, "required": true, "description": "Client timezone for localized responses"}], "responses": {"200": {"description": "Profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ProfileResponse"}, "examples": {"success": {"summary": "Successful profile response", "value": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "staffId": "STAFF001", "discipline": "Physical Therapy", "phone": "+**********", "dateOfBirth": "1990-01-01T00:00:00.000Z", "role": "Clinician"}}}}}}, "400": {"description": "Bad Request - Missing or invalid headers", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"missing_headers": {"summary": "Missing required headers", "value": {"errors": [{"field": "headers", "message": "Please ensure all required headers are included in your request", "code": "HEADER_VALIDATION_ERROR"}], "message": "Please ensure all required headers are included in your request"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing authentication token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"no_token": {"summary": "No authorization token provided", "value": {"errors": [{"field": "auth", "message": "No authorization token was found", "code": "INVALID_TOKEN"}], "message": "No authorization token was found"}}, "invalid_token": {"summary": "Invalid or expired token", "value": {"errors": [{"field": "auth", "message": "Token invalid or expired", "code": "INVALID_TOKEN"}], "message": "Token invalid or expired"}}}}}}, "404": {"description": "Not Found - Profile not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"profile_not_found": {"summary": "Profile not found", "value": {"errors": [{"field": "profile", "message": "Your profile information could not be found. Please contact your administrator.", "code": "PROFILE_NOT_FOUND"}], "message": "Your profile information could not be found. Please contact your administrator."}}}}}}, "500": {"description": "Internal Server Error - Server-side error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V2ErrorResponse"}, "examples": {"schema_error": {"summary": "<PERSON><PERSON><PERSON> error", "value": {"errors": [{"field": "profile", "message": "There was an issue loading your profile data. Please try again later.", "code": "SCHEMA_ERROR"}], "message": "There was an issue loading your profile data. Please try again later."}}, "internal_error": {"summary": "General internal error", "value": {"errors": [{"field": "profile", "message": "We're having trouble loading your profile right now. Please try again in a few moments.", "code": "INTERNAL_ERROR"}], "message": "We're having trouble loading your profile right now. Please try again in a few moments."}}}}}}}}}}, "components": {"schemas": {"Filter": {"type": "object", "properties": {"firstName": {"type": "string", "example": "thillai"}, "lastName": {"type": "string", "example": "sathish"}}}, "ApiResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "string", "example": "Needs to be developed"}}}, "ChangePasswordResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "Password updated."}}}}}, "DeleteVisitResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "<PERSON><PERSON><PERSON> deleted successfully."}}}, "VisitDeleteRequest": {"type": "object", "properties": {"no": {"type": "string", "example": "**********"}}}, "LoginResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "userId": {"type": "string", "example": "67a95e6208705766eb2a4619"}, "roles": {"type": "string", "example": "scribble_admin"}, "scopes": {"type": "array", "items": {"type": "string"}, "example": ["sso.write"]}, "accessToken": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ2IjoyLCJ..."}, "refreshToken": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0eXBlIjoic..."}, "created": {"type": "string", "format": "date-time", "example": "2025-02-10T02:03:14.080Z"}, "updated": {"type": "string", "format": "date-time", "example": "2025-02-10T02:03:14.080Z"}}}}}, "ClinicianResponse": {"type": "object", "properties": {"status": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"_id": {"type": "string"}, "userId": {"type": "string"}, "clinicianNo": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "primaryPhone": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "__v": {"type": "integer"}, "address1": {"type": "string"}, "city": {"type": "string"}, "discipline": {"type": "string"}, "dob": {"type": "string", "format": "date"}, "state": {"type": "string", "minLength": 2, "maxLength": 2}, "status": {"type": "string"}, "zip": {"type": "string", "pattern": "^[0-9]{5}$"}}, "required": ["_id", "userId", "clinician<PERSON>o", "firstName", "lastName", "primaryPhone", "createdAt", "updatedAt", "__v", "address1", "city", "discipline", "dob", "state", "status", "zip"]}}, "total_records_available": {"type": "integer"}}, "required": ["status", "data", "total_records_available"]}, "ClientResponse": {"type": "object", "properties": {"status": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"_id": {"type": "string"}, "clientId": {"type": "string"}, "clientGroupId": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "dateOfBirth": {"type": "string", "format": "date"}, "address": {"type": "object", "properties": {"addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string", "minLength": 2, "maxLength": 2}, "zipcode": {"type": "string"}, "county": {"type": "string"}}}, "phone": {"type": "string", "pattern": "^\\(\\d{3}\\) \\d{3}-\\d{4}$"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "__v": {"type": "integer"}}, "required": ["_id", "clientId", "clientGroupId", "firstName", "lastName", "dateOfBirth", "phone", "createdAt", "updatedAt", "__v"]}}, "total_records_available": {"type": "integer"}}, "required": ["status", "data", "total_records_available"]}, "VisitDetailResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"overallCounts": {"type": "object", "properties": {"_id": {"type": "string", "nullable": true, "example": null}, "totalVisits": {"type": "integer", "example": 5}, "newVisits": {"type": "integer", "example": 2}, "inProgressVisits": {"type": "integer", "example": 1}, "completedVisits": {"type": "integer", "example": 2}}}, "todayVisits": {"type": "array", "items": {"type": "object", "properties": {"_id": {"type": "string", "nullable": true, "example": null}, "totalVisits": {"type": "integer", "example": 4}, "newVisits": {"type": "integer", "example": 2}, "inProgressVisits": {"type": "integer", "example": 1}, "completedVisits": {"type": "integer", "example": 1}}}}}}}}, "FormGetResponse": {}, "FormPutResponse": {"type": "object", "properties": {"status": {"type": "string"}, "data": {"type": "object", "properties": {"_id": {"type": "string"}, "formTypeId": {"type": "integer"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "__v": {"type": "integer"}, "assessmentForm": {"type": "array", "items": {"type": "object", "properties": {"question_code": {"type": "string"}, "question": {"type": "string"}, "question_type": {"type": "string", "enum": ["TEXT_INPUT", "CHECK_LIST", "RADIO", "DROPDOWN"]}, "options": {"type": "array", "items": {"type": "string"}}, "section": {"type": "string"}}}}, "formName": {"type": "string"}}}}}, "TenantResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"tenantName": {"type": "string", "example": "amedysis1"}, "databaseName": {"type": "string", "example": "amedysis1"}, "createdBy": {"type": "string", "format": "ObjectId", "example": "67a89f512d0f29cb9a87c940"}, "_id": {"type": "string", "format": "ObjectId", "example": "67a89fad069cd3c52f8cff1f"}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-02-09T12:29:33.215Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-02-09T12:29:33.215Z"}, "__v": {"type": "integer", "example": 0}}}}}, "TenantListResponse": {"type": "object", "properties": {"_id": {"type": "string", "example": "67b4a43ef61eb03853b863fb"}, "tenantName": {"type": "string", "example": "kantime"}, "databaseName": {"type": "string", "example": "kantime"}, "createdBy": {"type": "string", "example": "67b4a3fdf61eb03853b863f5"}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-02-18T15:16:14.100Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-02-18T15:16:14.100Z"}, "__v": {"type": "integer", "example": 0}}}, "VisitUpdateRequest": {"type": "object", "properties": {"visitStatus": {"type": "string", "enum": ["New", "In Progress", "Visit Not Conducted", "Past Due", "Submitted for Processing", "Completed", "Needs Attention", "Missed"], "example": "In Progress"}, "visitDate": {"type": "string", "format": "date", "example": "2025-02-13"}, "visitStartTime": {"type": "string", "example": "11:00 AM"}, "visitType": {"type": "string", "example": "Normal"}}}, "AssessmentUpdateRequest": {"type": "object", "properties": {"status": {"type": "string", "enum": ["Not Started", "In Progress", "Draft Saved", "Submitted to AI", "Ready for Review", "Submitted to EMR", "Completed", "AI Processing Failed", "EMR Processing Failed"], "example": "In Progress"}, "question": {"type": "array", "items": {"type": "object", "properties": {"question_code": {"type": "string"}, "question": {"type": "string"}, "question_type": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}, "section": {"type": "string"}, "answer": {"type": "string"}}}}}}, "AssessmentResponse": {"type": "object", "properties": {"status": {"type": "string"}, "disciplineId": {"type": "object", "properties": {"_id": {"type": "string"}, "name": {"type": "string"}}}, "formTypeId": {"type": "object", "properties": {"_id": {"type": "string"}, "name": {"type": "string"}}}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Assessment"}}, "total_records_available": {"type": "integer"}}}, "LoginRequest": {"oneOf": [{"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "Admin@123"}}, "required": ["username", "password"]}]}, "LogoutResponse": {"type": "object", "properties": {"status": {"type": "string"}, "data": {"type": "object"}}}, "FieldSchema": {"type": "object", "properties": {"isRequired": {"type": "boolean"}, "isVisible": {"type": "boolean"}, "label": {"type": "string"}, "questionType": {"type": "string", "enum": ["TEXT_INPUT"]}, "options": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}}, "required": ["name", "value"]}}, "required": ["isRequired", "isVisible", "label", "questionType", "options"]}, "Assessment": {"type": "object", "properties": {"_id": {"type": "string"}, "formTypeId": {"type": "integer"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "__v": {"type": "integer"}, "assessmentForm": {"type": "array", "items": {"$ref": "#/components/schemas/AssessmentForm"}}, "formName": {"type": "string"}}}, "AssessmentForm": {"type": "object", "properties": {"question_code": {"type": "string"}, "question": {"type": "string"}, "question_type": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}, "section": {"type": "string"}}}, "FormPostResponse": {"type": "object", "properties": {"status": {"type": "string"}, "data": {"type": "object", "properties": {"_id": {"type": "string"}, "formName": {"type": "string"}, "assessmentForm": {"type": "array", "items": {"$ref": "#/components/schemas/AssessmentForm"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "__v": {"type": "integer"}}}}}, "FormPostRequest": {"type": "object", "properties": {"formName": {"type": "string"}, "assessmentForm": {"type": "array", "items": {"type": "object", "properties": {"question_code": {"type": "string"}, "question": {"type": "string"}, "question_type": {"type": "string", "enum": ["TEXT_INPUT", "CHECK_LIST", "RADIO", "DROPDOWN"]}, "options": {"type": "array", "items": {"type": "string"}}, "section": {"type": "string"}}}}}}, "UserResponseModel": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"roleId": {"type": "string", "example": "67a89fad069cd3c52f8cff22"}, "tenantId": {"type": "string", "example": "67a89fad069cd3c52f8cff1f"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "$2b$10$8/mIZz4SPDAcPkRvrbopUOtZu87JS2dFhnByr4iW5f6fO9EszyV6e"}, "isVerified": {"type": "string", "example": "false"}, "isDeleted": {"type": "string", "example": "false"}, "loginAttempts": {"type": "integer", "example": 0}, "_id": {"type": "string", "example": "67a8a17fda90d3413efda928"}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-02-09T12:37:19.762Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-02-09T12:37:19.763Z"}, "__v": {"type": "integer", "example": 0}}}}}, "RolesResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}}}}}, "Role": {"type": "object", "properties": {"_id": {"type": "string", "example": "67b4b17c9805bd2c1aedbc9e"}, "roleName": {"type": "string", "example": "user"}, "scope": {"type": "array", "items": {"type": "string"}, "example": ["assessment.read", "assessment.write", "assessment.remove"]}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-02-18T16:12:44.330Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-02-18T16:12:44.331Z"}, "__v": {"type": "integer", "example": 0}}}, "VisitResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"episodeId": {"type": "string", "format": "uuid", "example": "67bd8382c5ae4cbd95965ee1"}, "clinicianId": {"type": "string", "format": "uuid", "example": "67bd80867756b55693740021"}, "clientId": {"type": "string", "format": "uuid", "example": "67bd8383c5ae4cbd95965ee5"}, "visitId": {"type": "string", "example": "4192052"}, "visitDate": {"type": "string", "format": "date", "example": "02/13/2025"}, "week": {"type": "string", "example": "02/09/2025 - 02/15/2025"}, "visitType": {"type": "string", "example": "Normal"}, "visitStatus": {"type": "string", "enum": ["New", "In Progress", "Visit Not Conducted", "Past Due", "Submitted for Processing", "Completed", "Needs Attention", "Missed"], "example": "New"}, "service": {"type": "string", "example": "PT SOC OASIS"}, "serviceCode": {"type": "string", "example": "PT SOC"}, "_id": {"type": "string", "format": "uuid", "example": "67bd8386c5ae4cbd95965ee7"}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-02-25T08:47:02.177Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-02-25T08:47:02.177Z"}, "__v": {"type": "integer", "example": 0}}}}}, "VisitListResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "array", "items": {"type": "object", "properties": {"_id": {"type": "string", "example": "67cf182ecd6dbb2d123519e8"}, "visitId": {"type": "string", "example": "4192052"}, "visitDate": {"type": "string", "example": "2025-02-13T00:00:00.000Z"}, "visitStartTime": {"type": "string", "example": "11:00 AM"}, "visitType": {"type": "string", "example": "Normal"}, "visitStatus": {"type": "string", "enum": ["New", "In Progress", "Visit Not Conducted", "Past Due", "Submitted for Processing", "Completed", "Needs Attention", "Missed"], "example": "New"}, "createdAt": {"type": "string", "example": "2025-02-13T11:00:00.000Z"}, "updatedAt": {"type": "string", "example": "2025-02-13T11:00:00.000Z"}, "episodeId": {"type": "string", "example": "67cf182ecd6dbb2d123519e9"}, "episodeNo": {"type": "string", "example": "EP001"}, "episodeStartDate": {"type": "string", "example": "2025-02-13T00:00:00.000Z"}, "episodeEndDate": {"type": "string", "example": "2025-04-13T00:00:00.000Z"}, "episodeDuration": {"type": "number", "example": 60}, "clientId": {"type": "string", "example": "67cf182ecd6dbb2d123519eb"}, "clientFirstName": {"type": "string", "example": "<PERSON>"}, "clientLastName": {"type": "string", "example": "Wim<PERSON><PERSON>"}, "clientStaffNo": {"type": "string", "example": "71251"}, "clientGender": {"type": "string", "example": "Female"}, "clientDob": {"type": "string", "example": "10/10/1952"}, "clientAge": {"type": "number", "example": 72}, "clientPrimaryPhone": {"type": "string", "example": "(*************"}, "clientAddress1": {"type": "string", "example": "6331 W Cholla St"}, "clientAddress2": {"type": "string", "example": ""}, "clientCity": {"type": "string", "example": "Glendale"}, "clientState": {"type": "string", "example": "AZ"}, "clientZip": {"type": "string", "example": "85308"}, "clientCounty": {"type": "string", "example": "Maricopa"}, "clinicianId": {"type": "string", "example": "67cf182ecd6dbb2d123519ea"}, "clinicianEmail": {"type": "string", "example": "<EMAIL>"}, "clinicianFirstName": {"type": "string", "example": "Amber"}, "clinicianLastName": {"type": "string", "example": "<PERSON>"}, "clinicianGender": {"type": "string", "example": "Female"}, "clinicianAddress1": {"type": "string", "example": "409 E. <PERSON>er Street"}, "clinicianAddress2": {"type": "string", "example": ""}, "clinicianCity": {"type": "string", "example": "Phoenix"}, "clinicianState": {"type": "string", "example": "AZ"}, "clinicianZip": {"type": "string", "example": "85012"}, "clinicianPrimaryPhone": {"type": "string", "example": "(*************"}}}}, "total_records_available": {"type": "number", "example": 1}}}, "ErrorResponse": {"type": "object", "properties": {"status": {"type": "string"}, "errorMessage": {"type": "string"}, "errorCode": {"enum": ["GENERAL", "NOT_FOUND", "MISSING_DATA", "INVALID_DATA", "EXPIRED_TOKEN", "INVALID_TOKEN", "INVALID_CODE", "VERIFIED_ACCOUNT", "FEDERATED_ACCOUNT", "WRONG_INPUT", "TOO_MANY_REQUESTS"], "type": "string"}, "data": {"type": "object"}}, "required": ["status", "errorMessage", "data"]}, "ErrorResponseRecoverPassword": {"type": "object", "properties": {"status": {"type": "string"}, "errorMessage": {"type": "string"}, "errorCode": {"enum": ["GENERAL", "NOT_FOUND", "MISSING_DATA", "INVALID_DATA", "EXPIRED_TOKEN", "INVALID_TOKEN", "INVALID_CODE", "VERIFIED_ACCOUNT", "FEDERATED_ACCOUNT", "WRONG_INPUT", "TOO_MANY_REQUESTS"], "type": "string"}, "data": {"type": "object", "properties": {"errorCode": {"type": "string"}}}}, "required": ["status", "errorMessage", "data"]}, "Visit": {"type": "object", "properties": {"episode": {"type": "object", "properties": {"episodeNo": {"type": "integer", "description": "Episode number", "example": 1}, "startDate": {"type": "string", "format": "date", "description": "Start date of the episode in MM/DD/YYYY format", "example": "06/01/2025"}, "endDate": {"type": "string", "format": "date", "description": "End date of the episode in MM/DD/YYYY format", "example": "08/01/2025"}}, "required": ["episodeNo", "startDate", "endDate"]}, "client": {"type": "object", "properties": {"clientNo": {"type": "integer", "description": "Unique client identifier", "example": 680}, "firstName": {"type": "string", "description": "First name of the client", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name of the client", "example": "<PERSON>"}, "dateOfBirth": {"type": "string", "format": "date", "description": "Date of birth in MM/DD/YYYY format", "example": "01/26/1987"}, "gender": {"type": "string", "description": "Gender of the client", "example": "Female"}, "email": {"type": "string", "format": "email", "description": "Email address of the client", "example": "<EMAIL>"}, "address": {"type": "object", "properties": {"addressLine1": {"type": "string", "description": "Primary address line", "example": "8200 Richard Viaduct"}, "addressLine2": {"type": "string", "description": "Secondary address line", "example": ""}, "state": {"type": "string", "description": "State abbreviation", "example": "NJ"}, "city": {"type": "string", "description": "City name", "example": "North Heather"}, "zipcode": {"type": "string", "description": "ZIP code", "example": "27676"}, "county": {"type": "string", "description": "County name", "example": "<PERSON>"}}, "required": ["addressLine1", "state", "city", "zipcode"]}, "phone": {"type": "string", "description": "Primary contact number", "example": "(*************"}, "emergencyContact": {"type": "string", "description": "Name of emergency contact person", "example": "<PERSON>, <PERSON>"}, "emergencyContactPhone": {"type": "string", "description": "Phone number of emergency contact", "example": "(*************"}}, "required": ["clientNo", "firstName", "lastName", "dateOfBirth", "gender", "phone"]}, "clinician": {"type": "object", "properties": {"clinicianNo": {"type": "integer", "description": "Unique clinician identifier", "example": 2002}}, "required": ["clinician<PERSON>o"]}, "visit": {"type": "object", "properties": {"visitId": {"type": "integer", "description": "Unique visit identifier", "example": 456856}, "visitDate": {"type": "string", "format": "date", "description": "Date of the visit in MM/DD/YYYY format", "example": "09/29/2025"}, "visitStartTime": {"type": "string", "description": "Start time of the visit", "example": "11:00 AM"}, "visitType": {"type": "string", "description": "Type of visit", "example": "SOC"}}, "required": ["visitId", "visitDate", "visitType"]}}, "required": ["episode", "client", "clinician", "visit"]}, "SuccessResponseRecoverPassword": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "object", "properties": {"message": {"type": "string", "example": "A password recovery link has been sent to your email address. Please check your inbox and follow the instructions to reset your password."}}}}, "required": ["status", "data"]}, "UploadAudioResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "data": {"type": "string", "example": "Audio file uploaded and message sent to SQS"}}}, "Form": {"type": "object", "properties": {"assessmentForm": {"type": "array", "items": {"type": "object", "properties": {"question_code": {"type": "string"}, "question": {"type": "string"}, "question_type": {"type": "string", "enum": ["TEXT_INPUT", "CHECK_LIST", "RADIO", "DROPDOWN"]}, "options": {"type": "array", "items": {"type": "string"}}, "section": {"type": "string"}}}}}}, "V2LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User's email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "User's password (must meet security requirements)", "minLength": 8, "example": "SecurePassword123!"}}, "required": ["email", "password"]}, "V2LoginSuccessResponse": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "JWT access token for API authentication", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ2IjoyLCJ0eXBlIjoiYWNjZXNzIiwiaWQiOiI2ODJmM2ZhMGNiYTEzODMxNjQyMmFmZTIiLCJyb2xlcyI6WyJjbGluaWNpYW4iXSwic2NvcGVzIjpbImFzc2Vzc21lbnQuY3JlYXRlIiwiYXNzZXNzbWVudC5yZWFkIiwiYXNzZXNzbWVudC51cGRhdGUiLCJhc3Nlc3NtZW50LmRlbGV0ZSIsImFuc3dlci5jcmVhdGUiLCJhbnN3ZXIucmVhZCIsImFuc3dlci51cGRhdGUiLCJhbnN3ZXIuZGVsZXRlIiwiZm9ybS5jcmVhdGUiLCJmb3JtLnJlYWQiLCJmb3JtLnVwZGF0ZSIsImZvcm0uZGVsZXRlIiwidmlzaXQuY3JlYXRlIiwidmlzaXQucmVhZCIsInZpc2l0LnVwZGF0ZSIsInNlbGYucmVhZCIsInNlbGYudXBkYXRlIl0sImlzc3VlciI6IlNjcmliYmxlIiwiaWF0IjoxNzUzMjU2MjUwLCJleHAiOjE3NTMyNjM0NTB9.6veuBiuVl11G00qktcVUswnRMK0XGwh931a-hoCvhuI"}, "message": {"type": "string", "description": "Success message", "example": "Login successful"}}, "required": ["accessToken", "message"]}, "V2ErrorResponse": {"type": "object", "properties": {"errors": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string", "description": "Field or context where the error occurred", "example": "auth"}, "message": {"type": "string", "description": "Human-readable error message", "example": "Please check your email and password"}, "code": {"type": "string", "description": "Machine-readable error code", "example": "AUTH_ERROR"}}, "required": ["field", "message", "code"]}}, "message": {"type": "string", "description": "Summary error message", "example": "Please check your email and password"}}, "required": ["errors", "message"]}, "DeleteAccountResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "Status of the account deletion audit record creation", "example": "ok"}}, "required": ["status"]}, "V2ProfileResponse": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "User's email address", "example": "<EMAIL>"}, "firstName": {"type": "string", "description": "User's first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User's last name", "example": "<PERSON><PERSON>"}, "staffId": {"type": "string", "description": "User's staff identification number", "example": "STAFF001"}, "discipline": {"type": "string", "nullable": true, "description": "User's professional discipline or specialty", "example": "Physical Therapy"}, "phone": {"type": "string", "nullable": true, "description": "User's phone number", "example": "+**********"}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true, "description": "User's date of birth", "example": "1990-01-01T00:00:00.000Z"}, "role": {"type": "string", "nullable": true, "description": "User's role in the system", "example": "Clinician"}}, "required": ["email", "firstName", "lastName", "staffId"]}}, "responses": {}, "parameters": {}, "examples": {}, "requestBodies": {}, "headers": {}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "links": {}, "callbacks": {}}}