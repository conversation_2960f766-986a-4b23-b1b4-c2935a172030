[{"container": {"id": "fokk8854gzmagli617rrdg", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Clinical Records", "subHeading": ""}, "items": [{"id": "l5wh0h00nxr112ueucz0nxm", "questionCode": "A1005", "controlName": "checklist", "displayText": "Checklist", "description": "Are you of Hispanic, Latino/a, or Spanish origin?(Check all that apply) \n", "labelName": "Ethnicity ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "1ge2rhlfy2za0aowecsbef", "value": "A", "label": "A - No, not of Hispanic, Latino/a, or Spanish origin\t"}, {"id": "dhomyb82dlsnyyt5v3d3w9", "value": "B", "label": "B - Yes, Mexican, Mexican American, Chicano/a"}, {"id": "wksvt74n157lmpqprhcxx", "value": "3", "label": "C - Yes, Puerto Rican"}, {"id": "w2ltyo1i28qprxqqm32zc", "value": "D", "label": "D - Yes, Cuban"}, {"id": "les4f9ke1onhkkab0b782g", "value": "5", "label": "E - Yes, another Hispanic, Latino, or Spanish origin"}, {"id": "af98uylubwbpyzm0ew86j", "value": "6", "label": "X - Patient unable to respond"}, {"id": "nk2wqaim9ls1b44vx70d78", "value": "7", "label": "Y - Patient declines to respond"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742724777996, "isContainer": false, "level": 0}, {"id": "nrav0uo8vypqq9z7nivta", "questionCode": "M0090", "controlName": "date-field", "displayText": "Date Picker", "description": "Pick a date:", "labelName": "Date Assessment Completed", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742724951260, "isContainer": false, "level": 0}, {"id": "tgg02miqgogrdbnareqmr", "questionCode": "A1010", "controlName": "checklist", "displayText": "Checklist", "description": "What is your race?\n", "labelName": "Race (Check all that apply)", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "1ge2rhlfy2za0aowecsbef", "value": "A", "label": "A - White"}, {"id": "dhomyb82dlsnyyt5v3d3w9", "value": "B", "label": "B - Black or African American"}, {"id": "ox9ckghfejarg4eyyh2w6m", "value": "3", "label": "C - American Indian or Alaska Native"}, {"id": "38iv06vl3t9bzmjf5wlkhv", "value": "4", "label": "D - Asian Indian"}, {"id": "8azb4g1cr3nq8gzq77b2d", "value": "5", "label": "E - Chinese"}, {"id": "8xokk70bqdrlu74fj3hyi", "value": "6", "label": "F - Filipino"}, {"id": "d7ge5rldzii7omzsz77tyr", "value": "7", "label": "G - Japanese"}, {"id": "yjblbmuazndimr2igmmkzp", "value": "8", "label": "H - Korean"}, {"id": "8ew2egy17j4ercw2i0dtb5", "value": "9", "label": "I - Vietnamese"}, {"id": "bcca346z0ahq121f8m2s", "value": "10", "label": "J - Other Asian"}, {"id": "om7yvbr4cwy4q4ubjhtba", "value": "11", "label": "K - Native Hawaiian"}, {"id": "f1zb2kk49nfatnqvoqe3h", "value": "12", "label": "L - Guamanian or Chamorro"}, {"id": "4262ha0r95pkro19jdgs7", "value": "13", "label": "M - Samoan"}, {"id": "kqfd3f8d9pjmrksbpw4xh", "value": "14", "label": "N - Other Pacific Islander"}, {"id": "jp1ekkihh8idysgszayn4p", "value": "15", "label": "X - Patient unable to respond"}, {"id": "j8qav4w591pwxifapn09li", "value": "16", "label": "Y - Patient declines to respond"}, {"id": "zal668t13ez9wdacuit7", "value": "17", "label": "Z - None of the above"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742725433683, "isContainer": false, "level": 0}, {"id": "7496vnfya6j92qiwmp0y3h", "questionCode": "A1110.A1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "What is your preferred language?\n", "labelName": "Language ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "n89o1ojaiqpdjktyo09cki", "value": "1", "label": "English"}, {"id": "qxruub5ps4ewgcw5anwsc", "value": "2", "label": "Armenian"}, {"id": "g12yjghtwnr7yp1vngn0m5", "value": "3", "label": "Chinese"}, {"id": "1y6f8gymu8idrhej0phrot", "value": "4", "label": "<PERSON><PERSON>"}, {"id": "y3i7sy9pabg9zjrq98ul3", "value": "5", "label": "French"}, {"id": "e8hav6frphf0prabdpnkll", "value": "6", "label": "Not assessed/no information"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742733579804, "isContainer": false, "level": 0, "answer_code": "n89o1ojaiqpdjktyo09cki"}, {"id": "iqocear3cmnsh96q96m1j", "questionCode": "A1110.A2", "controlName": "toggle", "displayText": "Toggle", "description": "What is your preferred language?", "labelName": "Language", "placeholder": "Not assessed/no information", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1743226909962, "isContainer": false, "level": 0}, {"id": "rwl8wgvpz4ijni8jev994n", "questionCode": "A1110.B", "controlName": "radio-group", "displayText": "Radio", "description": "Do you need language interpreter?", "labelName": "Language", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "e6nd33y8tld9cvqa54yazv", "value": "0", "label": "No"}, {"id": "zgp65oyv7lg50c2q2nc80u", "value": "1", "label": "Yes"}, {"id": "z1br9ac5voe2mgjckjuyby", "value": "9", "label": "Unable to determine"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742734119340, "isContainer": false, "level": 0}, {"id": "ylrh1ooy0gb1vkp2575f95", "questionCode": "A1110.C1", "controlName": "radio-group", "displayText": "Radio", "description": "Was a language Interpreter used?", "labelName": "Language", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "No"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "Yes"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742797244709, "index": 4, "isContainer": false, "level": 0}, {"id": "jjfdtzvpebhmhztxmk92g", "questionCode": "A1110.C2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Language Interpreter Used", "placeholder": "Please write your notes here.", "labelName": "Language Interpreter Used", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742734536904, "index": 5, "isContainer": false, "level": 0}, {"id": "qkln3r2xtbz4e0pik28jo", "questionCode": "A1250", "controlName": "checklist", "displayText": "Checklist", "description": "Has lack of transportation kept you from medical appointments, meetings, work, or from getting things needed for daily living?", "labelName": "Transportation", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "qa1vcly8b3cnoimu9sky", "value": "A", "label": "A - Yes, it has kept me from medical appointments or from getting my medications"}, {"id": "3f3rxud20saiibnc2s1j6", "value": "B", "label": "B - Yes, it has kept me from non-medical meetings, appointments, work, or from getting things that I need"}, {"id": "3em5i3y0s4cz0057bf7cm", "value": "3", "label": "C - No"}, {"id": "yrz69of72chzb3od9vi49", "value": "4", "label": "X - Patient unable to respond"}, {"id": "c0qhik2hb72htixsrlp5k", "value": "5", "label": "Y - Patient declines to respond"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742731745118, "isContainer": false, "level": 0}, {"id": "6lzcvjhneb9pp5it2zb9m", "questionCode": "M0080", "controlName": "radio-group", "displayText": "Radio", "description": "What is the discipline of the person completing the assessment?", "labelName": "Discipline of Person Completing Assessment", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "RN"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "PT"}, {"id": "jaej8qfrs3kqq908zih5i", "value": "3", "label": "SLP/ST"}, {"id": "uzldh8pgvk73yv7wjt8fe", "value": "4", "label": "OT"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742796183956, "isContainer": false, "level": 0}, {"id": "t6b8z5s7run1fnfgtj<PERSON><PERSON>", "questionCode": "M0100", "controlName": "radio-group", "displayText": "Radio", "description": "What is the reason for completing this assessment?", "labelName": "This Assessment is Currently Being Completed for the Following Reason", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Start of Care - further visits planned"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "Resumption of care (after inpatient stay)"}, {"id": "6mddm8hq31vtm2xj3e9jl", "value": "3", "label": "Recertification (follow-up) reassessment "}, {"id": "ht655ooy2pre7du5hzh7h", "value": "4", "label": "Other follow-up"}, {"id": "cj06fc5ofuoi4t2xz7qzc", "value": "5", "label": "Transferred to an inpatient facility - patient not discharged from agency"}, {"id": "160x122cwfeicoy6kpkjr", "value": "6", "label": "Transferred to an inpatient facility - patient discharged from agency"}, {"id": "k5ylrgazdhv4crab1usm", "value": "7", "label": "Death at home"}, {"id": "e4ndtmurwcr25w49tois1y", "value": "8", "label": "Discharge from agency "}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742796306888, "isContainer": false, "level": 0}, {"id": "q60wciyhagjwp9u4ksh8nk", "questionCode": "M0102.A", "controlName": "date-field", "displayText": "Date Picker", "description": "What date did the physician indicate as the specific start of care (resumption of care) when the patient was referred for home health services?", "labelName": "Date of Physician-ordered Start of Care (Resumption of Care)", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742796457386, "isContainer": false, "level": 0}, {"id": "8cunr4ij8t3vdhkpsqgl0d", "questionCode": "M0102.B", "controlName": "checkbox", "displayText": "Checkbox", "description": "What date did the physician indicate as the specific start of care (resumption of care) when the patient was referred for home health services?", "labelName": "Date of Physician-ordered Start of Care (Resumption of Care)", "placeholder": "NA - No specific SOC/ROC date ordered by physician", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742796547201, "isContainer": false, "level": 0}, {"id": "2wlhoios6skovpryfnh77f", "questionCode": "M0104", "controlName": "date-field", "displayText": "Date Picker", "description": "Indicate the date that the written or verbal referral for initiation or resumption of care was received by the HHA", "labelName": "Date of Referral", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742797017945, "isContainer": false, "level": 0}, {"id": "henq442fotficbzv28pcaj", "questionCode": "Q0001", "controlName": "radio-group", "displayText": "Radio", "description": "Did the Patient receive any Medicare Home Health service in the past 60 days from the Requested SOC Date? ", "labelName": "Tim<PERSON>", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742797046537, "isContainer": false, "level": 0}]}, {"container": {"id": "kfghcvq1gpofl3mi7zw7b", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Patient History & Diagnosis"}, "items": [{"id": "hyfu675xadjwv9wo90cn6c", "questionCode": "M1000.A", "controlName": "checklist", "displayText": "Checklist", "description": "From which of the following Inpatient facilities was the patient discharged during the past 14 days?", "labelName": "Discharge Facility (Past 14 Days)", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "Long-term nursing facility (NF)"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "Skilled nursing facility (SNF/TCU)"}, {"id": "96wxobyvi0hu3gj0smnwep", "value": "3", "label": "Short-stay acute hospital (IPPS)"}, {"id": "5u0f4mmi9uxk6p6bpkfop", "value": "4", "label": "Long-term care hospital (LTCH)"}, {"id": "70iated5qlvjtvqc9ubqxe", "value": "5", "label": "Inpatient rehabilitation hospital or unit (IRF)"}, {"id": "0et0e290z5we0hwgh65ha73c", "value": "6", "label": "Psychiatric hospital or unit"}, {"id": "jtsao6spx7dnz8lumjlwnd", "value": "7", "label": "<PERSON><PERSON> was not discharged from an inpatient facility ➔ Skip to B0200, Hearing at SOC,Skip to B1300, Health Literacy at ROC"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742797828985, "isContainer": false, "level": 0}, {"id": "qo3pao04n6x8r0e643qco", "questionCode": "M1000.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "From which of the following Inpatient facilities was the patient discharged during the past 14 days?", "placeholder": "Other(Specify)", "labelName": "Discharge Facility(Past 14 Days)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742798021864, "isContainer": false, "level": 0}, {"id": "amd74x453inibwef9lm718", "questionCode": "M1005.A", "controlName": "date-field", "displayText": "Date Picker", "description": "What is the Inpatient Discharge Date (Most Recent) for the patient?\n", "labelName": "Inpatient Discharge Date: (Most recent)", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742798107128, "isContainer": false, "level": 0}, {"id": "hhuwd9gew46x2yka9i7wt", "questionCode": "M1005.B", "controlName": "checkbox", "displayText": "Checkbox", "description": "What is the Inpatient Discharge Date (Most Recent) for the patient?\t\n", "labelName": "Inpatient Discharge Date: (Most recent) ", "placeholder": "UK - Unknown or Not Available", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742798287975, "isContainer": false, "level": 0}, {"id": "fnwm17tmkgat13lq8hh0h", "questionCode": "Q0002.A", "controlName": "radio-group", "displayText": "Radio", "description": "Is there any infection in the past 30 Days Prior to this assessment?", "labelName": "Infection Control ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742798434242, "isContainer": false, "level": 0}, {"id": "2uozydqki7yggjikkccp26", "questionCode": "Q0002.B", "controlName": "radio-group", "displayText": "Radio", "description": "Is there any infection present during this assessment?", "labelName": "Infection Control ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742798482756, "isContainer": false, "level": 0}, {"id": "v2gitxizt6ecbma6v2vw6", "questionCode": "Q0002.C", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is the patient's current treatment regimen associated with infection?\n", "placeholder": "Current treatment regimen associated with infection:", "labelName": "Infection Control", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742798526214, "isContainer": false, "level": 0}, {"id": "juwu1q86w5f57r250vnso", "questionCode": "M1028", "controlName": "checklist", "displayText": "Checklist", "description": "Comorbidities and Co-existing Conditions: (Check all that apply)\n", "labelName": "Active Diagnoses", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "<PERSON><PERSON><PERSON> (DM)"}, {"id": "5xwv1j0ifbcjam2oxpjztg", "value": "3", "label": "None of the above"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799017215, "isContainer": false, "level": 0}, {"id": "bxnekybl5vardfdb2gypw", "questionCode": "M1033.A", "controlName": "checklist", "displayText": "Checklist", "description": "Which of the following signs or symptoms characterize this patient as at risk for hospitalization?", "labelName": "Risk of Hospitalization", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "History of falls (2 or more falls - or any fall with an injury - in the past 12 months)"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "Unintentional weight loss of a total of 10 pounds or more in past 12 months"}, {"id": "0orfb57y4iuq52yugpfs489", "value": "3", "label": "Multiple hospitalizations (2 or more) in the past 6 months"}, {"id": "vc8e0fktrwo0zuijgptg", "value": "4", "label": "Multiple emergency department visits (2 or more) in the past 6 months"}, {"id": "br9drr8dy1w4lcxfjdkk2r", "value": "5", "label": "Decline in mental, emotional, or behavioral status in the past 3 months"}, {"id": "szs52mq2ltecdnskiaoi", "value": "6", "label": "Reported or observed history of difficulty complying with any medical instructions (for example, medications, diet, exercise) in the past 3 months"}, {"id": "tgvxafly845g2jfjjwnaf", "value": "7", "label": "Currently taking 5 or more medications"}, {"id": "ykz7tlpzdhg292z267ul4", "value": "8", "label": "Currently reports exhaustion"}, {"id": "xxq1iqdwyplw9mpo2lxfe8", "value": "9", "label": "Other risk(s) not listed in 1-8"}, {"id": "wjq8xu0pl2kp4fhbalzph", "value": "10", "label": "None of the above"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799438976, "isContainer": false, "level": 0}, {"id": "98k8cv9am7bphdbmkg2za", "questionCode": "M1033.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Risks of Hospitalization", "placeholder": "Other Risks", "labelName": "Risk of Hospitalization", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799684706, "isContainer": false, "level": 0}, {"id": "e6saxystofv5jq2xolb1nf", "questionCode": "J0510", "controlName": "radio-group", "displayText": "Radio", "description": "Over the past 5 days, how much of the time has pain made it hard for you to sleep at night?", "labelName": "Pain Effect on Sleep", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "0", "label": "Does not apply – I have not had any pain or hurting in the past 5 days ➔ Skip to M1400, Short of Breath at SOC/ROC; Skip to J1800, Any Falls Since SOC/ROC at DC"}, {"id": "5kk718pbti69wey254kocd", "value": "1", "label": "Rarely or not at all"}, {"id": "yb8oxb88q3o4fpqm9lp8fe", "value": "3", "label": "Occasionally"}, {"id": "5t06nv1bkej5pdgfa0ehl5", "value": "4", "label": "Frequently"}, {"id": "jf0vhqs8swngcjt2gkbr1m", "value": "5", "label": "Almost constantly"}, {"id": "of696fs8vwohsqniox6nqf", "value": "6", "label": "Unable to answer"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799803788, "isContainer": false, "level": 0}, {"id": "5qod3<PERSON><PERSON>c4c3tn3hkjc8", "questionCode": "J0520", "controlName": "radio-group", "displayText": "Radio", "description": "Over the past 5 days, how often have you limited your participation in rehabilitation therapy sessions due to pain?", "labelName": "Pain Interference with Therapy Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "0", "label": "Does not apply – I have not received rehabilitation therapy in the past 5 days"}, {"id": "5kk718pbti69wey254kocd", "value": "1", "label": "Rarely or not at all"}, {"id": "e8krwvwrdp8ryo3tz4vcs", "value": "3", "label": "Occasionally"}, {"id": "0m1bvwmr4domwp1yp5h3ri", "value": "4", "label": "Frequently"}, {"id": "tw26cod6h9q45t2lr2dqrc", "value": "5", "label": "Almost constantly"}, {"id": "kjay4sbdoxexn508cht52a", "value": "6", "label": "Unable to answer"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799908170, "isContainer": false, "level": 0}, {"id": "pvgqn4e5qtyc2dpq1c6v", "questionCode": "J0530", "controlName": "radio-group", "displayText": "Radio", "description": "Over the past 5 days, how often you have limited your day-to-day activities (excluding rehabilitation therapy session) because of pain?", "labelName": "Pain Interference with Day-to-Day Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Rarely or not at all"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "Occasionally"}, {"id": "sz4bgvol1aklozn4ccrfh", "value": "3", "label": "Frequently"}, {"id": "wdspsawgs8wg6uw3fdio", "value": "4", "label": "Almost constantly"}, {"id": "cgm9fdh2n5ds6q9gg0q2m", "value": "5", "label": "Unable to answer"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800037317, "isContainer": false, "level": 0}, {"id": "wks1pberbbd7pjbdv5g9c2", "questionCode": "M1060.A", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Height(in inches). Record most recent height measure since the most recent SOC/ROC", "placeholder": "inches", "labelName": "Height and Weight – While measuring, if the number is X.1 – X.4 round down; X.5 or greater round up", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800284164, "isContainer": false, "level": 0}, {"id": "wlfnvwf7kzo1bv694dtxc", "questionCode": "M1060.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Weight (in pounds). Base weight on most recent measure in last 30 days; measure weight consistently, according to standard agency practice (for example, in a.m. after voiding, before meal, with shoes off, etc.)", "placeholder": "pounds", "labelName": "Height and Weight – While measuring, if the number is X.1 – X.4 round down; X.5 or greater round up", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800337530, "isContainer": false, "level": 0}, {"id": "59g6ekx1zh5nj9iwht2tg", "questionCode": "M1060.C", "controlName": "radio-group", "displayText": "Radio", "description": "Patient’s scales were used to obtain weight?\t", "labelName": "Height and Weight – While measuring, if the number is X.1 – X.4 round down; X.5 or greater round up", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800393382, "isContainer": false, "level": 0}, {"id": "qpbhsjop4u7ri4ynzdzwd", "questionCode": "K0520.A", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Parenteral/IV feeding", "labelName": "Nutritional Approaches. Check all of the nutritional approaches that apply on admission", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "z11qlmczjd9t6oufaoc1yf", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800593477, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "hvcy28gdodkg5lqainoim", "questionCode": "K0520.B", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Feeding tube (e.g., nasogastric or abdominal (PEG))", "labelName": "Nutritional Approaches. Check all of the nutritional approaches that apply on admission", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "Yes"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "No"}, {"id": "1nspeugv6b0i3hp0lwg0k4d", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800847872, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "9ipbr14it051xotadmyfty", "questionCode": "K0520.C", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Mechanically altered diet – require change in texture of food or liquids (e.g., pureed food, thickened liquids)", "labelName": "Nutritional Approaches. Check all of the nutritional approaches that apply on admission", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "owv6ko96jalj2sc54s86me", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800918572, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "bajev210w7nlviw5njgeq", "questionCode": "K0520.D", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Therapeutic diet (e.g., low salt, diabetic, low cholesterol)", "labelName": "Nutritional Approaches. Check all of the nutritional approaches that apply on admission", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "os7jnxullz9tppjti8f88", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800989304, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "1rh8vk4x84n5evjng22ocq", "questionCode": "K0520.Z", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "None of the above", "labelName": "Nutritional Approaches. Check all of the nutritional approaches that apply on admission", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "x66iiophkgr7z59o33v3", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801068086, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "wx1bkhxg41lpq0mav7gq", "questionCode": "O0110.A1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Cancer Treatments: Chemotherapy", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "t9fsd6t88pyybfuwt4pv", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801280931, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "nu0h5h0hwgb4amu94z70ca", "questionCode": "O0110.A2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Cancer Treatments: IV", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "t73c3ti5hnq6h9xlo<PERSON>", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801370982, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "wr3i9pvyq4m72xtw9ijk1c", "questionCode": "O0110.A3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Cancer Treatments: Oral", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801441681, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "vrad80w5u8sbjrlu80xmhc", "questionCode": "O0110.A10", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Cancer Treatments: Other", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "qqz3thag7ktxzwkn137u9", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801533513, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "f5w063xxon8dgbmpbfw8d", "questionCode": "O0110.B1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Cancer Treatments: Radiation", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "7hj0dlncmtjzkl2dv7wrv", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801630629, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "kn369id9him7n0z8z37u", "questionCode": "O0110.C1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Oxygen Therapy", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "ozy639dj9dz567i786x", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801694327, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "zcarp21cfmiynsbil9s22f", "questionCode": "O0110.C2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Continuous", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "okzhqj99j5qf7uq95enjr", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801748628, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "u93ldv8wtsdarps4na0wxf", "questionCode": "O0110.C3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Intermittent", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "rtk6olryv6sg2wlzz3thfd", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801796492, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "eg0khgyq3qo95sguu9n6", "questionCode": "O0110.C4", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: High-concentration", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "qw22xgz3nmv4tqpczyh8", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801850091, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "29az9dry1rcr0fb5cbr5wl", "questionCode": "O0110.D1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Suctioning", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "pmooe8ma95kn3f6jjdcwf", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742801925274, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "fmjxk5enqdp88h9j0odgwi", "questionCode": "O0110.D2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Scheduled", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "ukx7cy0sor8h9akuq8j2h", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742802212286, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "c5hq19e0yihpuxvctb3m2n", "questionCode": "O0110.D3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: As Needed", "labelName": "Special Treatments, Procedures, and Programs ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "f9ycsl6sv5f4rwdgru49rn", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742802270102, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "nm0rulr1rnnfzynrtvbcp", "questionCode": "O0110.E1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Tracheostomy care", "labelName": "Special Treatments, Procedures, and Programs ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "qgorbh4u3bepzjdmo3p2dh", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742802902977, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "bampnor1tf6hndwxzp8ex4", "questionCode": "O0110.F1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Invasive Mechanical Ventilator (ventilator or respirator)", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "iovs3qwt5kj3efxcq6ljtc", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803024274, "isContainer": false, "level": 0, "answer_code": "6a3pdnnbkhs0qlca4bn3vm"}, {"id": "kc2qwmp3bws9ctrwoeiwbe", "questionCode": "O0110.G1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: Non-invasive Mechanical Ventilator", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "gnxj9k4edhubw58ia5uppu", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803130579, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "qndcgph2wthcvnqvtk4sfv", "questionCode": "O0110.G2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: BiPAP", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "ud52bap3o840ho4azkqy5", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803181839, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "ga8uh5ar4xsilujsjrwt", "questionCode": "O0110.G3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Respiratory Therapies: CPAP", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "ipcli22hptkltxadhpzei8", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803233805, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "cv9hse3hgtgj5u4xgnuhqs", "questionCode": "O0110.H1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: IV Medications", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "geiehx2vuwl58ce2te50g2", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803296470, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "fa7havnl2345khmqwt063", "questionCode": "O0110.H2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Vasoactive medications", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "5ldi1grugu76oxn9tykv6a", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803342618, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "x7vopiiq4ut1izvykz0y8", "questionCode": "O0110.H3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Antibiotics", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "zlr0y253d87pn38nt9314", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803398552, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "i4t2op5kch7ne9gd718qw", "questionCode": "O0110.H4", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Anticoagulation", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "iuh1ky2d0imjp8b5eif95", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803449452, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "uwaffg9g7zdpaw99e5hc5", "questionCode": "O0110.H10", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Other", "labelName": "Label for Dropdown", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "370r889gx9vae25cwqtl2h", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803493051, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "k1l0q1rw9fn6nsbpguqos", "questionCode": "O0110.I1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Transfusions", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "hp80pqngpxaztxwdik46if", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803662565, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "m3hdsxbxpg9euyszfi2lpq", "questionCode": "O0110.J1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Dialysis", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "aoeog6884shgzu5m07aaiw", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803722282, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "jnfqmiai4bc10pvyux2zf", "questionCode": "O0110.J2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Hemodialysis", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "n38k50cytklpr673tvyqp", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803773430, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "dhgid0kdds63judax098n6", "questionCode": "O0110.J3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Peritoneal dialysis", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "fwpf6mrie9rds0vghcp9", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803834896, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "7fkd0i0m2p9bmqyfbsleu7", "questionCode": "O0110.O1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: IV Access", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "ft2n1q16amtdrlcu10e19", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803888079, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "b2jac4u3hpmywmabdae9xo", "questionCode": "O0110.O2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: <PERSON><PERSON><PERSON><PERSON>", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "y3c9aiutfv0kxg04x9fu5", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803939346, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "8z59pjr0adhinmo38964l", "questionCode": "O0110.O3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Mid-line", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "l22mbzmhqe4oinrkiau2p", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742803986312, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "wrrjjtf1kzhr8y9zoh7ur", "questionCode": "O0110.O4", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Other: Central (e.g., PICC, tunneled, port)", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "7enllltp6e2jkg885k189", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742804034443, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "65ye8ys2f8o8nplu9gjzzb", "questionCode": "O0110.Z1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "None of the Above", "labelName": "Special Treatments, Procedures, and Programs", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "fchplvwcawm5at7puguta3", "value": "1", "label": "No"}, {"id": "6a3pdnnbkhs0qlca4bn3vm", "value": "2", "label": "Yes"}, {"id": "vdvwy06gjwtdsvcpuh7h", "value": "3", "label": "Not assessed/no information"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742804085565, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "k7pc80i6lqpiq1low5302", "questionCode": "Q0003.A1", "controlName": "checklist", "displayText": "Checklist", "description": "What is the patient's pertinent history and/or previous outcomes? ", "labelName": "Pertinent History and / or previous outcomes (note dates of onset, exacerbation when known) (Reference M1000 & M1005) ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "N/A"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "Hypertension"}, {"id": "i5m3pqrk0dc0lcoessapcwd", "value": "3", "label": "Cardiac"}, {"id": "pzugefs77qvybe4i4eccl", "value": "4", "label": "Diabetes"}, {"id": "qdzfslj9b79djelzpfwd4", "value": "5", "label": "Respiratory"}, {"id": "u9i0sfzcdjuswddcm49f", "value": "6", "label": "Osteoporosis"}, {"id": "ocepzx221lai0pw1kgoec", "value": "7", "label": "Fractures"}, {"id": "rq016r4t3cedghsmbgdkqq", "value": "8", "label": "Infection"}, {"id": "zersg2klx3dwgmlv9yatv", "value": "9", "label": "Immunosuppressed"}, {"id": "8e2c60m3n484z9b0xdhwa8", "value": "10", "label": "Open Wound"}, {"id": "w057x3kly3hgjuj3y6dpgn", "value": "11", "label": "Cancer"}, {"id": "374ue94hv5mkt0oumpip6a", "value": "12", "label": "Surgeries"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742807951220, "isContainer": false, "level": 0}, {"id": "vrtt1lot25etf8a8fmk1x9", "questionCode": "Q0003.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Cancer(Site)", "placeholder": "Add your text here", "labelName": "Pertinent History and / or previous outcomes", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742808294656, "isContainer": false, "level": 0}, {"id": "9ikw8zlqk9uxwifqgki0p", "questionCode": "Q0003.A3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Surgeries", "placeholder": "Add your text here", "labelName": "Pertinent History and / or previous outcomes", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742808354905, "isContainer": false, "level": 0}, {"id": "89430717n5gu0bjk4zcnq8", "questionCode": "Q0003.B1", "controlName": "radio-group", "displayText": "Radio", "description": "Prior Hospitalization History", "labelName": "Prior Hospitalizations", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "No"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "Yes"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742808542336, "isContainer": false, "level": 0}, {"id": "5drnldsa3lun31q18etk3p", "questionCode": "Q0003.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Prior Hospitalizations: Number of times", "placeholder": "Please write your notes here.", "labelName": "Prior Hospitalizations", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742808589135, "isContainer": false, "level": 0}, {"id": "ik92eofiyatfsqlgzmok", "questionCode": "Q0003.B3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Prior Hospitalizations: Reason(s)/Date(s)", "placeholder": "Please write your notes here.", "labelName": "Prior Hospitalizations", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742808777932, "isContainer": false, "level": 0}, {"id": "33ko2cif66umxkjcs7roj", "questionCode": "Q0003.B4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Prior Hospitalizations: Other(Specify)", "placeholder": "Please write your notes here.", "labelName": "Prior Hospitalizations", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742808861553, "isContainer": false, "level": 0}, {"id": "54iq15agnksjrasjb5usg", "questionCode": "Q0003.C1", "controlName": "radio-group", "displayText": "Radio", "description": "Was Client knowledge Assessed?", "labelName": "Pertinent History and / or previous outcomes", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742808949513, "isContainer": false, "level": 0}, {"id": "nadah86lp1gfuk7h4ko2nv", "questionCode": "Q0004.A", "controlName": "radio-group", "displayText": "Radio", "description": "What is the patient's prognosis?", "labelName": "Prognosis", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Poor"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "Guarded"}, {"id": "9cghj0tpc2sx87ipfev0o", "value": "3", "label": "Fair"}, {"id": "tdq80dwcivxvp712a1omi", "value": "4", "label": "Good"}, {"id": "w6hf54sxc6o3xb9kn9a3lz", "value": "5", "label": "Excellent"}, {"id": "0c6sewz6mdnvbl8tk3g5u2t", "value": "6", "label": "Other"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742809175387, "isContainer": false, "level": 0}, {"id": "k1hkld6us9p8d45bv5q2ih", "questionCode": "Q0004.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is the patient's prognosis?", "placeholder": "Please write your notes here.", "labelName": "Prognosis", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742809248943, "isContainer": false, "level": 0}, {"id": "q30pahbuwhn6pg1ansev9", "questionCode": "Q0005", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What allergies does the patient have?", "placeholder": "Please write your notes here.", "labelName": "Allergies", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742810298447, "isContainer": false, "level": 0}, {"id": "a6sv14ys7vj0nxkkba3hly", "questionCode": "Q0006.A", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Physician Info:", "placeholder": "Please write your notes here.", "labelName": "Patient Contact with Physician", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742810443142, "isContainer": false, "level": 0}, {"id": "rlhbwnfkigkpb53sovjc", "questionCode": "Q0006.B", "controlName": "radio-group", "displayText": "Radio", "description": "Was visit related to the primary reason for Home health?", "labelName": "Patient Contact with Physician", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742810494308, "isContainer": false, "level": 0}, {"id": "qunoz2wmyws363r1a6txxl", "questionCode": "Q0006.C", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is the Reason for visit?", "placeholder": "Please write your notes here.", "labelName": "Patient Contact with Physician", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742810543836, "isContainer": false, "level": 0}, {"id": "wahe3fm326abugihwkmg0u", "questionCode": "Q0006.D", "controlName": "date-field", "displayText": "Date Picker", "description": "When was your last visit?", "labelName": "Patient Contact with Physician", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742810643223, "isContainer": false, "level": 0}, {"id": "r5hothfy2hbeiqq1ly3bno", "questionCode": "Q0007.A1", "controlName": "radio-group", "displayText": "Radio", "description": "Is Patient Homebound?", "labelName": " Homebound Reasons", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742811119760, "isContainer": false, "level": 0}, {"id": "hmkgqkar8w8zfge8jtrpg", "questionCode": "Q0007.A2", "controlName": "checklist", "displayText": "Checklist", "description": "What are the primary reasons for your situation being homebound?", "labelName": "Homebound Reasons", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "Need assistance for all activities"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "Residual weakness"}, {"id": "fp79t1z437eizbst2dfpnr", "value": "3", "label": "Require assistance to ambulate"}, {"id": "z44j7zps6tivs7946941", "value": "4", "label": "Confusion, Unable to go out from home alone"}, {"id": "501f1gy6pcuij9dzf0r7f", "value": "5", "label": "Unable to safely leave home unassisted"}, {"id": "jlmhv5za6xbst0q718awp", "value": "6", "label": "Severe SOB, SOB upon exertion"}, {"id": "tyw9yuziccsgkt99gasln", "value": "7", "label": "Dependent upon adaptive device(s)"}, {"id": "bo1nzdj21hq1ttyv18ddqmi", "value": "8", "label": "Medical restrictions"}, {"id": "wnivn2qkayo1bcie6ioets", "value": "9", "label": "Other"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742811290573, "isContainer": false, "level": 0}, {"id": "bgtiyw2ghkd4yfwxi5drjl", "questionCode": "Q0007.A3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "If others, please specify", "placeholder": "Please write your notes here.", "labelName": "Homebound Reasons", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742812746972, "isContainer": false, "level": 0}, {"id": "6pu0d90jj3l0ch2ufva1e7w", "questionCode": "Q0007.B", "controlName": "radio-group", "displayText": "Radio", "description": "Is the Patient Chronic and Stable?", "labelName": "Homebound Reasons", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742812798962, "isContainer": false, "level": 0}, {"id": "20jjnzbl8tkmwpg17y3vog", "questionCode": "Q0007.C", "controlName": "radio-group", "displayText": "Radio", "description": "Does the Patient leaving the home require Considerable and Taxing effort ?", "labelName": "Homebound Reasons", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Yes"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "No"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742812926278, "isContainer": false, "level": 0}, {"id": "zuup51xarzns6c36a0ayb", "questionCode": "Q0007.D", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Additional Comments", "placeholder": "Please write your notes here.", "labelName": "Homebound Reasons", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742813055575, "isContainer": false, "level": 0}, {"id": "xkehh91z36fpam8zfsa", "questionCode": "Q0008.A", "controlName": "checklist", "displayText": "Checklist", "description": "Which advanced directives are currently in place for the patient?", "labelName": "Advance Directive ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "Living will"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "Education needed and provided"}, {"id": "ak46d6vhkxkb0gpjdt031i", "value": "3", "label": "Do not resuscitate"}, {"id": "rvj0u6znsi97f7zr4erq1g", "value": "4", "label": "Copies on file"}, {"id": "o6fs7phxtt0rjn6nitsu5o", "value": "5", "label": "Organ donor"}, {"id": "u94rb6dacegvc0aasomhe", "value": "6", "label": "Funeral arrangements made"}, {"id": "6ymr9phydrekinsw221hsr", "value": "7", "label": "POA"}, {"id": "yibvm5kvuv3594u3bn0bi", "value": "8", "label": "Healthcare representative\t"}, {"id": "0eg0ovocb4hbtbsm8l7lvz9", "value": "9", "label": "Patient/Caregiver reports no Advanced Directives in place\t"}, {"id": "u4m8tj1rvxlk77x3erm7p", "value": "10", "label": "Copies Requested"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742814401844, "isContainer": false, "level": 0}, {"id": "vwxwz64ps6ba24w1mrd8kv", "questionCode": "Q0008.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Additional Comments:", "placeholder": "Please write your notes here.", "labelName": "Advance Directive ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742814617237, "isContainer": false, "level": 0}, {"id": "z2et6i47e589xu99cl3kmb", "questionCode": "Q0009", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is the Primary Reason for Home Health?", "placeholder": "Please write your notes here.", "labelName": "Primary Reason for Home Health", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742814644786, "isContainer": false, "level": 0}, {"id": "93jy7093ena75mmtopme2y", "questionCode": "Q0010", "controlName": "radio-group", "displayText": "Radio", "description": "What is the Acuity Level of the patient?", "labelName": "Acuity Level", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "RED"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "YELLOW"}, {"id": "lnwmlc7gvqswskfbj4sj29", "value": "3", "label": "GREEN"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742814668086, "isContainer": false, "level": 0}, {"id": "vlktjnep9fo8d09fi67yvh", "questionCode": "Q0011", "controlName": "radio-group", "displayText": "Radio", "description": "What is the level of transportation assistance?", "labelName": "Transportation Assistance Level", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Non-ambulatory [TAL–1] "}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "Wheelchair [TAL–2] "}, {"id": "ixs0nbd5u9x1r0k5k0rf", "value": "3", "label": "Ambulatory [TAL-3]"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742814711285, "isContainer": false, "level": 0}]}, {"container": {"id": "5b8zcw6b9j6hnzndmjbgcf", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Living Arrangements"}, "items": [{"id": "ca1utldve60pljf7me78p", "questionCode": "M1100.A", "controlName": "radio-group", "displayText": "Radio", "description": "Is patient lives alone?", "labelName": "Patient Living Situation: Which of the following best describes the patient's residential circumstances and availability of assistance? ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "Around the Clock"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "Regular daytime"}, {"id": "m4qkkrag9c3lvcjc7vxhv", "value": "3", "label": "Regular nighttime"}, {"id": "2t0eywkx7g2cn34ofuadqq", "value": "4", "label": "Occasional/short-term assistance"}, {"id": "1t2idazebzk17ftyxksjhv", "value": "5", "label": "No assistance available"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742815088848, "isContainer": false, "level": 0}, {"id": "zjac5xp886nqw91z9mmr2r", "questionCode": "M1100.B", "controlName": "radio-group", "displayText": "Radio", "description": "Is <PERSON><PERSON> lives with other person(s) in the home?", "labelName": "Patient Living Situation: Which of the following best describes the patient's residential circumstances and availability of assistance?", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "6", "label": "Around the Clock"}, {"id": "5kk718pbti69wey254kocd", "value": "7", "label": "Regular daytime"}, {"id": "6mwp3qpznjxog8v4n82luf", "value": "3", "label": "Regular nighttime"}, {"id": "6psnkpiny33hhmqck9qlz", "value": "4", "label": "Occasional/short-term assistance"}, {"id": "ki3dn7dt5fa8ndztayovh", "value": "5", "label": "No assistance available"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742815213395, "isContainer": false, "level": 0}, {"id": "7ph4dq7nzl3ufmsnc3r3ln", "questionCode": "M1100.C", "controlName": "radio-group", "displayText": "Radio", "description": "Is <PERSON><PERSON> lives in congregate situation (for example, assisted living, residential care home)?", "labelName": "Patient Living Situation: Which of the following best describes the patient's residential circumstances and availability of assistance?", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "11", "label": "Around the Clock"}, {"id": "5kk718pbti69wey254kocd", "value": "12", "label": "Regular daytime"}, {"id": "onq3gpnsix7p33h4i829j", "value": "3", "label": "Regular nighttime"}, {"id": "3pr67rvbqdf7yux4j0p89k", "value": "4", "label": "Occasional/short-term assistance"}, {"id": "mui8s5kmckrd25yjbw9qh", "value": "5", "label": "No assistance available"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742815286724, "isContainer": false, "level": 0}, {"id": "h06fw0i9fal9kzw6unmlnp", "questionCode": "Q0012.A", "controlName": "radio-group", "displayText": "Radio", "description": "Has the home safety evaluation been completed?", "labelName": "Home Safety Evaluation", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "w8cbv57i58nzhl9ga84ig", "value": "1", "label": "N/A"}, {"id": "98u5sd2wprkchi4q6b6pm", "value": "2", "label": "No Home Safety Problems Identified"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742885761058, "isContainer": false, "level": 0}, {"id": "s99pk9ft6ul581ot0y7gzj", "questionCode": "Q0012.B", "controlName": "checklist", "displayText": "Checklist", "description": "Is there an evacuation plan in place?", "labelName": "Fire/Emergency Preparedness", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "jt2zlup7u5jxdb6dlwrd79", "value": "1", "label": "No Problem Identified"}, {"id": "wmdq4qdltt6dj8jqb81kq", "value": "2", "label": "No working telephone"}, {"id": "etryjg2vzx73ypxvqkkf4m", "value": "3", "label": "No functional fire extinguisher(s)\t"}, {"id": "adkt47jqdlsmnqeklebid9", "value": "4", "label": "No alternate exits identified"}, {"id": "6ud9aj10f496lzctb10h8o", "value": "5", "label": "Emergency numbers are not accessible"}, {"id": "0dvoieyu11fhntft8w84mxf", "value": "6", "label": "Exits are obstructed"}, {"id": "fxur6u9r4ehjcwpcro4cqc", "value": "7", "label": "Windows are obstructed"}, {"id": "4r0klss17qcesu8mmxvfy", "value": "8", "label": "No functional smoke alarm(s)"}, {"id": "0nzxql2rwpzhl59gct353", "value": "9", "label": "Patient unaware of escape route"}, {"id": "9phqfju0io1zaqlapyex2", "value": "10", "label": "Plan for power failure"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742885859172, "isContainer": false, "level": 0}, {"id": "0u6zzx1z13wjuky01o2bnbg", "questionCode": "Q0012.C", "controlName": "checklist", "displayText": "Checklist", "description": "Are there any structural barriers that could impede evacuation?\n", "labelName": "Structural Barriers", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "jt2zlup7u5jxdb6dlwrd79", "value": "1", "label": "No Problem Identified"}, {"id": "wmdq4qdltt6dj8jqb81kq", "value": "2", "label": "Stairs inside home which must be used by the patient (e.g., to get to toileting, sleeping, eating areas)\t"}, {"id": "9c37zugvp3pbfukkibekei", "value": "3", "label": "Narrow or obstructed doorways"}, {"id": "clnr6otv5p74hiljz6er", "value": "4", "label": "Stairs inside home which are used optionally (e.g., to get laundry facilities)"}, {"id": "qkl2xoppyea4bnzz03gaq", "value": "5", "label": "Equipment or supplies creating obstruction"}, {"id": "9w737ll9bpg7ekgvg9ocv", "value": "6", "label": "Stairs leading from inside house to outside"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742885992070, "isContainer": false, "level": 0}, {"id": "m26ztu4m485cv9h8n00yk", "questionCode": "Q0012.D", "controlName": "checklist", "displayText": "Checklist", "description": "What are the environmental risk factors?", "labelName": "Environmental Risk Factors ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "jt2zlup7u5jxdb6dlwrd79", "value": "1", "label": "No Problem Identified"}, {"id": "wmdq4qdltt6dj8jqb81kq", "value": "2", "label": "No electricity in home"}, {"id": "8yzyfuzs1giwgz5bpl9slh", "value": "3", "label": "Inadequate ventilation"}, {"id": "ctiwq54gv69pynnthrbf5d", "value": "4", "label": "Unsafe storage/usage of oxygen"}, {"id": "yw5vla0f7nk8fqroq8ukj7", "value": "5", "label": "Inadequate lighting"}, {"id": "mr86kbg5xjhm2xxzv35kn", "value": "6", "label": "Inadequate floor, roof, or windows"}, {"id": "faduz55ugodzd5wtu12ot", "value": "7", "label": "Unsecured firearms in home"}, {"id": "7bey63nzkzjknpb5xtfpto", "value": "8", "label": "Unsafe gas/electric appliance"}, {"id": "8aazyor4phrhy1mwt703hv", "value": "9", "label": "Unsafe floor coverings"}, {"id": "3y4bt722zfeg6suws6dwxh", "value": "10", "label": "Pet policy reviewed"}, {"id": "1hbq0yvl9kgkdx3z1w7r0m", "value": "11", "label": "Unsafe electric outlets and/or cords"}, {"id": "94hrc71oklyscdtfn3zm", "value": "12", "label": "Cluttered living area (e.g., tripping hazard)\t"}, {"id": "0j0144y4jhzequ0216t9j", "value": "13", "label": "Pet put away"}, {"id": "zuzzemoi85efsko2y4t3ha", "value": "14", "label": "Electrical equipment used near water"}, {"id": "dvl7g6f3dw9uc8mwu28ccr", "value": "15", "label": "Inadequate stair railings"}, {"id": "vrrf4uw1079rl8ifinl4f", "value": "16", "label": "pets outside"}, {"id": "gvt1bkkqw09nplh54m8fhe", "value": "17", "label": "Inadequate heating"}, {"id": "sit6qlockmidltgpjkzf", "value": "18", "label": "Improperly stored hazardous materials"}, {"id": "9epm41q5p39qe8mw0obu8n", "value": "19", "label": "Oxygen in Use"}, {"id": "c8ukjd4acweqwprv5bz55h", "value": "20", "label": "Inadequate cooling"}, {"id": "ien6adwe2bu13juqs7xil", "value": "21", "label": "Unsafe storage of supplies/equipment\t"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742887650711, "isContainer": false, "level": 0}, {"id": "luolod7vvdf9reff5rdbm", "questionCode": "Q0012.E", "controlName": "checklist", "displayText": "Checklist", "description": "What are the sanitation hazards?", "labelName": "Sanitation Hazards", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "jt2zlup7u5jxdb6dlwrd79", "value": "1", "label": "No Problem Identified"}, {"id": "wmdq4qdltt6dj8jqb81kq", "value": "2", "label": "Inadequate water supply\t"}, {"id": "pysshh04ugydqnnymvmb", "value": "3", "label": "Outdoor toileting facilities only"}, {"id": "v1m2rcf2t1dj1dvzgwfx7", "value": "4", "label": "Inadequate food refrigeration\t"}, {"id": "vlvnm7jtrvh5le5tz9vo4w", "value": "5", "label": "No scheduled trash pickup"}, {"id": "wnkvj5mtb4u6skrtmchlt", "value": "6", "label": "Contaminated water"}, {"id": "o6kiuqhts952g7k91etf6", "value": "7", "label": "Inadequate sewage disposal"}, {"id": "7npccknua9nh26znw7ia", "value": "8", "label": "Inadequate cooking facilities"}, {"id": "d0kqafx054dkypo2lzxcd", "value": "9", "label": "Cluttered/soiled living area"}, {"id": "pys1vqu20vo1kp73yjgczwk", "value": "10", "label": "Inadequate sewage"}, {"id": "412cdrxcebftnwxk011fcn", "value": "11", "label": "Inadequate/improper food storage\t"}, {"id": "lhli21jpy6qo4zgdshz4p", "value": "12", "label": "Insects/rodents present"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742887854096, "isContainer": false, "level": 0}, {"id": "moqpyidlp1kwuqdtqez0od", "questionCode": "Q0012.F", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What corrective actions are needed to address the identified safety concerns?\n", "placeholder": "Please write your notes here.", "labelName": "Corrective Action Plan / Comments/ Other Safety Concerns:", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742888001427, "isContainer": false, "level": 0}, {"id": "lnrr34fz5hghvc3hylygbd", "questionCode": "Q0013.A", "controlName": "toggle", "displayText": "Toggle", "description": "What is Community Agencies/Social Service Screening?\n", "labelName": "Community Agencies/Social Service Screening", "placeholder": "N/A", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742904752034, "isContainer": false, "level": 0}, {"id": "8bhexr0t8bmkiywc82e6cd", "questionCode": "Q0013.B", "controlName": "radio-group", "displayText": "Radio", "description": "What is Community Agencies/Social Service Screening?\n", "labelName": "Community Resource Info needed to manage care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742904776328, "isContainer": false, "level": 0}, {"id": "ne5d03m0jen41hrpeq6nfr", "questionCode": "Q0013.C", "controlName": "radio-group", "displayText": "Radio", "description": "Has the patient experienced a change in their body image? \n", "labelName": "Altered affect(i.e., depression,grief,body image chg.)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742905008177, "isContainer": false, "level": 0}, {"id": "5lq2dzo56fsicd0m3kvlck", "questionCode": "Q0013.D", "controlName": "radio-group", "displayText": "Radio", "description": "Is the individual experiencing suicidal ideation?", "labelName": "Suicide Ideation", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742905035953, "index": 11, "isContainer": false, "level": 0}, {"id": "8v689jky5izp130k4j31c", "questionCode": "Q0013.E", "controlName": "radio-group", "displayText": "Radio", "description": "Are you experiencing any abuse or neglect?\n", "labelName": "Suspected Abu<PERSON>/Neglect, i.e.: Unexplained bruises, inadequate food, fearful of family member, c/g exploitation of funds, sexual abuse, neglect, left unattended if needs constant supervision.", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742905311716, "isContainer": false, "level": 0}, {"id": "ni0a42m26yhdjkhc7g4q0d", "questionCode": "Q0013.F", "controlName": "radio-group", "displayText": "Radio", "description": "Do you have adequate methods for cooking and shopping for groceries?\n", "labelName": "Inadequate method to cook or shop for groceries", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742905440408, "index": 13, "isContainer": false, "level": 0}, {"id": "e467qka6a9e8f2rgrqqxwc", "questionCode": "Q0013.G1", "controlName": "radio-group", "displayText": "Radio", "description": "MSW referral needed for?", "labelName": "MSW referral", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742905505661, "isContainer": false, "level": 0}, {"id": "ugrp64nx6msqx4qd9psh8q", "questionCode": "Q0013.G2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Referral notes", "placeholder": "Please write your notes here.", "labelName": "MSW referral", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1743228133086, "isContainer": false, "level": 0}, {"id": "akdyponq93gpoe5rod81qn", "questionCode": "Q0013.H", "controlName": "radio-group", "displayText": "Radio", "description": "Has the coordinator been notified?\n", "labelName": "Coordinator notified", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742905675888, "isContainer": false, "level": 0}, {"id": "7i9cavudys9f2fyxf7w6z", "questionCode": "Q0014.A", "controlName": "toggle", "displayText": "Toggle", "description": "Is the patient currently administered supplemental oxygen therapy?", "labelName": "Oxygen Use Safety", "placeholder": "N/A", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742906341779, "isContainer": false, "level": 0}, {"id": "uh2xxvhj34bv70iawh30s", "questionCode": "Q0014.B", "controlName": "radio-group", "displayText": "Radio", "description": "Are there signs posted?", "labelName": "Oxygen Use Safety: Signs Posted", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742906394212, "isContainer": false, "level": 0}, {"id": "ey8wbmw1b4kak4gmvbpyft", "questionCode": "Q0014.C", "controlName": "radio-group", "displayText": "Radio", "description": "Do you use flammable products?", "labelName": "Oxygen Use Safety: Handle Smoking/Flammables Safety", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "1", "label": "Yes"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "2", "label": "No"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742906452012, "isContainer": false, "level": 0}, {"id": "3vtcm8vjs25mfn8x4xw1pk", "questionCode": "Q0014.D", "controlName": "checklist", "displayText": "Checklist", "description": "What is the backup oxygen plan?", "labelName": "Oxygen Use Safety: Oxygen Back-Up", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "8v7yy8pw45pg7iy332vre6", "value": "1", "label": "Available"}, {"id": "qxpsgaci2nakwm06k9eqzg", "value": "2", "label": "Knows How to Use"}, {"id": "sc7zcik5gl9ifrrnuzwo", "value": "3", "label": "Electrical/Fire Safety"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742906884807, "isContainer": false, "level": 0}, {"id": "rrvpezh3alt5dgcucclbj", "questionCode": "Q0014.E", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Is the patient safe?", "placeholder": "Please write your notes here.", "labelName": "Oxygen Use Safety: Plan/Comments", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742906969218, "isContainer": false, "level": 0}, {"id": "w7jq4qdjz91oo4wrgbjru", "questionCode": "Q0014.F1", "controlName": "checklist", "displayText": "Checklist", "description": "Were instructions and materials provided?", "labelName": "Instructions/Materials Provided", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "8v7yy8pw45pg7iy332vre6", "value": "1", "label": "Rights and Responsibilities"}, {"id": "qxpsgaci2nakwm06k9eqzg", "value": "2", "label": "State Hotline Number"}, {"id": "pvfxl2zprici4gus1esbb", "value": "3", "label": "Advance Directives"}, {"id": "evllkq7eiinr40ahoymk99", "value": "4", "label": "Do not Resuscitate (DNR)"}, {"id": "5agap75b9n8om2bodf1yt", "value": "5", "label": "HIPAA Notice of Privacy Practices"}, {"id": "1iczvj0y25qim9e9yfd8brc", "value": "6", "label": "OASIS Privacy Notice"}, {"id": "znmal2dd6g1t979tk1gf", "value": "7", "label": "Emergency Planning in the event service is disrupted"}, {"id": "ewitkiidwtj8yuyek5y5k3", "value": "8", "label": "Agency Phone Number or After office hour number"}, {"id": "2qtehx0zuc6xi2n3qc84n", "value": "9", "label": "When to contact Physician and/or Agency"}, {"id": "balp2tt4m9qugabcq6ls9r", "value": "10", "label": "Standards Precaution/hand washing"}, {"id": "zsj7z8x93fm9iwk0n6o9r", "value": "11", "label": "Basic Home Safety"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742907033692, "isContainer": false, "level": 0}, {"id": "3r1ui57n3iua57kzyh5qpn", "questionCode": "Q0014.F2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Are there any specific diseases to be noted?", "placeholder": "Please write your notes here.", "labelName": "Instructions/Materials Provided: Disease(specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742907235998, "index": 23, "isContainer": false, "level": 0}, {"id": "3devq70i2xxy1v1lblh1", "questionCode": "Q0014.F3", "controlName": "toggle", "displayText": "Toggle", "description": "", "labelName": "Instructions/Materials Provided", "placeholder": "Medication Regime/Administration", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742907336283, "isContainer": false, "level": 0}, {"id": "0b8lfkuyfmr3b2surqmqlg", "questionCode": "Q0015.A", "controlName": "checklist", "displayText": "Checklist", "description": "What functional limitations do you have?", "labelName": "Functional Limitations ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "8v7yy8pw45pg7iy332vre6", "value": "1", "label": "Amputation"}, {"id": "qxpsgaci2nakwm06k9eqzg", "value": "2", "label": "Bowel/Bladder Incontinence"}, {"id": "4o6svyc3bb7g9zeehi3vj", "value": "3", "label": "Contracture"}, {"id": "on768qdgyopv0g85bi017l", "value": "4", "label": "Hearing"}, {"id": "17oo5h9hniiezs2k69eolp", "value": "5", "label": "Paralysis"}, {"id": "86fgn7nf9lontmct11db", "value": "6", "label": "Endurance"}, {"id": "yt0fu9r1dbezvz1s8gdb", "value": "7", "label": "Ambulation"}, {"id": "s0kfj0c8uabhbi46ebgwtl", "value": "8", "label": "Speech"}, {"id": "icn8j6srb8e6c2tr65cszw", "value": "9", "label": "Legally Blind"}, {"id": "m7l1tcgo6gni86osxzkv79", "value": "10", "label": "Dyspnea with minimal exertion"}, {"id": "u6t3krrfoxrfd8dn7ov0f", "value": "11", "label": "Other "}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742908661064, "isContainer": false, "level": 0}, {"id": "u1ucp7j9abhgoh7k314a4", "questionCode": "Q0015.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Functional Limitations: Other", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1743228525479, "isContainer": false, "level": 0}, {"id": "24jyprprsxyvhskpowbr2", "questionCode": "Q0016", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What safety measures are in place?", "placeholder": "Please write your notes here.", "labelName": "Safety Measures", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1742910089246, "isContainer": false, "level": 0}]}, {"container": {"id": "n2nnaaszf49p6h4ktinci9", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Sensory"}, "items": [{"id": "ez9mcz2tqw54j0i2s2bz6f", "questionCode": "B0200", "controlName": "radio-group", "displayText": "Radio", "description": "Can you hear well with your hearing aid or hearing appliances?", "labelName": "Hearing: Ability to hear (with hearing aid or hearing appliances if normally used)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "0", "label": "Adequate - no difficulty in normal conversation, social interaction, listening to TV"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "1", "label": "Minimal difficulty – difficulty in some environments (e.g., when person speaks softly, or setting is noisy)"}, {"id": "fedrdxfcjr3nek55vsskg", "value": "3", "label": "Moderate difficulty - speaker has to increase volume and speak distinctly"}, {"id": "6tdrqhj866hfb2n69bkf97", "value": "4", "label": "Highly impaired - absence of useful hearing"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742910353975, "isContainer": false, "level": 0}, {"id": "glfbi9ba6loun3s6xk2nx9", "questionCode": "B1000", "controlName": "radio-group", "displayText": "Radio", "description": "Can you see well with your glasses or visual appliances in adequate light?\n", "labelName": "Vision: Ability to see in adequate light (with glasses or other visual appliances)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "0", "label": "Adequate - sees fine detail, such as regular print in newspapers/books"}, {"id": "7z1r3snvjzmwvvobfhp9k", "value": "1", "label": "Impaired – sees large print, but not regular print in newspapers/books"}, {"id": "l7gkfdysdsp1vcoqneol2o", "value": "2", "label": "Moderately impaired – limited vision; not able to see newspaper headlines but can identify objects"}, {"id": "xyfmjszm02jeudofo6jsc", "value": "3", "label": "Highly impaired - object identification in question, but eyes appear to follow objects"}, {"id": "w3wkpsf0gtfzgt68zyyq", "value": "4", "label": "Severely impaired -– no vision or sees only light, colors or shapes; eyes do not appear to follow objects"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742910432425, "index": 0, "isContainer": false, "level": 0}, {"id": "knt7uthp06ysuc4qh3xao", "questionCode": "B1300", "controlName": "radio-group", "displayText": "Radio", "description": "How often do you need to have someone help you when you read instructions, pamphlets, or other written material from your doctor or pharmacy?\n", "labelName": "Health Literacy (From Creative Commons ©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "0", "label": "Never"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "1", "label": "Rarely"}, {"id": "y6tygk7kl7befmnmu2b3i", "value": "3", "label": "Sometimes"}, {"id": "gh3dpu5z0km0guxpu5su", "value": "4", "label": "Often"}, {"id": "eorplnl5a675d3y8x489e", "value": "5", "label": "Always"}, {"id": "grqujqi79la66y0d7tsyi", "value": "6", "label": "Patient declines to respond"}, {"id": "902b454866nk22l1spt3df", "value": "7", "label": "Patient unable to respond"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742910873051, "isContainer": false, "level": 0}, {"id": "revpk7j1je6ma06ayzn92", "questionCode": "Q0017.A", "controlName": "checklist", "displayText": "Checklist", "description": "Do you experience any issues with your nose?", "labelName": "Nose", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "8v7yy8pw45pg7iy332vre6", "value": "1", "label": "Not Assessed"}, {"id": "qxpsgaci2nakwm06k9eqzg", "value": "2", "label": "No Problem Found"}, {"id": "xyup3mj95isl8zt9sl3ica", "value": "3", "label": "Congestion"}, {"id": "8qh6y77cs8utoh7x4xi3n", "value": "4", "label": "Epistaxis"}, {"id": "h6nrmq4tkuddkxi1hi9u69", "value": "5", "label": "Loss of smell"}, {"id": "udb7hg35u8uqqqud6tnf8", "value": "6", "label": "Sinus problem"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742911825240, "isContainer": false, "level": 0}, {"id": "2kiskwlq0q7ri58nkd5gyk", "questionCode": "Q0017.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What are the reasons for non-assessment?", "placeholder": "Please write your notes here.", "labelName": "Notes: Reason for not assessed", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1743228642955, "isContainer": false, "level": 0}, {"id": "qfnjxy5ax9geq0opalrx", "questionCode": "Q0017.C", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Notes: Other", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1743228759101, "isContainer": false, "level": 0}, {"id": "kkd70wj7nfqgj81altb5", "questionCode": "Q0018.A", "controlName": "checklist", "displayText": "Checklist", "description": "Are you experiencing any throat issues?", "labelName": "Throat", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "8v7yy8pw45pg7iy332vre6", "value": "1", "label": "Not Assessed"}, {"id": "qxpsgaci2nakwm06k9eqzg", "value": "2", "label": "No Problem Found"}, {"id": "mmnuiyv98581g619t6mszs", "value": "3", "label": "Dysphagia"}, {"id": "0asf3pbjpet0q2db7r79t", "value": "4", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "xt64rkensb6rqq8mfjjit", "value": "5", "label": "Lesions"}, {"id": "8expt2mdi9mwobaffsl5u", "value": "6", "label": "Sore throat"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742912500595, "isContainer": false, "level": 0}, {"id": "8h58tvq7dnupannq9kuhrd", "questionCode": "Q0018.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What are the reasons for non-assessment?", "placeholder": "Please write your notes here.", "labelName": "Throat: Reason for not assessed", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1743228844537, "isContainer": false, "level": 0}, {"id": "imtdrcn3kmliojb64ydz", "questionCode": "Q0018.C", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Throat: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1743228901258, "isContainer": false, "level": 0}, {"id": "1urueddabbt1kn3cvpp48u", "questionCode": "Q0019.A1", "controlName": "checklist", "displayText": "Checklist", "description": "Do you have any issues with your ears?", "labelName": "Ears", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "8v7yy8pw45pg7iy332vre6", "value": "1", "label": "Not Assessed"}, {"id": "qxpsgaci2nakwm06k9eqzg", "value": "2", "label": "No Problem Found"}, {"id": "pxgx10ln74mrsmqdmet6d", "value": "3", "label": "Vertigo"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742913107962, "isContainer": false, "level": 0}, {"id": "4mk7oo0yvllp3dzaemge7", "questionCode": "Q0019.B1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Please specify which ear is experiencing hearing difficulties, the left or the right or both.\n", "labelName": "Ears: HOH", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "v3ef1e5ij4ln0j4ob0bagi", "value": "1", "label": "L"}, {"id": "wd3tl5zjnrdl0rufujeft", "value": "2", "label": "R"}, {"id": "a7tm1nb9tpg13r6oeg38vmo", "value": "3", "label": "L&R"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742927752808, "isContainer": false, "level": 0, "answer_code": "v3ef1e5ij4ln0j4ob0bagi"}, {"id": "y50s8skngfofv8spa25y", "questionCode": "Q0019.B2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Which of your ears is deaf - the left or the right or both?", "labelName": "Ears: <PERSON><PERSON>", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "v3ef1e5ij4ln0j4ob0bagi", "value": "1", "label": "L"}, {"id": "wd3tl5zjnrdl0rufujeft", "value": "2", "label": "R"}, {"id": "v77uji9w06oay3s4f51b7u", "value": "3", "label": "L&R"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742928040362, "isContainer": false, "level": 0, "answer_code": "wd3tl5zjnrdl0rufujeft"}, {"id": "pwyzds30p0pmvi7lyn231", "questionCode": "Q0019.B3", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Is the hearing aid designed for use in either the right or left or both ears?", "labelName": "Ears: Hearing Aid", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "v3ef1e5ij4ln0j4ob0bagi", "value": "1", "label": "L"}, {"id": "wd3tl5zjnrdl0rufujeft", "value": "2", "label": "R"}, {"id": "4yr79klxkarprvnqarxm8e", "value": "3", "label": "L&R"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742928190977, "isContainer": false, "level": 0, "answer_code": "wd3tl5zjnrdl0rufujeft"}, {"id": "sqfm11brxlcpya91dd3uz", "questionCode": "Q0019.B4", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Please indicate whether you experience tinnitus in your left ear, right ear, or both ears.\n", "labelName": "Ears: <PERSON><PERSON><PERSON>", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "v3ef1e5ij4ln0j4ob0bagi", "value": "1", "label": "L"}, {"id": "wd3tl5zjnrdl0rufujeft", "value": "2", "label": "R"}, {"id": "tn6t9qo9ujl640g8zdh3m", "value": "3", "label": "L&R"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742928334461, "isContainer": false, "level": 0, "answer_code": "wd3tl5zjnrdl0rufujeft"}, {"id": "gbso3xo98h8jar1pdw3t2r", "questionCode": "Q0019.C1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Ears: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742928394723, "index": 11, "isContainer": false, "level": 0}, {"id": "mxwda75oosrpurp9d227d8", "questionCode": "Q0020.A", "controlName": "checklist", "displayText": "Checklist", "description": "What conditions should be considered when creating a dental treatment plan?", "labelName": "Mouth", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "hyy35mor0ngmea29lavr0f", "value": "1", "label": "Not Assessed"}, {"id": "nrsym01xfrbkge8xvwbm1", "value": "2", "label": "No Problem Found"}, {"id": "6bo9uatruf9wr11n8y3xd", "value": "3", "label": "Dentures"}, {"id": "ei3ddb7erjl3an1v1iuqnn", "value": "4", "label": "Upper"}, {"id": "cmcs6cpz5zjvn1mpldsbul", "value": "5", "label": "Lower"}, {"id": "khcpfwgv4fp9gvcjlotja", "value": "6", "label": "Partial"}, {"id": "5lk2ts2ryvd83cuf4vnir", "value": "7", "label": "Masses/Tumors"}, {"id": "xjjca2fu8zlbyd6wdig0j", "value": "8", "label": "Gingivitis"}, {"id": "wd2gqkdn6kiewzldk5as", "value": "9", "label": "Ulcerations"}, {"id": "jffxdqhggok3wqt58i7iog", "value": "10", "label": "<PERSON><PERSON><PERSON>"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742928583175, "isContainer": false, "level": 0}, {"id": "v2vzyzc4s3nwlvsbnvx53i", "questionCode": "Q0020.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What are the reasons for non-assessment?", "placeholder": "Please write your notes here.", "labelName": "Mouth: Reason for not assessed", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742928980048, "isContainer": false, "level": 0}, {"id": "c95x5cbfwaodb3gp0g8o7l", "questionCode": "Q0020.C", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Mouth: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742929062830, "isContainer": false, "level": 0}, {"id": "mhmmkol2f4exm16d1nwzp", "questionCode": "Q0021.A", "controlName": "checklist", "displayText": "Checklist", "description": "Permissible Patient Activities", "labelName": "Activities Permitted", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "hyy35mor0ngmea29lavr0f", "value": "1", "label": "Complete bedrest\t"}, {"id": "nrsym01xfrbkge8xvwbm1", "value": "2", "label": "Bedrest/BRP"}, {"id": "31nw2nnrqid7lf0mnroah", "value": "3", "label": "Up as tolerated"}, {"id": "848tgzd712zo5pesv590b", "value": "4", "label": "Transfer bed/chair"}, {"id": "ok9j1dhc6d8<PERSON><PERSON>ykc8q6", "value": "5", "label": "Exercises prescribed"}, {"id": "19hgepzmsiinjqx3dh6748", "value": "6", "label": "Partial weight bearing"}, {"id": "slerea3axsrc2qsityhpt4", "value": "7", "label": "Independent in home"}, {"id": "43r4vmru76qgsw4frp1swq", "value": "8", "label": "Crutches"}, {"id": "6hfn2k80has2du3h1pzoy3", "value": "9", "label": "<PERSON><PERSON>"}, {"id": "q5lnd12qslj48u2bonb", "value": "10", "label": "Wheelchair"}, {"id": "f1hb5wxgyq8g277un7u8k", "value": "11", "label": "<PERSON>"}, {"id": "3ekx1i4ku8z5y3qkmjij15", "value": "12", "label": "No restrictions"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742929211639, "index": 15, "isContainer": false, "level": 0}, {"id": "8o1yr1w2rpcl691pedq0f", "questionCode": "Q0021.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Activities Permitted: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742929453157, "isContainer": false, "level": 0}]}, {"container": {"id": "y8kn1l24zooc68gky1shan", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Integumentary"}, "items": [{"id": "bt7ij5el6ie74j4tmittpt", "questionCode": "M1306", "controlName": "radio-group", "displayText": "Radio", "description": "Does this patient have at least one Unhealed Pressure Ulcer/ Injury at Stage 2 or Higher or designated as Unstageable ?(Excludes Stage 1 pressure injuries and all healed pressure ulcers / injuries)", "labelName": "Patient Ulcer Assessment", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "No"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "Yes"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981251758, "isContainer": false, "level": 0}, {"id": "yb0xsc9kzje4jcn5fmtaij", "questionCode": "M1311.A1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stage 2: Partial thickness loss of dermis presenting as a shallow open ulcer with red pink wound bed, without slough. May also present as an intact or open/ruptured blister. Number of Stage 2 pressure ulcers", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981621738, "isContainer": false, "level": 0}, {"id": "lz8wunku0mqtsxk80yvd2p", "questionCode": "M1311.B1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stage 3: Full thickness tissue loss. Subcutaneous fat may be visible but bone, tendon, or muscle is not exposed. Slough may be present but does not obscure the depth of tissue loss. May include undermining and tunneling.\nNumber of Stage 3 pressure ulcers", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981646867, "index": 1, "isContainer": false, "level": 0}, {"id": "d95u9bin73wqlo31kod67h", "questionCode": "M1311.C1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stage 4: Full thickness tissue loss with exposed bone, tendon, or muscle. Slough or eschar may be present on some parts of the wound bed. Often includes undermining and tunneling. Number of Stage 4 pressure ulcers", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981718116, "isContainer": false, "level": 0}, {"id": "b5malcderwdber3xqcxxr", "questionCode": "M1311.D1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Unstageable: Non-removable dressing/device: Known but not stageable due to non-removable dressing/device\nNumber of unstageable pressure ulcers due to non - removable dressing / device", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982037592, "index": 3, "isContainer": false, "level": 0}, {"id": "11bxzl74c2ikaf70nl90ia5", "questionCode": "M1311.E1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Unstageable: Slough and/or eschar: Known but not stageable due to coverage of wound bed by slough and/or eschar\nNumber of unstageable pressure ulcers due to coverage of wound bed by slough and/or eschar", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982071289, "index": 4, "isContainer": false, "level": 0}, {"id": "qca7q49m5k7hkbdssfnkk", "questionCode": "M1311.F1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Unstageable: Deep tissue injury: Number of unstageable pressure injuries presenting as deep tissue injury", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 174**********, "index": 5, "isContainer": false, "level": 0}, {"id": "uetwouiss8m7gl56oh7ka", "questionCode": "M1322", "controlName": "radio-group", "displayText": "Radio", "description": "Intact skin with non-blanchable redness of a localized area usually over a bony prominence. The area may be painful, firm, soft, warmer, or cooler as compared to adjacent tissue. Darkly pigmented skin may not have a visible blanching; in dark skin tones only it may appear with persistent blue or purple hues.", "labelName": "Patient Ulcer Assessment: Current Number of Stage 1 Pressure Injuries", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "0"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "1"}, {"id": "8br4nnt09caqqvgogwjy", "value": "2", "label": "2"}, {"id": "i76baibnumglbyyp3pav3l", "value": "3", "label": "3"}, {"id": "cbkk14m4f9b9xh98quzvlq", "value": "4", "label": "4 or more"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982274108, "isContainer": false, "level": 0}, {"id": "wq8so25jw1q6rdh41lmq", "questionCode": "M1324", "controlName": "radio-group", "displayText": "Radio", "description": "What is the current stage of the most severe unhealed pressure ulcer/injury that is clinically stageable?", "labelName": "Stage of Most Problematic Unhealed Pressure Ulcer / Injury that is Stageable: (Excludes pressure ulcer/injury that cannot be staged due to a non-removable dressing/device, coverage of wound bed by slough and/or eschar, or deep tissue injury.)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "Stage 1"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Stage 2"}, {"id": "usir9h7i0ieja76ppfdu8", "value": "3", "label": "Stage 3"}, {"id": "g9d5vcqpwqnyjhl95rezgr", "value": "4", "label": "Stage 4"}, {"id": "9rzcus4z2m5z2snx7g26h9", "value": "5", "label": "Patient has no pressure ulcers/injuries or no stageable pressure ulcers/injuries"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982494165, "index": 7, "isContainer": false, "level": 0}, {"id": "ozwwu9a9xgr8mp4eqz30q", "questionCode": "M1330", "controlName": "radio-group", "displayText": "Radio", "description": "Does this patient have a Stasis Ulcer?", "labelName": "Patient Ulcer Assessment", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "No, Skip to M1340, Surgical Wound"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "Yes, patient has both observable and unobservable stasis ulcers"}, {"id": "3pejolsmxxpgll88zlokzv", "value": "3", "label": "Yes, patient has observable stasis ulcers only"}, {"id": "5zy7ikt6m6diziotmn93o", "value": "4", "label": "Yes, patient has unobservable stasis ulcers only (known but not observable due to non-removable dressing/device)➔Skip to M1340, Surgical Wound"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982604720, "isContainer": false, "level": 0}, {"id": "q42yc1mmhf80chqms842o8g", "questionCode": "M1332", "controlName": "radio-group", "displayText": "Radio", "description": "What is the current number of observable stasis ulcers?", "labelName": "Current Number of Stasis Ulcer(s) that are Observable", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "One"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Two"}, {"id": "uz3t9gn7r6xmu5r5m19jj", "value": "3", "label": "Three"}, {"id": "6ioza4vaqiflgh3f2kpd1", "value": "4", "label": "Four or More"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982662719, "isContainer": false, "level": 0}, {"id": "zjod6qe6jzck0z845r8t3", "questionCode": "M1334", "controlName": "radio-group", "displayText": "Radio", "description": "What is the current condition of the most prominent stasis ulcer?\n", "labelName": "Status of Most Problematic Stasis Ulcer that is Observable", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "Fully granulating"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Early/partial granulating"}, {"id": "316lvl1lbjh6baxss8cs84", "value": "3", "label": "Not Healing"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742989299515, "isContainer": false, "level": 0}, {"id": "2jk1v5hyisskybyd2dh10j", "questionCode": "M1340", "controlName": "radio-group", "displayText": "Radio", "description": "Does this patient have a Surgical Wound ?", "labelName": "Surgical Wound", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "No, Skip to N0415, High - Risk Drug Classes: Use and Indication"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "Yes, patient has at least one observable surgical wound"}, {"id": "yhey3mx3qgis7eep4ryy", "value": "3", "label": "Surgical wound known but not observable due to non-removable dressing/device ➔ Skip to N0415, High - Risk Drug Classes: Use and Indication"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742989354080, "isContainer": false, "level": 0}, {"id": "zs0ffnxnmr6rhtz22y8l", "questionCode": "M1342", "controlName": "radio-group", "displayText": "Radio", "description": "What is the status of the most problematic stasis ulcer that is observable?", "labelName": "Status of Most Problematic Surgical Wound that is Observable", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "Newly epithelialized"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "Fully granulating"}, {"id": "150z0heaetlh9uzj596djn", "value": "3", "label": "Early/partial granulating"}, {"id": "uax1yecvobgoh3lj50fuo", "value": "4", "label": "Not Healing"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742989419996, "index": 1, "isContainer": false, "level": 0}, {"id": "z2r03in243kg5mm5hphjb", "questionCode": "Q0023.A", "controlName": "checklist", "displayText": "Checklist", "description": "What is the condition of the patient's skin?", "labelName": "Skin Condition", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "c40ppk742ta53vp10r3ox7", "value": "1", "label": "N/A"}, {"id": "fvfq359qr8p407sh7d0l", "value": "2", "label": "No problem Found"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742989503295, "isContainer": false, "level": 0}, {"id": "p2irm0jng684scuqn5ph02", "questionCode": "Q0023.B", "controlName": "radio-group", "displayText": "Radio", "description": "What is the condition of the patient's skin color?", "labelName": "Skin Color Condition", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "<PERSON><PERSON>"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Jaundiced"}, {"id": "srynyvvy3acbyuwlr1u3q", "value": "3", "label": "Cyanotic"}, {"id": "1o1fh3g9ijw2hs8wgfg2yx", "value": "4", "label": "Ecchymosis"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742989557412, "isContainer": false, "level": 0}, {"id": "ulx7gpogrcsfqd7xpi199", "questionCode": "Q0023.C", "controlName": "radio-group", "displayText": "Radio", "description": "What is the temperature of the patient's skin?", "labelName": "Skin Temp", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "Hot"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Cool"}, {"id": "27fyy3amugpn7jclr5thjn", "value": "3", "label": "Diaphoretic"}, {"id": "z89m4hlch7n4545uk7ol", "value": "4", "label": "Diaphoretic"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742989979573, "isContainer": false, "level": 0}, {"id": "bhln6ncx1m2g41z3z2lpk", "questionCode": "Q0023.D", "controlName": "radio-group", "displayText": "Radio", "description": "What is the turgor of the patient's skin?", "labelName": "Skin <PERSON>", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "Good"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Poor"}, {"id": "dy2g4585rumn8ak79xa16i", "value": "3", "label": "Fair"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742990117621, "isContainer": false, "level": 0}, {"id": "kxgblx5c982uq3l2rxes2", "questionCode": "Q0023.E", "controlName": "checklist", "displayText": "Checklist", "description": "What are the Lesions of the patient’s skin?", "labelName": "Skin Lesions", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "c40ppk742ta53vp10r3ox7", "value": "1", "label": "Wounds"}, {"id": "fvfq359qr8p407sh7d0l", "value": "2", "label": "Incision"}, {"id": "u5a823omqrjn2v4s0yk70j", "value": "3", "label": "<PERSON><PERSON><PERSON>"}, {"id": "ssq6rgrd9w6cw0ffacq38", "value": "4", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "zq982ry4ahrk0y47pc5wn", "value": "5", "label": "Rashes"}, {"id": "suk5oi463q9l6kxltiuosf", "value": "6", "label": "<PERSON><PERSON><PERSON>"}, {"id": "qap8smy4g4aho328uzc1kj", "value": "7", "label": "Itching"}, {"id": "mo15hxj8lcf8grxob36guk", "value": "8", "label": "Petechiae"}, {"id": "te6gfhj0xt72xumlxa98g", "value": "9", "label": "Purpura"}, {"id": "4dq85ixdo4tscszpvrola", "value": "10", "label": "Dry"}, {"id": "su1kyjysbkkkrboax2ndlg", "value": "11", "label": "Sc<PERSON>"}, {"id": "l2n6389bb29muyo9zhpfd", "value": "12", "label": "Redness"}, {"id": "z6s2ujzk51diwskpjotyg", "value": "13", "label": "<PERSON><PERSON><PERSON>"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742990175532, "isContainer": false, "level": 0}, {"id": "nqx2832dbyi58mzvo146", "questionCode": "Q0023.F", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Skin: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742990379754, "index": 7, "isContainer": false, "level": 0}, {"id": "r21frioczziob3rju0fxe8", "questionCode": "Q0024.A", "controlName": "checklist", "displayText": "Checklist", "description": "Are the patient's nails observed for any problems?", "labelName": "Nails", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "c40ppk742ta53vp10r3ox7", "value": "1", "label": "N/A"}, {"id": "fvfq359qr8p407sh7d0l", "value": "2", "label": "No problem Found"}, {"id": "n96xi7iw47boh4hpmnkxf", "value": "3", "label": "Poor nail care"}, {"id": "g0s5szcnzfkpw6c3xbqc7q", "value": "4", "label": "Diabetic foot care performed (skip, if not diabetic)"}, {"id": "epd340tbkdoqjl9aqurdwa", "value": "5", "label": "Current diabetic skin lesion/location"}, {"id": "hnelq17feqsry0oilphm", "value": "6", "label": "Foot care education this visit or planned for future visits"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742990462219, "index": 8, "isContainer": false, "level": 0}, {"id": "y1zfwpph0ga09cfpk56ibj", "questionCode": "Q0024.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Are the patient's nails ingrown?", "placeholder": "Please write your notes here.", "labelName": "Nails", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742990643173, "index": 9, "isContainer": false, "level": 0}, {"id": "fi647ryrwv2mdindcrsnm", "questionCode": "Q0024.C", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Nails: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742990850742, "isContainer": false, "level": 0}, {"id": "iyc4zj9dh7bingipbwri0p", "questionCode": "Q0024.D", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Additional Notes:", "placeholder": "Please write your notes here.", "labelName": "Nails: Notes", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742990887559, "isContainer": false, "level": 0}]}, {"container": {"id": "mnaqo7fnoe3wbzks3eetq", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Respiratory"}, "items": [{"id": "i3ex8ijj3qfcu3km3j33gv", "questionCode": "M1400.A", "controlName": "radio-group", "displayText": "Radio", "description": "When is the patient dyspneic or noticeably Short of Breath? Click here to open sticky note Click here to View Careplan Help List ", "labelName": "Shortness of Breath", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "Patient is not short of breath"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "When walking more than 20 feet, climbing stairs"}, {"id": "5lkb6ixkzfu21aqvgh63jh", "value": "3", "label": "With moderate exertion (For example: while dressing, using commode or bedpan, walking distances less than 20 feet)"}, {"id": "fdxnk0kwmbqfcpqj35d3lo", "value": "4", "label": "With minimal exertion (For example: while eating, talking, or performing other ADLs) or with agitation"}, {"id": "uy656qe9s6i8hjdivjln4", "value": "5", "label": "At rest (during day or night)"}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742991166855, "isContainer": false, "level": 0}, {"id": "oozlx894cmf27y69h0u5u", "questionCode": "M1400.B", "controlName": "radio-group", "displayText": "Radio", "description": "Has the patient's dyspnea been assessed or reported?", "labelName": "Shortness of Breath", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "Assessed"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Reported"}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742991629603, "isContainer": false, "level": 0}, {"id": "e08gs9wnpi7om2rxnpaofs", "questionCode": "Q0025.A1", "controlName": "checklist", "displayText": "Checklist", "description": "Does the patient have any pre-existing respiratory conditions?", "labelName": "<PERSON><PERSON>'s history associated with Dyspnea", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "c40ppk742ta53vp10r3ox7", "value": "1", "label": "N/A"}, {"id": "fvfq359qr8p407sh7d0l", "value": "2", "label": "COPD"}, {"id": "f2qmo32eg9jnjar751lqag", "value": "3", "label": "Asthma"}, {"id": "mwr18xps8qbes4xxawjjki", "value": "4", "label": "Pneumonia"}, {"id": "0ipaw7ushe5fl62ycqr40nl", "value": "5", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "6w6lpdrel83l34saa5m0ir", "value": "6", "label": "Pleural effusion"}, {"id": "4h3e0snok5dmq2qfsyosgo", "value": "7", "label": "Heart failure\t"}, {"id": "6d6ai7zj7zj2q2uaf0y89d", "value": "8", "label": "TB"}, {"id": "h2c8uqykru979r8oqpqqg3", "value": "9", "label": "Pulmonary embolism"}, {"id": "3hwtigoci8ozhlmo8wwefp", "value": "10", "label": "Lung cancer\t"}, {"id": "cyt6algxsbko0yt5l0aygj", "value": "11", "label": "Recent cardiothoracic surgeries"}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742991891963, "isContainer": false, "level": 0}, {"id": "ogg51phjgzkex2g4s58ex", "questionCode": "Q0024.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "<PERSON><PERSON>'s history associated with Dyspnea: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992164324, "index": 2, "isContainer": false, "level": 0}, {"id": "tlkz0bscvtpjnkgigwpqg", "questionCode": "Q0024.B1", "controlName": "checklist", "displayText": "Checklist", "description": "Are there any other symptoms associated with the dyspnea?(check all that apply)", "labelName": "<PERSON><PERSON>'s history associated with Dyspnea", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "c40ppk742ta53vp10r3ox7", "value": "1", "label": "No symptoms present"}, {"id": "fvfq359qr8p407sh7d0l", "value": "2", "label": "Accessory muscles used\t"}, {"id": "68lm73a7xik52tjjbrkvjv", "value": "3", "label": "Ineffective breathing pattern"}, {"id": "zgd335mi5oyar1wlmp58t", "value": "4", "label": "Abnormal breath sounds"}, {"id": "6glpffwqav3osku6zikunp", "value": "5", "label": "<PERSON><PERSON>"}, {"id": "gaovrp49w7l3m9g6fny44a", "value": "6", "label": "Dyspnea"}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992217630, "isContainer": false, "level": 0}, {"id": "dw6dqa38t7o0svdp99n7shi", "questionCode": "Q0024.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Patient's history associated with Dyspnea: Other symptoms(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992295272, "index": 4, "isContainer": false, "level": 0}, {"id": "ehqd1t4bzvfx846j7vt7gd", "questionCode": "Q0024.C1", "controlName": "checklist", "displayText": "Checklist", "description": "What medications is the patient currently taking for respiratory?", "labelName": "<PERSON><PERSON>'s history associated with Dyspnea", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "c40ppk742ta53vp10r3ox7", "value": "1", "label": "Oxygen  "}, {"id": "fvfq359qr8p407sh7d0l", "value": "2", "label": "IPPB"}, {"id": "j1eohmf045w6lu2weyu39", "value": "3", "label": "Inhalers"}, {"id": "f8gdwksk73bsctqmdcuq1d", "value": "4", "label": "Chest physiotherapy"}, {"id": "d31443indohyol5km332sc", "value": "5", "label": "Vaporizer or humidifier"}, {"id": "8b2n9zeoobgfnx3mh3n64q", "value": "6", "label": "Dyspnea"}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992439053, "isContainer": false, "level": 0}, {"id": "qsajbemysj0ffrxi24zo68", "questionCode": "Q0024.C2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Respiratory: Medications Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992513102, "index": 6, "isContainer": false, "level": 0}, {"id": "geeq6ben6ivfx6w1zqkbng", "questionCode": "Q0024.D1", "controlName": "checklist", "displayText": "Checklist", "description": "Elaborate on the patient's smoking history.\n", "labelName": "Smoking History", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "c40ppk742ta53vp10r3ox7", "value": "1", "label": "Never smoked"}, {"id": "fvfq359qr8p407sh7d0l", "value": "2", "label": "History of smoking cessation attempts "}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992641833, "index": 7, "isContainer": false, "level": 0}, {"id": "n8kerbtr22noktxolajx8", "questionCode": "Q0024.D2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "No of cigarettes/ cigars per day:", "placeholder": "Enter Number", "labelName": "Smoking History", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992732300, "index": 8, "isContainer": false, "level": 0}, {"id": "i16y1yakna5gcs11mocga", "questionCode": "Q0024.D3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Is there a prior history of smoking cessation?", "placeholder": "# of years ago", "labelName": "Smoking History", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992770199, "isContainer": false, "level": 0}, {"id": "4tsq7w2tcb37yp44034r3w", "questionCode": "Q0024.D4", "controlName": "toggle", "displayText": "Toggle", "description": "Is patient agreeable to stop smoking?\t\n", "labelName": "Smoking History", "placeholder": "Yes/No", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992929797, "index": 10, "isContainer": false, "level": 0}, {"id": "3z1fov582lklnvfpmrikz", "questionCode": "Q0024.D5", "controlName": "toggle", "displayText": "Toggle", "description": "Was patient /caregiver knowledge base assessed?\t\n", "labelName": "Smoking History", "placeholder": "Yes/No", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742992995061, "index": 11, "isContainer": false, "level": 0}]}, {"container": {"id": "anr5e1slt4tcjn160zuack", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Cardiac Status"}, "items": [{"id": "o56f1pc74jhm7l0ntam09r", "questionCode": "Q0025.A1", "controlName": "checklist", "displayText": "Checklist", "description": "What is the patient's history related to the circulatory system?(check all that apply)", "labelName": "Patient’s history associated with Circulatory system", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "N/A"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "HTN"}, {"id": "5lgahdftyfa8ijzlt2isrs", "value": "3", "label": "CHF"}, {"id": "lnojdtcjt8nx65t3d0uif", "value": "4", "label": "<PERSON><PERSON>"}, {"id": "it301xvefj868c3q9vdb5", "value": "5", "label": "Atrial Fibrillation"}, {"id": "m9y497zdg0f6bf54tjoocb", "value": "6", "label": "Atherosclerosis"}, {"id": "x6p5lf4m4y8lb0b0rai5oi", "value": "7", "label": "MI"}, {"id": "feoneuiersdlql5elwf8xd", "value": "8", "label": "CAD"}, {"id": "ri6stti6lvd7uxf91hpwca", "value": "9", "label": "PVD"}, {"id": "n3o550g5v8lbfnl1nqxsd", "value": "10", "label": "CVA"}, {"id": "szg0mq8zxmyho54bsf1s8", "value": "11", "label": "Embolism and thrombosis"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743033518712, "isContainer": false, "level": 0}, {"id": "h8lslvwwy2kpmstbttcn88", "questionCode": "Q0025.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Patient’s history associated with Circulatory system: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743033674207, "isContainer": false, "level": 0}, {"id": "fkynp9hoitm3ak3288yezl", "questionCode": "Q0025.B1", "controlName": "checklist", "displayText": "Checklist", "description": "What symptoms are commonly associated with the specified medical conditions?", "labelName": "Specify any symptoms present associated with these conditions", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "No symptoms identified"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Fatigue"}, {"id": "z6il072yhdiop6z0ffmblp", "value": "3", "label": "Diminished exercise capacity"}, {"id": "295whs6n5wpujfiqzr0u79", "value": "4", "label": "Dyspnea with exertion"}, {"id": "86y2bkkiz1qdiy4vzm96xn", "value": "5", "label": "<PERSON><PERSON>"}, {"id": "42ap3nxi29qv7o07zbivs", "value": "6", "label": "Abnormally high blood pressure"}, {"id": "32pal034fp35zbmx5t4hwg", "value": "7", "label": "Abnormally low blood pressure"}, {"id": "mjfwmqxpj594k9j1lrtqlr", "value": "8", "label": "Irregular heart beat"}, {"id": "2y1uk2wb6kw5p9knxsvo8d", "value": "9", "label": "Weakness"}, {"id": "h676p0m1joq6fn5y1o0qg", "value": "10", "label": "Dizziness"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743033731805, "isContainer": false, "level": 0}, {"id": "d5k9jkie2krj38w1klxh8a", "questionCode": "Q0025.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Specify any symptoms present associated with these conditions: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034063734, "isContainer": false, "level": 0}, {"id": "msruubt1l1mcwtmxzcotd4", "questionCode": "Q0025.C1", "controlName": "radio-group", "displayText": "Radio", "description": "Was patient /caregiver knowledge base assessed?", "labelName": "Patient’s history associated with Circulatory system", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Yes"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "No"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034116602, "isContainer": false, "level": 0}, {"id": "zeelmx2ihsmyehrenopjn", "questionCode": "Q0025.C2", "controlName": "checklist", "displayText": "Checklist", "description": "Which deficits were identified? (Check all that apply)\n", "labelName": "Indicate deficits identified", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "No knowledge deficits identified"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Nature of disease process"}, {"id": "frafitl7zagvjpos7morq", "value": "3", "label": "S & S of complications"}, {"id": "1kg4fl2ld6anop19eyn2h", "value": "4", "label": "Actions to take in an emergency"}, {"id": "u4ab82arvdblntq2kwflqa", "value": "5", "label": "Prescribed treatment regime"}, {"id": "g78fdnb8yz6zft7pfm5og", "value": "6", "label": "Oxygen therapy"}, {"id": "m8y96q7c5ur7rdjmzrel2d", "value": "7", "label": "Anticoagulant therapy"}, {"id": "64yzawc8wgapub974x75lg", "value": "8", "label": "How to take pulse"}, {"id": "o3cg2eus8ho3p22arja10w", "value": "9", "label": "How to take blood pressure"}, {"id": "j4oiibrwxl8273aqezyr4n", "value": "10", "label": "Diet and dietary restrictions"}, {"id": "gg10rw7akunvy5ry4rwke", "value": "11", "label": "Energy conservation techniques"}, {"id": "h3sindp6k3uqnc8za5rh6", "value": "12", "label": "Risk factors for falls/injuries"}, {"id": "l7gxumjg1ek78tk9j87ns", "value": "13", "label": "Recording weight"}, {"id": "3c4czuw3alucg0hf9uwj1o", "value": "14", "label": "Keeping emergency phone numbers kept by phone\t"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034157932, "isContainer": false, "level": 0}, {"id": "11pmkxduzy8w0f1gw4hq4n", "questionCode": "Q0025.C3", "controlName": "toggle", "displayText": "Toggle", "description": "Was physician contacted regarding need for therapy services?", "labelName": "Patient’s history associated with Circulatory system", "placeholder": "Yes/No", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034334705, "isContainer": false, "level": 0}, {"id": "8lmykrbvu4t9j6ay9dbalk", "questionCode": "Q0026.A1", "controlName": "checkbox", "displayText": "Checkbox", "description": "What is the PT/INR value?", "labelName": "PT/INR", "placeholder": "N/A", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034508929, "isContainer": false, "level": 0}, {"id": "pb05suiqfro1072nzcsvs", "questionCode": "Q0026.B1", "controlName": "toggle", "displayText": "Toggle", "description": "Is <PERSON><PERSON> Taking an Anticoagulant?", "labelName": "PT/INR", "placeholder": "Yes/No", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034548829, "isContainer": false, "level": 0}, {"id": "lqikq8hc2ohumijrdc337", "questionCode": "Q0026.C1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "PT(Sec):", "placeholder": "Enter Number", "labelName": "PT/INR", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034597670, "isContainer": false, "level": 0}, {"id": "m220o2yq993atvymlef7r", "questionCode": "Q0026.C2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "INR:", "placeholder": "Enter Number", "labelName": "PT/INR", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034650725, "isContainer": false, "level": 0}, {"id": "lxyxjgjjufnsa4vlq2wbc", "questionCode": "Q0026.D1", "controlName": "date-field", "displayText": "Date Picker", "description": "Results From:", "labelName": "PT/INR", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743034687308, "isContainer": false, "level": 0}, {"id": "ia4oclygjtdx94gfbjvc2", "questionCode": "Q0027.A1", "controlName": "checklist", "displayText": "Checklist", "description": "Have you experienced any changes in your urinary habits, such as increased frequency, urgency, pain during urination, or blood in your urine?", "labelName": "Genitourinary", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "N/A"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "No Problem Found"}, {"id": "ocqfhztbfq0uk6xe1c1n9g", "value": "3", "label": "Urgency/frequency"}, {"id": "yj19gbwjq32xxnffhuafu", "value": "4", "label": "Hesitancy"}, {"id": "s6xekx33f8j8eac1in14z3", "value": "5", "label": "Hematuria"}, {"id": "tlfazl50pl8ce7ps6yss4c", "value": "6", "label": "Hx UTIs"}, {"id": "mz8btidwn8n5863fcbuz97", "value": "7", "label": "Burning/pain"}, {"id": "9fxkfyaos7rcy4p01vljlc", "value": "8", "label": "Nocturia"}, {"id": "q7xxx0n2ysaylgqqsu8qv", "value": "9", "label": "Oliguria/anuria"}, {"id": "eahqa44tf3h9kf59yith2", "value": "10", "label": "Urinary Retention"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743035816332, "isContainer": false, "level": 0}, {"id": "9i0ecb22gg44ae0p1mk1lo", "questionCode": "Q0027.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Urinary Incontinence (when occurs)", "placeholder": "Please write your notes here.", "labelName": "Genitourinary", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036019974, "isContainer": false, "level": 0}, {"id": "y8yb6fr50r8iccmqo0m8ij", "questionCode": "Q0027.A3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Diapers/other:", "placeholder": "Please write your notes here.", "labelName": "Genitourinary", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036042857, "isContainer": false, "level": 0}, {"id": "j68mjd47vxe5sanwe7crw", "questionCode": "Q0027.B1", "controlName": "radio-group", "displayText": "Radio", "description": "What is the color and clarity of the patient's urine?", "labelName": "Genitourinary: Color", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Yellow/straw"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Amber"}, {"id": "7copez6de0o5r8zl1ofl3h", "value": "3", "label": "Brown/gray"}, {"id": "dgoobwr7i6a57f3mcc7trt", "value": "4", "label": "Blood-tinged"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036088444, "isContainer": false, "level": 0}, {"id": "jci233nu34mhpdquc7h1pm", "questionCode": "Q0027.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Other ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036152018, "isContainer": false, "level": 0}, {"id": "gkmreyn9q0py3kjdbrhipb", "questionCode": "Q0027.C1", "controlName": "radio-group", "displayText": "Radio", "description": "What is the clarity of the patient's urine?", "labelName": "Genitourinary: Clarity", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Clear"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Cloudy"}, {"id": "zwoo1rrwzhsviuz1jhgoin", "value": "3", "label": "Sediment/mucous"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036224973, "isContainer": false, "level": 0}, {"id": "uljkpoa3sm7t5b67f9ns", "questionCode": "Q0027.D1", "controlName": "radio-group", "displayText": "Radio", "description": "Is there an odor of urine?", "labelName": "Genitourinary: <PERSON><PERSON>", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Yes"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "No"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036483861, "isContainer": false, "level": 0}, {"id": "ptlhacygqxpsksrs9utcr", "questionCode": "Q0027.E1", "controlName": "radio-group", "displayText": "Radio", "description": "Does the patient use any catheters or other urinary devices?", "labelName": "Genitourinary: Urina<PERSON> Catheter", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "<PERSON>"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "<PERSON>"}, {"id": "qckywzwg9ixyajgh<PERSON>qdan", "value": "3", "label": "<PERSON><PERSON>"}, {"id": "otyyxtaa9ce7gy9dffnse", "value": "4", "label": "Irrigation"}, {"id": "hrabzxr9kz5ndz0a6h8po", "value": "5", "label": "External Texas"}, {"id": "ni9sx3wlc7ifkwayt14w6q", "value": "6", "label": "Condom"}, {"id": "3dpuokxzt660838pvg99i6s", "value": "7", "label": "Suprapubic"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036770802, "isContainer": false, "level": 0}, {"id": "3r8c2o7igzc4np9s31b1r", "questionCode": "Q0027.E2", "controlName": "date-field", "displayText": "Date Picker", "description": "<PERSON> inserted (date)", "labelName": "Genitourinary: Urina<PERSON> Catheter", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036944800, "isContainer": false, "level": 0}, {"id": "vwoofp7kwz76rvg1txm98", "questionCode": "Q0027.E3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "<PERSON> inserted with:", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Urina<PERSON> Catheter", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743036996560, "isContainer": false, "level": 0}, {"id": "ixwwfvpk9rlz8tk4t4ax5", "questionCode": "Q0027.E4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Inflated balloon with: ml", "placeholder": "Enter Number", "labelName": "Genitourinary: Urina<PERSON> Catheter", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037031309, "isContainer": false, "level": 0}, {"id": "y37hr861qvfs795ryb6mop", "questionCode": "Q0027.E5", "controlName": "checklist", "displayText": "Checklist", "description": "Inflated balloon with", "labelName": "Genitourinary: Urina<PERSON> Catheter", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "without difficulty"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Suprapubic"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037090542, "isContainer": false, "level": 0}, {"id": "12y0dhe85erkh8twmllp7hp", "questionCode": "Q0027.E6", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Irrigation solution: Type (specify)", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Urina<PERSON> Catheter", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037166858, "isContainer": false, "level": 0}, {"id": "jt4gdr9hqosp0p5fkrvmhf", "questionCode": "Q0027.E7", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Amount: ml", "placeholder": "Enter Number", "labelName": "Genitourinary: Urina<PERSON> Catheter", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037241790, "isContainer": false, "level": 0}, {"id": "1hv4ku99bp9n0ulz1mz5iq", "questionCode": "Q0027.E8", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Frequency", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Urina<PERSON> Catheter", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037297023, "isContainer": false, "level": 0}, {"id": "cku9x26jvhvnrmpd574ywc", "questionCode": "Q0027.E9", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Returns:", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Urina<PERSON> Catheter", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037332073, "isContainer": false, "level": 0}, {"id": "czls8v34bo7bdi1ibm7r85", "questionCode": "Q0027.E10", "controlName": "radio-group", "displayText": "Radio", "description": "Was the procedure tolerated by the patient?", "labelName": "Genitourinary: Urina<PERSON> Catheter", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Yes"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "No"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037442042, "isContainer": false, "level": 0}, {"id": "y4cfpewuiebb5x5ideqyn", "questionCode": "Q0027.E11", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Urostomy (describe skin around stoma)", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Uros<PERSON><PERSON>", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037494469, "isContainer": false, "level": 0}, {"id": "o7usfdrxoyq57348lupnlg", "questionCode": "Q0027.E12", "controlName": "radio-group", "displayText": "Radio", "description": "Ostomy care managed by:", "labelName": "Genitourinary", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Self"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Caregiver"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037570118, "isContainer": false, "level": 0}, {"id": "as7772k1t6bsh9zlrccdkh", "questionCode": "Q0027.E13", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: <PERSON><PERSON><PERSON> (specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037613517, "isContainer": false, "level": 0}, {"id": "ln17yl8u1zeirbrt7m4st", "questionCode": "Q0027.E14", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Disease Management Problems Explain:", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Disease Management", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037658318, "isContainer": false, "level": 0}, {"id": "bvrom5shw7v2j3dhy7ge", "questionCode": "Q0027.E15", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Dialysis Center:", "placeholder": "Please write your notes here.", "labelName": "Genitourinary: Dialysis Center", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037697267, "isContainer": false, "level": 0}, {"id": "ksrz4ooz5iy6s4m4y4fgj", "questionCode": "Q0028.A1", "controlName": "checklist", "displayText": "Checklist", "description": "Genital Examination", "labelName": "Genitalia", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "N/A"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "No Problem Found"}, {"id": "amxfkkrrw11x4wllhm7co", "value": "3", "label": "Discharge/Drainage"}, {"id": "iurcifzjb0a7rrpfhos1ru", "value": "4", "label": "Lesions/Blisters/Masses/Cysts"}, {"id": "oq3dafys9t96be7b7gvz", "value": "5", "label": "Inflammation"}, {"id": "td5yqk44mz41zxiprseyy", "value": "6", "label": "Surgical alteration"}, {"id": "9y19npm2nh6na2m8g1bbd8", "value": "7", "label": "Prostate problem"}, {"id": "geilhrazuvm1mcvh22i29a", "value": "8", "label": "Self-testicular exam"}, {"id": "df5ankzr2l5rju7imk1yuh", "value": "9", "label": "Menopause"}, {"id": "yyrkuonx8ctogjdn1st1", "value": "10", "label": "Hysterectomy"}, {"id": "mfjmcoili6hjoix0zumzbl", "value": "11", "label": "Breast self-exam"}, {"id": "o9pivb4v2f8hnhwzuuhvmv", "value": "12", "label": "Discharge: R/l"}, {"id": "kiph8o53au8akttzyincp8", "value": "13", "label": "Mastectomy"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743037907503, "isContainer": false, "level": 0}, {"id": "okhcbqmggv91c2exekn8", "questionCode": "Q0028.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Discharge/Drainage: (describe)", "placeholder": "Please write your notes here.", "labelName": "Genitalia: Discharge/Drainage", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038672223, "isContainer": false, "level": 0}, {"id": "wmib2evp6jmkrm7yd4x21", "questionCode": "Q0028.A3", "controlName": "date-field", "displayText": "Date Picker", "description": "Prostate problem: BPH/TURP Date", "labelName": "Genitalia", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038302724, "isContainer": false, "level": 0}, {"id": "6vs8up2etbd3v0vcxwppb9", "questionCode": "Q0028.A4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Frequency:", "placeholder": "Please write your notes here.", "labelName": "Genitalia: Self-testicular exam. ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038327893, "isContainer": false, "level": 0}, {"id": "0bwbtodxv6yhz3o61gwgcg", "questionCode": "Q0028.A5", "controlName": "date-field", "displayText": "Date Picker", "description": "Hysterectomy Date", "labelName": "Genitalia: Hysterectomy ", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038363873, "isContainer": false, "level": 0}, {"id": "6z1qto3m815pztbhyyd5pp", "questionCode": "Q0028.A6", "controlName": "date-field", "displayText": "Date Picker", "description": "Date last PAP", "labelName": "Genitalia: Date last PAP", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038530488, "isContainer": false, "level": 0}, {"id": "ktsqlvvorpbq4gfojjwoeq", "questionCode": "Q0028.A7", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Results", "placeholder": "Please write your notes here.", "labelName": "Genitalia: Results", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038563334, "isContainer": false, "level": 0}, {"id": "c327u36omrdqs0isi1kf8k", "questionCode": "Q0028.A8", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Self-testicular exam. Frequency:", "placeholder": "Please write your notes here.", "labelName": "Genitalia: Self-testicular exam.", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038605636, "isContainer": false, "level": 0}, {"id": "s915nk88imo9it21s8ya2b", "questionCode": "Q0028.A9", "controlName": "date-field", "displayText": "Date Picker", "description": "Mastectomy: R/l Date", "labelName": "Genitalia: Mastectomy: R/l Date", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038790588, "isContainer": false, "level": 0}, {"id": "bsrqv9w5k17zel51i5jhb", "questionCode": "Q0028.A10", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Genitalia: Other (specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038941950, "isContainer": false, "level": 0}, {"id": "uuq0417mx2nbrukq8bwd", "questionCode": "M1600", "controlName": "radio-group", "displayText": "Radio", "description": "Has this patient been treated for a Urinary Tract Infection in the past 14 days?", "labelName": "Urinary Tract Infection", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Yes"}, {"id": "6fk7575o5lrokh4hrqx7ta", "value": "3", "label": "NA - Patient on prophylactic treatment"}, {"id": "woflicqcnl86dbsbtrdhc", "value": "4", "label": "UK - Unknown [Omit “UK” option on DC ]"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038992783, "isContainer": false, "level": 0}, {"id": "luzalx0oemdi8h3fclwxv", "questionCode": "M1610", "controlName": "radio-group", "displayText": "Radio", "description": "Does the patient experience urinary incontinence?", "labelName": "Urinary Incontinence or Urinary Catheter Presence:", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No incontinence or catheter (includes anuria or ostomy for urinary drainage)"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Patient is incontinent"}, {"id": "8znn92jn3i1ocbo413ajd", "value": "3", "label": "Patient requires a urinary catheter (specifically: external, indwelling, intermittent, suprapubic)"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743039050113, "isContainer": false, "level": 0}, {"id": "ltjpe6b9nph4dzrwarsbv", "questionCode": "M1620", "controlName": "radio-group", "displayText": "Radio", "description": "What is the frequency of the patient's bowel incontinence?", "labelName": "Bowel Incontinence Frequency", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Very rarely or never has bowel incontinence"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Less than once weekly"}, {"id": "ohsrl3pjbji9nk51fpge6a", "value": "3", "label": "One to three times weekly"}, {"id": "vrcgvcdvufobxez4lw5ge", "value": "4", "label": "Four to six times weekly"}, {"id": "gqn6jqvdtutkznommljx1j", "value": "5", "label": "On a daily basis"}, {"id": "3flv9xnirk4w5j3oeglxui", "value": "6", "label": "More often than once daily"}, {"id": "nq7ihjaf5vtf4i95hfsiqh", "value": "7", "label": "NA - Pat<PERSON> has ostomy for bowel elimination"}, {"id": "5hforz0ilt5mnzsy3ewtwb", "value": "8", "label": "UK - Unknown [Omit “UK” option on FU, DC ]"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743039258804, "isContainer": false, "level": 0}, {"id": "7u4hxeimy2qtffhnlg9jgg", "questionCode": "Q0029.A1", "controlName": "checklist", "displayText": "Checklist", "description": "What are the main functions of the gastrointestinal (GI) tract?", "labelName": "Gastrointestinal", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "N/A"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "No Problem Found"}, {"id": "f48cfgta79gkcmgj38a26n", "value": "3", "label": "<PERSON><PERSON><PERSON>"}, {"id": "89lb5el3z4kesvx364vc5n", "value": "4", "label": "Constipation"}, {"id": "oxjcxlwybonqaz73nmeeo9", "value": "5", "label": "<PERSON><PERSON>"}, {"id": "g1f8qaai19uinr36hpknac", "value": "6", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "fkewahv18vinpfh4ulrl2p", "value": "7", "label": "Indigestion"}, {"id": "jb5idxn8lijr597uwp7mwr", "value": "8", "label": "Vomiting"}, {"id": "k5j5opnomk4txsja084yl", "value": "9", "label": "Impaction"}, {"id": "vzqnlcl48zohphjzb5f14", "value": "10", "label": "Rectal bleeding"}, {"id": "o3y2qrr1hsihtsgnl0ld4", "value": "11", "label": "<PERSON><PERSON><PERSON>"}, {"id": "4542ipfiyziz6c0ct2pqdh", "value": "12", "label": "Pain"}, {"id": "yexdzod2tsechuf0nycbu6", "value": "13", "label": "Diarrhea"}, {"id": "xvq1ryvr1jrg87bpuqowp9", "value": "14", "label": "Flatulence"}, {"id": "vnlkglyyy3jv7y9kle625", "value": "15", "label": "Hemorrhoids"}, {"id": "d28ju5lobof5dhlygs5o4r", "value": "16", "label": "Gallbladder problem"}, {"id": "iaigresnnhh5a59fykujim", "value": "17", "label": "Jaundice"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743056711665, "isContainer": false, "level": 0}, {"id": "gye8emug02rx7wz1jayqj", "questionCode": "Q0029.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Gastrointestinal: Other (specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743056919866, "isContainer": false, "level": 0}, {"id": "4graak7aeb6iy08st86g9q", "questionCode": "Q0029.A3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is the Frequency of stools?", "placeholder": "Please write your notes here.", "labelName": "Gastrointestinal: Frequency of stools", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743056977749, "isContainer": false, "level": 0}, {"id": "ygi1k6j909dbvwwdeig5j", "questionCode": "Q0029.B1", "controlName": "radio-group", "displayText": "Radio", "description": "What are Bowel Sounds?", "labelName": "Gastrointestinal: Bowel Sounds", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Absent"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Hypoactive"}, {"id": "60g5ouhtbws09jdmuc8f9q", "value": "3", "label": "Hyperactive"}, {"id": "pybffm4lgtqduo9p4kyrbp", "value": "4", "label": "Active"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743057179606, "isContainer": false, "level": 0}, {"id": "zpbk4w5hrr1r0h08d8iso", "questionCode": "Q0029.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Gastrointestinal Notes:", "placeholder": "Please write your notes here.", "labelName": "Gastrointestinal: Hypoactive", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743057570278, "isContainer": false, "level": 0}, {"id": "mtup3wynsy6ueeozj143f", "questionCode": "Q0029.B3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Hyperactive Notes:", "placeholder": "Please write your notes here.", "labelName": "Gastrointestinal: Hyperactive", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743057599557, "isContainer": false, "level": 0}, {"id": "ykhvlh8l8ua956aquzwhv", "questionCode": "Q0029.B4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Active Notes", "placeholder": "Please write your notes here.", "labelName": "Gastrointestinal:  Active", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743057622225, "isContainer": false, "level": 0}, {"id": "rwiceztc97pyjzpi04g9l", "questionCode": "Q0029.C1", "controlName": "checklist", "displayText": "Checklist", "description": "Abdominal Condition: Select all applicable symptoms.\n", "labelName": "Gastrointestinal: Abdomen", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "Distention"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Hard"}, {"id": "p4iv4m0c9rd74hmd1dnm7", "value": "3", "label": "Soft"}, {"id": "lpyx8y5gwikuxoyluw29un", "value": "4", "label": "Pain w/Palpation"}, {"id": "te5xv2699snw1h2nis2", "value": "5", "label": "Tenderness"}, {"id": "o2zh290k9blpkzsl4d5hhc", "value": "6", "label": "Ascites"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743057667605, "isContainer": false, "level": 0}, {"id": "rk3i5e3hbwfbhm9884k4v", "questionCode": "Q0029.C2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Abdominal girth", "placeholder": "cm", "labelName": "Gastrointestinal: Abdomen", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743057827170, "isContainer": false, "level": 0}, {"id": "aux671fnyacgyk9yepqvf", "questionCode": "Q0029.C3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Abdomen: Other(Specify)", "placeholder": "Please write your notes here.", "labelName": "Gastrointestinal: Abdomen", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743057885819, "isContainer": false, "level": 0}, {"id": "qo9gqzu08qikv9ye85eg9", "questionCode": "Q0029.D1", "controlName": "radio-group", "displayText": "Radio", "description": "How often does the patient use laxatives or enemas?", "labelName": "Gastrointestinal: Laxative/Enema use", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Daily"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Weekly"}, {"id": "zkak7150izsynpjbnbznfl", "value": "3", "label": "Monthly"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058046901, "isContainer": false, "level": 0}, {"id": "fnxd6r1srjgi2c5mxf1l7f", "questionCode": "Q0029.D2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations: ", "placeholder": "Please write your notes here.", "labelName": " Laxative/Enema use: Other", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058100417, "isContainer": false, "level": 0}, {"id": "07r3pvtn9b3kd5src5cktud", "questionCode": "Q0029.E1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "How often do you experience this symptom?", "placeholder": "Enter Number", "labelName": "Frequent BMs", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058291915, "isContainer": false, "level": 0}, {"id": "wuuxk6x4dg8dw8vlbxa3vp", "questionCode": "Q0029.E2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Infrequent BMs _ days since last?", "placeholder": "Enter Number", "labelName": "Infrequent BMs", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058354279, "isContainer": false, "level": 0}, {"id": "266ml4ts0emmm3hy6i6l07", "questionCode": "Q0029.E3", "controlName": "date-field", "displayText": "Date Picker", "description": "Last BM Date:", "labelName": "Last BM Date", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058400796, "isContainer": false, "level": 0}, {"id": "l562v851rohnlwhn1roegc", "questionCode": "Q0029.E4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What are the Types of Ostomy?", "placeholder": "Please write your notes here.", "labelName": "<PERSON><PERSON><PERSON>", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058448896, "isContainer": false, "level": 0}, {"id": "iq59k5icihqepfuyfhghr", "questionCode": "Q0029.E5", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stoma Appearance", "placeholder": "Please write your notes here.", "labelName": "Ostomy: <PERSON><PERSON> Appearance", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058584459, "isContainer": false, "level": 0}, {"id": "wzsqn6rz6frnvsys81sk", "questionCode": "Q0029.E6", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stool Appearance", "placeholder": "Please write your notes here.", "labelName": "Ostomy: <PERSON><PERSON> Appearance", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058633493, "isContainer": false, "level": 0}, {"id": "0puxtzpi13qkmwzbdizz9yk", "questionCode": "Q0029.E7", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Surrounding Skin:", "placeholder": "Please write your notes here.", "labelName": "Ostomy: Surrounding Skin", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058660097, "isContainer": false, "level": 0}, {"id": "lt0qj8jjxcxvwzqrset6", "questionCode": "Q0029.E8", "controlName": "toggle", "displayText": "Toggle", "description": "Intact", "labelName": "<PERSON><PERSON><PERSON>", "placeholder": "Enable/Disable", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058688245, "isContainer": false, "level": 0}, {"id": "qgpyzl8vojshmb3farzrd", "questionCode": "Q0029.F1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Detail Bowel regime/program:", "placeholder": "Please write your notes here.", "labelName": "Gastrointestinal: Bowel regime/program", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743058957474, "isContainer": false, "level": 0}, {"id": "axwhvzpz43aou0pbapx7k", "questionCode": "Q0030.A1", "controlName": "checklist", "displayText": "Checklist", "description": "Does the patient have a history of endocrine disorders?", "labelName": "Endocrine/ Hematology", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "N/A"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "No Problems Found"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059099231, "isContainer": false, "level": 0}, {"id": "9gmj91iwkbmeg73fw7fgl", "questionCode": "Q0030.A2", "controlName": "radio-group", "displayText": "Radio", "description": "Type of Diabetes:", "labelName": "Endocrine/ Hematology: Diabetes", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Type I Juvenile"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Type II"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059183485, "isContainer": false, "level": 0}, {"id": "zrfc3dqcuh9qokzhf0a73m", "questionCode": "Q0030.A3", "controlName": "date-field", "displayText": "Date Picker", "description": "Date of Onset:", "labelName": "Endocrine/ Hematology: Date of Onset", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059238883, "isContainer": false, "level": 0}, {"id": "qgx5b7agike5vsn0op7q7b", "questionCode": "Q0030.B1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other endocrine disorder (specify)", "placeholder": "Please write your notes here.", "labelName": "Endocrine/ Hematology: Other", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059342569, "isContainer": false, "level": 0}, {"id": "6zrtya6bhrblu8prw8apv", "questionCode": "Q0030.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Diet/Oral control (specify):", "placeholder": "Please write your notes here.", "labelName": "Endocrine/ Hematology: Diet/Oral control", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059370915, "isContainer": false, "level": 0}, {"id": "qogtylcfgxq9tt41sa8qqb", "questionCode": "Q0030.B3", "controlName": "date-field", "displayText": "Date Picker", "description": "On insulin since:", "labelName": "Endocrine/ Hematology", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059410398, "isContainer": false, "level": 0}, {"id": "3sui0ed2v0mxtvbbxb7ttp", "questionCode": "Q0030.C1", "controlName": "radio-group", "displayText": "Radio", "description": "Insulin drawn up by:\t\n", "labelName": "Medication Management: (select primary person responsible)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Patient"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Nurse"}, {"id": "x9d1f9dgmsjebenhaeobm5", "value": "3", "label": "Caregiver"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059443047, "isContainer": false, "level": 0}, {"id": "7nfwrqrildi1vbuw3ay48y", "questionCode": "Q0031.C2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other(Specify):", "placeholder": "Please write your notes here.", "labelName": "Insulin drawn up by: Other", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059513828, "isContainer": false, "level": 0}, {"id": "x5fm1ecr7j9efwro1f50ye", "questionCode": "Q0031.D1", "controlName": "radio-group", "displayText": "Radio", "description": "Insulin administered by:\t\n", "labelName": "Medication Management: (select primary person responsible)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Patient"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Nurse"}, {"id": "ewbvqx3xxecv4j7ovs9727", "value": "3", "label": "Caregiver"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059565464, "isContainer": false, "level": 0}, {"id": "mwdqx9qdthtsk1i248ttec", "questionCode": "Q0031.D2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other(Specify):", "placeholder": "Please write your notes here.", "labelName": "Insulin administered by: Other", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059747942, "isContainer": false, "level": 0}, {"id": "upci9ad8wwgz2sb8arf5", "questionCode": "Q0031.E1", "controlName": "radio-group", "displayText": "Radio", "description": "Monitored by:\t\n", "labelName": "Medication Management: (select primary person responsible): Monitored by:", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Patient"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Nurse"}, {"id": "1qxkiyh2ehimwgob3rqby9", "value": "3", "label": "Caregiver"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059828008, "isContainer": false, "level": 0}, {"id": "2a8a5ac3kc6zybrex6tdrf", "questionCode": "Q0031.E2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other(Specify):", "placeholder": "Please write your notes here.", "labelName": "Monitored by: Other", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059869077, "isContainer": false, "level": 0}, {"id": "fukhxwl8i8riljowkje1kf", "questionCode": "Q0031.F1", "controlName": "toggle", "displayText": "Toggle", "description": "Hyperglycemic S&S:", "labelName": "Endocrine/ Hematology", "placeholder": "Glycosuria/Polyuria/Polydipsia", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059935225, "isContainer": false, "level": 0}, {"id": "t1atlad96ujb07hr2twp6", "questionCode": "Q0031.G1", "controlName": "toggle", "displayText": "Toggle", "description": "Hypoglycemic S&S:", "labelName": "Endocrine/ Hematology", "placeholder": "Sweats/Polyphagia/Weak/<PERSON>aint/<PERSON><PERSON>or", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743059975107, "isContainer": false, "level": 0}, {"id": "fpne7osfxriddz9memk7fu", "questionCode": "M1630", "controlName": "radio-group", "displayText": "Radio", "description": "Does this patient have an ostomy for bowel elimination that (within the last 14 days): a) was related to an inpatient facility stay, or b) necessitated a change in medical or treatment regimen ?", "labelName": "O<PERSON><PERSON> for Bowel Elimination", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Patient does not have an ostomy for bowel elimination."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Patient's ostomy was not related to an inpatient stay and did not necessitate change in medical or treatment regimen"}, {"id": "su9bnfee8mgixwrdsrd8u", "value": "3", "label": "The ostomy was related to an inpatient stay or did necessitate change in medical or treatment regimen"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743060133900, "isContainer": false, "level": 0}]}, {"container": {"id": "kb87is8lpr9ic4vs25qjr", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Nutritional Status"}, "items": [{"id": "u62vyi8a9q91my336cpgbg", "questionCode": "Q0032.A1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Determining the patient's nutritional requirements.", "placeholder": "Sodium Diet", "labelName": "Nutritional Requirements", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062519117, "isContainer": false, "level": 0}, {"id": "2ge5d6nr0y7zcuoc97bt5f", "questionCode": "Q0032.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Determining the patient's nutritional requirements.\n", "placeholder": "Calorie ADA Diet", "labelName": "Nutritional Requirements", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062561116, "isContainer": false, "level": 0}, {"id": "7k8jifd7op6sdx1de3jg7", "questionCode": "Q0032.A3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Determining the patient's nutritional requirements.\n", "placeholder": "Bland Diet", "labelName": "Nutritional Requirements", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062640473, "isContainer": false, "level": 0}, {"id": "rbayrtk3pwcw7jnj04fqj", "questionCode": "Q0032.A4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Determining the patient's nutritional requirements.\n", "placeholder": "<PERSON><PERSON>", "labelName": "Nutritional Requirements", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062709413, "isContainer": false, "level": 0}, {"id": "5rzqfty6unqjmi7z4x3fjg", "questionCode": "Q0032.A5", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Determining the patient's nutritional requirements.\n", "placeholder": "Protein Low Diet", "labelName": "Nutritional Requirements", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062737247, "isContainer": false, "level": 0}, {"id": "zwur2peh99c0hnlatpplf3", "questionCode": "Q0032.A6", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Determining the patient's nutritional requirements.\n", "placeholder": "Carbohydrate Hi Diet ", "labelName": "Nutritional Requirements", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062765102, "isContainer": false, "level": 0}, {"id": "thtuca75ven4uhgu63wkcf", "questionCode": "Q0032.A7", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Determining the patient's nutritional requirements.\n", "placeholder": "Carbohydrate Low Diet", "labelName": "Nutritional Requirements", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062793899, "isContainer": false, "level": 0}, {"id": "iqx4fxi4stgt1sxd2t3b9", "questionCode": "Q0032.A8", "controlName": "checklist", "displayText": "Checklist", "description": "Determining the patient's nutritional requirements.\n", "labelName": "Nutritional Requirements", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "Enteral feeding "}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Mechanical (Soft, Hi-Fiber, etc.)"}, {"id": "bs5c7i3wwdzbmogh6721h", "value": "3", "label": "NG Tube"}, {"id": "w4i35hcojqpqqpryodukh", "value": "4", "label": "Regular"}, {"id": "7gngdiri3cc9n0sendjo0m", "value": "5", "label": "PEG Tube"}, {"id": "q1vpn24hcdit86ffvzjae", "value": "6", "label": "No Concentrated Sweets"}, {"id": "kwobxt1wsbj4snvfd57jur", "value": "7", "label": "<PERSON><PERSON>"}, {"id": "lpv84232m2p6vzisnjnup6", "value": "8", "label": "No Added Salt"}, {"id": "uej8lnr4k1my8w3rvf6ry", "value": "9", "label": "NPO"}], "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062821030, "isContainer": false, "level": 0}, {"id": "4izz9ptykac9jkhbf618be", "questionCode": "Q0032.A9", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Nutritional Requirements: Other(Specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743062972278, "isContainer": false, "level": 0}, {"id": "3ar9a5881in608qsbtrtxv", "questionCode": "Q0033.A1", "controlName": "checklist", "displayText": "Checklist", "description": "What are the different types of enteral feeding access devices?", "labelName": "Enteral Feedings - Access Device", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "N/A"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "No Problem Found"}, {"id": "1vvduj7jgvzfl3i11dkjco", "value": "3", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "nnr5sjbmimz0ftva8tf1g", "value": "4", "label": "Gastrostomy"}, {"id": "n70hr57t0zaqkattsrn8w7", "value": "5", "label": "Jejunostomy"}, {"id": "b6t69n13i1t9fkip366d4v", "value": "6", "label": "<PERSON><PERSON>"}, {"id": "ypid8n7g6s9tmod9tgygqq", "value": "7", "label": "Continuous"}], "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063048419, "isContainer": false, "level": 0}, {"id": "yisq72newhcjouqx8b85v", "questionCode": "Q0033.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Other (specify)", "labelName": "Enteral Feedings - Access Device", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063216573, "isContainer": false, "level": 0}, {"id": "i8xhw0s450ovlka1w5eq1d", "questionCode": "Q0033.A3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Pump (type/specify)", "placeholder": "Other Observations:", "labelName": "Enteral Feedings - Access Device", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063254705, "isContainer": false, "level": 0}, {"id": "vr17pe8po7jj2jcmx96rvf", "questionCode": "Q0033.A4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Feedings: Type (amt./rate)", "placeholder": "Other Observations:", "labelName": "Enteral Feedings - Access Device", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063357670, "isContainer": false, "level": 0}, {"id": "l1ag55fctris4zndlzr1k", "questionCode": "Q0033.A5", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Flush Protocol: (amt./specify)", "placeholder": "Other Observations:", "labelName": "Enteral Feedings - Access Device", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063420522, "isContainer": false, "level": 0}, {"id": "a5qjvlor6j7v0j8a6zdyx", "questionCode": "Q0033.B1", "controlName": "radio-group", "displayText": "Radio", "description": " Performed by:", "labelName": "Enteral Feedings - Access Device", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Self"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "RN"}, {"id": "cpsaiq9anslk8nrpkse1ne", "value": "3", "label": "Caregiver"}], "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063454370, "isContainer": false, "level": 0}, {"id": "50zxrggy9wietuoi1ijem", "questionCode": "Q0033.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Performed by", "placeholder": "Other Observations:", "labelName": "Enteral Feedings - Access Device", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063500886, "isContainer": false, "level": 0}, {"id": "jq7rp4pbn7l6y7nb3t9agr", "questionCode": "Q0033.B3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Dressing/Site care:", "placeholder": "Please write your notes here.", "labelName": "Enteral Feedings - Access Device", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063556218, "isContainer": false, "level": 0}, {"id": "1c76zv2uy7588xrn79zp2m", "questionCode": "Q0033.B4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Instructions / Comments:", "placeholder": "Please write your notes here.", "labelName": "Enteral Feedings - Access Device", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "kb87is8lpr9ic4vs25qjr", "_tempId": 1743063599202, "isContainer": false, "level": 0}]}, {"container": {"id": "7yafoql2y54z3a7rvkgsbc", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Neuro/Emotional/Behavioral"}, "items": [{"id": "d13q83uktc7zhd0tq9bgr9", "questionCode": "C0100", "controlName": "radio-group", "displayText": "Radio", "description": "Should Brief Interview for Mental Status (C0200-C0500) be Conducted?", "labelName": "Interview for Mental Status", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No (patient is rarely/never understood) ➔ Skip to C1310, Signs and Symptoms of Delirium (from CAM ©)"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes ➔ Continue to C0200, Repetition of Three Words"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743063758582, "isContainer": false, "level": 0}, {"id": "za4etnxaqhe4tsbld5galp", "questionCode": "C0200", "controlName": "radio-group", "displayText": "Radio", "description": "I am going to say three words for you to remember. Please repeat the words after I have said all three. The words are: sock, blue, and bed. Now tell me the three words.\nNumber of words repeated after first attempt", "labelName": "Repetition of Three Words", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "0", "label": "None"}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "1", "label": "One"}, {"id": "kc8s74rrf6p8vvxhy6emy2", "value": "3", "label": "Two"}, {"id": "9l9jo1qwq4g8d36qvpa7b", "value": "4", "label": "Three"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743236755876, "isContainer": false, "level": 0}, {"id": "ba8eshkyr1vgc4y2dnfitu", "questionCode": "C0300.A", "controlName": "radio-group", "displayText": "Radio", "description": "Please tell me what year it is right now.", "labelName": "Temporal Orientation (Orientation to year, month, and day)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Missed by more than 5 years or no answer"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Missed by 2-5 years"}, {"id": "4wjwoeh3vudbdah9zbpqtb", "value": "3", "label": "Missed by 1 year"}, {"id": "k9en65vtwkfdcbf7t8m35c", "value": "4", "label": "Correct"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743063900213, "isContainer": false, "level": 0}, {"id": "pxp63uyzzjn0ih76sb8jonl", "questionCode": "C0300.B", "controlName": "radio-group", "displayText": "Radio", "description": "What month are we in right now?", "labelName": "Temporal Orientation (Orientation to year, month, and day)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Missed by more than 1 month or no answer"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Missed by 6 days to 1 month"}, {"id": "mwqryyi6vp7qjjgyusnxq", "value": "3", "label": "Accurate within 5 days"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743063956629, "isContainer": false, "level": 0}, {"id": "o1e7f5v3o2jzewnnbhfso", "questionCode": "C0300.C", "controlName": "radio-group", "displayText": "Radio", "description": "What day of the week is today?", "labelName": "Temporal Orientation", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Incorrect or no answer"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Correct"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064031233, "isContainer": false, "level": 0}, {"id": "xi0jm66fn2r7ync8jtj03a", "questionCode": "C0400.A", "controlName": "radio-group", "displayText": "Radio", "description": "Able to recall “sock”", "labelName": "Recall: Let's go back to an earlier question. What were those three words that I asked you to repeat?", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No <PERSON> could not recall"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes, after cueing (\"something to wear\")"}, {"id": "qonnax3y6akebhe2fswua", "value": "3", "label": "Yes, no cue required"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064067445, "isContainer": false, "level": 0}, {"id": "3fd4ytde6kvqok3x0kfyif", "questionCode": "C0400.B", "controlName": "radio-group", "displayText": "Radio", "description": "Able to recall “blue”", "labelName": "Recall", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No <PERSON> could not recall"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes, after cueing (\"a color\")"}, {"id": "r3wdz95ptsqzony4rcali8", "value": "3", "label": "Yes, no cue required"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064168458, "isContainer": false, "level": 0}, {"id": "04ni2ixio7ed4sumdr8z4r2", "questionCode": "C0400.C", "controlName": "radio-group", "displayText": "Radio", "description": "Able to recall “bed”", "labelName": "Recall", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No could not recall"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes, after cueing (\"a piece of furniture\")"}, {"id": "2d7ishr8844dq0dsr597l", "value": "3", "label": "Yes, no cue required"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064267641, "isContainer": false, "level": 0}, {"id": "jhw7henivd8g53wlb2mvu", "questionCode": "C0500", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Add scores for questions C0200-C0400 and fill in total score (00-15)\nEnter 99 if the patient was unable to complete the interview", "placeholder": "Enter Code", "labelName": "BIMS Summary Score", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064328626, "isContainer": false, "level": 0}, {"id": "jrzcki5ttsqxrf51kbaw", "questionCode": "C1310.A", "controlName": "radio-group", "displayText": "Radio", "description": "Acute Onset of Mental Status Change\nIs there evidence of an acute change in mental status from the patient's baseline?", "labelName": "Signs and Symptoms of Delirium (from CAM©) ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064399046, "isContainer": false, "level": 0}, {"id": "pv1lioi2an8s3vzenez9bm", "questionCode": "C1310.B", "controlName": "radio-group", "displayText": "Radio", "description": "Inattention – Did the patient have difficulty focusing attention, for example, being easily distractible or having difficulty keeping track of what was being said?", "labelName": "Signs and Symptoms of Delirium (from CAM©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Behavior not present"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Behavior continuously present, does not fluctuate"}, {"id": "izjjy3x0j7q8cs4r9h1gyp", "value": "3", "label": "Behavior present, fluctuates"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064482355, "isContainer": false, "level": 0}, {"id": "kjri64mzfcsd0s00n4y2ap", "questionCode": "C1310.C", "controlName": "radio-group", "displayText": "Radio", "description": "Disorganized thinking– Was the patient's thinking disorganized or incoherent (rambling or irrelevant conversation, unclear or illogical flow of ideas, or unpredictable switching from subject to subject)?", "labelName": "Signs and Symptoms of Delirium (from CAM©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Behavior not present"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Behavior continuously present, does not fluctuate"}, {"id": "r9ndx0m7fvel4hn8lkb3gb", "value": "3", "label": "Behavior present, fluctuates"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064536037, "isContainer": false, "level": 0}, {"id": "hira13a5zh4ftfvhlr5rpk", "questionCode": "C1310.D", "controlName": "radio-group", "displayText": "Radio", "description": "Altered level of consciousness: – Did the patient have altered level of consciousness, as indicated by any of the following criteria?\nvigilant – startled easily to any sound or touch\nlethargic – repeatedly dozed off when being asked questions, but responded to voice or touch\nstuporous – very difficult to arouse and keep aroused for the interview\ncomatose – could not be aroused", "labelName": "Signs and Symptoms of Delirium (from CAM©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Behavior not present"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Behavior continuously present, does not fluctuate"}, {"id": "hgp8of9zbpal4wz1s7jmm", "value": "9", "label": "Behavior present, fluctuates"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064603604, "isContainer": false, "level": 0}, {"id": "7wtwxwyt6qw920glaesuuw", "questionCode": "D0150.A1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Little interest or pleasure in doing things", "labelName": "Patient <PERSON> Interview (PHQ-2 to 9) - Say to patient: \"Over the last 2 weeks, have you been bothered by any of the following problems?\"", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "twmx32d11apv3t3ww9yy6", "value": "3", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065047370, "isContainer": false, "level": 0}, {"id": "rxrxj6sz72k9hdaux8qdkj", "questionCode": "D0150.A2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Little interest or pleasure in doing things", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "rj31cmpwttv8gmxk693fh", "value": "2", "label": "7-11 days (half or more of the days)"}, {"id": "5e3ufoabzxcuoltbrhud8", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065273248, "isContainer": false, "level": 0}, {"id": "xm50vv290fc8flviik769", "questionCode": "D0150.B1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Feeling down, depressed, or hopeless", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "4ljf3d2uz5o871oxro1lru", "value": "9", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065388751, "isContainer": false, "level": 0}, {"id": "g4mmswl90jocghdz16li5g", "questionCode": "D0150.B2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Feeling down, depressed, or hopeless", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "vpraaroq5mrsthtn541oa", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "ha80sm55vpim06cdglicid", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065516008, "isContainer": false, "level": 0}, {"id": "pbt42x72w6go6gtcclgk3b", "questionCode": "D0150.C1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Trouble falling or staying asleep, or sleeping too much", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "ut3xwg56p4qwfb80d9tdf9", "value": "9", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065586606, "isContainer": false, "level": 0}, {"id": "fmajdtcnkvhlyosl7ginw", "questionCode": "D0150.C2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Trouble falling or staying asleep, or sleeping too much", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "9i3ulsot39kjmdqsy4kkn", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "5vd0j2cfa7ujha443y9c6o", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065640847, "isContainer": false, "level": 0}, {"id": "k0646jtq2utyacnwhw22m", "questionCode": "D0150.D1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Feeling tired or having little energy", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "7fnuxpotw2axhyb6q6babm", "value": "3", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065851254, "isContainer": false, "level": 0}, {"id": "f5zo88li7adidnxn4vks8", "questionCode": "D0150.D2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Feeling tired or having little energy", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "hsvg2otdt7ha5kv1n7cb", "value": "3", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065910270, "isContainer": false, "level": 0}, {"id": "r8ih8jce00ed50vedixslq", "questionCode": "D0150.D2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Feeling tired or having little energy", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "jmt16dtgdyinmrpajyhct", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "igbjdqn7f9gqtl8f6t55r", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065965260, "isContainer": false, "level": 0}, {"id": "deb4xt8onfpg6rd3we6vm4", "questionCode": "D0150.E1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Poor appetite or overeating", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "e5fgi5qtl27l4vdhlsq37n", "value": "3", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066026105, "isContainer": false, "level": 0}, {"id": "tjhncy3y6ec2e9ld4tmjha", "questionCode": "D0150.E2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Poor appetite or overeating", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "nlbpd0ftoju11nvg0zwu", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "p6am3vumyedzszcgkd40e", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066072868, "isContainer": false, "level": 0}, {"id": "856xuxj1jvrxtrnaqbi5p", "questionCode": "D0150.F1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Feeling bad about yourself – or that you are a failure or have let yourself or your family down", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "tkf4bt8t7h4lex8clq2tt", "value": "3", "label": "No response "}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066144497, "isContainer": false, "level": 0}, {"id": "ttocuqs1tqu1w524rmono", "questionCode": "D0150.F2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Feeling bad about yourself – or that you are a failure or have let yourself or your family down", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "fhfep1jjtgjbv2snq30rts", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "1e2b7xxkpljdtf8cibte0t", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066193450, "isContainer": false, "level": 0}, {"id": "9yt0wusw3x7llebaid5czp", "questionCode": "D0150.G1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Trouble concentrating on things, such as reading the newspaper or watching television", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "8xj6rpg8zvrzeozqa3xiv", "value": "3", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066256530, "isContainer": false, "level": 0}, {"id": "jnfty3nurgd262hrslo0c", "questionCode": "D0150.G2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Trouble concentrating on things, such as reading the newspaper or watching television", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "pfbbf2v68xgr7e8vz3t5", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "nb3nf23vlmhdb93nahi645", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066303013, "isContainer": false, "level": 0}, {"id": "q0mh542384qn9h7f10quu", "questionCode": "D0150.H1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Moving or speaking so slowly that other people could have noticed. Or the opposite – being so fidgety or restless that you have been moving around a lot more than usual", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "z3rsvd9w9v960tmkvo5df", "value": "3", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066375727, "isContainer": false, "level": 0}, {"id": "8ubz7ygm1uof4wsxeweiku", "questionCode": "D0150.H2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Moving or speaking so slowly that other people could have noticed. Or the opposite – being so fidgety or restless that you have been moving around a lot more than usual\n", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "27jfqix0cfqvrqbbtzgb0h", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "q20y7hcrdltvekuplrin", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066423820, "isContainer": false, "level": 0}, {"id": "4v2qriaf2klidnluo000w9", "questionCode": "D0150.I1", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Presence: Thoughts that you would be better off dead, or of hurting yourself in some way", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "vq2qnms1g9jgjkez3kks1t", "value": "3", "label": "No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066661599, "isContainer": false, "level": 0}, {"id": "p7qhhs3a797jakqxha7o9", "questionCode": "D0150.I2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Thoughts that you would be better off dead, or of hurting yourself in some way", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "2-6 days (several days)"}, {"id": "r19ii4fpb0de6q4973v46", "value": "3", "label": "7-11 days (half or more of the days)"}, {"id": "8io5dzf3z2dvt4e7b1iu0d", "value": "4", "label": "12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066710127, "isContainer": false, "level": 0}, {"id": "o1f63o159rldo17hoycig", "questionCode": "D0160", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Symptom Frequency. Total score must be between 00 and 27. Enter 99 if unable to complete interview (i.e., Symptom Frequency is blank for 3 or more required items)", "placeholder": "Total score must be between 00 and 27. Enter 99 if unable to complete interview (i.e., Symptom Frequency is blank for 3 or more required items)", "labelName": "Total Severity Score", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066775273, "isContainer": false, "level": 0}, {"id": "6r283c34asy2uel12xj6", "questionCode": "D0700", "controlName": "radio-group", "displayText": "Radio", "description": "How often do you feel lonely or isolated from those around you?", "labelName": "Social Isolation", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Rarely"}, {"id": "8l11iklomsf51rfxcul1i", "value": "2", "label": "Sometimes"}, {"id": "e44z775xllhe0f5edal1h", "value": "3", "label": "Often"}, {"id": "z3rk2854p6me955nn7cyik", "value": "5", "label": "Always"}, {"id": "k94mlj2e4d94py8qdwyg", "value": "6", "label": "Patient declines to respond"}, {"id": "7wop45s69d58gkkurz8vfg", "value": "7", "label": "Patient unable to respond"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066855680, "isContainer": false, "level": 0}, {"id": "wcfu9s2ptw70lp7yafsfy", "questionCode": "Q0034.A1", "controlName": "checklist", "displayText": "Checklist", "description": "Have you noticed any changes in patient's mood or energy levels? (Check all that apply)", "labelName": "Mental Status", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "Oriented"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Forgetful"}, {"id": "ck2vq9o72sjcy9y0dh28as", "value": "3", "label": "Disoriented"}, {"id": "61sv7bjfs37iy0q47nea8", "value": "4", "label": "Agitated"}, {"id": "rlnjqgv2l68bo48wsiy2n5", "value": "5", "label": "Comatose"}, {"id": "7jbvjdbmk8l5kp9medxlte", "value": "6", "label": "Depressed"}, {"id": "43ib11twnghr2ojbcaqfm", "value": "7", "label": "Lethargic"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066952035, "isContainer": false, "level": 0}, {"id": "q59t5vdhrebusgoqpya58", "questionCode": "Q0034.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Mental Status: Other (specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067261798, "isContainer": false, "level": 0}, {"id": "mg3vx8rqfgq3yhdu8hmm1g", "questionCode": "M1700", "controlName": "radio-group", "displayText": "Radio", "description": "Patient's current (day of assessment) level of alertness, orientation, comprehension, concentration, and immediate memory for simple commands.", "labelName": "Cognitive Functioning", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "<PERSON><PERSON>/Oriented, able to focus and shift attention, comprehends and recalls task directions independently."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Requires prompting (cuing, repetition, reminders) only under stressful or unfamiliar conditions."}, {"id": "rqimm72ic6g58zrhl533e", "value": "3", "label": "Requires assistance and some direction in specific situations (For example: on all tasks involving shifting of attention), or consistently requires low stimulus environment due to distractibility."}, {"id": "lcdnl4ql4so3xzfxqwcv13", "value": "4", "label": "Requires considerable assistance in routine situations. Is not alert and oriented or is unable to shift attention and recall directions more than half the time."}, {"id": "dqn37q0datcbofdsbyx8zw", "value": "5", "label": "Totally dependent due to disturbances such as constant disorientation, coma, persistent vegetative state, or delirium."}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067521203, "isContainer": false, "level": 0}, {"id": "x9atp9u3d8de4i8gxixi88", "questionCode": "M1710", "controlName": "radio-group", "displayText": "Radio", "description": "Reported or Observed Within the Last 14 Days? ", "labelName": "When Confused", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "In new or complex situations only."}, {"id": "goe3ckffsh3bshvm25z7h", "value": "3", "label": "On awakening or at night only"}, {"id": "axskuru7bnfka41dtdyze", "value": "4", "label": "During the day and evening, but not constantly."}, {"id": "2kzjon8c5ksmghufa231u", "value": "5", "label": "Constantly"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067579327, "isContainer": false, "level": 0}, {"id": "hez0q1wyg7h3dw3l2t181u", "questionCode": "M1720", "controlName": "radio-group", "displayText": "Radio", "description": "Reported or Observed Within the Last 14 Days", "labelName": "When Anxious", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "0", "label": "None of the time."}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "1", "label": "Less often than daily."}, {"id": "bcfz9vbzungevm8r55698t", "value": "3", "label": "Daily, but not constantly"}, {"id": "3pmaxxybjnszp6r4hw8xm", "value": "4", "label": "All of the time."}, {"id": "9q56btzcnzii6mbonlkwv", "value": "5", "label": "Patient non-responsive"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743237045513, "isContainer": false, "level": 0}, {"id": "np6myl2s0tg19xkawqpye4", "questionCode": "M1740", "controlName": "checklist", "displayText": "Checklist", "description": "Cognitive, behavioral and psychiatric symptoms that are demonstrated atleast once a week (Reported or Observed): (Mark all that apply)", "labelName": "Cognitive, behavioral and psychiatric", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "Memory deficit: failure to recognize familiar persons/places, inability to recall events of past 24 hours, significant memory loss so that supervision is required"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Impaired decision-making: failure to perform usual ADLs or IADLs, inability to appropriately stop activities, jeopardizes safety through actions"}, {"id": "8hmwhaj16ibufmpasdungc", "value": "3", "label": "Verbal disruption: yelling, threatening, excessive profanity, sexual references, etc."}, {"id": "4evm8e9mvlumnf6lrz12b", "value": "4", "label": "Physical aggression: aggressive or combative to self and others (e.g., hits self, throws objects, punches, dangerous maneuvers with wheelchair or other objects)"}, {"id": "gghhm1jbfcthwi0th2m0l4", "value": "5", "label": "Disruptive, infantile, or socially inappropriate behavior (excludes verbal actions)"}, {"id": "41uelp87dbgcxkuo9u2un", "value": "6", "label": "Delusional, hallucinatory, or paranoid behavior"}, {"id": "2dxhxduk11qpz6xsdh2g6r", "value": "7", "label": "None of the above behaviors demonstrated"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067748541, "isContainer": false, "level": 0}, {"id": "z290j3ofkxdq7wjq7i8gtp", "questionCode": "M1745", "controlName": "radio-group", "displayText": "Radio", "description": "Frequency of Disruptive Behavior Symptoms (Reported or Observed) Any physical, verbal, or other disruptive/dangerous symptoms that are injurious to self or others or jeopardize personal safety", "labelName": "Behavior", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Never."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Less than once a month."}, {"id": "uy52amv5shia9aru7atjd", "value": "3", "label": "Once a month."}, {"id": "2zdj7o8mlncg3iwhjn7dt8", "value": "4", "label": "Several times each month."}, {"id": "5wz5699t474mx0l58nzr7", "value": "5", "label": "Several times a week"}, {"id": "ktylv1fwsp9grt7ihcpen", "value": "6", "label": "Atleast daily"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067826258, "isContainer": false, "level": 0}]}, {"container": {"id": "s07xcug37e1vbppvdvxl2", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "ADLs & IADLs"}, "items": [{"id": "3vd6a1t9zmkjebc2k41vx", "questionCode": "M1800", "controlName": "radio-group", "displayText": "Radio", "description": "Current ability to tend safely to personal hygiene needs (i.e., washing face and hands, hair care, shaving or make up, teeth or denture care, fingernail care).", "labelName": "Grooming", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to groom self unaided, with or without the use of assistive devices or adapted methods."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Grooming utensils must be placed within reach before able to complete grooming activities."}, {"id": "f0ktkmo4owdfhg2jjj41b", "value": "3", "label": "Someone must assist the patient to groom self."}, {"id": "bt3s9o0q9zjymu0izyjf2", "value": "4", "label": "Patient depends entirely upon someone else for grooming needs."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068009505, "isContainer": false, "level": 0}, {"id": "q6r9oh9yqfkev0520a08f", "questionCode": "M1810", "controlName": "radio-group", "displayText": "Radio", "description": "Safely (with or without dressing aids) including undergarments, pullovers, front-opening shirts and blouses, managing zippers, buttons and snaps:", "labelName": "Current Ability to Dress Upper Body", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to get clothes out of closets and drawers, put them on and remove them from the upper body without assistance."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Able to dress upper body without assistance if clothing is laid out or handed to the patient."}, {"id": "wnt128kamxgcj0t3imleak", "value": "3", "label": "Someone must help the patient put on upper body clothing."}, {"id": "tvkv312n7wl8xbc7ptvtdi", "value": "4", "label": "Patient depends entirely upon another person to dress the upper body"}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068059936, "isContainer": false, "level": 0}, {"id": "xk1v8xn93umwl5jqv5bd", "questionCode": "M1820", "controlName": "radio-group", "displayText": "Radio", "description": "Safely (with or without dressing aids) including undergarments, slacks, socks or nylons, shoes:", "labelName": "Current Ability to Dress Lower Body", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to obtain, put on and remove clothing and shoes without assistance."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Able to dress lower body without assistance if clothing and shoes are laid out or handed to the patient."}, {"id": "jldymrnb3ztyowlae4bbga", "value": "3", "label": "Someone must help the patient put on undergarments, slacks, socks or nylons, shoes."}, {"id": "9u2terfeieay50ijb1fgh8", "value": "4", "label": "Patient depends entirely upon another person to dress the lower body"}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068145453, "isContainer": false, "level": 0}, {"id": "5api2tmyp5ueosd3d9gq6u", "questionCode": "M1830", "controlName": "radio-group", "displayText": "Radio", "description": "Current ability to wash entire body safely. Excludes grooming (washing face, washing hands, and shampooing hair):", "labelName": "Bathing", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to bathe self in shower or tub independently, including getting in and out of tub/shower."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "With the use of device, is able to bathe self in shower or tub independently, including getting in and out of tub/shower."}, {"id": "5datfqkcn33ktklub01hwr", "value": "3", "label": "Able to bathe in shower or tub with the intermittent assistance of another person: a) for intermittent supervision or encouragement or reminders. OR b) to get in and out of the shower or tub, OR c) for washing difficult to reach areas."}, {"id": "rvrh4br49fh91qtd4kzh8g", "value": "4", "label": "Able to participate in bathing self in shower or tub, but requires presence of another person throughout the bath for assistance or supervision"}, {"id": "qy15ro2xzieppwf9ufz26", "value": "5", "label": "Unable to use the shower or tub, but able to bathe self independently with or without the use of devices at the sink, in chair, or on commode"}, {"id": "y2hrmo48clapq9j6jut6mh", "value": "6", "label": "Unable to use the shower or tub, but able to participate in bathing self in bed, at the sink, in bedside chair, or on commode, with the assistance or supervision of another person."}, {"id": "jj1m0s7vogpb75u5pwzyj", "value": "7", "label": "Unable to participate effectively in bathing and is bathed totally by another person."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068211169, "isContainer": false, "level": 0}, {"id": "svm8mo8dujc5ks91foxr", "questionCode": "M1840", "controlName": "radio-group", "displayText": "Radio", "description": "Current ability to get to and from the toilet or bedside commode safely and transfer on and off toilet/commode: ", "labelName": "<PERSON><PERSON><PERSON>", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to get to and from the toilet and transfer independently with or without a device."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "When reminded, assisted, or supervised by another person, able to get to and from the toilet and transfer."}, {"id": "pv31mloczmf9oluhx293hg", "value": "3", "label": "Unable to get to and from the toilet but is able to use a bedside commode with or without assistance."}, {"id": "di341hm0eus7ynn9jgqn", "value": "4", "label": "Unable to get to and from the toilet or bedside commode but is able to use a bedpan/urinal independently"}, {"id": "dx9520sksqjlxj7pz9eneo", "value": "5", "label": "Is totally dependent in toileting."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068293335, "isContainer": false, "level": 0}, {"id": "40yagjn3s4tycmk3odmhyi", "questionCode": "M1845", "controlName": "radio-group", "displayText": "Radio", "description": "Current ability to maintain perineal hygiene safely, adjust clothes and/or incontinence pads before and after using toilet, commode, bedpan, urinal. If managing ostomy, includes cleaning area around stoma, but not managing equipment: ", "labelName": "Toileting Hygiene", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to manage toileting hygiene and clothing management without assistance."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Able to manage toileting hygiene and clothing management without assistance if supplies/implements are laid out for the patient."}, {"id": "w5z1nhhq7xnand81t27z3m", "value": "3", "label": "Someone must help the patient to maintain toileting hygiene and/or adjust clothing."}, {"id": "87i7nz7xsg23f7zssi2ttj", "value": "4", "label": "Patient depends entirely upon another person to maintain toileting hygiene."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068365369, "isContainer": false, "level": 0}, {"id": "pwnfswqfodrlndbn7j4k", "questionCode": "M1850", "controlName": "radio-group", "displayText": "Radio", "description": "Current ability to move safely from bed to chair, or ability to turn and position self in bed if patient is bedfast.", "labelName": "Transferring", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to independently transfer."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Able to transfer with minimal human assistance or with use of an assistive device."}, {"id": "m5oss45m718vo2em1t72qb", "value": "3", "label": "Able to bear weight and pivot during the transfer process but unable to transfer self."}, {"id": "popame1xa9yqzjq0xapxd", "value": "4", "label": "Unable to transfer self and is unable to bear weight or pivot when transferred by another person."}, {"id": "l6e8n80ef4prglne6gogxf", "value": "5", "label": "<PERSON><PERSON>, unable to transfer but is able to turn and position self in bed"}, {"id": "vmqk9m21tdnthw5zm3utn", "value": "6", "label": "<PERSON><PERSON>, unable to transfer but is unable to turn and position self"}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068419616, "isContainer": false, "level": 0}, {"id": "b6buzto30ulebuhmhbxgvg", "questionCode": "M1860", "controlName": "radio-group", "displayText": "Radio", "description": "Current ability to walk safely, once in a standing position, or use a wheelchair, once in a seated position, on a variety of surfaces:", "labelName": "Ambulation/Locomotion", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to independently walk on even and uneven surfaces and negotiate stairs with or without railings(i.e. needs no human assistance or assistive device)."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "With the use of one handed device(e.g. cane, single crutch, hemi-walker),able to independently walk on even and uneven surfaces and negotiate stairs with or without railings."}, {"id": "pdi27rck08kyfiq9rbu9gp", "value": "3", "label": "Requires use of a two-handed device(e.g. walker or crutches) to walk alone on a level surface and/or requires human supervision or assistance to negotiate stairs or steps or uneven surfaces"}, {"id": "eesqrti4zkf7eh5h49rmm", "value": "4", "label": "Able to Walk only with the supervision or assistance of another person at all times."}, {"id": "yd8dp37x3adk89hxxc8pes", "value": "5", "label": "Chairfast, unable to ambulate but is able to wheel self independently."}, {"id": "c3iy996301ltct0dbtbs4", "value": "6", "label": "Chairfast, unable to ambulate and is unable to wheel self."}, {"id": "wztyrttpmarbvdv08tq4zt", "value": "7", "label": "Bedfast, unable to ambulate or be up in a chair."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068483087, "isContainer": false, "level": 0}, {"id": "nbb7v7amsr70iweeywsxiw4", "questionCode": "M1870", "controlName": "radio-group", "displayText": "Radio", "description": "Current ability to feed self meals and snacks safely. Note: This refers only to the process of eating, chewing and swallowing, not preparing the food to be eaten:", "labelName": "Feeding or Eating", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to independently feed self."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Able to feed self independently but requires: (a) meal set-up, OR (b) intermittent assistance or supervision from another person, OR (c) a liquid, pureed or ground meat diet."}, {"id": "g0tfioj1pj9ztq04t2ehzo", "value": "3", "label": "Unable to feed self and must be assisted or supervised throughout the meal/snack."}, {"id": "d8l9ly0jahf97va2tgfgn6", "value": "4", "label": "Able to take in nutrients orally and receives supplemental nutrients through a nasogastric tube or gastrostomy."}, {"id": "15aikqrykdjujrq2tbnfla", "value": "5", "label": "Unable to take in nutrients orally and is fed nutrients through a nasogastric tube or gastrostomy."}, {"id": "j9skt1uw2tj4nuwngxgatq", "value": "6", "label": "Unable to take nutrients orally or by tube feeding"}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068562147, "isContainer": false, "level": 0}, {"id": "tw2y1bmntzlvlzo1t172se", "questionCode": "Q0035", "controlName": "multiline-text-field", "displayText": "Notes", "description": "TUG Assessment Detail:", "placeholder": "Please write your notes here.", "labelName": "TUG Assessment", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068628937, "isContainer": false, "level": 0}, {"id": "lhmsdjidzjhnxnrvlgqnzr", "questionCode": "Q0036.A1", "controlName": "checklist", "displayText": "Checklist", "description": "Question Description", "labelName": "Musculoskeletal", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "Not Assessed"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "No Problem Found"}, {"id": "cgohj4ye7k7qzmpx60t69d", "value": "3", "label": "Arthritis"}, {"id": "i6rek9ggo2a96rke0xnm26", "value": "4", "label": "Atrophy"}, {"id": "zip1r49lu6m4z353mqo5l3", "value": "5", "label": "Contractures"}, {"id": "u81gmkk9p8zf15d0juu0o", "value": "6", "label": "Deformities"}, {"id": "rern69ixr0cs57v1dtkzz", "value": "7", "label": "Gout"}, {"id": "e3hahgmpmsoys878yfojp", "value": "8", "label": "Swollen joints"}, {"id": "nhnx0rjskukfnydueera0b", "value": "9", "label": "Abnormal gait"}, {"id": "4b9lxbwrn4jhcbyt9a7a88", "value": "10", "label": "Decreased ROM"}, {"id": "g3po793q1evy1gt3gzh9t", "value": "11", "label": "Weakness"}, {"id": "fmvm5uf0mpvzhr87ikp34", "value": "12", "label": "Joint pain"}, {"id": "dulnep45qcq3a7wpn599uh", "value": "13", "label": "Leg cramps"}, {"id": "93kpv9axzf7pl3qs3ju33", "value": "14", "label": "Numbness"}, {"id": "bh5kg5il39s59xgryzu7lj", "value": "15", "label": "Amputation"}, {"id": "5yvdcv7fv13mx8l0ceznf", "value": "16", "label": "Paralysis"}, {"id": "hvxughl9i2af86b7yt87ob", "value": "17", "label": "History of fracture"}, {"id": "bqrnmfifla3bfjt3yjf41", "value": "18", "label": "History of joint replacement"}, {"id": "tnwv46zj019mxunc0ci7ma", "value": "19", "label": "Prosthesis, appliance, etc."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068737018, "isContainer": false, "level": 0}, {"id": "23wud8vcsr5ta3niawv3bb", "questionCode": "Q0036.A2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Reason not assessed:", "placeholder": "Please write your notes here.", "labelName": "Musculoskeletal", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068952489, "isContainer": false, "level": 0}, {"id": "1qqh9lxct6uadetjk4qw8i", "questionCode": "Q0036.A3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations:", "placeholder": "Please write your notes here.", "labelName": "Musculoskeletal: Other (specify)", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068989176, "isContainer": false, "level": 0}, {"id": "xw9vun37u3v2jvp4fr5ng", "questionCode": "Q0036.A4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Additional Notes:", "placeholder": "Please write your notes here.", "labelName": "Musculoskeletal", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743069026940, "isContainer": false, "level": 0}]}, {"container": {"id": "6uek8ko7kxb2tpm3mbndoo", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section GG", "subHeading": "Functional Abilities and Goals"}, "items": [{"id": "yf8s00ykt409s32l46lk7x", "questionCode": "GG0100.A", "controlName": "radio-group", "displayText": "Radio", "description": "Self Care: Code the patient’s need for assistance with bathing, dressing, using the toilet, and eating prior to the current illness, exacerbation, or injury.", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "yfbeqv51if5f38c0cwich", "value": "3", "label": "Dependent – A helper completed the activities for the patient."}, {"id": "4hg8rayf8u3rd5itnbv9n", "value": "4", "label": "Unknown"}, {"id": "y55lj1ag23pmmqr8vcpcpd", "value": "5", "label": "Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743069979255, "isContainer": false, "level": 0}, {"id": "c09mgo7bb5l9ceshgn6zq", "questionCode": "GG0100.B", "controlName": "radio-group", "displayText": "Radio", "description": "Indoor Mobility (Ambulation): Code the patient's need for assistance with walking from room to room (with or without a device such as cane, crutch or walker) prior to the current illness, exacerbation, or injury.", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "cdu0ckrrel5w5ocuwhgea", "value": "3", "label": "Dependent – A helper completed the activities for the patient."}, {"id": "453smwtquq6r0h6w07unho", "value": "4", "label": "Unknown"}, {"id": "0shjer94fy4yjryykw6ok", "value": "5", "label": "Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070167933, "isContainer": false, "level": 0}, {"id": "1tmhcw2450rkfb3rq39p9", "questionCode": "GG0100.C", "controlName": "radio-group", "displayText": "Radio", "description": "Stairs: Code the patient's need for assistance with internal or external stairs (with or without a device such as cane, crutch, or walker) prior to the current illness, exacerbation or injury.", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "gkue070h13pu7k65nb3qes", "value": "3", "label": "Dependent – A helper completed the activities for the patient."}, {"id": "z8sfanaf6v6knr4ueqs2j", "value": "4", "label": "Unknown"}, {"id": "mm3kvt2e3vhqj4chhwr6g", "value": "5", "label": "Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070223249, "isContainer": false, "level": 0}, {"id": "4ty59gpblj56nzz3qeu95", "questionCode": "GG0100.D", "controlName": "radio-group", "displayText": "Radio", "description": "Functional Cognition: Code the patient's need for assistance with planning regular tasks, such as shopping or remembering to take medication prior to the current illness, exacerbation, or injury.", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "litqp8r0g6wuf4g9stl9", "value": "3", "label": "Dependent – A helper completed the activities for the patient."}, {"id": "jfy5bwjyoiyzyu92imupa", "value": "4", "label": "Unknown"}, {"id": "5qwnwvadsnoa2up3z8s4sq", "value": "5", "label": "Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070276631, "isContainer": false, "level": 0}, {"id": "wdf3eu963357f65mqhix", "questionCode": "GG0110", "controlName": "checklist", "displayText": "Checklist", "description": "Indicate devices and aids used by the patient prior to the current illness, exacerbation, or injury. (Mark all that apply)", "labelName": "Prior Device Use", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "Manual wheelchair"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "Motorized wheelchair and / or scooter"}, {"id": "pb4nif1tluhbakjg8gku0t", "value": "3", "label": "Mechanical lift"}, {"id": "p76bb1smg6xktayv6auo", "value": "4", "label": "<PERSON>"}, {"id": "e86y4qdz1lppos9fkge08", "value": "5", "label": "Orthotics / Prosthetics"}, {"id": "1180rxmlaziqm1w80o124", "value": "6", "label": "None of the above"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070397812, "isContainer": false, "level": 0}, {"id": "kztefohs5glh93f8toqi8", "questionCode": "GG0130.A", "controlName": "radio-group", "displayText": "Radio", "description": "Eating: The ability to use suitable utensils to bring food and/or liquid to the mouth and swallow food and/or liquid once the meal is placed before the patient", "labelName": "Self-Care ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "22eacmch2wl5f5bl9z39nj", "value": "3", "label": "Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "p8upm8zcz0k0adyav5bh287", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "w3jay3hlimi11z0sdbdkp", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "jv8a1y6w4slh55yf3hdv4", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "c55cbi4b5s8ce2cbi0zngp", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "yq24uahranzb13roa8p58", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "yqr99h7e67084lovyw0xmc", "value": "9", "label": "Not attempted due to environmental limitations(e.g., lack of equipment, weather constraints)"}, {"id": "fbvl0l31srcqejute45doa", "value": "10", "label": "Not attempted due to medical conditions or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070610214, "isContainer": false, "level": 0}, {"id": "6cy21xohnmhiah04gqpwlg", "questionCode": "GG0130.B", "controlName": "radio-group", "displayText": "Radio", "description": "Oral Hygiene: The ability to use suitable items to clean teeth. Dentures (if applicable): The ability to insert and remove dentures into and from mouth, and manage denture soaking and rinsing with use of equipment.", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "c6nvsv2o6k5cj0krukdkz5", "value": "3", "label": "Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "6nwqiaak958msbmut09bh9", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ltdk0vxjm8pl57rup2v9r", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "4bynm1yscdsxhz4gntct7c", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "oxei7l2x0g9ueyfu218bui", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "zyn5qg2hw4fgktafysx3a", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "q3idjs98t8elupf0o2a02", "value": "9", "label": "Not attempted due to environmental limitations(e.g., lack of equipment, weather constraints)"}, {"id": "ax0odgc3zecuv6ige7jgqk", "value": "10", "label": "Not attempted due to medical conditions or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070738508, "isContainer": false, "level": 0}, {"id": "pn6k9majiv9kein9gjw87h", "questionCode": "GG0130.C", "controlName": "radio-group", "displayText": "Radio", "description": "Toileting Hygiene: The ability to maintain perineal hygiene, adjust clothes before and after voiding or having a bowel movement. If managing an ostomy, include wiping the opening but not managing equipment.", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "a063y1in5vtced3z2kpmul", "value": "3", "label": "Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "byu2meb1kusmp15gsapxj", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ag3apk5vasv8gobwgox1kg", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "9ucgsqo361lx3l7al1tqw", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "m3cn0zoycpmrjq0rrlee8p", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "rrxlr3cpcyekcrpmm7xpk9", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "udtse02ljco5mljc82dba", "value": "9", "label": "Not attempted due to environmental limitations(e.g., lack of equipment, weather constraints)"}, {"id": "4z85ledxx2goqolsqx35mh", "value": "10", "label": "Not attempted due to medical conditions or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070834947, "isContainer": false, "level": 0}, {"id": "fvykuj8df9rvmyriaw8pqo", "questionCode": "GG0130.E", "controlName": "radio-group", "displayText": "Radio", "description": "Shower/bathe self: The ability to bathe self, including washing, rinsing, and drying self (excludes washing of back and hair). Does not include transferring in/out of tub/shower.", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Button 2"}, {"id": "rb16e2rj2kat3nuhqqet7", "value": "3", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "qj7pawc8sgtf7v45xudf", "value": "4", "label": "Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "kmoteqn33xqzehbn83mq", "value": "5", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "091qnsrfiiga1pcjtb9pcip", "value": "6", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "ibgwvlsaxag188y6dwbxl", "value": "7", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "xv967wvv9oswq5ofgyv30m", "value": "8", "label": "<PERSON><PERSON> refused"}, {"id": "b6j5nt4e6enpbt2fgznii", "value": "9", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "zdhrauk3v7u2vwem92v0j", "value": "10", "label": "Not attempted due to environmental limitations(e.g., lack of equipment, weather constraints)"}, {"id": "45if6p4rl5epz181xvet2n", "value": "11", "label": "Not attempted due to medical conditions or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070944221, "isContainer": false, "level": 0}, {"id": "q9lcfarvd3zy9zhagjafn", "questionCode": "GG0130.F", "controlName": "radio-group", "displayText": "Radio", "description": "Upper body dressing: The ability to dress and undress above the waist; including fasteners, if applicable. ", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "p2mcza4f1fp0n6ljmeru3o", "value": "3", "label": "Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "r10crooc9a0joe4a4mip2k", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ls1qj9l1hkgt52vc4jnhg", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "a27qo9zong88zr9gdmh57", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "mupwrmoytbq8mc077rasp", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "rrt8mkfq9h97p075wrbvkc", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "qgjq88n4iuch6l5bf3vkq", "value": "9", "label": "Not attempted due to environmental limitations(e.g., lack of equipment, weather constraints)"}, {"id": "blppiyt729ia4p350ri21m", "value": "10", "label": "Not attempted due to medical conditions or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071036653, "isContainer": false, "level": 0}, {"id": "bllkh1nzpwlcpskcr6rol", "questionCode": "GG0130.G", "controlName": "radio-group", "displayText": "Radio", "description": "Lower body dressing: The ability to dress and undress below the waist, including fasteners; does not include footwear.", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "04", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "tvgdmxi3uybpu3mfgsyh", "value": "3", "label": "Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "fdg1kj7lq8wkw6ntn9v8tl", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "fltlcks1f2fbtyyznyyoqg", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "mtvt388qf1mxkaomcxwhh", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "wx55orndqjfteb3iy02vn", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "5t4aouai71kyl8beeqng4", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "4nl4d4ajct94ngkdfz0fg", "value": "9", "label": "Not attempted due to environmental limitations(e.g., lack of equipment, weather constraints)"}, {"id": "olupm2umqksd8deagrleum", "value": "10", "label": "Not attempted due to medical conditions or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071133496, "isContainer": false, "level": 0}, {"id": "d4tj2j7uslechc19banhj", "questionCode": "GG0130.H", "controlName": "radio-group", "displayText": "Radio", "description": "Putting on/taking off footwear: The ability to put on and take off socks and shoes or other footwear that is appropriate for safe mobility; including fasteners, if applicable.", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Button 2"}, {"id": "5l6n3l4hqutx3eemxkpkpo", "value": "3", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "8x48dki04evo71nxdb7gxn", "value": "4", "label": "Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "50n7n2fucedg01z7130dwp", "value": "5", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "pilgx2yrlkcain5mq1t7k", "value": "6", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "tk5qjsawuqgzn5953w5rf", "value": "7", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071250419, "isContainer": false, "level": 0}, {"id": "1u3lcn3hcgbxqcskt616wb", "questionCode": "GG0170.A", "controlName": "radio-group", "displayText": "Radio", "description": "Roll left and right: The ability to roll from lying on back to left and right side, and return to lying on back on the bed.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "g5lcn6c8c6ii0ryk36f0eq", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "s8v0mcmiaqoucjkiy7xy4s", "value": "04", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "3kmqtvgxmaep06fil2mqt", "value": "03", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "u8jg9043r5atb8wgzhhxv", "value": "02", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "ulkfuew17vnm24jsyty24", "value": "01", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "u932nt7ah566688cagaex", "value": "07", "label": "<PERSON><PERSON> refused"}, {"id": "gh7zfq7qaert156mo5hiqp", "value": "09", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "vwlrtglb0jlaa4zpz4h20i", "value": "10", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "xa39c6y65eo7bg8kd0den", "value": "88", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071409875, "isContainer": false, "level": 0}, {"id": "umy8xny2mgq4s1iqtayjf2", "questionCode": "GG0170.B", "controlName": "radio-group", "displayText": "Radio", "description": "Sit to lying: The ability to move from sitting on side of bed to lying flat on the bed. ", "labelName": "Mobility ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Button 2"}, {"id": "ay9s9dcxs38sxfiuxg7k", "value": "3", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "ts82eo9fgoeh7u2s3087s9", "value": "4", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "4ctrzcl80k9omdueulktyh", "value": "5", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "m66bjtg2ype1zqvylvjc6", "value": "6", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "x7yhkvviggy0rq6h65klb", "value": "7", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "hpatfo8ux2enldhe09029n", "value": "8", "label": "<PERSON><PERSON> refused"}, {"id": "okumaw1vqvajyeudox6xs", "value": "9", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "s3vjij6midk919glt97ica", "value": "10", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "k026xvszi87yi2s850dnq", "value": "11", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071613747, "isContainer": false, "level": 0}, {"id": "7kmndd5n99a5pwmk6dhwjx", "questionCode": "GG0170.C", "controlName": "radio-group", "displayText": "Radio", "description": "Lying to sitting on side of bed: The ability to move from lying on the back to sitting on the side of the bed with no back support.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "lhrbbv26eaepngq9c3615s", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "h03oleu0zi7be1lap0ztxn", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "5lcd4dv6iv68ehdlv584ja", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "m82az7rwlut0m860ktv32nc", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "ytaf969soqi3kbwrnjk8db", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "z4ooufftb3v2mjdhntx4", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "yxafl7nx3fmxuk3yttiw0d", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "xtd11ojlabcrejieov4nap", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071728646, "isContainer": false, "level": 0}, {"id": "khoaxhek31qksi03w54c5b", "questionCode": "GG0170.D", "controlName": "radio-group", "displayText": "Radio", "description": "Sit to stand: The ability to come to a standing position from sitting in a chair, wheelchair, or on the side of the bed. ", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "n29x60zp3nflpkcddmgsiq", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "vl8upvfzbpqlb5d5d08jq", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "n0agav05qnc3ija4a7lrh", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "u9e581unh5qs3rfkrdtjec", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "la0b7aimctoxfxldh2q9a", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "50o3akqyo3bfxo94sdfgr6", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "t17e4mqu6rvs23rysnb6", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "25u7t0767io5jhncwfdvqd", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071840875, "isContainer": false, "level": 0}, {"id": "29kgvhx2jurkifnirnae6", "questionCode": "GG0170.E", "controlName": "radio-group", "displayText": "Radio", "description": "Chair/bed-to-chair transfer: The ability to transfer to and from a bed to a chair (or wheelchair).", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "k0jtv0ukvch0fypfzw2sk", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "ggjhmy3lo3lduw3qbu07sl", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "197pwyuyy4w4bc4y9r8m2m", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "a4qzfs9fu9qu8r4d2gzf3r", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "txaiyupelxgse1di07pqt", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "io79p360se3re3z383nb1", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "s4ac5pv4nj1x7nbzju3cd", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "apzeb2r00ao8q95gcaj5iv", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071968640, "isContainer": false, "level": 0}, {"id": "adukb92ehls1kosym7c7by", "questionCode": "GG0170.F", "controlName": "radio-group", "displayText": "Radio", "description": "Toilet transfer: The ability to get on and off a toilet or commode. ", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "9d8v5v02x2nsld3cot4w1j", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "d79roloa22cgzdg98ldta", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "murwlrzymtcw5wxs48blan", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "niud8bw9gcjt6lklm7gqbc", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "sa6xnb7ixqhj8zjgxpadn", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "38olmkz5pke5s21v60yldq", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "u8b6e9mptibd9xu57ck8w5", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "hq8s4qw4ma1ggbpejl0jw", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072058086, "isContainer": false, "level": 0}, {"id": "pnpgylt1xo834e1n5duq5", "questionCode": "GG0170.G", "controlName": "radio-group", "displayText": "Radio", "description": "Car Transfer: The ability to transfer in and out of a car or van on the passenger side. Does not include the ability to open/close door or fasten seat belt", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "z9gg3c5g34gyn9blfjba39", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "mn53unpqdlfh0u28g1trb4", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "tmccb89jnmcc5ou2n01g8", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "bkkwana8wdoma8ul4qnzaq", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "swyg3iuxjiab4q9g23tj3f", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "vmt9b7y6zikluxtbz4vkz", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "jnaxo91u8mcpy0azy40xce", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "e0c8o5hjaofwh6pvpst47i", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072226971, "isContainer": false, "level": 0}, {"id": "tybf9psjwoujhpcb0scq", "questionCode": "GG0170.I", "controlName": "radio-group", "displayText": "Radio", "description": "Walk 10 feet: Once standing, the ability to walk at least 10 feet in a room, corridor, or similar space.\n If SOC/ROC performance is coded 07, 09, 10 or 88, skip to GG0170M, 1 step (curb).", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "wsn8k8dtnek1ov2m1kolvv", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "i4isotuo4sdj5odbo4i8d", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "hasiudguaxd7zu23g6bdko", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "7o4t4gnqpakitj2ur8x4h", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "yk44tm3ucddxsa6zp4v0m", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "jf8gsy5l41bdnsjae8o1s6", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "cszirwfl2lrtas6zmm530l", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "re6bz8k9rz3pexmfkfpjx", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072375595, "isContainer": false, "level": 0}, {"id": "ou133zbtu4bljfq2n4bof", "questionCode": "GG0170.J", "controlName": "radio-group", "displayText": "Radio", "description": "Walk 50 feet with two turns: Once standing, the ability to walk 50 feet and make two turns.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "nboqnetmzuv1tbdhtxmla", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "ufhmmuc28so9tae29rr78g", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "jcclour4i2tkb25gv49hh", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "jz1o0mjspshs2gojlue2he", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "jpjnpuot1pq2co7eeiwg", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "1xw9m3er7pwun7d2jx845i", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "dxy8nqb7nxs8eaj65c9a52", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "rqzyl6hbgqfm1c2569mkm", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072469266, "isContainer": false, "level": 0}, {"id": "h120vx9liafwnc4x52hbj", "questionCode": "GG0170.K", "controlName": "radio-group", "displayText": "Radio", "description": "Walk 150 feet: Once standing, the ability to walk at least 150 feet in a corridor or similar space.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "gz138xwbmdljkoo5s3hw09", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "97bf8hhz0yle7c8z6u311", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ljo7yy8tksomz2vtri6k", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "nm0k2zyrqtz966i2lnxw", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "00wt06jy3ramscgeval6zn", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "xo2cqnzv139rsa5pv9evdl", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "12rli24r9k89azqz6r189lr", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "3cpawpeopw773r8qb78fmd", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072564716, "isContainer": false, "level": 0}, {"id": "nryrhykxt9145ymszgeoa", "questionCode": "GG0170.L", "controlName": "radio-group", "displayText": "Radio", "description": "Walking 10 feet on uneven surfaces: The ability to walk 10 feet on uneven or sloping surfaces (indoor or outdoor), such as turf or gravel.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "5i1yr6wb8j48k3b87tvu6v", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "walvey5f8riicoj0b1x8j", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "yva4e5ofib90a2umxw6ol", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "xvjyigo73lq88t9rnjbkwy", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "uk7jtm8yht7gpzkgjgczis", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "nrlkgil6pfnkltxc7558n", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "ueknxr3yubbwk65kf8", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "vynjbtnlzgh85abbcxv91", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072689813, "isContainer": false, "level": 0}, {"id": "9pss8itsz8fl7p5aweqchq", "questionCode": "GG0170.M", "controlName": "radio-group", "displayText": "Radio", "description": "1 step (curb): The ability to go up and down a curb or up and down one step.\nIf SOC/ROC performance is coded 07, 09, 10 or 88, ➔ Skip to GG0170P, Picking up object.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "6v2kj15fbyjpmsr3djnog", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "1y7ebvz2dr7yskxe6do85", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "jhp2ks33z8n0u00590l09c", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "lqy9h9yc1ij9tp015dxdb", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "5sxjnb9j826wex2runlwqd", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "o3djqwdj6dh4ziwoi8rri", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "aavkn5lfjo4c39rizphpgj", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "s0zc88rkz6qxtf4m4x7wb", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072775796, "isContainer": false, "level": 0}, {"id": "kjhp2c15lxz09jopawcof", "questionCode": "GG0170.N", "controlName": "radio-group", "displayText": "Radio", "description": "4 steps: The ability to go up and down four steps with or without a rail.\nIf SOC/ROC performance is coded 07, 09, 10 or 88, skip to GG0170P, Picking up object.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "l8o5xuaw17cmcm94dilsag", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "efdyagowmd8pvyobsapvmp", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "q56fttymtwrm81brez4gs8", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "645mf2959hq3tjosj0kv9b", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "q1ysyjv5p6rqdsicw4e", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "53z6r6olgr2uxek7zhhk2", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "imzly0nb7v9wlnbg3koqo", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "ai4yd7zhwx9c0egg3wideh", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072856811, "isContainer": false, "level": 0}, {"id": "jamg9ooneld0yakyfd12", "questionCode": "GG0170.O", "controlName": "radio-group", "displayText": "Radio", "description": "12 steps: The ability to go up and down 12 steps with or without a rail.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "x9iuhye1incg1p76x3549", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "kaoyab7b2gqayt28ktfa9o", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "79q6cyk3y59o348uv4ee6", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "sptsfvvkn4ed75msn0fygp", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "qg6p2mxvohby9lesm3qfir", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "8u4mkkcp0pi3p0sjx3tmlr", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "6k33aumfi1dtvnk38p79na", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "f9upuq7qcmd4l8q9b52j0f", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072953862, "isContainer": false, "level": 0}, {"id": "9xhajpk87fk9y8i2ie65xn", "questionCode": "GG0170.P", "controlName": "radio-group", "displayText": "Radio", "description": "Picking up object: The ability to bend/stoop from a standing position to pick up a small object, such as a spoon, from the floor. ", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "aznmlq0a5zobzx77wrlze", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "rrbdcwcg3wqw2h5ua2olb", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "x4t8ixky2xn6gqxfvwyk4n", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "h7nsitk2ubszrx149gm87", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "m9oins3dccmlw305s1dnxq", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "xr5mmcez31s4ehyruycf27", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "1t9os7jvko5h3zgp48ghqc", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "ed5w1mtiwvb5i48v8cbcnh", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073054174, "isContainer": false, "level": 0}, {"id": "7ttimxga4csdxpyoxizz", "questionCode": "GG0170.Q", "controlName": "radio-group", "displayText": "Radio", "description": "Does patient use wheelchair and/or scooter?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No - Skip to M1600, Urinary Tract Infection"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes - Continue to GG0170R, Wheel 50 feet with two turns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073143482, "isContainer": false, "level": 0}, {"id": "ty9v6dfpk8xfokg0pfc6", "questionCode": "GG0170.R", "controlName": "radio-group", "displayText": "Radio", "description": "Wheel 50 feet with two turns: Once seated in wheelchair/scooter, the ability to wheel at least 50 feet and make two turns.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "tf116yx4mon1m8lz18j0bj", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "vgxh1fs2fwg9s0fgjrpmoc", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ac5fqyxzgnv02oibtmw5ld1", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "t1w4ilfa6ykhhmhfjaep", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "3ko7xtsgpx1lgiowxw2ct", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "lxk4ff7lmb1o7zckgjmqi", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "pki0y97qwnrsryrdk1ir", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "v1nl8rg3xh3noxau9i91r", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073195526, "isContainer": false, "level": 0}, {"id": "gza62srdrqgda9tcofn8xj", "questionCode": "GG0170.R1", "controlName": "radio-group", "displayText": "Radio", "description": "Indicate the type of wheelchair or scooter used.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Manual"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Motorized"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073273054, "isContainer": false, "level": 0}, {"id": "96kwhzzilh7jshpv98bmk", "questionCode": "GG0170.S", "controlName": "radio-group", "displayText": "Radio", "description": "Wheel 150 feet: Once seated in wheelchair/scooter, the ability to wheel at least 150 feet in a corridor or similar space. ", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Independent – Patient completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "xxml6wt4xbig1fa6i8jdvg", "value": "3", "label": "Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "pikj54wepw9wd69ldwm52r", "value": "4", "label": "Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ys5ftzjzl8or8ivefudb8", "value": "5", "label": "Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "0esqvw32h10i3sjfijxby2a", "value": "6", "label": "Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "0i3o2xqpdyzpp1unsnp8w9", "value": "7", "label": "<PERSON><PERSON> refused"}, {"id": "wgegjyac8qkfxop03agkyw", "value": "8", "label": "Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "autl1nt3xvraogjgvgz6cc", "value": "9", "label": "Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "2dd2lf2uoqfgb62wvqllp9", "value": "10", "label": "Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073321438, "isContainer": false, "level": 0}, {"id": "4rhk8ejon5vjpf3ksx3to", "questionCode": "GG0170.S1", "controlName": "radio-group", "displayText": "Radio", "description": "Indicate the type of wheelchair or scooter used.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Manual"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Motorized"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073406307, "isContainer": false, "level": 0}]}, {"container": {"id": "r7s3ru51g241glh0aw6a5", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Medications"}, "items": [{"id": "95z9nurkn9f6vkbyhjiadb", "questionCode": "N0415.A1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Antipsychotic: Is taking?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "mtbwbspbb27k0vk179712", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073621983, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "59kt91zy9lbe1sr8al8dhq", "questionCode": "N0415.A2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Antipsychotic: Indication Noted?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "dnibolx8vxeeax25g9aeip", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073712766, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "tb18c88v0hxoiw1vwg2g", "questionCode": "N0415.E1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Anticoagulant: Is Taking?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "p08k0i859ylxt99mw02yd", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073748147, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "wzv32fbkp2qiq4ci8cclq", "questionCode": "N0415.E2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Anticoagulant: Indication Noted?", "labelName": "Label for Dropdown", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "6xl2hzvnzrjcbhg8d5it7n", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073786198, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "8nno8a7v0359smmvums7ws", "questionCode": "N0415.F1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Antibiotic: Is taking?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "yj254jcd2ai0gowyemk8m", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073861113, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "cml7x5j9imw1l6h7o3xoe", "questionCode": "N0415.F2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Antibiotic: Indication Noted", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "y2rnghv8vd6t6rmhmgyyf", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073897896, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "pxywwlu5po6si9qwx5svb", "questionCode": "N0415.H1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Opioid: Is Taking?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "kj37kfuwrtikkq9sb0psw", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073948429, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "e1dcsgh7eznjclyjqrjkv", "questionCode": "N0415.H2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Opioid: Indication Noted?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "vn5zcz1fo7b872nzdrujxu", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743073988111, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "rhnimfmio8skc7lkqt2ixs", "questionCode": "N0415.I1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Antiplatelet: Is Taking?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "5xg03cs76hre3j68yjbewq", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074039577, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "7iczafn6tm8cx5cif5yjs", "questionCode": "N0415.I2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Antiplatelet: Indication Noted?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "94y5amepzfindcrektud3", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074090310, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "8r47s9jcxqut09twsos32", "questionCode": "N0415.J1", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Hypoglycemic (including insulin): Is Taking?", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "pbs5filo7hoyuyjbjafu0i", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074144276, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "0xkibnvcmmdijgxzaaftjzf", "questionCode": "N0415.J2", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "Hypoglycemic (including insulin): Indication Noted", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "ppiihul0uba8hxe6m2ckoh", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074179359, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "0vgiakl9lmyd0lwux5xbkt", "questionCode": "N0415.Z", "controlName": "select-drop-down", "displayText": "Dropdown", "description": "None of the above", "labelName": "High-Risk Drug Classes", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "kj9oe5avu9mgcc1nl7qqal", "value": "1", "label": "No"}, {"id": "ltj7j8pebdsv5fv2x6z2r", "value": "2", "label": "Yes"}, {"id": "jzr2ujcyjrooh114e5jbhp", "value": "3", "label": "No Information"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074229893, "isContainer": false, "level": 0, "answer_code": ""}, {"id": "xvis67sszkqlayqg9jvxlo", "questionCode": "M2001", "controlName": "radio-group", "displayText": "Radio", "description": "Did a complete drug regimen review identify potential clinically significant medication issues?", "labelName": "Drug Regimen Review", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "No - No issues found during review → Skip to M2010, Patient/Caregiver High-Risk Drug Education"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Yes - Issues found during review"}, {"id": "ofbm370uzewpdjyvvrhos", "value": "9", "label": "NA - Patient is not taking any medications → Skip to O0110, Special Treatments, Procedures, and Programs"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074295758, "isContainer": false, "level": 0}, {"id": "cmo1tnp3rfcb8h28a5lel", "questionCode": "M2003", "controlName": "radio-group", "displayText": "Radio", "description": "Did the agency contact a physician (or physician-designee) by midnight of the next calendar day and complete prescribed/recommended actions in response to the identified potential clinically significant medication issues?", "labelName": "Medication Follow-up", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074342377, "isContainer": false, "level": 0}, {"id": "bdmzfobliv8o1f2ck8cnms", "questionCode": "M2010", "controlName": "radio-group", "displayText": "Radio", "description": "Has the patient/caregiver received instruction on special precautions for all high-risk medications (such as hypoglycemics, anticoagulants, etc.) and how and when to report problems that may occur ?", "labelName": "Patient/Caregiver High Risk Drug Education", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes"}, {"id": "ebd6bbdqxq3h9gg7k6xm3", "value": "3", "label": "Patient not taking any high risk drug or patient /caregiver fully knowledgeable about special precautions associated with all high-risk medications."}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074398033, "isContainer": false, "level": 0}, {"id": "78v4rwm6s6edjpdcmnb1z", "questionCode": "M2020", "controlName": "radio-group", "displayText": "Radio", "description": "Patient's current ability to prepare and take all oral medications reliably and safely, including administration of the correct dosage at the appropriate times/intervals. Excludes injectable and IV medications. (NOTE: This refers to ability, not compliance or willingness.)", "labelName": "Management of Oral Medications", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to independently take the correct oral medication(s) and proper dosage(s) at the correct times."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Able to take medication(s) at the correct times(if a. Individual dosages prepared in advance by another person or b. another person develops a drug diary or chart)."}, {"id": "q7gmgto3fc5o3f4cjtl3m", "value": "3", "label": "Able to take medication(s) at the correct times if given reminders by another person at the appropriate times."}, {"id": "u8rjxligf2q6ag85sh3ie", "value": "4", "label": "Unable to take medication unless administered by another person."}, {"id": "9416bwfi74m5qi9tliefqx", "value": "5", "label": "No oral medications prescribed."}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074471105, "isContainer": false, "level": 0}, {"id": "6xc4bykhhv2a7gyemdi4u6", "questionCode": "M2030", "controlName": "radio-group", "displayText": "Radio", "description": "Patient's current ability to prepare and take all prescribed injectable medications reliably and safely, including administration of correct dosage at the appropriate times/intervals. Excludes IV medications.", "labelName": "Management of Injectable Medications", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "Able to independently take the correct medication(s) and proper dosage(s) at the correct times"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Able to take injectable medication(s) at the correct times(if a. Individual syringes are prepared in advance by another person or b. another person develops a drug diary or chart)"}, {"id": "xn5vicdjnbg9l3itex5lf", "value": "3", "label": "Able to take medication(s) at the correct times if given reminders by another person based on the frequency of the injection"}, {"id": "tnqo1iu6tgb1sl0bnvgdol", "value": "4", "label": "Unable to take injectable medication unless administered by another person"}, {"id": "ha5tykyvqwubaot3ke6bif", "value": "5", "label": "No injectable medications prescribed"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074521530, "isContainer": false, "level": 0}, {"id": "1n20zvergt5vwj1vxoksbs", "questionCode": "Q0037.A1", "controlName": "radio-group", "displayText": "Radio", "description": "During your infusion therapy, describe the condition of the patient's access site or line:", "labelName": "Infusion Therapy/Site Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "1", "label": "N/A"}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "2", "label": "No problem Found"}, {"id": "27j8myw01axaaft0cgx7fi", "value": "3", "label": "Peripheral Line"}, {"id": "1qg5caaokcrebhyy14q3ff", "value": "4", "label": "Catheter"}, {"id": "xe8u25ywqhj8wnuc12r8s", "value": "5", "label": "Central Line"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743240362798, "isContainer": false, "level": 0}, {"id": "bsbjvu8ww1ev94h131c0s", "questionCode": "Q0037.B1", "controlName": "checklist", "displayText": "Checklist", "description": "What is the Purpose of Intravenous Access?", "labelName": "Infusion Therapy/Site Care: Purpose of Intravenous Access", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "r5ketnvadspf98zviujyj9", "value": "1", "label": "Antibiotic Therapy"}, {"id": "0gevq7mcs4jsuxokgyj23f", "value": "2", "label": "Chemotherapy"}, {"id": "woi57ifd3qepma5fc86jz", "value": "3", "label": "Hydration"}, {"id": "yoia404nr1ewonch0bpj4o", "value": "4", "label": "Lab Draws"}, {"id": "zk50zr6accb70jdb0pot8", "value": "5", "label": "Pain Control"}, {"id": "0xemz1mzc5ikdod0qaq0nsm", "value": "6", "label": "Parenteral Nutrition"}, {"id": "l7wpey49xaem0vmuuvb52", "value": "7", "label": "Maintain Venous Access"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743240662803, "isContainer": false, "level": 0}, {"id": "h90qg8w1qe26flfho5c71", "questionCode": "Q0037.B2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observations", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care: Purpose of Intravenous Access:", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743240756144, "isContainer": false, "level": 0}, {"id": "ydo61go5goplk4ld4fud7", "questionCode": "Q0037.C1", "controlName": "checklist", "displayText": "Checklist", "description": "Type", "labelName": "Infusion Therapy/Site Care", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "r5ketnvadspf98zviujyj9", "value": "1", "label": "PICC"}, {"id": "0gevq7mcs4jsuxokgyj23f", "value": "2", "label": "PASV"}, {"id": "906fhvdwqxcplty5kofqwb", "value": "3", "label": "<PERSON><PERSON><PERSON>"}, {"id": "oa9f04yah787kbgkrv7cx", "value": "4", "label": "<PERSON><PERSON>"}, {"id": "6lu6zzcxr9qm3om90ohfb", "value": "5", "label": "Port-a-cath\t"}, {"id": "qgxk5y3yieg9xksxrhwtc", "value": "6", "label": "Per<PERSON>her<PERSON>/<PERSON><PERSON>lock"}, {"id": "lyab9ih2m8hywgius42dh", "value": "7", "label": "Extension Tubing"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743240890126, "isContainer": false, "level": 0}, {"id": "5ge3favjbwl3my4glctoa7", "questionCode": "Q0037.C2", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Catheter(size):", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241005824, "isContainer": false, "level": 0}, {"id": "d28bfv40sbiu12aa6nr5et", "questionCode": "Q0037.C3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Type/Brand:", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241246419, "isContainer": false, "level": 0}, {"id": "485k18b1eil7h350w00v7q", "questionCode": "Q0037.C4", "controlName": "radio-group", "displayText": "Radio", "description": "What is the number of lumens types? ", "labelName": "Infusion Therapy/Site Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "1", "label": "Single"}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "2", "label": "Double"}, {"id": "wp2ln8dtiots2ewyzfqb9", "value": "3", "label": "Triple"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241039275, "isContainer": false, "level": 0}, {"id": "ek7z9866bx94uml65ba9p", "questionCode": "Q0037.C5", "controlName": "radio-group", "displayText": "Radio", "description": "Which type of pump is it?", "labelName": "Infusion Therapy/Site Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "1", "label": "<PERSON><PERSON><PERSON>"}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "2", "label": "Cadd"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241386835, "isContainer": false, "level": 0}, {"id": "aue8nqewy257irrhivfa7", "questionCode": "Q0037.C6", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Observation", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care: Pump", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241498201, "isContainer": false, "level": 0}, {"id": "uwq38pf9lzym1qe2lpw5", "questionCode": "Q0037.C7", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Enter the Size/Gauge/Length:  \n", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241533553, "isContainer": false, "level": 0}, {"id": "e4k88j0c5uk3mrx2f6yqvo", "questionCode": "Q0037.C8", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Insertion Site", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241727824, "isContainer": false, "level": 0}, {"id": "weftffknvkq8gw3f96iofc", "questionCode": "Q0037.C9", "controlName": "date-field", "displayText": "Date Picker", "description": "Insertion Date", "labelName": "Infusion Therapy/Site Care", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241802749, "isContainer": false, "level": 0}, {"id": "5jzrqg3uwyqmhy1mgnkxhd", "questionCode": "Q0037.C10", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Describe Flush Solution/Frequency:", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care: Flush Solution/Frequency", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241837511, "isContainer": false, "level": 0}, {"id": "fz9fmufxf1rb08npyjy8d8", "questionCode": "Q0037.C11", "controlName": "checklist", "displayText": "Checklist", "description": "Site Assessment:", "labelName": "Infusion Therapy/Site Care", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "items": [{"id": "r5ketnvadspf98zviujyj9", "value": "1", "label": "No Problems"}, {"id": "0gevq7mcs4jsuxokgyj23f", "value": "2", "label": "Reddened"}, {"id": "ycxkf27xar3hkkofkm3yn", "value": "3", "label": "<PERSON><PERSON>"}, {"id": "i4qu0rhdzkqudtnbmqfo2", "value": "4", "label": "Drainage"}, {"id": "13l8qsmb955kvect2nanr1r", "value": "5", "label": "Catheter loose"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241884044, "isContainer": false, "level": 0}, {"id": "y87a6gjbq5y8mdbyin7mp", "questionCode": "Q0037.C12", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Catheter length:", "placeholder": "cm/in", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743241979745, "isContainer": false, "level": 0}, {"id": "4i4l0ehy089uyons17cmh", "questionCode": "Q0037.C12", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Arm Circumference(if applicable):", "placeholder": "cm/in", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242025792, "isContainer": false, "level": 0}, {"id": "0dxbydpfumh5sd6j3w3lo6s", "questionCode": "Q0037.C13", "controlName": "multiline-text-field", "displayText": "Notes", "description": "External catheter length:  \n", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242071376, "isContainer": false, "level": 0}, {"id": "6d4f3f8lh9vrta8tye16ql", "questionCode": "Q0037.C14", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Dressing Change Freq:  ", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242145924, "isContainer": false, "level": 0}, {"id": "otwmbeizone5exytiqkloj", "questionCode": "Q0037.C15", "controlName": "toggle", "displayText": "Toggle", "description": "Question Description", "labelName": "Infusion Therapy/Site Care", "placeholder": "Sterlie/Clean", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242487836, "isContainer": false, "level": 0}, {"id": "1ed3ccgdqmtmqy5zqf01do", "questionCode": "Q0037.C16", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Injection Cap Change Freq(Specify): ", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242517717, "isContainer": false, "level": 0}, {"id": "i3slkt7j29gb142yifwmrs", "questionCode": "Q0037.C17", "controlName": "radio-group", "displayText": "Radio", "description": "Performed By?", "labelName": "Infusion Therapy/Site Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "1", "label": "Patient"}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "2", "label": "RN"}, {"id": "voi9qz6aovvzxho37ufb", "value": "3", "label": "Caregiver"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242558136, "isContainer": false, "level": 0}, {"id": "0hr9cuyahi3sas40ncmpo4p", "questionCode": "Q0037.C18", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Performed By Other?", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care: Performed By ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242605838, "isContainer": false, "level": 0}, {"id": "9v4q41tr6bts3iubzcxqkk", "questionCode": "Q0037.C19", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Notes", "placeholder": "Please write your notes here.", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242670766, "isContainer": false, "level": 0}, {"id": "z5efysv18tmx04kzx0wl", "questionCode": "Q0037.D1", "controlName": "checkbox", "displayText": "Checkbox", "description": "Is IV therapy required for the patient?", "labelName": "Infusion Care Provided during visit ", "placeholder": "IV Care Provided", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743242763101, "isContainer": false, "level": 0}, {"id": "qyxqrgex0fbt0y3yg3atsg", "questionCode": "Q0037.D2", "controlName": "checkbox", "displayText": "Checkbox", "description": "Venipuncture for IV start", "labelName": "Infusion Care Provided during visit", "placeholder": "Will venipuncture be performed for the IV start?", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743243090368, "isContainer": false, "level": 0}, {"id": "0pxcse86q19g34y3gy7seui", "questionCode": "Q0037.D3", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Venipuncture for IV start", "placeholder": "Location", "labelName": "Infusion Care Provided during visit", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743243146276, "isContainer": false, "level": 0}, {"id": "ng2hcas80n0tldll6s9d2a", "questionCode": "Q0037.D4", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Venipuncture for IV start", "placeholder": "Device Used: ", "labelName": "Infusion Care Provided during visit", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743243265829, "isContainer": false, "level": 0}, {"id": "idshdvikayomnkvoszyr", "questionCode": "Q0037.D5", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Venipuncture for IV start", "placeholder": "Attempts:", "labelName": "Infusion Therapy/Site Care", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743243316880, "isContainer": false, "level": 0}, {"id": "kowtlvhyajrtxle4r3anki", "questionCode": "Q0037.D6", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What instructions should be given to the patient regarding the site dressing?\n", "placeholder": "Please write your notes here.", "labelName": "Site Dressing Change(describe): \t", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743243688795, "isContainer": false, "level": 0}, {"id": "b5tdcopadoaos8pndy8fn", "questionCode": "Q0037.D7", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is the process for changing the extension tubing or cap?", "placeholder": "Please write your notes here.", "labelName": "Extension Tubing/Cap Change", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743243905991, "isContainer": false, "level": 0}, {"id": "6896rjp9cdvtwzuqd80ko", "questionCode": "Q0037.D8", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Explain the procedure for flushing an infusion line:", "placeholder": "Please write your notes here.", "labelName": "Infusion Start/Flush", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743244039940, "isContainer": false, "level": 0}]}, {"container": {"id": "ttfknz1r1pz22ehvgkfy", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Care Management"}, "items": [{"id": "xjo0u1ky7tm1fxfbtlzx9k", "questionCode": "M2102", "controlName": "radio-group", "displayText": "Radio", "description": "Determine the ability and willingness of non-agency caregivers(such as family members, friends, or privately paid caregivers) to provide assistance for the following activities, if assistance is needed. Excludes all care by your agency staff.", "labelName": "Types and Sources of Assistance", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No assistance needed - patient is independent or does not have needs in this area"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Non-agency caregiver(s) currently provide assistance"}, {"id": "vngfkw9x3ye4ob21n723q", "value": "3", "label": "Non-agency caregiver(s) need training/ supportive services to provide assistance"}, {"id": "tawm1vufoqm1q0ezkct8x2", "value": "4", "label": "Non-agency caregiver(s) are not likely to provide assistance OR it is unclear if they will provide assistance"}, {"id": "ox2wrv56976d0svpt9r14", "value": "5", "label": "Assistance needed, but no non-agency caregiver(s) available"}], "containerId": "ttfknz1r1pz22ehvgkfy", "_tempId": 1743074613185, "isContainer": false, "level": 0}]}, {"container": {"id": "x8av5m7v9fl7x8uqvt069s", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Tab", "subHeading": "Goals / Interventions Summary"}, "items": [{"id": "xiimn9p9vqgmeos7stl1v", "questionCode": "Q0037.A", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Assessment Summary Note (Print Assessment Summary in Plan of Care: No):", "placeholder": "Please write your notes here.", "labelName": "Assessment Summary", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "x8av5m7v9fl7x8uqvt069s", "_tempId": 1743074700201, "isContainer": false, "level": 0}, {"id": "unmwclcv3aa17segpbon6", "questionCode": "Q0037.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Other Interventions:", "placeholder": "Please write your notes here.", "labelName": "Assessment Summary", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "x8av5m7v9fl7x8uqvt069s", "_tempId": 1743074790120, "isContainer": false, "level": 0}, {"id": "wa9q7u8dig0wtups38houn", "questionCode": "Q0037.C", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Goals:", "placeholder": "Please write your notes here.", "labelName": "Goals Summary", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "containerId": "x8av5m7v9fl7x8uqvt069s", "_tempId": 1743074818719, "isContainer": false, "level": 0}]}]