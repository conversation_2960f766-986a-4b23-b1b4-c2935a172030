const { v4: uuidv4 } = require("uuid");
const jwt = require("jsonwebtoken");
const logger = require("../lib/logger");

function extractTokenFromRequest(req) {
  if (
    req.headers.authorization &&
    req.headers.authorization.split(" ")[0] === "Bearer"
  ) {
    return req.headers.authorization.split(" ")[1];
  }

  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }

  if (req.query && req.query.token) {
    return req.query.token;
  }
  return null;
}

function extractUserIdFromToken(token) {
  try {
    const decoded = jwt.decode(token);
    if (!decoded) return null;

    // Handle different token versions
    const identity = decoded.v === 3 ? decoded.sub : decoded.id;
    return identity || null;
  } catch (error) {
    logger.error(`Logger Error extracting userId from token: ${error}`);
    // If token is invalid or expired, we'll just log without userId
    return null;
  }
}

function requestLogger(req, res, next) {
  req.apiId = req.headers["x-api-request-id"] || uuidv4();
  const startTime = Date.now();

  // Extract userId from JWT token if available
  const token = extractTokenFromRequest(req);
  const userId = token ? extractUserIdFromToken(token) : null;
  const userIdLog = userId ? ` | UserId: ${userId}` : "";

  logger.info(`--------------------------------------------`);
  logger.info(
    `Request started for Request ID: ${req.apiId} | ${req.method} | ${req.originalUrl}${userIdLog}`,
  );

  res.on("finish", () => {
    const endTime = Date.now();
    const duration = endTime - startTime;
    logger.info(
      `Request ended for Request ID: ${req.apiId} | ${req.method}${req.originalUrl} | ${duration}ms${userIdLog}`,
    );
  });

  next();
}

module.exports = requestLogger;
