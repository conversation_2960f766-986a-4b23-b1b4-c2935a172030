const { z } = require("zod");
const {
  createValidationError,
  createError,
} = require("../lib/v2-responses.js");

/**
 * Enhanced V2 Zod validation middleware that can validate multiple sources including headers
 * @param {Object} schemas - Object containing schemas for different sources
 * @param {z.ZodSchema} schemas.body - Schema for request body
 * @param {z.ZodSchema} schemas.query - Schema for query parameters
 * @param {z.ZodSchema} schemas.params - Schema for URL parameters
 * @param {z.ZodSchema} schemas.headers - Schema for request headers
 * @param {string} errorType - Type of error to return ('auth' for login, 'validation' for general)
 * @returns {Function} Express middleware function
 */
const v2ValidateMulti = (schemas, errorType = "validation") => {
  return (req, res, next) => {
    try {
      const errors = [];

      // Validate each source and collect errors
      if (schemas.body) {
        try {
          req.body = schemas.body.parse(req.body);
        } catch (error) {
          if (error instanceof z.ZodError) {
            errors.push(...error.errors);
          }
        }
      }

      if (schemas.query) {
        try {
          req.query = schemas.query.parse(req.query);
        } catch (error) {
          if (error instanceof z.ZodError) {
            errors.push(...error.errors);
          }
        }
      }

      if (schemas.params) {
        try {
          req.params = schemas.params.parse(req.params);
        } catch (error) {
          if (error instanceof z.ZodError) {
            errors.push(...error.errors);
          }
        }
      }

      if (schemas.headers) {
        try {
          req.headers = schemas.headers.parse(req.headers);
        } catch (error) {
          if (error instanceof z.ZodError) {
            errors.push(...error.errors);
          }
        }
      }

      if (errors.length > 0) {
        // For authentication endpoints, provide user-friendly but secure error messages
        if (errorType === "auth") {
          // Check if it's a missing field error
          const hasMissingFields = errors.some(
            (err) =>
              err.code === "invalid_type" && err.message.includes("required"),
          );
          if (hasMissingFields) {
            const errorResponse = createError(
              "Please enter your email and password",
              "credentials",
              "MISSING_FIELDS",
            );
            return res.status(400).json(errorResponse);
          }

          // For other auth validation errors, provide a generic but helpful message
          const errorResponse = createError(
            "Please check your email and password",
            "credentials",
            "AUTH_ERROR",
          );
          return res.status(401).json(errorResponse);
        }

        // For other endpoints, provide field-specific errors
        const errorResponse = createValidationError({ errors });
        return res.status(400).json(errorResponse);
      }

      next();
    } catch {
      // Handle unexpected errors with generic message
      const errorResponse = createError(
        "Request validation failed",
        "general",
        "VALIDATION_ERROR",
      );
      return res.status(400).json(errorResponse);
    }
  };
};

module.exports = { v2ValidateMulti };
