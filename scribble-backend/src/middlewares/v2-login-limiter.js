const rateLimit = require("express-rate-limit");
const { createError } = require("../lib/v2-responses.js");

const v2LoginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 login attempts per windowMs
  handler: (req, res) => {
    const errorResponse = createError(
      "Too many login attempts. Please wait 15 minutes before trying again.",
      "auth",
      "RATE_LIMIT_EXCEEDED",
    );
    return res.status(429).json(errorResponse);
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Key generator to use device ID if available, otherwise fall back to IP
  keyGenerator: (req) => {
    const deviceId = req.headers["x-device-id"];
    const ip = req.ip;
    return deviceId ? `${deviceId}:${ip}` : ip;
  },
  // Skip rate limiting for successful logins to avoid blocking legitimate users
  skipSuccessfulRequests: true,
  // Skip rate limiting for failed requests that are already handled (like invalid credentials)
  skipFailedRequests: false,
});

module.exports = v2LoginLimiter;
