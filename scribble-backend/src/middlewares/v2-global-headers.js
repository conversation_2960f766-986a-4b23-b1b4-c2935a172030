const { v2HeadersSchema } = require("../validations/headers.validation.js");
const { createError } = require("../lib/v2-responses.js");

/**
 * Global header validation middleware for all v2 API endpoints
 * Validates required headers and returns generic error response if any are missing
 */
const v2GlobalHeaders = (req, res, next) => {
  try {
    // Normalize headers to lowercase for case-insensitive matching
    const normalizedHeaders = {};
    Object.keys(req.headers).forEach((key) => {
      normalizedHeaders[key.toLowerCase()] = req.headers[key];
    });

    // Validate all required headers
    v2HeadersSchema.parse(normalizedHeaders);
    next();
  } catch {
    // Generic error response for missing/invalid headers
    const errorResponse = createError(
      "Please ensure all required headers are included in your request",
      "headers",
      "HEADER_VALIDATION_ERROR",
    );

    return res.status(400).json(errorResponse);
  }
};

module.exports = { v2GlobalHeaders };
