const rateLimit = require("express-rate-limit");
const { ErrorResponse } = require("../lib/responses.js");

const passwordResetEmailLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // limit each IP to 3 email requests per windowMs
  handler: (req, res) => {
    return res
      .status(429)
      .json(
        new ErrorResponse(
          "Please wait at least 5 minutes between password reset email requests.",
          req?.apiId,
          "TOO_MANY_REQUESTS",
        ),
      );
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

module.exports = passwordResetEmailLimiter;
