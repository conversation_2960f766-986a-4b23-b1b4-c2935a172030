[{"container": {"id": "fokk8854gzmagli617rrdg", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section A", "subHeading": "Clinical Records (Administrative)"}, "items": [{"id": "l5wh0h00nxr112ueucz0nxm", "questionCode": "A1005", "controlName": "checklist", "displayText": "Checklist", "description": "Are you of Hispanic, Latino/a, or Spanish origin? (Check all that apply) \n", "labelName": "Ethnicity ", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "1ge2rhlfy2za0aowecsbef", "value": "A", "label": "A - No, not of Hispanic, Latino/a, or Spanish origin\t"}, {"id": "dhomyb82dlsnyyt5v3d3w9", "value": "B", "label": "B - Yes, Mexican, Mexican American, Chicano/a"}, {"id": "wksvt74n157lmpqprhcxx", "value": "3", "label": "C - Yes, Puerto Rican"}, {"id": "w2ltyo1i28qprxqqm32zc", "value": "D", "label": "D - Yes, Cuban"}, {"id": "les4f9ke1onhkkab0b782g", "value": "5", "label": "E - Yes, another Hispanic, Latino, or Spanish origin"}, {"id": "af98uylubwbpyzm0ew86j", "value": "6", "label": "X - Patient unable to respond"}, {"id": "nk2wqaim9ls1b44vx70d78", "value": "7", "label": "Y - Patient declines to respond"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742724777996, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "nrav0uo8vypqq9z7nivta", "questionCode": "M0090", "controlName": "date-field", "displayText": "Date Picker", "description": "Date Assessment Completed", "labelName": "Date Assessment Completed", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "isDisplayCheatSheet": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742724951260, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "tgg02miqgogrdbnareqmr", "questionCode": "A1010", "controlName": "checklist", "displayText": "Checklist", "description": "What is your race?\n", "labelName": "Race (Check all that apply)", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "1ge2rhlfy2za0aowecsbef", "value": "A", "label": "A - White"}, {"id": "dhomyb82dlsnyyt5v3d3w9", "value": "B", "label": "B - Black or African American"}, {"id": "ox9ckghfejarg4eyyh2w6m", "value": "3", "label": "C - American Indian or Alaska Native"}, {"id": "38iv06vl3t9bzmjf5wlkhv", "value": "4", "label": "D - Asian Indian"}, {"id": "8azb4g1cr3nq8gzq77b2d", "value": "5", "label": "E - Chinese"}, {"id": "8xokk70bqdrlu74fj3hyi", "value": "6", "label": "F - Filipino"}, {"id": "d7ge5rldzii7omzsz77tyr", "value": "7", "label": "G - Japanese"}, {"id": "yjblbmuazndimr2igmmkzp", "value": "8", "label": "H - Korean"}, {"id": "8ew2egy17j4ercw2i0dtb5", "value": "9", "label": "I - Vietnamese"}, {"id": "bcca346z0ahq121f8m2s", "value": "10", "label": "J - Other Asian"}, {"id": "om7yvbr4cwy4q4ubjhtba", "value": "11", "label": "K - Native Hawaiian"}, {"id": "f1zb2kk49nfatnqvoqe3h", "value": "12", "label": "L - Guamanian or Chamorro"}, {"id": "4262ha0r95pkro19jdgs7", "value": "13", "label": "M - Samoan"}, {"id": "kqfd3f8d9pjmrksbpw4xh", "value": "14", "label": "N - Other Pacific Islander"}, {"id": "jp1ekkihh8idysgszayn4p", "value": "15", "label": "X - Patient unable to respond"}, {"id": "j8qav4w591pwxifapn09li", "value": "16", "label": "Y - Patient declines to respond"}, {"id": "zal668t13ez9wdacuit7", "value": "17", "label": "Z - None of the above"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742725433683, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "l6vbeuif3ddxwnidg38sbq", "questionCode": "A1110.A", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is your preferred language?", "placeholder": "Enter language", "labelName": "Language", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": true, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1744283816987, "isContainer": false, "level": 0}, {"id": "rwl8wgvpz4ijni8jev994n", "questionCode": "A1110.B", "controlName": "radio-group", "displayText": "Radio", "description": "Do you need a language interpreter?", "labelName": "Language", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "e6nd33y8tld9cvqa54yazv", "value": "0", "label": "0 - No"}, {"id": "zgp65oyv7lg50c2q2nc80u", "value": "1", "label": "1 - Yes"}, {"id": "z1br9ac5voe2mgjckjuyby", "value": "9", "label": "9 - Unable to determine"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742734119340, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "qkln3r2xtbz4e0pik28jo", "questionCode": "A1250", "controlName": "checklist", "displayText": "Checklist", "description": "Has lack of transportation kept you from medical appointments, meetings, work, or from getting things needed for daily living?", "labelName": "Transportation", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "qa1vcly8b3cnoimu9sky", "value": "A", "label": "A - Yes, it has kept me from medical appointments or from getting my medications"}, {"id": "3f3rxud20saiibnc2s1j6", "value": "B", "label": "B - Yes, it has kept me from non-medical meetings, appointments, work, or from getting things that I need"}, {"id": "3em5i3y0s4cz0057bf7cm", "value": "3", "label": "C - No"}, {"id": "yrz69of72chzb3od9vi49", "value": "4", "label": "X - Patient unable to respond"}, {"id": "c0qhik2hb72htixsrlp5k", "value": "5", "label": "Y - Patient declines to respond"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742731745118, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "6lzcvjhneb9pp5it2zb9m", "questionCode": "M0080", "controlName": "radio-group", "displayText": "Radio", "description": "What is the discipline of the person completing the assessment?", "labelName": "Discipline of Person Completing Assessment", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "1 - RN"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "2 - <PERSON>"}, {"id": "jaej8qfrs3kqq908zih5i", "value": "3", "label": "3 - SLP/ST"}, {"id": "uzldh8pgvk73yv7wjt8fe", "value": "4", "label": "4 - OT"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742796183956, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "t6b8z5s7run1fnfgtj<PERSON><PERSON>", "questionCode": "M0100", "controlName": "radio-group", "displayText": "Radio", "description": "What is the reason for completing this assessment?", "labelName": "This Assessment is Currently Being Completed for the Following Reason", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "1 - Start of Care - further visits planned"}, {"id": "5kk718pbti69wey254kocd", "value": "3", "label": "3 - Resumption of care (after inpatient stay)"}, {"id": "6mddm8hq31vtm2xj3e9jl", "value": "4", "label": "4 - Recertification (follow-up) reassessment "}, {"id": "ht655ooy2pre7du5hzh7h", "value": "5", "label": "5 - Other follow-up"}, {"id": "cj06fc5ofuoi4t2xz7qzc", "value": "6", "label": "6 - Transferred to an inpatient facility - patient not discharged from agency"}, {"id": "160x122cwfeicoy6kpkjr", "value": "7", "label": "7 - Transferred to an inpatient facility - patient discharged from agency"}, {"id": "k5ylrgazdhv4crab1usm", "value": "8", "label": "8 - Death at home"}, {"id": "e4ndtmurwcr25w49tois1y", "value": "9", "label": "9 - <PERSON>harge from agency"}], "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742796306888, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "q60wciyhagjwp9u4ksh8nk", "questionCode": "M0102.A", "controlName": "date-field", "displayText": "Date Picker", "description": "What date did the physician indicate as the specific start of care (resumption of care) when the patient was referred for home health services?", "labelName": "Date of Physician-ordered Start of Care (Resumption of Care)", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "isDisplayCheatSheet": false, "containerId": "fokk8854gzmagli617rrdg", "_tempId": 1742796457386, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "kfghcvq1gpofl3mi7zw7b", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section B", "subHeading": "Patient History & Diagnosis"}, "items": [{"id": "hyfu675xadjwv9wo90cn6c", "questionCode": "M1000.A", "controlName": "checklist", "displayText": "Checklist", "description": "In the past two weeks, were you discharged from any inpatient facility?", "labelName": "Discharge Facility (Past 14 Days)", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "1 - Long-term nursing facility (NF)"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "2 - Skilled nursing facility (SNF/TCU)"}, {"id": "96wxobyvi0hu3gj0smnwep", "value": "3", "label": "3 - Short-stay acute hospital (IPPS)"}, {"id": "5u0f4mmi9uxk6p6bpkfop", "value": "4", "label": "4 - Long-term care hospital (LTCH)"}, {"id": "70iated5qlvjtvqc9ubqxe", "value": "5", "label": "5 - Inpatient rehabilitation hospital or unit (IRF)"}, {"id": "0et0e290z5we0hwgh65ha73c", "value": "6", "label": "6 - Psychiatric hospital or unit"}, {"id": "jtsao6spx7dnz8lumjlwnd", "value": "7", "label": "NA <PERSON> <PERSON><PERSON> was not discharged from an inpatient facility ➔ Skip to B0200, Hearing at SOC,Skip to B1300, Health Literacy at ROC"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742797828985, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "amd74x453inibwef9lm718", "questionCode": "M1005.A", "controlName": "date-field", "displayText": "Date Picker", "description": "What is the Inpatient Discharge Date (most recent)?\n", "labelName": "Inpatient Discharge Date (most recent)", "itemType": "control", "icon": {"displayName": "Calendar"}, "required": false, "isDisplayCheatSheet": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742798107128, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "4fqlp2n57fasp2n3o99fw", "questionCode": "M1005.B", "controlName": "checkbox", "displayText": "Checkbox", "description": "Inpatient Discharge Date (most recent): UK - Unknown or Not Available", "labelName": "Inpatient Discharge Date", "placeholder": "UK - Unknown or Not Available", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1744441697124, "isContainer": false, "level": 0}, {"id": "juwu1q86w5f57r250vnso", "questionCode": "M1028", "controlName": "checklist", "displayText": "Checklist", "description": "Do you have diabetes, or any issues with blood flow in your legs or feet? Are you being treated for any other medical conditions?\n", "labelName": "Active Diagnoses", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "1 - Peripheral Vascular Disease (PVD) or Peripheral Arterial Disease (PAD)"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "2 - <PERSON><PERSON><PERSON> (DM)"}, {"id": "5xwv1j0ifbcjam2oxpjztg", "value": "3", "label": "3 - None of the above"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799017215, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "bxnekybl5vardfdb2gypw", "questionCode": "M1033.A", "controlName": "checklist", "displayText": "Checklist", "description": "Which of the following signs or symptoms characterize this patient as at risk for hospitalization?", "labelName": "Risk of Hospitalization", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "0tys2wbkyikd9pytk8q7cx9", "value": "1", "label": "1 - History of falls (2 or more falls - or any fall with an injury - in the past 12 months)"}, {"id": "76vxigu2vsvat366qhsqx", "value": "2", "label": "2 - Unintentional weight loss of a total of 10 pounds or more in past 12 months"}, {"id": "0orfb57y4iuq52yugpfs489", "value": "3", "label": "3 - Multiple hospitalizations (2 or more) in the past 6 months"}, {"id": "vc8e0fktrwo0zuijgptg", "value": "4", "label": "4 - Multiple emergency department visits (2 or more) in the past 6 months"}, {"id": "br9drr8dy1w4lcxfjdkk2r", "value": "5", "label": "5 - Decline in mental, emotional, or behavioral status in the past 3 months"}, {"id": "szs52mq2ltecdnskiaoi", "value": "6", "label": "6 - Reported or observed history of difficulty complying with any medical instructions (for example, medications, diet, exercise) in the past 3 months"}, {"id": "tgvxafly845g2jfjjwnaf", "value": "7", "label": "7 - Currently taking 5 or more medications"}, {"id": "ykz7tlpzdhg292z267ul4", "value": "8", "label": "8 - Currently reports exhaustion"}, {"id": "xxq1iqdwyplw9mpo2lxfe8", "value": "9", "label": "9 - Other risk(s) not listed in 1-8"}, {"id": "wjq8xu0pl2kp4fhbalzph", "value": "10", "label": "10 - None of the above"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799438976, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "98k8cv9am7bphdbmkg2za", "questionCode": "M1033.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What are the other risks of hospitalization?", "placeholder": "9 - Other risk(s) not listed in 1-8", "labelName": "Risk of Hospitalization: Others", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799684706, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "e6saxystofv5jq2xolb1nf", "questionCode": "J0510", "controlName": "radio-group", "displayText": "Radio", "description": "Over the past 5 days, how much of the time has pain made it hard for you to sleep at night?", "labelName": "Pain Effect on Sleep", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "0", "label": "0 - Does not apply – I have not had any pain or hurting in the past 5 days ➔ Skip to M1400, Short of Breath at SOC/ROC; Skip to J1800, Any Falls Since SOC/ROC at DC"}, {"id": "5kk718pbti69wey254kocd", "value": "1", "label": "1 - Rarely or not at all"}, {"id": "yb8oxb88q3o4fpqm9lp8fe", "value": "2", "label": "2 - Occasionally"}, {"id": "5t06nv1bkej5pdgfa0ehl5", "value": "4", "label": "3 - Frequently"}, {"id": "jf0vhqs8swngcjt2gkbr1m", "value": "4", "label": "4 - Almost constantly"}, {"id": "of696fs8vwohsqniox6nqf", "value": "8", "label": "8 - Unable to answer"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799803788, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "5qod3<PERSON><PERSON>c4c3tn3hkjc8", "questionCode": "J0520", "controlName": "radio-group", "displayText": "Radio", "description": "Over the past 5 days, how often have you limited your participation in rehab therapy sessions due to pain?", "labelName": "Pain Interference with Therapy Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "0", "label": "0 - Does not apply – I have not received rehabilitation therapy in the past 5 days"}, {"id": "5kk718pbti69wey254kocd", "value": "1", "label": "1 - Rarely or not at all"}, {"id": "e8krwvwrdp8ryo3tz4vcs", "value": "2", "label": "2 - Occasionally"}, {"id": "0m1bvwmr4domwp1yp5h3ri", "value": "4", "label": "3 - Frequently"}, {"id": "tw26cod6h9q45t2lr2dqrc", "value": "4", "label": "4 - Almost constantly"}, {"id": "kjay4sbdoxexn508cht52a", "value": "8", "label": "8 - Unable to answer"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742799908170, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "pvgqn4e5qtyc2dpq1c6v", "questionCode": "J0530", "controlName": "radio-group", "displayText": "Radio", "description": "Over the past 5 days, how often you have limited your day-to-day activities (excluding rehab therapy session) because of pain?", "labelName": "Pain Interference with Day-to-Day Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "hihlekfguo0fa9a1dr3as", "value": "1", "label": "1 - Rarely or not at all"}, {"id": "5kk718pbti69wey254kocd", "value": "2", "label": "2 - Occasionally"}, {"id": "sz4bgvol1aklozn4ccrfh", "value": "3", "label": "3 - Frequently"}, {"id": "wdspsawgs8wg6uw3fdio", "value": "4", "label": "4 - Almost constantly"}, {"id": "cgm9fdh2n5ds6q9gg0q2m", "value": "8", "label": "8 - Unable to answer"}], "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800037317, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "wks1pberbbd7pjbdv5g9c2", "questionCode": "M1060.A", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is the most recent height measurement?", "placeholder": "inches", "labelName": "Height and Weight – While measuring, if the number is X.1 – X.4 round down; X.5 or greater round up", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": true, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800284164, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "wlfnvwf7kzo1bv694dtxc", "questionCode": "M1060.B", "controlName": "multiline-text-field", "displayText": "Notes", "description": "What is your most recent weight within the last 30 days?", "placeholder": "pounds. measure weight consistently, according to standard agency practice (for example, in a.m. after voiding, before meal, with shoes off, etc.)", "labelName": "Height and Weight – While measuring, if the number is X.1 – X.4 round down; X.5 or greater round up", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": true, "containerId": "kfghcvq1gpofl3mi7zw7b", "_tempId": 1742800337530, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "5b8zcw6b9j6hnzndmjbgcf", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section C", "subHeading": "Living Arrangements"}, "items": [{"id": "67rsvowr50uze9kz0dde1", "questionCode": "M1100", "controlName": "radio-group", "displayText": "Radio", "description": "Can you tell me about your living situation – do you live alone, with others? And when would someone be available to help you if needed – all the time, during the day, at night, occasionally, or not at all?", "labelName": "Patient Living Situation: Availability of Assistance", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "elp1csjhn6hfl8mvfof5l5", "value": "1", "label": "Patient lives alone: Around the Clock"}, {"id": "hajuowheqhbz0g9v8720g", "value": "2", "label": "Patient lives alone: Regular daytime"}, {"id": "8wn62q4c0aauslspbwbb7", "value": "3", "label": "Patient lives alone: Regular nighttime"}, {"id": "agudzbkggfbp30azaikxe", "value": "4", "label": "Patient lives alone: Occasional/short-term assistance"}, {"id": "88m2px7w9su9xzi8n47o04", "value": "5", "label": "Patient lives alone: No assistance available"}, {"id": "zxg5rulxprfsvu91zn6mks", "value": "6", "label": "Patient lives with other person(s) in the home: Around the Clock"}, {"id": "8pvu81b8rrfxblxywbsb", "value": "7", "label": "Patient lives with other person(s) in the home: Regular daytime"}, {"id": "u9656uxdly8efkp87nt2i", "value": "8", "label": "Patient lives with other person(s) in the home: Regular nighttime"}, {"id": "cjoeeu1pedifh13hod491f", "value": "9", "label": "Patient lives with other person(s) in the home: Occasional/short-term assistance"}, {"id": "1wmzz6i5bitt2sq9pp4mm", "value": "10", "label": "Patient lives with other person(s) in the home: No assistance available"}, {"id": "wuu5tk8lz8mo58hao1afnr", "value": "11", "label": "Patient lives in congregate situation (for example, assisted living, residential care home): Around the Clock"}, {"id": "r7krz7zw6q13m4la33nu3i", "value": "12", "label": "Patient lives in congregate situation (for example, assisted living, residential care home): Regular daytime"}, {"id": "7okgnlfwilmvisvxpocfli", "value": "13", "label": "Patient lives in congregate situation (for example, assisted living, residential care home): Regular nighttime"}, {"id": "40aqvsha3tms3lpqhb2dwl", "value": "14", "label": "Patient lives in congregate situation (for example, assisted living, residential care home): Occasional/short-term assistance"}, {"id": "mb142fn5puyn7tfgmi8b8", "value": "15", "label": "Patient lives in congregate situation (for example, assisted living, residential care home): No assistance available"}], "containerId": "5b8zcw6b9j6hnzndmjbgcf", "_tempId": 1745919276596, "isContainer": false, "level": 0, "isDisplayCheatSheet": true}]}, {"container": {"id": "n2nnaaszf49p6h4ktinci9", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section D", "subHeading": "Sensory"}, "items": [{"id": "ez9mcz2tqw54j0i2s2bz6f", "questionCode": "B0200", "controlName": "radio-group", "displayText": "Radio", "description": "Do you use a hearing aid? How well can you hear during everyday conversations, say in-person or on the phone?", "labelName": "Hearing: Ability to hear (with hearing aid or hearing appliances if normally used)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "0", "label": "0. Adequate - no difficulty in normal conversation, social interaction, listening to TV"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "1", "label": "1. Minimal difficulty – difficulty in some environments (e.g., when person speaks softly, or setting is noisy)"}, {"id": "fedrdxfcjr3nek55vsskg", "value": "2", "label": "2. Moderate difficulty - speaker has to increase volume and speak distinctly"}, {"id": "6tdrqhj866hfb2n69bkf97", "value": "3", "label": "3. Highly impaired - absence of useful hearing"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742910353975, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "glfbi9ba6loun3s6xk2nx9", "questionCode": "B1000", "controlName": "radio-group", "displayText": "Radio", "description": "Do you use glasses? Can you see things up close, like reading a book or newspaper?\n", "labelName": "Vision: Ability to see in adequate light (with glasses or other visual appliances)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "0", "label": "0. Adequate - sees fine detail, such as regular print in newspapers/books"}, {"id": "7z1r3snvjzmwvvobfhp9k", "value": "1", "label": "1. Impaired – sees large print, but not regular print in newspapers/books"}, {"id": "l7gkfdysdsp1vcoqneol2o", "value": "2", "label": "2. Moderately impaired – limited vision; not able to see newspaper headlines but can identify objects"}, {"id": "xyfmjszm02jeudofo6jsc", "value": "3", "label": "3. Highly impaired - object identification in question, but eyes appear to follow objects"}, {"id": "w3wkpsf0gtfzgt68zyyq", "value": "4", "label": "4. Severely impaired -– no vision or sees only light, colors or shapes; eyes do not appear to follow objects"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742910432425, "index": 0, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "knt7uthp06ysuc4qh3xao", "questionCode": "B1300", "controlName": "radio-group", "displayText": "Radio", "description": "How often do you need to have someone help you when you read instructions, pamphlets, or other written material from your doctor or pharmacy?", "labelName": "Health Literacy (From Creative Commons ©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ed3pocmly9m55bcubvzh5k", "value": "0", "label": "0. Never"}, {"id": "h4fppj53dv4wp5lmggmhcg", "value": "1", "label": "1. Rarely"}, {"id": "y6tygk7kl7befmnmu2b3i", "value": "2", "label": "2. Sometimes"}, {"id": "gh3dpu5z0km0guxpu5su", "value": "3", "label": "3. Often"}, {"id": "eorplnl5a675d3y8x489e", "value": "4", "label": "4. Always"}, {"id": "grqujqi79la66y0d7tsyi", "value": "7", "label": "7. Patient declines to respond"}, {"id": "902b454866nk22l1spt3df", "value": "8", "label": "8. <PERSON><PERSON> unable to respond"}], "containerId": "n2nnaaszf49p6h4ktinci9", "_tempId": 1742910873051, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "y8kn1l24zooc68gky1shan", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section E", "subHeading": "Integumentary"}, "items": [{"id": "bt7ij5el6ie74j4tmittpt", "questionCode": "M1306", "controlName": "radio-group", "displayText": "Radio", "description": "Does this patient have at least one unhealed pressure ulcer/injury at Stage 2 or higher or designated as unstageable (excludes Stage 1 pressure injuries and all healed pressure ulcers/injuries)?", "labelName": "Patient Ulcer Assessment", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "rna72avvk5uwgybj2n33r", "value": "3", "label": "0 - No ➔ Skip to M1322, Current Number of Stage 1 Pressure Injuries"}, {"id": "gyt9qmijxcfyxkqkdjl46", "value": "3", "label": "1 - Yes"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981251758, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "yb0xsc9kzje4jcn5fmtaij", "questionCode": "M1311.A1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stage 2: Partial thickness loss of dermis presenting as a shallow open ulcer with red pink wound bed, without slough. May also present as an intact or open/ruptured blister. Number of Stage 2 pressure ulcers?", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981621738, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "lz8wunku0mqtsxk80yvd2p", "questionCode": "M1311.B1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stage 3: Full thickness tissue loss. Subcutaneous fat may be visible but bone, tendon, or muscle is not exposed. Slough may be present but does not obscure the depth of tissue loss. May include undermining and tunneling.\nNumber of Stage 3 pressure ulcers?", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981646867, "index": 1, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "d95u9bin73wqlo31kod67h", "questionCode": "M1311.C1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Stage 4: Full thickness tissue loss with exposed bone, tendon, or muscle. Slough or eschar may be present on some parts of the wound bed. Often includes undermining and tunneling. Number of Stage 4 pressure ulcers?", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742981718116, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "b5malcderwdber3xqcxxr", "questionCode": "M1311.D1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Unstageable: Non-removable dressing/device: Known but not stageable due to non-removable dressing/device.\nNumber of unstageable pressure ulcers due to non - removable dressing/device?", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982037592, "index": 3, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "11bxzl74c2ikaf70nl90ia5", "questionCode": "M1311.E1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Unstageable: Slough and/or eschar: Known but not stageable due to coverage of wound bed by slough and/or eschar\nNumber of unstageable pressure ulcers due to coverage of wound bed by slough and/or eschar?", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982071289, "index": 4, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "qca7q49m5k7hkbdssfnkk", "questionCode": "M1311.F1", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Unstageable: Deep tissue injury: Number of unstageable pressure injuries presenting as deep tissue injury?", "placeholder": "Enter Number", "labelName": "Current Number of Unhealed Pressure Ulcers/Injuries at Each Stage ", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982104028, "index": 5, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "uetwouiss8m7gl56oh7ka", "questionCode": "M1322", "controlName": "radio-group", "displayText": "Radio", "description": "Intact skin with non-blanchable redness of a localized area usually over a bony prominence. The area may be painful, firm, soft, warmer, or cooler as compared to adjacent tissue. Darkly pigmented skin may not have a visible blanching; in dark skin tones only it may appear with persistent blue or purple hues.", "labelName": "Patient Ulcer Assessment: Current Number of Stage 1 Pressure Injuries", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "0 - <PERSON>"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "1 - One"}, {"id": "8br4nnt09caqqvgogwjy", "value": "2", "label": "2 - Two"}, {"id": "i76baibnumglbyyp3pav3l", "value": "3", "label": "3 - Three"}, {"id": "cbkk14m4f9b9xh98quzvlq", "value": "4", "label": "4 - Four or more"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982274108, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "wq8so25jw1q6rdh41lmq", "questionCode": "M1324", "controlName": "radio-group", "displayText": "Radio", "description": "What is the current stage of the most severe unhealed pressure ulcer/injury that is clinically stageable?", "labelName": "Stage of Most Problematic Unhealed Pressure Ulcer/Injury that is Stageable: (Excludes pressure ulcer/injury that cannot be staged due to a non-removable dressing/device, coverage of wound bed by slough and/or eschar, or deep tissue injury)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "1 - Stage 1"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "2 - Stage 2"}, {"id": "usir9h7i0ieja76ppfdu8", "value": "3", "label": "3 - Stage 3"}, {"id": "g9d5vcqpwqnyjhl95rezgr", "value": "4", "label": "4 - Stage 4"}, {"id": "9rzcus4z2m5z2snx7g26h9", "value": "5", "label": "NA - Patient has no pressure ulcers/injuries or no stageable pressure ulcers/injuries"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982494165, "index": 7, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "ozwwu9a9xgr8mp4eqz30q", "questionCode": "M1330", "controlName": "radio-group", "displayText": "Radio", "description": "Does this patient have a Stasis Ulcer?", "labelName": "Patient Ulcer Assessment", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "0 - No ➔Skip to M1340, Surgical Wound"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "1 - Yes, patient has both observable and unobservable stasis ulcers"}, {"id": "3pejolsmxxpgll88zlokzv", "value": "2", "label": "2 - Yes, patient has observable stasis ulcers only"}, {"id": "5zy7ikt6m6diziotmn93o", "value": "3", "label": "3 - Yes, patient has unobservable stasis ulcers only (known but not observable due to non-removable dressing/device)➔Skip to M1340, Surgical Wound"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982604720, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "q42yc1mmhf80chqms842o8g", "questionCode": "M1332", "controlName": "radio-group", "displayText": "Radio", "description": "What is the current number of observable stasis ulcers?", "labelName": "Current Number of Stasis Ulcer(s) that are Observable", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "1 - One"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "2 - Two"}, {"id": "uz3t9gn7r6xmu5r5m19jj", "value": "3", "label": "3 - Three"}, {"id": "6ioza4vaqiflgh3f2kpd1", "value": "4", "label": "4 - Four or More"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1742982662719, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "aedoogsezjckm6vzmcd32k", "questionCode": "M1334", "controlName": "radio-group", "displayText": "Radio", "description": "What is the current condition of the most prominent stasis ulcer?", "labelName": "Status of Most Problematic Stasis Ulcer that is Observable", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "kmof84ywounklky8b5ag1i", "value": "1", "label": "1 - Fully granulating"}, {"id": "681z54axy5wrjtqa0o3eeg", "value": "2", "label": "2 - Early/partial granulating"}, {"id": "6cdof6x76nepyced2s59m", "value": "3", "label": "3 - <PERSON>"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1743591166992, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "rr5rd3bpdh6wxrc81xyxq", "questionCode": "M1340", "controlName": "radio-group", "displayText": "Radio", "description": "Does this patient have a Surgical Wound?", "labelName": "Surgical Wound", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "1jag30ujkzmvlaygoqqvv", "value": "4", "label": "0 - No ➔Skip to N0415, High-Risk Drug Classes: Use and Indication"}, {"id": "6h9qggve6xdnkdji43ww9", "value": "4", "label": "1 - Yes, patient has at least one observable surgical wound"}, {"id": "nbkraedj1yl584f4fqyk", "value": "4", "label": "2 - Surgical wound known but not observable due to non-removable dressing/device ➔ Skip to N0415, High-Risk Drug Classes: Use and Indication"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1743591519995, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "7qc8v4kg6iwfsi140bft4p", "questionCode": "M1342", "controlName": "radio-group", "displayText": "Radio", "description": "What is the status of the most problematic stasis ulcer that is observable?", "labelName": "Status of Most Problematic Surgical Wound that is Observable", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "kmof84ywounklky8b5ag1i", "value": "0", "label": "0 - Newly epithelialized"}, {"id": "681z54axy5wrjtqa0o3eeg", "value": "1", "label": "1 - Fully granulating"}, {"id": "lvlun1r976i6cke99yqung", "value": "3", "label": "2 - Early/partial granulating"}, {"id": "cclj9a8m50mkqcxr9bwpfg", "value": "4", "label": "3 - <PERSON>"}], "containerId": "y8kn1l24zooc68gky1shan", "_tempId": 1743591638318, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "mnaqo7fnoe3wbzks3eetq", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section F", "subHeading": "Respiratory"}, "items": [{"id": "i3ex8ijj3qfcu3km3j33gv", "questionCode": "M1400.A", "controlName": "radio-group", "displayText": "Radio", "description": "Do you experience shortness of breath? If so, when does it occur—during light activities, such as eating, when walking short distances (like 20 feet), or only while resting?", "labelName": "Shortness of Breath", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "0", "label": "0 - Patient is not short of breath"}, {"id": "snldb0lo19an87bhug1ar", "value": "1", "label": "1 - When walking more than 20 feet, climbing stairs"}, {"id": "5lkb6ixkzfu21aqvgh63jh", "value": "2", "label": "2 - With moderate exertion (For example: while dressing, using commode or bedpan, walking distances less than 20 feet)"}, {"id": "fdxnk0kwmbqfcpqj35d3lo", "value": "3", "label": "3 - With minimal exertion (For example: while eating, talking, or performing other ADLs) or with agitation"}, {"id": "uy656qe9s6i8hjdivjln4", "value": "4", "label": "4 - At rest (during day or night)"}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742991166855, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "oozlx894cmf27y69h0u5u", "questionCode": "M1400.B", "controlName": "radio-group", "displayText": "Radio", "description": "Has the patient's dyspnea been assessed or reported?", "labelName": "Shortness of Breath", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "161bjkhpjlx7zxc79sop83", "value": "1", "label": "Assessed"}, {"id": "snldb0lo19an87bhug1ar", "value": "2", "label": "Reported"}], "containerId": "mnaqo7fnoe3wbzks3eetq", "_tempId": 1742991629603, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "anr5e1slt4tcjn160zuack", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section G", "subHeading": "Elimination"}, "items": [{"id": "uuq0417mx2nbrukq8bwd", "questionCode": "M1600", "controlName": "radio-group", "displayText": "Radio", "description": "Over the past two weeks, have you received any treatment, such as antibiotics, for a urinary tract infection, bladder infection, or any other type of urine infection?", "labelName": "Urinary Tract Infection", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Yes"}, {"id": "6fk7575o5lrokh4hrqx7ta", "value": "2", "label": "NA - Patient on prophylactic treatment"}, {"id": "8g0vv05ozq8oqxoe6be9fg", "value": "5", "label": "UK - Unknown [Omit “UK” option on DC]"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743038992783, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "luzalx0oemdi8h3fclwxv", "questionCode": "M1610", "controlName": "radio-group", "displayText": "Radio", "description": "Do you have any accidents or leaking with your urine?", "labelName": "Urinary Incontinence or Urinary Catheter Presence:", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - No incontinence or catheter (includes anuria or ostomy for urinary drainage) "}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Patient is incontinent"}, {"id": "8znn92jn3i1ocbo413ajd", "value": "2", "label": "2 - Patient requires a urinary catheter (specifically: external, indwelling, intermittent, suprapubic)"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743039050113, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "ltjpe6b9nph4dzrwarsbv", "questionCode": "M1620", "controlName": "radio-group", "displayText": "Radio", "description": "How frequently do you experience loss of bowel control? Would you say never, less than once a week, between one and six times a week, or seven or more times a week?", "labelName": "Bowel Incontinence Frequency", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Very rarely or never has bowel incontinence"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Less than once weekly"}, {"id": "ohsrl3pjbji9nk51fpge6a", "value": "2", "label": "2 - One to three times weekly"}, {"id": "vrcgvcdvufobxez4lw5ge", "value": "3", "label": "3 - Four to six times weekly"}, {"id": "gqn6jqvdtutkznommljx1j", "value": "4", "label": "4 - On a daily basis"}, {"id": "3flv9xnirk4w5j3oeglxui", "value": "5", "label": "5 - More often than once daily"}, {"id": "nq7ihjaf5vtf4i95hfsiqh", "value": "7", "label": "NA - Pat<PERSON> has ostomy for bowel elimination"}, {"id": "5hforz0ilt5mnzsy3ewtwb", "value": "8", "label": "UK - Unknown [Omit “UK” option on FU, DC ]"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743039258804, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "fpne7osfxriddz9memk7fu", "questionCode": "M1630", "controlName": "radio-group", "displayText": "Radio", "description": "Do you have an ostomy or bag for bowel movements?", "labelName": "O<PERSON><PERSON> for Bowel Elimination", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "x4yyg09l5v0lix0u7m1ktr", "value": "4", "label": "0 - <PERSON><PERSON> does not have an ostomy for bowel elimination"}, {"id": "ug1u2gm6l4t8m6b73ce3q", "value": "5", "label": "1 - <PERSON><PERSON>'s ostomy was not related to an inpatient stay and did not necessitate change in medical or treatment regimen"}, {"id": "4wvneebj23ipwopw0srsqh", "value": "6", "label": "2 - The ostomy was related to an inpatient stay or did necessitate change in medical or treatment regimen"}], "containerId": "anr5e1slt4tcjn160zuack", "_tempId": 1743060133900, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "7yafoql2y54z3a7rvkgsbc", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section H", "subHeading": "Neuro/Emotional/Behavioral"}, "items": [{"id": "d13q83uktc7zhd0tq9bgr9", "questionCode": "C0100", "controlName": "radio-group", "displayText": "Radio", "description": "Should a brief interview for Mental Status (C0200-C0500) be conducted?", "labelName": "Interview for Mental Status", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No (patient is rarely/never understood) ➔ Skip to C1310, Signs and Symptoms of Delirium (from CAM ©)"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes ➔ Continue to C0200, Repetition of Three Words"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743063758582, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "za4etnxaqhe4tsbld5galp", "questionCode": "C0200", "controlName": "radio-group", "displayText": "Radio", "description": "I am going to say three words for you to remember. Please repeat the words after I have said all three. The words are: sock, blue, and bed. Now tell me the three words.", "labelName": "Repetition of Three Words", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "0", "label": "0.  None"}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "1", "label": "1.  One"}, {"id": "kc8s74rrf6p8vvxhy6emy2", "value": "2", "label": "2.  Two"}, {"id": "9l9jo1qwq4g8d36qvpa7b", "value": "3", "label": "3.  Three"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743236755876, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "ba8eshkyr1vgc4y2dnfitu", "questionCode": "C0300.A", "controlName": "radio-group", "displayText": "Radio", "description": "Please tell me what year it is right now.", "labelName": "Temporal Orientation (Orientation to year, month, and day)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "qrc6xui9brdlwt6edje2", "value": "5", "label": "0.  Missed by more than 5 years or no answer"}, {"id": "fybd5iq8tvtoqjlwu2ok5s", "value": "6", "label": "1.  Missed by 2-5 years"}, {"id": "x73sfr92zpi3a320pocva5", "value": "7", "label": "2.  Missed by 1 year"}, {"id": "ohhk8hqvxgqnhnexg36hgq", "value": "8", "label": "3.  <PERSON><PERSON><PERSON>"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743063900213, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "pxp63uyzzjn0ih76sb8jonl", "questionCode": "C0300.B", "controlName": "radio-group", "displayText": "Radio", "description": "What month are we in right now?", "labelName": "Temporal Orientation (Orientation to year, month, and day)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Missed by more than 1 month or no answer"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Missed by 6 days to 1 month"}, {"id": "mwqryyi6vp7qjjgyusnxq", "value": "3", "label": "2.  Accurate within 5 days"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743063956629, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "o1e7f5v3o2jzewnnbhfso", "questionCode": "C0300.C", "controlName": "radio-group", "displayText": "Radio", "description": "What day of the week is today?", "labelName": "Temporal Orientation", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Incorrect or no answer"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  <PERSON><PERSON><PERSON>"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064031233, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "xi0jm66fn2r7ync8jtj03a", "questionCode": "C0400.A", "controlName": "radio-group", "displayText": "Radio", "description": "Let's go back to an earlier question. What were those three words that I asked you to repeat?\nWas the patient able to recall the word sock?", "labelName": "Recall", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No <PERSON> could not recall"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes, after cueing (\"something to wear\")"}, {"id": "qonnax3y6akebhe2fswua", "value": "3", "label": "2.  Yes, no cue required"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064067445, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "3fd4ytde6kvqok3x0kfyif", "questionCode": "C0400.B", "controlName": "radio-group", "displayText": "Radio", "description": "Was the patient able to recall the color blue?", "labelName": "Recall", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No <PERSON> could not recall"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes, after cueing (\"a color\")"}, {"id": "r3wdz95ptsqzony4rcali8", "value": "3", "label": "2.  Yes, no cue required"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064168458, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "04ni2ixio7ed4sumdr8z4r2", "questionCode": "C0400.C", "controlName": "radio-group", "displayText": "Radio", "description": "Was the patient able to recall bed?", "labelName": "Recall", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No could not recall"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes, after cueing (\"a piece of furniture\")"}, {"id": "2d7ishr8844dq0dsr597l", "value": "3", "label": "2.  Yes, no cue required"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064267641, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "jhw7henivd8g53wlb2mvu", "questionCode": "C0500", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Add scores for questions C0200-C0400 and fill in total score (00-15)\nEnter 99 if the patient was unable to complete the interview", "placeholder": "Enter Code", "labelName": "BIMS Summary Score", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064328626, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "jrzcki5ttsqxrf51kbaw", "questionCode": "C1310.A", "controlName": "radio-group", "displayText": "Radio", "description": "Acute Onset of Mental Status Change - Is there evidence of an acute change in mental status from the patient's baseline?", "labelName": "Signs and Symptoms of Delirium (from CAM©) ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064399046, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "pv1lioi2an8s3vzenez9bm", "questionCode": "C1310.B", "controlName": "radio-group", "displayText": "Radio", "description": "Inattention – Did the patient have difficulty focusing attention, for example, being easily distracted or having difficulty keeping track of what was being said?", "labelName": "Signs and Symptoms of Delirium (from CAM©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Behavior not present"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Behavior continuously present, does not fluctuate"}, {"id": "izjjy3x0j7q8cs4r9h1gyp", "value": "2", "label": "2.  Behavior present, fluctuates"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064482355, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "kjri64mzfcsd0s00n4y2ap", "questionCode": "C1310.C", "controlName": "radio-group", "displayText": "Radio", "description": "Disorganized thinking– Was the patient's thinking disorganized or incoherent (rambling or irrelevant conversation, unclear or illogical flow of ideas, or unpredictable switching from subject to subject)?", "labelName": "Signs and Symptoms of Delirium (from CAM©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Behavior not present"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Behavior continuously present, does not fluctuate"}, {"id": "r9ndx0m7fvel4hn8lkb3gb", "value": "2", "label": "2.  Behavior present, fluctuates"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064536037, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "hira13a5zh4ftfvhlr5rpk", "questionCode": "C1310.D", "controlName": "radio-group", "displayText": "Radio", "description": "Altered level of consciousness: – Did the patient have altered level of consciousness, as indicated by any of the following criteria?\nvigilant – startled easily to any sound or touch\nlethargic – repeatedly dozed off when being asked questions, but responded to voice or touch\nstuporous – very difficult to arouse and keep aroused for the interview\ncomatose – could not be aroused", "labelName": "Signs and Symptoms of Delirium (from CAM©)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Behavior not present"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Behavior continuously present, does not fluctuate"}, {"id": "hgp8of9zbpal4wz1s7jmm", "value": "2", "label": "2.  Behavior present, fluctuates"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743064603604, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "7wtwxwyt6qw920glaesuuw", "questionCode": "D0150.A1", "controlName": "radio-group", "displayText": "Radio", "description": "In the last two weeks, have you experienced little interest or pleasure in doing things? How often?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "twmx32d11apv3t3ww9yy6", "value": "3", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065047370, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "rxrxj6sz72k9hdaux8qdkj", "questionCode": "D0150.A2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Little interest or pleasure in doing things", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "rj31cmpwttv8gmxk693fh", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "5e3ufoabzxcuoltbrhud8", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065273248, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "xm50vv290fc8flviik769", "questionCode": "D0150.B1", "controlName": "radio-group", "displayText": "Radio", "description": "In the last two weeks, have you felt sad, down, or hopeless? How often?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "4ljf3d2uz5o871oxro1lru", "value": "9", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065388751, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "g4mmswl90jocghdz16li5g", "questionCode": "D0150.B2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Feeling down, depressed, or hopeless", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "vpraaroq5mrsthtn541oa", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "ha80sm55vpim06cdglicid", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065516008, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "pbt42x72w6go6gtcclgk3b", "questionCode": "D0150.C1", "controlName": "radio-group", "displayText": "Radio", "description": "Have you experienced any changes in your sleep patterns over the last two weeks? Have you had trouble falling asleep, staying asleep, or sleeping too much? How often has this bothered you?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "ut3xwg56p4qwfb80d9tdf9", "value": "9", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065586606, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "fmajdtcnkvhlyosl7ginw", "questionCode": "D0150.C2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Trouble falling or staying asleep, or sleeping too much", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "9i3ulsot39kjmdqsy4kkn", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "5vd0j2cfa7ujha443y9c6o", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065640847, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "k0646jtq2utyacnwhw22m", "questionCode": "D0150.D1", "controlName": "radio-group", "displayText": "Radio", "description": "How would you describe your energy levels these past two weeks? Have you been feeling unusually tired or lacking energy? How often have you felt this way?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "7fnuxpotw2axhyb6q6babm", "value": "9", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065851254, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "r8ih8jce00ed50vedixslq", "questionCode": "D0150.D2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Feeling tired or having little energy", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "jmt16dtgdyinmrpajyhct", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "igbjdqn7f9gqtl8f6t55r", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743065965260, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "deb4xt8onfpg6rd3we6vm4", "questionCode": "D0150.E1", "controlName": "radio-group", "displayText": "Radio", "description": "In the last two weeks, have you experienced poor appetite or overeating? How often has this bothered you?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "e5fgi5qtl27l4vdhlsq37n", "value": "9", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066026105, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "tjhncy3y6ec2e9ld4tmjha", "questionCode": "D0150.E2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Poor appetite or overeating", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "nlbpd0ftoju11nvg0zwu", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "p6am3vumyedzszcgkd40e", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066072868, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "856xuxj1jvrxtrnaqbi5p", "questionCode": "D0150.F1", "controlName": "radio-group", "displayText": "Radio", "description": "In the last two weeks, have you experienced feeling bad about yourself or having thoughts that you are failing or letting your family down? How often has this bothered you?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "tkf4bt8t7h4lex8clq2tt", "value": "9", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066144497, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "ttocuqs1tqu1w524rmono", "questionCode": "D0150.F2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Feeling bad about yourself – or that you are a failure or have let yourself or your family down", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "fhfep1jjtgjbv2snq30rts", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "1e2b7xxkpljdtf8cibte0t", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066193450, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "9yt0wusw3x7llebaid5czp", "questionCode": "D0150.G1", "controlName": "radio-group", "displayText": "Radio", "description": "In the last two weeks, have you had trouble concentrating on things, such as reading the newspaper or watching television? How often?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "8xj6rpg8zvrzeozqa3xiv", "value": "9", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066256530, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "jnfty3nurgd262hrslo0c", "questionCode": "D0150.G2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Trouble concentrating on things, such as reading the newspaper or watching television", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "pfbbf2v68xgr7e8vz3t5", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "nb3nf23vlmhdb93nahi645", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066303013, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "q0mh542384qn9h7f10quu", "questionCode": "D0150.H1", "controlName": "radio-group", "displayText": "Radio", "description": "In the last two weeks, have you had any problems with moving or speaking so slowly that other people could have noticed? How often have you been bothered by feeling so fidgety or restless that you move around a lot more than usual?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "z3rsvd9w9v960tmkvo5df", "value": "9", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066375727, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "8ubz7ygm1uof4wsxeweiku", "questionCode": "D0150.H2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Moving or speaking so slowly that other people could have noticed. Or the opposite – being so fidgety or restless that you have been moving around a lot more than usual\n", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "27jfqix0cfqvrqbbtzgb0h", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "q20y7hcrdltvekuplrin", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066423820, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "4v2qriaf2klidnluo000w9", "questionCode": "D0150.I1", "controlName": "radio-group", "displayText": "Radio", "description": "Over the last two weeks, have you had any thoughts that you might be better off dead, or thoughts about hurting yourself in any way?", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Yes"}, {"id": "vq2qnms1g9jgjkez3kks1t", "value": "3", "label": "9.  No response"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066661599, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "p7qhhs3a797jakqxha7o9", "questionCode": "D0150.I2", "controlName": "radio-group", "displayText": "Radio", "description": "Symptom Frequency: Thoughts that you would be better off dead, or of hurting yourself in some way", "labelName": "Patient Mood Interview (PHQ-2 to 9)", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never or 1 day"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  2-6 days (several days)"}, {"id": "r19ii4fpb0de6q4973v46", "value": "2", "label": "2.  7-11 days (half or more of the days)"}, {"id": "8io5dzf3z2dvt4e7b1iu0d", "value": "3", "label": "3.  12-14 days (nearly every day)"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066710127, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "o1f63o159rldo17hoycig", "questionCode": "D0160", "controlName": "multiline-text-field", "displayText": "Notes", "description": "Please provide the total score for all frequency responses listed in Column 2, Symptom Frequency. The total score should be calculated based on the responses and must fall within the range of 00 to 27. If the interview cannot be completed due to three or more required items being blank, please enter 99.", "placeholder": "Total score must be between 00 and 27. Enter 99 if unable to complete interview (i.e., Symptom Frequency is blank for 3 or more required items)", "labelName": "Total Severity Score", "rows": 4, "itemType": "control", "icon": {"displayName": "Type"}, "required": false, "isDisplayCheatSheet": false, "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066775273, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "6r283c34asy2uel12xj6", "questionCode": "D0700", "controlName": "radio-group", "displayText": "Radio", "description": "How often do you feel lonely or isolated from those around you?", "labelName": "Social Isolation", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0.  Never"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1.  Rarely"}, {"id": "8l11iklomsf51rfxcul1i", "value": "2", "label": "2.  Sometimes"}, {"id": "e44z775xllhe0f5edal1h", "value": "3", "label": "3.  Often"}, {"id": "z3rk2854p6me955nn7cyik", "value": "4", "label": "4.  Always"}, {"id": "k94mlj2e4d94py8qdwyg", "value": "7", "label": "7.  Patient declines to respond"}, {"id": "7wop45s69d58gkkurz8vfg", "value": "8", "label": "8.  <PERSON><PERSON> unable to respond"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743066855680, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "mg3vx8rqfgq3yhdu8hmm1g", "questionCode": "M1700", "controlName": "radio-group", "displayText": "Radio", "description": "How would you describe your level of alertness and ability to understand things right now? Would you say you are alert and oriented, or sometimes confused, or confusion is frequent?", "labelName": "Cognitive Functioning", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - <PERSON><PERSON>/<PERSON>ed, able to focus and shift attention, comprehends and recalls task directions independently."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Requires prompting (cuing, repetition, reminders) only under stressful or unfamiliar conditions."}, {"id": "rqimm72ic6g58zrhl533e", "value": "2", "label": "2 - Requires assistance and some direction in specific situations (For example: on all tasks involving shifting of attention), or consistently requires low stimulus environment due to distractibility."}, {"id": "lcdnl4ql4so3xzfxqwcv13", "value": "3", "label": "3 - Requires considerable assistance in routine situations. Is not alert and oriented or is unable to shift attention and recall directions more than half the time."}, {"id": "dqn37q0datcbofdsbyx8zw", "value": "4", "label": "4 - Totally dependent due to disturbances such as constant disorientation, coma, persistent vegetative state, or delirium."}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067521203, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "x9atp9u3d8de4i8gxixi88", "questionCode": "M1710", "controlName": "radio-group", "displayText": "Radio", "description": "Reported or Observed Within the Last 14 Days?", "labelName": "When Confused", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Never"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - In new or complex situations only"}, {"id": "goe3ckffsh3bshvm25z7h", "value": "2", "label": "2 - On awakening or at night only"}, {"id": "axskuru7bnfka41dtdyze", "value": "3", "label": "3 - During the day and evening, but not constantly"}, {"id": "2kzjon8c5ksmghufa231u", "value": "4", "label": "4 - Constantly"}, {"id": "0uz41zn61w6nom8k26ls3lm", "value": "6", "label": "NA - Patient non-responsive"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067579327, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "hez0q1wyg7h3dw3l2t181u", "questionCode": "M1720", "controlName": "radio-group", "displayText": "Radio", "description": "Reported or Observed Within the Last 14 Days", "labelName": "When Anxious", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "kvge13mue6yhdc78gkqzg", "value": "0", "label": "0 - None of the time"}, {"id": "3miivcwq1tqplx1v4wnqta", "value": "1", "label": "1 - Less often than daily"}, {"id": "bcfz9vbzungevm8r55698t", "value": "2", "label": "2 - Daily, but not constantly"}, {"id": "3pmaxxybjnszp6r4hw8xm", "value": "3", "label": "3 - All of the time"}, {"id": "9q56btzcnzii6mbonlkwv", "value": "4", "label": "NA - Patient non-responsive"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743237045513, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "np6myl2s0tg19xkawqpye4", "questionCode": "M1740", "controlName": "checklist", "displayText": "Checklist", "description": "Cognitive, behavioral and psychiatric symptoms that are demonstrated at least once a week (reported or observed): (Mark all that apply)", "labelName": "Cognitive, Behavioral and Psychiatric", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "1 - Memory deficit: failure to recognize familiar persons/places, inability to recall events from the past 24 hours, significant memory loss so that supervision is required"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "2 - Impaired decision-making: failure to perform usual ADLs or IADLs, inability to appropriately stop activities, jeopardizes safety through actions"}, {"id": "8hmwhaj16ibufmpasdungc", "value": "3", "label": "3 - Verbal disruption: yelling, threatening, excessive profanity, sexual references, etc."}, {"id": "4evm8e9mvlumnf6lrz12b", "value": "4", "label": "4 - Physical aggression: aggressive or combative to self and others (e.g., hits self, throws objects, punches, dangerous maneuvers with wheelchair or other objects)"}, {"id": "gghhm1jbfcthwi0th2m0l4", "value": "5", "label": "5 - Disruptive, infantile, or socially inappropriate behavior (excludes verbal actions)"}, {"id": "41uelp87dbgcxkuo9u2un", "value": "6", "label": "6 - Delusional, hallucinatory, or paranoid behavior"}, {"id": "2dxhxduk11qpz6xsdh2g6r", "value": "7", "label": "7 - None of the above behaviors demonstrated"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067748541, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "z290j3ofkxdq7wjq7i8gtp", "questionCode": "M1745", "controlName": "radio-group", "displayText": "Radio", "description": "In the past month, how often have you experienced any of these kinds of behaviors?\n- Getting very angry or upset and shouting or yelling.\n - Physically hurting yourself or others.\n - Doing things that might put yourself in danger.", "labelName": "Behavior", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Never"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Less than once a month"}, {"id": "uy52amv5shia9aru7atjd", "value": "2", "label": "2 - Once a month"}, {"id": "2zdj7o8mlncg3iwhjn7dt8", "value": "3", "label": "3 - Several times each month"}, {"id": "5wz5699t474mx0l58nzr7", "value": "4", "label": "4 - Several times a week"}, {"id": "ktylv1fwsp9grt7ihcpen", "value": "5", "label": "5 - Atleast daily"}], "containerId": "7yafoql2y54z3a7rvkgsbc", "_tempId": 1743067826258, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "s07xcug37e1vbppvdvxl2", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section I", "subHeading": "ADLs & IADLs"}, "items": [{"id": "3vd6a1t9zmkjebc2k41vx", "questionCode": "M1800", "controlName": "radio-group", "displayText": "Radio", "description": "Can you tell me about any help you need with grooming, such as brushing your teeth, combing your hair, or shaving?", "labelName": "Grooming", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to groom self unaided, with or without the use of assistive devices or adapted methods."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Grooming utensils must be placed within reach before able to complete grooming activities."}, {"id": "f0ktkmo4owdfhg2jjj41b", "value": "2", "label": "2 - Someone must assist the patient to groom self."}, {"id": "bt3s9o0q9zjymu0izyjf2", "value": "3", "label": "3 - Patient depends entirely upon someone else for grooming needs."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068009505, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "q6r9oh9yqfkev0520a08f", "questionCode": "M1810", "controlName": "radio-group", "displayText": "Radio", "description": "Are you able to safely dress your upper body with or without help including pullovers, front-opening shirts and blouses, managing zippers, buttons and snaps?", "labelName": "Current Ability to Dress Upper Body", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to get clothes out of closets and drawers, put them on and remove them from the upper body without assistance."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Able to dress upper body without assistance if clothing is laid out or handed to the patient."}, {"id": "wnt128kamxgcj0t3imleak", "value": "2", "label": "2 - Someone must help the patient put on upper body clothing."}, {"id": "tvkv312n7wl8xbc7ptvtdi", "value": "3", "label": "3 - Patient depends entirely upon another person to dress the upper body."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068059936, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "xk1v8xn93umwl5jqv5bd", "questionCode": "M1820", "controlName": "radio-group", "displayText": "Radio", "description": "Are you able to safely dress your lower body with or without help including undergarments, slacks, socks or nylons, shoes?", "labelName": "Current Ability to Dress Lower Body", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to obtain, put on and remove clothing and shoes without assistance."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Able to dress lower body without assistance if clothing and shoes are laid out or handed to the patient."}, {"id": "jldymrnb3ztyowlae4bbga", "value": "2", "label": "2 - Someone must help the patient put on undergarments, slacks, socks or nylons, shoes."}, {"id": "9u2terfeieay50ijb1fgh8", "value": "3", "label": "3 - Patient depends entirely upon another person to dress the lower body."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068145453, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "5api2tmyp5ueosd3d9gq6u", "questionCode": "M1830", "controlName": "radio-group", "displayText": "Radio", "description": "Can you get in and out of the shower or tub, and wash yourself on your own, or do you use grab bars or a shower chair?", "labelName": "Bathing", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to bathe self in shower or tub independently, including getting in and out of tub/shower."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - With the use of device, is able to bathe self in shower or tub independently, including getting in and out of tub/shower."}, {"id": "5datfqkcn33ktklub01hwr", "value": "2", "label": "2 - Able to bathe in shower or tub with the intermittent assistance of another person: a) for intermittent supervision or encouragement or reminders, OR b) to get in and out of the shower or tub, OR c) for washing difficult to reach areas."}, {"id": "rvrh4br49fh91qtd4kzh8g", "value": "3", "label": "3 - Able to participate in bathing self in shower or tub, but requires presence of another person throughout the bath for assistance or supervision."}, {"id": "qy15ro2xzieppwf9ufz26", "value": "4", "label": "4 - Unable to use the shower or tub, but able to bathe self independently with or without the use of devices at the sink, in chair, or on commode."}, {"id": "y2hrmo48clapq9j6jut6mh", "value": "5", "label": "5 - Unable to use the shower or tub, but able to participate in bathing self in bed, at the sink, in bedside chair, or on commode, with the assistance or supervision of another person."}, {"id": "jj1m0s7vogpb75u5pwzyj", "value": "6", "label": "6 - Unable to participate effectively in bathing and is bathed totally by another person."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068211169, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "svm8mo8dujc5ks91foxr", "questionCode": "M1840", "controlName": "radio-group", "displayText": "Radio", "description": "Can you tell me how you usually get on and off the toilet? Do you need any help or special equipment to do this safely?", "labelName": "<PERSON><PERSON><PERSON>", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to get to and from the toilet and transfer independently with or without a device."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - When reminded, assisted, or supervised by another person, able to get to and from the toilet and transfer."}, {"id": "pv31mloczmf9oluhx293hg", "value": "2", "label": "2 - Unable to get to and from the toilet but is able to use a bedside commode with or without assistance."}, {"id": "di341hm0eus7ynn9jgqn", "value": "3", "label": "3 - Unable to get to and from the toilet or bedside commode but is able to use a bedpan/urinal independently."}, {"id": "dx9520sksqjlxj7pz9eneo", "value": "4", "label": "4 - Is totally dependent in toileting."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068293335, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "40yagjn3s4tycmk3odmhyi", "questionCode": "M1845", "controlName": "radio-group", "displayText": "Radio", "description": "How do you usually manage your toileting hygiene, like adjusting clothes or pads? Can you do it on your own, or need help?", "labelName": "Toileting Hygiene", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to manage toileting hygiene and clothing management without assistance."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Able to manage toileting hygiene and clothing management without assistance if supplies/implements are laid out for the patient."}, {"id": "w5z1nhhq7xnand81t27z3m", "value": "2", "label": "2 - Someone must help the patient to maintain toileting hygiene and/or adjust clothing."}, {"id": "87i7nz7xsg23f7zssi2ttj", "value": "3", "label": "3 - Patient depends entirely upon another person to maintain toileting hygiene."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068365369, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "pwnfswqfodrlndbn7j4k", "questionCode": "M1850", "controlName": "radio-group", "displayText": "Radio", "description": "How do you usually move from your bed to a chair? How do you turn or position yourself? Do you need some help?", "labelName": "Transferring", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to independently transfer."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Able to transfer with minimal human assistance or with use of an assistive device."}, {"id": "m5oss45m718vo2em1t72qb", "value": "2", "label": "2 - Able to bear weight and pivot during the transfer process but unable to transfer self."}, {"id": "popame1xa9yqzjq0xapxd", "value": "3", "label": " 3 - Unable to transfer self and is unable to bear weight or pivot when transferred by another person."}, {"id": "l6e8n80ef4prglne6gogxf", "value": "4", "label": "4 - <PERSON><PERSON>, unable to transfer but is able to turn and position self in bed."}, {"id": "vmqk9m21tdnthw5zm3utn", "value": "5", "label": "5 - <PERSON><PERSON>, unable to transfer but is unable to turn and position self."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068419616, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "b6buzto30ulebuhmhbxgvg", "questionCode": "M1860", "controlName": "radio-group", "displayText": "Radio", "description": "How do you manage walking? If you use a wheelchair, can you move around on different surfaces like carpet, tile, or outdoors?", "labelName": "Ambulation/Locomotion", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to independently walk on even and uneven surfaces and negotiate stairs with or without railings (specifically: needs no human assistance or assistive device)."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - With the use of a one-handed device (e.g. cane, single crutch, hemi-walker), able to independently walk on even and uneven surfaces and negotiate stairs with or without railings."}, {"id": "pdi27rck08kyfiq9rbu9gp", "value": "2", "label": "2 - Requires use of a two-handed device (e.g. walker or crutches) to walk alone on a level surface and/or requires human supervision or assistance to negotiate stairs or steps or uneven surfaces."}, {"id": "eesqrti4zkf7eh5h49rmm", "value": "3", "label": "3 - Able to walk only with the supervision or assistance of another person at all times."}, {"id": "yd8dp37x3adk89hxxc8pes", "value": "4", "label": "4 - <PERSON><PERSON>, unable to ambulate but is able to wheel self independently."}, {"id": "c3iy996301ltct0dbtbs4", "value": "5", "label": "5 - <PERSON><PERSON>, unable to ambulate and is unable to wheel self."}, {"id": "wztyrttpmarbvdv08tq4zt", "value": "6", "label": "6 - <PERSON><PERSON>, unable to ambulate or be up in a chair."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068483087, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "nbb7v7amsr70iweeywsxiw4", "questionCode": "M1870", "controlName": "radio-group", "displayText": "Radio", "description": "Are you able to eat meals and snacks okay? Do you need any help with chewing or swallowing?", "labelName": "Feeding or Eating", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to independently feed self."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Able to feed self independently but requires: (a) meal set-up, OR (b) intermittent assistance or supervision from another person, OR (c) a liquid, pureed or ground meat diet."}, {"id": "g0tfioj1pj9ztq04t2ehzo", "value": "2", "label": "2 - Unable to feed self and must be assisted or supervised throughout the meal/snack."}, {"id": "d8l9ly0jahf97va2tgfgn6", "value": "3", "label": "3 - Able to take in nutrients orally and receives supplemental nutrients through a nasogastric tube or gastrostomy."}, {"id": "15aikqrykdjujrq2tbnfla", "value": "4", "label": "4 - Unable to take in nutrients orally and is fed nutrients through a nasogastric tube or gastrostomy."}, {"id": "j9skt1uw2tj4nuwngxgatq", "value": "5", "label": "5 - Unable to take nutrients orally or by tube feeding."}], "containerId": "s07xcug37e1vbppvdvxl2", "_tempId": 1743068562147, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "6uek8ko7kxb2tpm3mbndoo", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section J", "subHeading": "GG Functional Abilities and Goals"}, "items": [{"id": "yf8s00ykt409s32l46lk7x", "questionCode": "GG0100.A", "controlName": "radio-group", "displayText": "Radio", "description": "Before your current illness or injury, how much help did you need with everyday activities like bathing, dressing, using a toilet, and eating?", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "3 – Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "2 – Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "yfbeqv51if5f38c0cwich", "value": "1", "label": "1 – Dependent – A helper completed all the activities for the patient."}, {"id": "4hg8rayf8u3rd5itnbv9n", "value": "8", "label": "8 – Unknown"}, {"id": "y55lj1ag23pmmqr8vcpcpd", "value": "9", "label": "9 – Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743069979255, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "c09mgo7bb5l9ceshgn6zq", "questionCode": "GG0100.B", "controlName": "radio-group", "displayText": "Radio", "description": "Before your current illness or injury, how much assistance did you need to walk from room to room, even with a cane or walker?", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "3 – Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "2 – Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "cdu0ckrrel5w5ocuwhgea", "value": "1", "label": "1 – Dependent – A helper completed all the activities for the patient."}, {"id": "453smwtquq6r0h6w07unho", "value": "8", "label": "8 – Unknown"}, {"id": "0shjer94fy4yjryykw6ok", "value": "9", "label": "9 – Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070167933, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "1tmhcw2450rkfb3rq39p9", "questionCode": "GG0100.C", "controlName": "radio-group", "displayText": "Radio", "description": "Before your current illness or injury, did you need any help to go up and down stairs, whether inside or outside?", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "3 – Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "2 – Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "gkue070h13pu7k65nb3qes", "value": "1", "label": "1 – Dependent – A helper completed all the activities for the patient."}, {"id": "z8sfanaf6v6knr4ueqs2j", "value": "8", "label": "8 – Unknown"}, {"id": "mm3kvt2e3vhqj4chhwr6g", "value": "9", "label": "9 – Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070223249, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "4ty59gpblj56nzz3qeu95", "questionCode": "GG0100.D", "controlName": "radio-group", "displayText": "Radio", "description": "Before your current illness or injury, did you need help planning tasks like shopping or remembering to take your medication?", "labelName": "Prior Functioning: Everyday Activities", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "3", "label": "3 – Independent – Patient completed the activities by him/herself, with or without an assistive device, with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "2 – Needed Some Help – Patient needed partial assistance from another person to complete any activities."}, {"id": "litqp8r0g6wuf4g9stl9", "value": "1", "label": "1 – Dependent – A helper completed all the activities for the patient."}, {"id": "jfy5bwjyoiyzyu92imupa", "value": "8", "label": "8 – Unknown"}, {"id": "5qwnwvadsnoa2up3z8s4sq", "value": "9", "label": "9 – Not Applicable"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070276631, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "wdf3eu963357f65mqhix", "questionCode": "GG0110", "controlName": "checklist", "displayText": "Checklist", "description": "Before your current illness or injury, what device or aid were you using, like a walker, cane, or hearing aid?", "labelName": "Prior Device Use", "itemType": "control", "icon": {"displayName": "SquareCheckBig"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "visgx18sdcak4vanylz84", "value": "1", "label": "<PERSON>. Manual wheelchair"}, {"id": "rnsuq47nl3qwoyyhuxubc", "value": "2", "label": "B. Motorized wheelchair and/or scooter"}, {"id": "pb4nif1tluhbakjg8gku0t", "value": "3", "label": "C. Mechanical lift"}, {"id": "p76bb1smg6xktayv6auo", "value": "4", "label": "<PERSON><PERSON>"}, {"id": "e86y4qdz1lppos9fkge08", "value": "5", "label": "E. Orthotics/prosthetics"}, {"id": "1180rxmlaziqm1w80o124", "value": "6", "label": "<PERSON>. None of the above"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070397812, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "kztefohs5glh93f8toqi8", "questionCode": "GG0130.A", "controlName": "radio-group", "displayText": "Radio", "description": "Are you able to use a spoon or a fork to bring food to your mouth and swallow it?", "labelName": "Self-Care ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "22eacmch2wl5f5bl9z39nj", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "p8upm8zcz0k0adyav5bh287", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "w3jay3hlimi11z0sdbdkp", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "jv8a1y6w4slh55yf3hdv4", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "c55cbi4b5s8ce2cbi0zngp", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "yq24uahranzb13roa8p58", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "yqr99h7e67084lovyw0xmc", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "fbvl0l31srcqejute45doa", "value": "88", "label": "88 - Not attempted due to medical conditions or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070610214, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "6cy21xohnmhiah04gqpwlg", "questionCode": "GG0130.B", "controlName": "radio-group", "displayText": "Radio", "description": "How do you manage brushing your teeth or handling your dentures? Can you do it on your own?", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "c6nvsv2o6k5cj0krukdkz5", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "6nwqiaak958msbmut09bh9", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ltdk0vxjm8pl57rup2v9r", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "4bynm1yscdsxhz4gntct7c", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "oxei7l2x0g9ueyfu218bui", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "zyn5qg2hw4fgktafysx3a", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "q3idjs98t8elupf0o2a02", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "ax0odgc3zecuv6ige7jgqk", "value": "88", "label": "88 - Not attempted due to medical conditions or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070738508, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "pn6k9majiv9kein9gjw87h", "questionCode": "GG0130.C", "controlName": "radio-group", "displayText": "Radio", "description": "Are you able to clean yourself after using the toilet or handling an ostomy?", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "a063y1in5vtced3z2kpmul", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "byu2meb1kusmp15gsapxj", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ag3apk5vasv8gobwgox1kg", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "9ucgsqo361lx3l7al1tqw", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "m3cn0zoycpmrjq0rrlee8p", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "rrxlr3cpcyekcrpmm7xpk9", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "udtse02ljco5mljc82dba", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "4z85ledxx2goqolsqx35mh", "value": "88", "label": "88 - Not attempted due to medical conditions or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070834947, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "fvykuj8df9rvmyriaw8pqo", "questionCode": "GG0130.E", "controlName": "radio-group", "displayText": "Radio", "description": "How do you handle washing and drying yourself, excluding your back and hair? Do you need help with that?", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "rb16e2rj2kat3nuhqqet7", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "qj7pawc8sgtf7v45xudf", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "kmoteqn33xqzehbn83mq", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "091qnsrfiiga1pcjtb9pcip", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "xv967wvv9oswq5ofgyv30m", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "b6j5nt4e6enpbt2fgznii", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "zdhrauk3v7u2vwem92v0j", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "45if6p4rl5epz181xvet2n", "value": "88", "label": "88 - Not attempted due to medical conditions or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743070944221, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "q9lcfarvd3zy9zhagjafn", "questionCode": "GG0130.F", "controlName": "radio-group", "displayText": "Radio", "description": "How do you manage dressing and undressing your upper body, including any buttons or zippers?", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "p2mcza4f1fp0n6ljmeru3o", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "r10crooc9a0joe4a4mip2k", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ls1qj9l1hkgt52vc4jnhg", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "a27qo9zong88zr9gdmh57", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "mupwrmoytbq8mc077rasp", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "rrt8mkfq9h97p075wrbvkc", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "qgjq88n4iuch6l5bf3vkq", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "blppiyt729ia4p350ri21m", "value": "88", "label": "88 - Not attempted due to medical conditions or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071036653, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "bllkh1nzpwlcpskcr6rol", "questionCode": "GG0130.G", "controlName": "radio-group", "displayText": "Radio", "description": "How do you manage dressing and undressing your lower body (does not include footwear)?", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "tvgdmxi3uybpu3mfgsyh", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "fdg1kj7lq8wkw6ntn9v8tl", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "fltlcks1f2fbtyyznyyoqg", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "mtvt388qf1mxkaomcxwhh", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "wx55orndqjfteb3iy02vn", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "5t4aouai71kyl8beeqng4", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "4nl4d4ajct94ngkdfz0fg", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "olupm2umqksd8deagrleum", "value": "88", "label": "88 - Not attempted due to medical conditions or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071133496, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "d4tj2j7uslechc19banhj", "questionCode": "GG0130.H", "controlName": "radio-group", "displayText": "Radio", "description": "How do you manage putting on and taking off your socks and shoes, including any fasteners?", "labelName": "Self-Care", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "5l6n3l4hqutx3eemxkpkpo", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides verbal cues and/or touching/steadying and/or contact guard assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "8x48dki04evo71nxdb7gxn", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "50n7n2fucedg01z7130dwp", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "pilgx2yrlkcain5mq1t7k", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "xh0u9t0z1ctc4b2nofs5p", "value": "7", "label": "07 - <PERSON><PERSON> refused"}, {"id": "qfkl1kp352azqj37ecxnl", "value": "8", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "yvh8rr5yfivoix7hglgv", "value": "9", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "65n9186jvc4bkcmplpiws8", "value": "10", "label": "88 - Not attempted due to medical conditions or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071250419, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "1u3lcn3hcgbxqcskt616wb", "questionCode": "GG0170.A", "controlName": "radio-group", "displayText": "Radio", "description": "How do you roll from your back to your sides and back again in bed?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "g5lcn6c8c6ii0ryk36f0eq", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "s8v0mcmiaqoucjkiy7xy4s", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "3kmqtvgxmaep06fil2mqt", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "u8jg9043r5atb8wgzhhxv", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "ulkfuew17vnm24jsyty24", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "u932nt7ah566688cagaex", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "gh7zfq7qaert156mo5hiqp", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "vwlrtglb0jlaa4zpz4h20i", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "xa39c6y65eo7bg8kd0den", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071409875, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "umy8xny2mgq4s1iqtayjf2", "questionCode": "GG0170.B", "controlName": "radio-group", "displayText": "Radio", "description": "How do you move from sitting on the side of the bed to lying down?", "labelName": "Mobility ", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "ay9s9dcxs38sxfiuxg7k", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "4ctrzcl80k9omdueulktyh", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort.03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "m66bjtg2ype1zqvylvjc6", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "x7yhkvviggy0rq6h65klb", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "hpatfo8ux2enldhe09029n", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "okumaw1vqvajyeudox6xs", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "s3vjij6midk919glt97ica", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "k026xvszi87yi2s850dnq", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071613747, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "7kmndd5n99a5pwmk6dhwjx", "questionCode": "GG0170.C", "controlName": "radio-group", "displayText": "Radio", "description": "How do you move from lying down to sitting up on the side of the bed?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "lhrbbv26eaepngq9c3615s", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "h03oleu0zi7be1lap0ztxn", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "5lcd4dv6iv68ehdlv584ja", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "m82az7rwlut0m860ktv32nc", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "ytaf969soqi3kbwrnjk8db", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "z4ooufftb3v2mjdhntx4", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "yxafl7nx3fmxuk3yttiw0d", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "xtd11ojlabcrejieov4nap", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071728646, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "khoaxhek31qksi03w54c5b", "questionCode": "GG0170.D", "controlName": "radio-group", "displayText": "Radio", "description": "How do you stand up from a chair, wheelchair, or the side of the bed?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "n29x60zp3nflpkcddmgsiq", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "vl8upvfzbpqlb5d5d08jq", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "n0agav05qnc3ija4a7lrh", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "u9e581unh5qs3rfkrdtjec", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "la0b7aimctoxfxldh2q9a", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "50o3akqyo3bfxo94sdfgr6", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "t17e4mqu6rvs23rysnb6", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "25u7t0767io5jhncwfdvqd", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071840875, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "29kgvhx2jurkifnirnae6", "questionCode": "GG0170.E", "controlName": "radio-group", "displayText": "Radio", "description": "How do you transfer yourself from the bed to a chair or wheelchair and back?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "k0jtv0ukvch0fypfzw2sk", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "ggjhmy3lo3lduw3qbu07sl", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "197pwyuyy4w4bc4y9r8m2m", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "a4qzfs9fu9qu8r4d2gzf3r", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "txaiyupelxgse1di07pqt", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "io79p360se3re3z383nb1", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "s4ac5pv4nj1x7nbzju3cd", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "apzeb2r00ao8q95gcaj5iv", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743071968640, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "adukb92ehls1kosym7c7by", "questionCode": "GG0170.F", "controlName": "radio-group", "displayText": "Radio", "description": "How do you get on and off the toilet or commode? Do you need help with it?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "9d8v5v02x2nsld3cot4w1j", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "d79roloa22cgzdg98ldta", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "murwlrzymtcw5wxs48blan", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "niud8bw9gcjt6lklm7gqbc", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "sa6xnb7ixqhj8zjgxpadn", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "38olmkz5pke5s21v60yldq", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "u8b6e9mptibd9xu57ck8w5", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "hq8s4qw4ma1ggbpejl0jw", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072058086, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "pnpgylt1xo834e1n5duq5", "questionCode": "GG0170.G", "controlName": "radio-group", "displayText": "Radio", "description": "How do you manage getting in and out of a car on the passenger side?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "z9gg3c5g34gyn9blfjba39", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "mn53unpqdlfh0u28g1trb4", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "tmccb89jnmcc5ou2n01g8", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "bkkwana8wdoma8ul4qnzaq", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "swyg3iuxjiab4q9g23tj3f", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "vmt9b7y6zikluxtbz4vkz", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "jnaxo91u8mcpy0azy40xce", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)."}, {"id": "e0c8o5hjaofwh6pvpst47i", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072226971, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "tybf9psjwoujhpcb0scq", "questionCode": "GG0170.I", "controlName": "radio-group", "displayText": "Radio", "description": "Walk 10 feet: Once standing, the ability to walk at least 10 feet in a room, corridor, or similar space.\n  If SOC/ROC performance is coded 07, 09, 10 or 88, skip to GG0170M, 1 step (curb).", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "wsn8k8dtnek1ov2m1kolvv", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "i4isotuo4sdj5odbo4i8d", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "hasiudguaxd7zu23g6bdko", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "7o4t4gnqpakitj2ur8x4h", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "yk44tm3ucddxsa6zp4v0m", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "jf8gsy5l41bdnsjae8o1s6", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "cszirwfl2lrtas6zmm530l", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "re6bz8k9rz3pexmfkfpjx", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072375595, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "ou133zbtu4bljfq2n4bof", "questionCode": "GG0170.J", "controlName": "radio-group", "displayText": "Radio", "description": "Walk 50 feet with two turns: Once standing, the ability to walk 50 feet and make two turns.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "nboqnetmzuv1tbdhtxmla", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "ufhmmuc28so9tae29rr78g", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "jcclour4i2tkb25gv49hh", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "jz1o0mjspshs2gojlue2he", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "jpjnpuot1pq2co7eeiwg", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "1xw9m3er7pwun7d2jx845i", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "dxy8nqb7nxs8eaj65c9a52", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "rqzyl6hbgqfm1c2569mkm", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072469266, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "h120vx9liafwnc4x52hbj", "questionCode": "GG0170.K", "controlName": "radio-group", "displayText": "Radio", "description": "Walk 150 feet: Once standing, the ability to walk at least 150 feet in a corridor or similar space.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "gz138xwbmdljkoo5s3hw09", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "97bf8hhz0yle7c8z6u311", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ljo7yy8tksomz2vtri6k", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "nm0k2zyrqtz966i2lnxw", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "00wt06jy3ramscgeval6zn", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "xo2cqnzv139rsa5pv9evdl", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "12rli24r9k89azqz6r189lr", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "3cpawpeopw773r8qb78fmd", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072564716, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "nryrhykxt9145ymszgeoa", "questionCode": "GG0170.L", "controlName": "radio-group", "displayText": "Radio", "description": "Walking 10 feet on uneven surfaces: The ability to walk 10 feet on uneven or sloping surfaces (indoor or outdoor), such as turf or gravel.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "5i1yr6wb8j48k3b87tvu6v", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "walvey5f8riicoj0b1x8j", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "yva4e5ofib90a2umxw6ol", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "xvjyigo73lq88t9rnjbkwy", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "uk7jtm8yht7gpzkgjgczis", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "nrlkgil6pfnkltxc7558n", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "ueknxr3yubbwk65kf8", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "vynjbtnlzgh85abbcxv91", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072689813, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "9pss8itsz8fl7p5aweqchq", "questionCode": "GG0170.M", "controlName": "radio-group", "displayText": "Radio", "description": "1 step (curb): The ability to go up and down a curb or up and down one step.\n If SOC/ROC performance is coded 07, 09, 10 or 88, ➔ Skip to GG0170P, Picking up object.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "6v2kj15fbyjpmsr3djnog", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "1y7ebvz2dr7yskxe6do85", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "jhp2ks33z8n0u00590l09c", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "lqy9h9yc1ij9tp015dxdb", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "5sxjnb9j826wex2runlwqd", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "o3djqwdj6dh4ziwoi8rri", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "aavkn5lfjo4c39rizphpgj", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "s0zc88rkz6qxtf4m4x7wb", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072775796, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "kjhp2c15lxz09jopawcof", "questionCode": "GG0170.N", "controlName": "radio-group", "displayText": "Radio", "description": "4 steps: The ability to go up and down four steps with or without a rail.\n If SOC/ROC performance is coded 07, 09, 10 or 88, skip to GG0170P, Picking up object.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "l8o5xuaw17cmcm94dilsag", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "efdyagowmd8pvyobsapvmp", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "q56fttymtwrm81brez4gs8", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "645mf2959hq3tjosj0kv9b", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "q1ysyjv5p6rqdsicw4e", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "53z6r6olgr2uxek7zhhk2", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "imzly0nb7v9wlnbg3koqo", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "ai4yd7zhwx9c0egg3wideh", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072856811, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "jamg9ooneld0yakyfd12", "questionCode": "GG0170.O", "controlName": "radio-group", "displayText": "Radio", "description": "12 steps: The ability to go up and down 12 steps with or without a rail.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "x9iuhye1incg1p76x3549", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "kaoyab7b2gqayt28ktfa9o", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "79q6cyk3y59o348uv4ee6", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "sptsfvvkn4ed75msn0fygp", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "qg6p2mxvohby9lesm3qfir", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "8u4mkkcp0pi3p0sjx3tmlr", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "6k33aumfi1dtvnk38p79na", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "f9upuq7qcmd4l8q9b52j0f", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743072953862, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "9xhajpk87fk9y8i2ie65xn", "questionCode": "GG0170.P", "controlName": "radio-group", "displayText": "Radio", "description": "Picking up object: The ability to bend/stoop from a standing position to pick up a small object, such as a spoon, from the floor.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "aznmlq0a5zobzx77wrlze", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "rrbdcwcg3wqw2h5ua2olb", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "x4t8ixky2xn6gqxfvwyk4n", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "h7nsitk2ubszrx149gm87", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "m9oins3dccmlw305s1dnxq", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "xr5mmcez31s4ehyruycf27", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "1t9os7jvko5h3zgp48ghqc", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "ed5w1mtiwvb5i48v8cbcnh", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073054174, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "7ttimxga4csdxpyoxizz", "questionCode": "GG0170.Q", "controlName": "radio-group", "displayText": "Radio", "description": "Do you use a wheelchair and/or scooter?", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "No - Skip to M1600, Urinary Tract Infection"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "Yes - Continue to GG0170R, Wheel 50 feet with two turns."}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073143482, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "ty9v6dfpk8xfokg0pfc6", "questionCode": "GG0170.R", "controlName": "radio-group", "displayText": "Radio", "description": "Wheel 50 feet with two turns: Once seated in wheelchair/scooter, the ability to wheel at least 50 feet and make two turns.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "tf116yx4mon1m8lz18j0bj", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "vgxh1fs2fwg9s0fgjrpmoc", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ac5fqyxzgnv02oibtmw5ld1", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "t1w4ilfa6ykhhmhfjaep", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "3ko7xtsgpx1lgiowxw2ct", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "lxk4ff7lmb1o7zckgjmqi", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "pki0y97qwnrsryrdk1ir", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "v1nl8rg3xh3noxau9i91r", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073195526, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "gza62srdrqgda9tcofn8xj", "questionCode": "GG0170.R1", "controlName": "radio-group", "displayText": "Radio", "description": "Indicate the type of wheelchair or scooter used.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Manual"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Motorized"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073273054, "isContainer": false, "level": 0, "answer_context": "Text Context", "isDisplayCheatSheet": true}, {"id": "96kwhzzilh7jshpv98bmk", "questionCode": "GG0170.S", "controlName": "radio-group", "displayText": "Radio", "description": "Wheel 150 feet: Once seated in wheelchair/scooter, the ability to wheel at least 150 feet in a corridor or similar space.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "06", "label": "06 - Independent – <PERSON><PERSON> completes the activity by themself with no assistance from a helper."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "05", "label": "05 - Setup or clean-up assistance – Helper SETS UP or CLEANS UP; patient completes activity. Helper assists only prior to or following the activity."}, {"id": "xxml6wt4xbig1fa6i8jdvg", "value": "04", "label": "04 - Supervision or touching assistance – Helper provides VERBAL CUES or TOUCHING/STEADYING assistance as patient completes activity. Assistance may be provided throughout the activity or intermittently."}, {"id": "pikj54wepw9wd69ldwm52r", "value": "03", "label": "03 - Partial/moderate assistance – Helper does LESS THAN HALF the effort. Helper lifts, holds or supports trunk or limbs, but provides less than half the effort."}, {"id": "ys5ftzjzl8or8ivefudb8", "value": "02", "label": "02 - Substantial/maximal assistance – Helper does MORE THAN HALF the effort. Helper lifts or holds trunk or limbs and provides more than half the effort."}, {"id": "0esqvw32h10i3sjfijxby2a", "value": "01", "label": "01 - Dependent – Helper does ALL of the effort. Patient does none of the effort to complete the activity. Or, the assistance of 2 or more helpers is required for the patient to complete the activity."}, {"id": "0i3o2xqpdyzpp1unsnp8w9", "value": "07", "label": "07 - <PERSON><PERSON> refused"}, {"id": "wgegjyac8qkfxop03agkyw", "value": "09", "label": "09 - Not applicable - Not attempted and the patient did not perform this activity prior to the current illness, exacerbation or injury."}, {"id": "autl1nt3xvraogjgvgz6cc", "value": "10", "label": "10 - Not attempted due to environmental limitations (e.g., lack of equipment, weather constraints)"}, {"id": "2dd2lf2uoqfgb62wvqllp9", "value": "88", "label": "88 - Not attempted due to medical condition or safety concerns"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073321438, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "4rhk8ejon5vjpf3ksx3to", "questionCode": "GG0170.S1", "controlName": "radio-group", "displayText": "Radio", "description": "Indicate the type of wheelchair or scooter used.", "labelName": "Mobility", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "1", "label": "Manual"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "2", "label": "Motorized"}], "containerId": "6uek8ko7kxb2tpm3mbndoo", "_tempId": 1743073406307, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "r7s3ru51g241glh0aw6a5", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section K", "subHeading": "Medications"}, "items": [{"id": "xvis67sszkqlayqg9jvxlo", "questionCode": "M2001", "controlName": "radio-group", "displayText": "Radio", "description": "Did a complete drug regimen review identify potential clinically significant medication issues?", "labelName": "Drug Regimen Review", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - No - No issues found during review → Skip to M2010, Patient/Caregiver High-Risk Drug Education"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Yes - Issues found during review"}, {"id": "ofbm370uzewpdjyvvrhos", "value": "9", "label": "9 - NA - Pat<PERSON> is not taking any medications → Skip to O0110, Special Treatments, Procedures, and Programs"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074295758, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "cmo1tnp3rfcb8h28a5lel", "questionCode": "M2003", "controlName": "radio-group", "displayText": "Radio", "description": "Did the agency contact a physician (or physician-designee) by midnight of the next calendar day and complete prescribed/recommended actions in response to the identified potential clinically significant medication issues?", "labelName": "Medication Follow-up", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Yes"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074342377, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "bdmzfobliv8o1f2ck8cnms", "questionCode": "M2010", "controlName": "radio-group", "displayText": "Radio", "description": "We want to make sure you feel comfortable and safe taking your medications. Have you received instructions on any special precautions you need to take?", "labelName": "Patient/Caregiver High Risk Drug Education", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": false, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - No"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Yes"}, {"id": "ebd6bbdqxq3h9gg7k6xm3", "value": "3", "label": "NA - Patient not taking any high-risk drug OR patient/caregiver fully knowledgeable about special precautions associated with all high-risk medications"}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074398033, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "78v4rwm6s6edjpdcmnb1z", "questionCode": "M2020", "controlName": "radio-group", "displayText": "Radio", "description": "Are you able to safely take your pills? Do you have any issues opening a bottle or with swallowing? Can you read the label okay? Do you need reminders? Are there any medications that are harder for you to manage than others?", "labelName": "Management of Oral Medications", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to independently take the correct oral medication(s) and proper dosage(s) at the correct times."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Able to take medication(s) at the correct times (if a. Individual dosages prepared in advance by another person or b. another person develops a drug diary or chart)."}, {"id": "q7gmgto3fc5o3f4cjtl3m", "value": "2", "label": "2 - Able to take medication(s) at the correct times if given reminders by another person at the appropriate times."}, {"id": "u8rjxligf2q6ag85sh3ie", "value": "3", "label": "3 - Unable to take medication unless administered by another person."}, {"id": "9416bwfi74m5qi9tliefqx", "value": "4", "label": "NA - No oral medications prescribed."}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074471105, "isContainer": false, "level": 0, "answer_context": "Text Context"}, {"id": "6xc4bykhhv2a7gyemdi4u6", "questionCode": "M2030", "controlName": "radio-group", "displayText": "Radio", "description": "Can you show me how you prepare your injectable medication? Can you draw up the correct dose into the syringe? Do you ever need reminders from someone to give yourself your injection at the right time?", "labelName": "Management of Injectable Medications", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - Able to independently take the correct medication(s) and proper dosage(s) at the correct times."}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Able to take injectable medication(s) at the correct times (if a. Individual syringes are prepared in advance by another person or b. another person develops a drug diary or chart)."}, {"id": "xn5vicdjnbg9l3itex5lf", "value": "2", "label": "2 - Able to take medication(s) at the correct times if given reminders by another person based on the frequency of the injection."}, {"id": "tnqo1iu6tgb1sl0bnvgdol", "value": "3", "label": "3 - Unable to take injectable medication unless administered by another person."}, {"id": "ha5tykyvqwubaot3ke6bif", "value": "4", "label": "NA - No injectable medications prescribed."}], "containerId": "r7s3ru51g241glh0aw6a5", "_tempId": 1743074521530, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}, {"container": {"id": "ttfknz1r1pz22ehvgkfy", "controlName": "step-container", "displayText": "Add Section", "itemType": "container", "heading": "Section L", "subHeading": "Care Management"}, "items": [{"id": "xjo0u1ky7tm1fxfbtlzx9k", "questionCode": "M2102", "controlName": "radio-group", "displayText": "Radio", "description": "[To Patient] Is there someone who is usually available to check on you and help if you get confused or forgetful?\n[To caregiver]: Do you feel you have the time and ability to provide the level of assistance needed for them (patient)?", "labelName": "Types and Sources of Assistance", "itemType": "control", "icon": {"displayName": "Radio"}, "required": false, "isDisplayCheatSheet": true, "items": [{"id": "ec6nin121vktfaacn5htd", "value": "0", "label": "0 - No assistance needed - patient is independent or does not have needs in this area"}, {"id": "rt2ylkamcjx3xgodnmgy", "value": "1", "label": "1 - Non-agency caregiver(s) currently provide assistance"}, {"id": "vngfkw9x3ye4ob21n723q", "value": "2", "label": "2 - Non-agency caregiver(s) need training/supportive services to provide assistance"}, {"id": "tawm1vufoqm1q0ezkct8x2", "value": "3", "label": "3 - Non-agency caregiver(s) are not likely to provide assistance OR it is unclear if they will provide assistance"}, {"id": "ox2wrv56976d0svpt9r14", "value": "4", "label": "4 - Assistance needed, but no non-agency caregiver(s) available"}], "containerId": "ttfknz1r1pz22ehvgkfy", "_tempId": 1743074613185, "isContainer": false, "level": 0, "answer_context": "Text Context"}]}]